{"name": "add_contact", "description": "This tool belongs to the Message API, which is used to manage user interactions in a workspace. Tool description: Add a contact to the workspace.", "parameters": {"type": "dict", "properties": {"user_name": {"type": "string", "description": "User name of contact to be added."}}, "required": ["user_name"]}, "response": {"type": "dict", "properties": {"added_status": {"type": "boolean", "description": "True if the contact was added successfully, False otherwise."}, "user_id": {"type": "string", "description": "User ID of the added contact."}, "message": {"type": "string", "description": "A message describing the result of the addition attempt."}}}}
{"name": "delete_message", "description": "This tool belongs to the Message API, which is used to manage user interactions in a workspace. Tool description: Delete the latest message sent to a receiver.", "parameters": {"type": "dict", "properties": {"receiver_id": {"type": "string", "description": "User ID of the user to send the message to."}, "message_id": {"type": "integer", "description": "ID of the message to be deleted."}}, "required": ["receiver_id"]}, "response": {"type": "dict", "properties": {"deleted_status": {"type": "boolean", "description": "True if the message was deleted successfully, False otherwise."}, "message_id": {"type": "integer", "description": "ID of the deleted message."}, "message": {"type": "string", "description": "A message describing the result of the deletion attempt."}}}}
{"name": "get_message_stats", "description": "This tool belongs to the Message API, which is used to manage user interactions in a workspace. Tool description: Get statistics about messages for the current user.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"stats": {"type": "dict", "description": "Dictionary containing message statistics.", "properties": {"received_count": {"type": "integer", "description": "Number of messages received by the current user."}, "total_contacts": {"type": "integer", "description": "Total number of contacts the user has interacted with."}}}}}}
{"name": "get_user_id", "description": "This tool belongs to the Message API, which is used to manage user interactions in a workspace. Tool description: Get user ID from user name.", "parameters": {"type": "dict", "properties": {"user": {"type": "string", "description": "User name of the user. "}}, "required": ["user"]}, "response": {"type": "dict", "properties": {"user_id": {"type": "string", "description": "User ID of the user"}}}}
{"name": "list_users", "description": "This tool belongs to the Message API, which is used to manage user interactions in a workspace. Tool description: List all users in the workspace.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"user_list": {"type": "array", "description": "List of all users in the workspace.", "items": {"type": "string"}}}}}
{"name": "message_get_login_status", "description": "This tool belongs to the Message API, which is used to manage user interactions in a workspace. Tool description: Get the login status of the current user.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"login_status": {"type": "boolean", "description": "True if the current user is logged in, False otherwise."}}}}
{"name": "message_login", "description": "This tool belongs to the Message API, which is used to manage user interactions in a workspace. Tool description: Log in a user with the given user ID to messeage application.", "parameters": {"type": "dict", "properties": {"user_id": {"type": "string", "description": "User ID of the user to log in. "}}, "required": ["user_id"]}, "response": {"type": "dict", "properties": {"login_status": {"type": "boolean", "description": "True if login was successful, False otherwise."}, "message": {"type": "string", "description": "A message describing the result of the login attempt."}}}}
{"name": "search_messages", "description": "This tool belongs to the Message API, which is used to manage user interactions in a workspace. Tool description: Search for messages containing a specific keyword.", "parameters": {"type": "dict", "properties": {"keyword": {"type": "string", "description": "The keyword to search for in messages."}}, "required": ["keyword"]}, "response": {"type": "dict", "properties": {"results": {"type": "array", "description": "List of dictionaries containing matching messages.", "items": {"type": "dict", "properties": {"receiver_id": {"type": "string", "description": "User ID of the receiver of the message."}, "message": {"type": "string", "description": "The message containing the keyword."}}}}}}}
{"name": "send_message", "description": "This tool belongs to the Message API, which is used to manage user interactions in a workspace. Tool description: Send a message to a user.", "parameters": {"type": "dict", "properties": {"receiver_id": {"type": "string", "description": "User ID of the user to send the message to."}, "message": {"type": "string", "description": "Message to be sent."}}, "required": ["receiver_id", "message"]}, "response": {"type": "dict", "properties": {"sent_status": {"type": "boolean", "description": "True if the message was sent successfully, False otherwise."}, "message_id": {"type": "integer", "description": "ID of the sent message."}, "message": {"type": "string", "description": "A message describing the result of the send attempt."}}}}
{"name": "view_messages_sent", "description": "This tool belongs to the Message API, which is used to manage user interactions in a workspace. Tool description: View all historical messages sent by the current user.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"messages": {"type": "dict", "description": "Dictionary of messages grouped by receiver An example of the messages dictionary is {\"USR001\":[\"Hello\"],\"USR002\":[\"World\"]}."}}}}
