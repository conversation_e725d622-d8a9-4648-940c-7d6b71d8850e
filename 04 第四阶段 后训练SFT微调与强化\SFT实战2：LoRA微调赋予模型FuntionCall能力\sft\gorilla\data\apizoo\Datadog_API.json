[{"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Key Management: Get all API keys", "api_call": "GET /api/v2/api_keys", "api_version": "v2", "api_arguments": {"page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[number]": "integer: Specific page number to return.", "sort": "string: API key attribute used to sort results. Sort order is ascending\nby default. In order to specify a descending sort, prefix the\nattribute with a minus sign.", "filter": "string: Filter API keys by the specified string.", "filter[created_at][start]": "string: Only include API keys created on or after the specified date.", "filter[created_at][end]": "string: Only include API keys created on or before the specified date.", "filter[modified_at][start]": "string: Only include API keys modified on or after the specified date.", "filter[modified_at][end]": "string: Only include API keys modified on or before the specified date.", "include": "string: Comma separated list of resource paths for related resources to include in the response. Supported resource paths are `created_by` and `modified_by`.", "filter[remote_config_read_enabled]": "boolean: Filter API keys by remote config read enabled status.", "filter[category]": "string: Filter API keys by category."}, "functionality": "List all API keys available for your account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Key Management: Create an API key", "api_call": "POST /api/v2/api_keys", "api_version": "v2", "api_arguments": {}, "functionality": "Create an API key.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Key Management: Delete an API key", "api_call": "DELETE /api/v2/api_keys/{api_key_id}", "api_version": "v2", "api_arguments": {"api_key_id": "[REQUIRED] string: The ID of the API key."}, "functionality": "Delete an API key.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Key Management: Get API key", "api_call": "GET /api/v2/api_keys/{api_key_id}", "api_version": "v2", "api_arguments": {"api_key_id": "[REQUIRED] string: The ID of the API key.", "include": "string: Comma separated list of resource paths for related resources to include in the response. Supported resource paths are `created_by` and `modified_by`."}, "functionality": "Get an API key.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Key Management: Edit an API key", "api_call": "PATCH /api/v2/api_keys/{api_key_id}", "api_version": "v2", "api_arguments": {"api_key_id": "[REQUIRED] string: The ID of the API key."}, "functionality": "Update an API key.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - API Management: Delete an API", "api_call": "DELETE /api/v2/apicatalog/api/{id}", "api_version": "v2", "api_arguments": {"id": "[REQUIRED] string: ID of the API to delete"}, "functionality": "Delete a specific API by ID.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - API Management: Get an API", "api_call": "GET /api/v2/apicatalog/api/{id}/openapi", "api_version": "v2", "api_arguments": {"id": "[REQUIRED] string: ID of the API to retrieve"}, "functionality": "Retrieve information about a specific API in [OpenAPI](https://spec.openapis.org/oas/latest.html) format file.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - API Management: Update an API", "api_call": "PUT /api/v2/apicatalog/api/{id}/openapi", "api_version": "v2", "api_arguments": {"id": "[REQUIRED] string: ID of the API to modify"}, "functionality": "Update information about a specific API. The given content will replace all API content of the given ID.\nThe ID is returned by the create API, or can be found in the URL in the API catalog UI.\n", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - API Management: Create a new API", "api_call": "POST /api/v2/apicatalog/openapi", "api_version": "v2", "api_arguments": {}, "functionality": "Create a new API from the [OpenAPI](https://spec.openapis.org/oas/latest.html) specification given.\nIt supports version `2.0`, `3.0` and `3.1` of the specification. A specific extension section, `x-datadog`,\nlet you specify the `teamHandle` for your team responsible for the API in Datadog.\nIt returns the created API ID.\n", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Spans Metrics: Get all span-based metrics", "api_call": "GET /api/v2/apm/config/metrics", "api_version": "v2", "api_arguments": {}, "functionality": "Get the list of configured span-based metrics with their definitions.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Spans Metrics: Create a span-based metric", "api_call": "POST /api/v2/apm/config/metrics", "api_version": "v2", "api_arguments": {}, "functionality": "Create a metric based on your ingested spans in your organization.\nReturns the span-based metric object from the request body when the request is successful.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Spans Metrics: Delete a span-based metric", "api_call": "DELETE /api/v2/apm/config/metrics/{metric_id}", "api_version": "v2", "api_arguments": {"metric_id": "[REQUIRED] string: The name of the span-based metric."}, "functionality": "Delete a specific span-based metric from your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Spans Metrics: Get a span-based metric", "api_call": "GET /api/v2/apm/config/metrics/{metric_id}", "api_version": "v2", "api_arguments": {"metric_id": "[REQUIRED] string: The name of the span-based metric."}, "functionality": "Get a specific span-based metric from your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Spans Metrics: Update a span-based metric", "api_call": "PATCH /api/v2/apm/config/metrics/{metric_id}", "api_version": "v2", "api_arguments": {"metric_id": "[REQUIRED] string: The name of the span-based metric."}, "functionality": "Update a specific span-based metric from your organization.\nReturns the span-based metric object from the request body when the request is successful.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - APM Retention Filters: List all APM retention filters", "api_call": "GET /api/v2/apm/config/retention-filters", "api_version": "v2", "api_arguments": {}, "functionality": "Get the list of APM retention filters.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - APM Retention Filters: Create a retention filter", "api_call": "POST /api/v2/apm/config/retention-filters", "api_version": "v2", "api_arguments": {}, "functionality": "Create a retention filter to index spans in your organization.\nReturns the retention filter definition when the request is successful.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - APM Retention Filters: Re-order retention filters", "api_call": "PUT /api/v2/apm/config/retention-filters-execution-order", "api_version": "v2", "api_arguments": {}, "functionality": "Re-order the execution order of retention filters.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - APM Retention Filters: Delete a retention filter", "api_call": "DELETE /api/v2/apm/config/retention-filters/{filter_id}", "api_version": "v2", "api_arguments": {"filter_id": "[REQUIRED] string: The ID of the retention filter."}, "functionality": "Delete a specific retention filter from your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - APM Retention Filters: Get a given APM retention filter", "api_call": "GET /api/v2/apm/config/retention-filters/{filter_id}", "api_version": "v2", "api_arguments": {"filter_id": "[REQUIRED] string: The ID of the retention filter."}, "functionality": "Get an APM retention filter.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - APM Retention Filters: Update a retention filter", "api_call": "PUT /api/v2/apm/config/retention-filters/{filter_id}", "api_version": "v2", "api_arguments": {"filter_id": "[REQUIRED] string: The ID of the retention filter."}, "functionality": "Update a retention filter from your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Key Management: Get all application keys", "api_call": "GET /api/v2/application_keys", "api_version": "v2", "api_arguments": {"page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[number]": "integer: Specific page number to return.", "sort": "string: Application key attribute used to sort results. Sort order is ascending\nby default. In order to specify a descending sort, prefix the\nattribute with a minus sign.", "filter": "string: Filter application keys by the specified string.", "filter[created_at][start]": "string: Only include application keys created on or after the specified date.", "filter[created_at][end]": "string: Only include application keys created on or before the specified date.", "include": "string: Resource path for related resources to include in the response. Only `owned_by` is supported."}, "functionality": "List all application keys available for your org", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Key Management: Delete an application key", "api_call": "DELETE /api/v2/application_keys/{app_key_id}", "api_version": "v2", "api_arguments": {"app_key_id": "[REQUIRED] string: The ID of the application key."}, "functionality": "Delete an application key", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Key Management: Get an application key", "api_call": "GET /api/v2/application_keys/{app_key_id}", "api_version": "v2", "api_arguments": {"app_key_id": "[REQUIRED] string: The ID of the application key.", "include": "string: Resource path for related resources to include in the response. Only `owned_by` is supported."}, "functionality": "Get an application key for your org.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Key Management: Edit an application key", "api_call": "PATCH /api/v2/application_keys/{app_key_id}", "api_version": "v2", "api_arguments": {"app_key_id": "[REQUIRED] string: The ID of the application key."}, "functionality": "Edit an application key", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Audit: Get a list of Audit Logs events", "api_call": "GET /api/v2/audit/events", "api_version": "v2", "api_arguments": {"filter[query]": "string: Search query following Audit Logs syntax.", "filter[from]": "string: Minimum timestamp for requested events.", "filter[to]": "string: Maximum timestamp for requested events.", "sort": "string: Order of events in results.", "page[cursor]": "string: List following results with a cursor provided in the previous query.", "page[limit]": "integer: Maximum number of events in the response."}, "functionality": "List endpoint returns events that match a Audit Logs search query.\n[Results are paginated][1].\n\nUse this endpoint to see your latest Audit Logs events.\n\n[1]: https://docs.datadoghq.com/logs/guide/collect-multiple-logs-with-pagination", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Audit: Search Audit Logs events", "api_call": "POST /api/v2/audit/events/search", "api_version": "v2", "api_arguments": {}, "functionality": "List endpoint returns Audit Logs events that match an Audit search query.\n[Results are paginated][1].\n\nUse this endpoint to build complex Audit Logs events filtering and search.\n\n[1]: https://docs.datadoghq.com/logs/guide/collect-multiple-logs-with-pagination", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - AuthN Mappings: List all AuthN Mappings", "api_call": "GET /api/v2/authn_mappings", "api_version": "v2", "api_arguments": {"page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[number]": "integer: Specific page number to return.", "sort": "string: Sort <PERSON> Mappings depending on the given field.", "filter": "string: Filter all mappings by the given string."}, "functionality": "List all AuthN Mappings in the org.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - AuthN Mappings: Create an AuthN Mapping", "api_call": "POST /api/v2/authn_mappings", "api_version": "v2", "api_arguments": {}, "functionality": "Create an AuthN Mapping.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - AuthN Mappings: Delete an AuthN Mapping", "api_call": "DELETE /api/v2/authn_mappings/{authn_mapping_id}", "api_version": "v2", "api_arguments": {"authn_mapping_id": "[REQUIRED] string: The UUID of the AuthN Mapping."}, "functionality": "Delete an AuthN Mapping specified by AuthN Mapping UUID.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - AuthN Mappings: Get an AuthN Mapping by UUID", "api_call": "GET /api/v2/authn_mappings/{authn_mapping_id}", "api_version": "v2", "api_arguments": {"authn_mapping_id": "[REQUIRED] string: The UUID of the AuthN Mapping."}, "functionality": "Get an AuthN Mapping specified by the AuthN Mapping UUID.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - AuthN Mappings: Edit an AuthN Mapping", "api_call": "PATCH /api/v2/authn_mappings/{authn_mapping_id}", "api_version": "v2", "api_arguments": {"authn_mapping_id": "[REQUIRED] string: The UUID of the AuthN Mapping."}, "functionality": "Edit an AuthN Mapping.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Case Management: Search cases", "api_call": "GET /api/v2/cases", "api_version": "v2", "api_arguments": {"page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[offset]": "integer: Specific offset to use as the beginning of the returned page.", "sort[field]": "string: Specify which field to sort", "filter": "string: Search query", "sort[asc]": "boolean: Specify if order is ascending or not"}, "functionality": "Search cases.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Case Management: Create a case", "api_call": "POST /api/v2/cases", "api_version": "v2", "api_arguments": {}, "functionality": "Create a Case", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Case Management: Get all projects", "api_call": "GET /api/v2/cases/projects", "api_version": "v2", "api_arguments": {}, "functionality": "Get all projects.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Case Management: Create a project", "api_call": "POST /api/v2/cases/projects", "api_version": "v2", "api_arguments": {}, "functionality": "Create a project.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Case Management: Remove a project", "api_call": "DELETE /api/v2/cases/projects/{project_id}", "api_version": "v2", "api_arguments": {"project_id": "[REQUIRED] string: Project UUID"}, "functionality": "Remove a project using the project's `id`.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Case Management: Get the details of a project", "api_call": "GET /api/v2/cases/projects/{project_id}", "api_version": "v2", "api_arguments": {"project_id": "[REQUIRED] string: Project UUID"}, "functionality": "Get the details of a project by `project_id`.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Case Management: Get the details of a case", "api_call": "GET /api/v2/cases/{case_id}", "api_version": "v2", "api_arguments": {"case_id": "[REQUIRED] string: <PERSON>'s UUID or key"}, "functionality": "Get the details of case by `case_id`", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Case Management: Archive case", "api_call": "POST /api/v2/cases/{case_id}/archive", "api_version": "v2", "api_arguments": {"case_id": "[REQUIRED] string: <PERSON>'s UUID or key"}, "functionality": "Archive case", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Case Management: Assign case", "api_call": "POST /api/v2/cases/{case_id}/assign", "api_version": "v2", "api_arguments": {"case_id": "[REQUIRED] string: <PERSON>'s UUID or key"}, "functionality": "Assign case to a user", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Case Management: Update case priority", "api_call": "POST /api/v2/cases/{case_id}/priority", "api_version": "v2", "api_arguments": {"case_id": "[REQUIRED] string: <PERSON>'s UUID or key"}, "functionality": "Update case priority", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Case Management: Update case status", "api_call": "POST /api/v2/cases/{case_id}/status", "api_version": "v2", "api_arguments": {"case_id": "[REQUIRED] string: <PERSON>'s UUID or key"}, "functionality": "Update case status", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Case Management: Unarchive case", "api_call": "POST /api/v2/cases/{case_id}/unarchive", "api_version": "v2", "api_arguments": {"case_id": "[REQUIRED] string: <PERSON>'s UUID or key"}, "functionality": "Unarchive case", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Case Management: Unassign case", "api_call": "POST /api/v2/cases/{case_id}/unassign", "api_version": "v2", "api_arguments": {"case_id": "[REQUIRED] string: <PERSON>'s UUID or key"}, "functionality": "Unassign case", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - CI Visibility Pipelines: Send pipeline event", "api_call": "POST /api/v2/ci/pipeline", "api_version": "v2", "api_arguments": {}, "functionality": "Send your pipeline event to your Datadog platform over HTTP. For details about how pipeline executions are modeled and what execution types we support, see [Pipeline Data Model And Execution Types](https://docs.datadoghq.com/continuous_integration/guides/pipeline_data_model/).\n\nPipeline events can be submitted with a timestamp that is up to 18 hours in the past.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - CI Visibility Pipelines: Aggregate pipelines events", "api_call": "POST /api/v2/ci/pipelines/analytics/aggregate", "api_version": "v2", "api_arguments": {}, "functionality": "Use this API endpoint to aggregate CI Visibility pipeline events into buckets of computed metrics and timeseries.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - CI Visibility Pipelines: Get a list of pipelines events", "api_call": "GET /api/v2/ci/pipelines/events", "api_version": "v2", "api_arguments": {"filter[query]": "string: Search query following log syntax.", "filter[from]": "string: Minimum timestamp for requested events.", "filter[to]": "string: Maximum timestamp for requested events.", "sort": "string: Order of events in results.", "page[cursor]": "string: List following results with a cursor provided in the previous query.", "page[limit]": "integer: Maximum number of events in the response."}, "functionality": "List endpoint returns CI Visibility pipeline events that match a [search query](https://docs.datadoghq.com/continuous_integration/explorer/search_syntax/).\n[Results are paginated similarly to logs](https://docs.datadoghq.com/logs/guide/collect-multiple-logs-with-pagination).\n\nUse this endpoint to see your latest pipeline events.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - CI Visibility Pipelines: Search pipelines events", "api_call": "POST /api/v2/ci/pipelines/events/search", "api_version": "v2", "api_arguments": {}, "functionality": "List endpoint returns CI Visibility pipeline events that match a [search query](https://docs.datadoghq.com/continuous_integration/explorer/search_syntax/).\n[Results are paginated similarly to logs](https://docs.datadoghq.com/logs/guide/collect-multiple-logs-with-pagination).\n\nUse this endpoint to build complex events filtering and search.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - CI Visibility Tests: Aggregate tests events", "api_call": "POST /api/v2/ci/tests/analytics/aggregate", "api_version": "v2", "api_arguments": {}, "functionality": "The API endpoint to aggregate CI Visibility test events into buckets of computed metrics and timeseries.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - CI Visibility Tests: Get a list of tests events", "api_call": "GET /api/v2/ci/tests/events", "api_version": "v2", "api_arguments": {"filter[query]": "string: Search query following log syntax.", "filter[from]": "string: Minimum timestamp for requested events.", "filter[to]": "string: Maximum timestamp for requested events.", "sort": "string: Order of events in results.", "page[cursor]": "string: List following results with a cursor provided in the previous query.", "page[limit]": "integer: Maximum number of events in the response."}, "functionality": "List endpoint returns CI Visibility test events that match a [log search query](https://docs.datadoghq.com/logs/explorer/search_syntax/).\n[Results are paginated similarly to logs](https://docs.datadoghq.com/logs/guide/collect-multiple-logs-with-pagination).\n\nUse this endpoint to see your latest test events.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - CI Visibility Tests: Search tests events", "api_call": "POST /api/v2/ci/tests/events/search", "api_version": "v2", "api_arguments": {}, "functionality": "List endpoint returns CI Visibility test events that match a [log search query](https://docs.datadoghq.com/logs/explorer/search_syntax/).\n[Results are paginated similarly to logs](https://docs.datadoghq.com/logs/guide/collect-multiple-logs-with-pagination).\n\nUse this endpoint to build complex events filtering and search.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Container Images: Get all Container Images", "api_call": "GET /api/v2/container_images", "api_version": "v2", "api_arguments": {"filter[tags]": "string: Comma-separated list of tags to filter Container Images by.", "group_by": "string: Comma-separated list of tags to group Container Images by.", "sort": "string: Attribute to sort Container Images by.", "page[size]": "integer: Maximum number of results returned.", "page[cursor]": "string: String to query the next page of results.\nThis key is provided with each valid response from the API in `meta.pagination.next_cursor`."}, "functionality": "Get all Container Images for your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Containers: Get All Containers", "api_call": "GET /api/v2/containers", "api_version": "v2", "api_arguments": {"filter[tags]": "string: Comma-separated list of tags to filter containers by.", "group_by": "string: Comma-separated list of tags to group containers by.", "sort": "string: Attribute to sort containers by.", "page[size]": "integer: Maximum number of results returned.", "page[cursor]": "string: String to query the next page of results.\nThis key is provided with each valid response from the API in `meta.pagination.next_cursor`."}, "functionality": "Get all containers for your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Cost Management: List Cloud Cost Management AWS CUR configs", "api_call": "GET /api/v2/cost/aws_cur_config", "api_version": "v2", "api_arguments": {}, "functionality": "List the AWS CUR configs.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Cost Management: Create Cloud Cost Management AWS CUR config", "api_call": "POST /api/v2/cost/aws_cur_config", "api_version": "v2", "api_arguments": {}, "functionality": "Create a Cloud Cost Management account for an AWS CUR config.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Cost Management: Delete Cloud Cost Management AWS CUR config", "api_call": "DELETE /api/v2/cost/aws_cur_config/{cloud_account_id}", "api_version": "v2", "api_arguments": {"cloud_account_id": "[REQUIRED] string: Cloud Account id."}, "functionality": "Archive a Cloud Cost Management Account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Cost Management: Update Cloud Cost Management AWS CUR config", "api_call": "PATCH /api/v2/cost/aws_cur_config/{cloud_account_id}", "api_version": "v2", "api_arguments": {"cloud_account_id": "[REQUIRED] string: Cloud Account id."}, "functionality": "Update the status of an AWS CUR config (active/archived).", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Cost Management: List related AWS accounts", "api_call": "GET /api/v2/cost/aws_related_accounts", "api_version": "v2", "api_arguments": {"filter[management_account_id]": "[REQUIRED] string: The ID of the management account to filter by."}, "functionality": "List the AWS accounts in an organization by calling 'organizations:ListAccounts' from the specified management account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Cost Management: List Cloud Cost Management Azure configs", "api_call": "GET /api/v2/cost/azure_uc_config", "api_version": "v2", "api_arguments": {}, "functionality": "List the Azure configs.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Cost Management: Create Cloud Cost Management Azure configs", "api_call": "POST /api/v2/cost/azure_uc_config", "api_version": "v2", "api_arguments": {}, "functionality": "Create a Cloud Cost Management account for an Azure config.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Cost Management: Delete Cloud Cost Management Azure config", "api_call": "DELETE /api/v2/cost/azure_uc_config/{cloud_account_id}", "api_version": "v2", "api_arguments": {"cloud_account_id": "[REQUIRED] string: Cloud Account id."}, "functionality": "Archive a Cloud Cost Management Account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Cost Management: Update Cloud Cost Management Azure config", "api_call": "PATCH /api/v2/cost/azure_uc_config/{cloud_account_id}", "api_version": "v2", "api_arguments": {"cloud_account_id": "[REQUIRED] string: Cloud Account id."}, "functionality": "Update the status of an  Azure config (active/archived).", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Cost Management: Cloud Cost Enabled", "api_call": "GET /api/v2/cost/enabled", "api_version": "v2", "api_arguments": {}, "functionality": "Get the Cloud Cost Management activity.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Usage Metering: Get active billing dimensions for cost attribution", "api_call": "GET /api/v2/cost_by_tag/active_billing_dimensions", "api_version": "v2", "api_arguments": {}, "functionality": "Get active billing dimensions for cost attribution. Cost data for a given month becomes available no later than the 19th of the following month.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Usage Metering: Get Monthly Cost Attribution", "api_call": "GET /api/v2/cost_by_tag/monthly_cost_attribution", "api_version": "v2", "api_arguments": {"start_month": "[REQUIRED] string: Datetime in ISO-8601 format, UTC, precise to month: `[YYYY-MM]` for cost beginning in this month.", "end_month": "[REQUIRED] string: Datetime in ISO-8601 format, UTC, precise to month: `[YYYY-MM]` for cost ending this month.", "fields": "[REQUIRED] string: Comma-separated list specifying cost types (e.g., `<billing_dimension>_on_demand_cost`, `<billing_dimension>_committed_cost`, `<billing_dimension>_total_cost`) and the\nproportions (`<billing_dimension>_percentage_in_org`, `<billing_dimension>_percentage_in_account`). Use `*` to retrieve all fields.\nExample: `infra_host_on_demand_cost,infra_host_percentage_in_account`\nTo obtain the complete list of active billing dimensions that can be used to replace\n`<billing_dimension>` in the field names, make a request to the [Get active billing dimensions API](https://docs.datadoghq.com/api/latest/usage-metering/#get-active-billing-dimensions-for-cost-attribution).", "sort_direction": "string: The direction to sort by: `[desc, asc]`.", "sort_name": "string: The billing dimension to sort by. Always sorted by total cost. Example: `infra_host`.", "tag_breakdown_keys": "string: Comma separated list of tag keys used to group cost. If no value is provided the cost will not be broken down by tags.\nTo see which tags are available, look for the value of `tag_config_source` in the API response.", "next_record_id": "string: List following results with a next_record_id provided in the previous query.", "include_descendants": "boolean: Include child org cost in the response. Defaults to `true`."}, "functionality": "Get monthly cost attribution by tag across multi-org and single root-org accounts.\nCost Attribution data for a given month becomes available no later than the 19th of the following month.\nThis API endpoint is paginated. To make sure you receive all records, check if the value of `next_record_id` is\nset in the response. If it is, make another request and pass `next_record_id` as a parameter.\nPseudo code example:\n```\nresponse := GetMonthlyCostAttribution(start_month, end_month)\ncursor := response.metadata.pagination.next_record_id\nWHILE cursor != null BEGIN\n  sleep(5 seconds)  # Avoid running into rate limit\n  response := GetMonthlyCostAttribution(start_month, end_month, next_record_id=cursor)\n  cursor := response.metadata.pagination.next_record_id\nEND\n```", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Key Management: Get all application keys owned by current user", "api_call": "GET /api/v2/current_user/application_keys", "api_version": "v2", "api_arguments": {"page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[number]": "integer: Specific page number to return.", "sort": "string: Application key attribute used to sort results. Sort order is ascending\nby default. In order to specify a descending sort, prefix the\nattribute with a minus sign.", "filter": "string: Filter application keys by the specified string.", "filter[created_at][start]": "string: Only include application keys created on or after the specified date.", "filter[created_at][end]": "string: Only include application keys created on or before the specified date.", "include": "string: Resource path for related resources to include in the response. Only `owned_by` is supported."}, "functionality": "List all application keys available for current user", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Key Management: Create an application key for current user", "api_call": "POST /api/v2/current_user/application_keys", "api_version": "v2", "api_arguments": {}, "functionality": "Create an application key for current user", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Key Management: Delete an application key owned by current user", "api_call": "DELETE /api/v2/current_user/application_keys/{app_key_id}", "api_version": "v2", "api_arguments": {"app_key_id": "[REQUIRED] string: The ID of the application key."}, "functionality": "Delete an application key owned by current user", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Key Management: Get one application key owned by current user", "api_call": "GET /api/v2/current_user/application_keys/{app_key_id}", "api_version": "v2", "api_arguments": {"app_key_id": "[REQUIRED] string: The ID of the application key."}, "functionality": "Get an application key owned by current user", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Key Management: Edit an application key owned by current user", "api_call": "PATCH /api/v2/current_user/application_keys/{app_key_id}", "api_version": "v2", "api_arguments": {"app_key_id": "[REQUIRED] string: The ID of the application key."}, "functionality": "Edit an application key owned by current user", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Dashboard Lists: Delete items from a dashboard list", "api_call": "DELETE /api/v2/dashboard/lists/manual/{dashboard_list_id}/dashboards", "api_version": "v2", "api_arguments": {"dashboard_list_id": "[REQUIRED] integer: ID of the dashboard list to delete items from."}, "functionality": "Delete dashboards from an existing dashboard list.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Dashboard Lists: Get items of a Dashboard List", "api_call": "GET /api/v2/dashboard/lists/manual/{dashboard_list_id}/dashboards", "api_version": "v2", "api_arguments": {"dashboard_list_id": "[REQUIRED] integer: ID of the dashboard list to get items from."}, "functionality": "Fetch the dashboard list’s dashboard definitions.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Dashboard Lists: Add Items to a Dashboard List", "api_call": "POST /api/v2/dashboard/lists/manual/{dashboard_list_id}/dashboards", "api_version": "v2", "api_arguments": {"dashboard_list_id": "[REQUIRED] integer: ID of the dashboard list to add items to."}, "functionality": "Add dashboards to an existing dashboard list.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Dashboard Lists: Update items of a dashboard list", "api_call": "PUT /api/v2/dashboard/lists/manual/{dashboard_list_id}/dashboards", "api_version": "v2", "api_arguments": {"dashboard_list_id": "[REQUIRED] integer: ID of the dashboard list to update items from."}, "functionality": "Update dashboards of an existing dashboard list.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - DORA Metrics: Send a deployment event for DORA Metrics", "api_call": "POST /api/v2/dora/deployment", "api_version": "v2", "api_arguments": {}, "functionality": "Use this API endpoint to provide data about deployments for DORA metrics.\n\nThis is necessary for:\n- Deployment Frequency\n- Change Lead Time\n- Change Failure Rate", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - DORA Metrics: Send an incident event for DORA Metrics", "api_call": "POST /api/v2/dora/incident", "api_version": "v2", "api_arguments": {}, "functionality": "Use this API endpoint to provide data about incidents for DORA metrics.\n\nThis is necessary for:\n- Change Failure Rate\n- Time to Restore", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Downtimes: Get all downtimes", "api_call": "GET /api/v2/downtime", "api_version": "v2", "api_arguments": {"current_only": "boolean: Only return downtimes that are active when the request is made.", "include": "string: Comma-separated list of resource paths for related resources to include in the response. Supported resource\npaths are `created_by` and `monitor`.", "page[offset]": "integer: Specific offset to use as the beginning of the returned page.", "page[limit]": "integer: Maximum number of downtimes in the response."}, "functionality": "Get all scheduled downtimes.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Downtimes: Schedule a downtime", "api_call": "POST /api/v2/downtime", "api_version": "v2", "api_arguments": {}, "functionality": "Schedule a downtime.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Downtimes: Cancel a downtime", "api_call": "DELETE /api/v2/downtime/{downtime_id}", "api_version": "v2", "api_arguments": {"downtime_id": "[REQUIRED] string: ID of the downtime to cancel."}, "functionality": "Cancel a downtime.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Downtimes: Get a downtime", "api_call": "GET /api/v2/downtime/{downtime_id}", "api_version": "v2", "api_arguments": {"downtime_id": "[REQUIRED] string: ID of the downtime to fetch.", "include": "string: Comma-separated list of resource paths for related resources to include in the response. Supported resource\npaths are `created_by` and `monitor`."}, "functionality": "Get downtime detail by `downtime_id`.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Downtimes: Update a downtime", "api_call": "PATCH /api/v2/downtime/{downtime_id}", "api_version": "v2", "api_arguments": {"downtime_id": "[REQUIRED] string: ID of the downtime to update."}, "functionality": "Update a downtime by `downtime_id`.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Events: Get a list of events", "api_call": "GET /api/v2/events", "api_version": "v2", "api_arguments": {"filter[query]": "string: Search query following events syntax.", "filter[from]": "string: Minimum timestamp for requested events.", "filter[to]": "string: Maximum timestamp for requested events.", "sort": "string: Order of events in results.", "page[cursor]": "string: List following results with a cursor provided in the previous query.", "page[limit]": "integer: Maximum number of events in the response."}, "functionality": "List endpoint returns events that match an events search query.\n[Results are paginated similarly to logs](https://docs.datadoghq.com/logs/guide/collect-multiple-logs-with-pagination).\n\nUse this endpoint to see your latest events.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Events: Search events", "api_call": "POST /api/v2/events/search", "api_version": "v2", "api_arguments": {}, "functionality": "List endpoint returns events that match an events search query.\n[Results are paginated similarly to logs](https://docs.datadoghq.com/logs/guide/collect-multiple-logs-with-pagination).\n\nUse this endpoint to build complex events filtering and search.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Get a list of incidents", "api_call": "GET /api/v2/incidents", "api_version": "v2", "api_arguments": {"include": "array: Specifies which types of related objects should be included in the response.", "page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[offset]": "integer: Specific offset to use as the beginning of the returned page."}, "functionality": "Get all incidents for the user's organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Create an incident", "api_call": "POST /api/v2/incidents", "api_version": "v2", "api_arguments": {}, "functionality": "Create an incident.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Search for incidents", "api_call": "GET /api/v2/incidents/search", "api_version": "v2", "api_arguments": {"include": "string: Specifies which types of related objects should be included in the response.", "query": "[REQUIRED] string: Specifies which incidents should be returned. The query can contain any number of incident facets\njoined by `ANDs`, along with multiple values for each of those facets joined by `OR`s. For\nexample: `state:active AND severity:(SEV-2 OR SEV-1)`.", "sort": "string: Specifies the order of returned incidents.", "page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[offset]": "integer: Specific offset to use as the beginning of the returned page."}, "functionality": "Search for incidents matching a certain query.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Delete an existing incident", "api_call": "DELETE /api/v2/incidents/{incident_id}", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident."}, "functionality": "Deletes an existing incident from the users organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Get the details of an incident", "api_call": "GET /api/v2/incidents/{incident_id}", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident.", "include": "array: Specifies which types of related objects should be included in the response."}, "functionality": "Get the details of an incident by `incident_id`.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Update an existing incident", "api_call": "PATCH /api/v2/incidents/{incident_id}", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident.", "include": "array: Specifies which types of related objects should be included in the response."}, "functionality": "Updates an incident. Provide only the attributes that should be updated as this request is a partial update.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Get a list of attachments", "api_call": "GET /api/v2/incidents/{incident_id}/attachments", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident.", "include": "array: Specifies which types of related objects are included in the response.", "filter[attachment_type]": "array: Specifies which types of attachments are included in the response."}, "functionality": "Get all attachments for a given incident.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Create, update, and delete incident attachments", "api_call": "PATCH /api/v2/incidents/{incident_id}/attachments", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident.", "include": "array: Specifies which types of related objects are included in the response."}, "functionality": "The bulk update endpoint for creating, updating, and deleting attachments for a given incident.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Get a list of an incident's integration metadata", "api_call": "GET /api/v2/incidents/{incident_id}/relationships/integrations", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident."}, "functionality": "Get all integration metadata for an incident.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Create an incident integration metadata", "api_call": "POST /api/v2/incidents/{incident_id}/relationships/integrations", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident."}, "functionality": "Create an incident integration metadata.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Delete an incident integration metadata", "api_call": "DELETE /api/v2/incidents/{incident_id}/relationships/integrations/{integration_metadata_id}", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident.", "integration_metadata_id": "[REQUIRED] string: The UUID of the incident integration metadata."}, "functionality": "Delete an incident integration metadata.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Get incident integration metadata details", "api_call": "GET /api/v2/incidents/{incident_id}/relationships/integrations/{integration_metadata_id}", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident.", "integration_metadata_id": "[REQUIRED] string: The UUID of the incident integration metadata."}, "functionality": "Get incident integration metadata details.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Update an existing incident integration metadata", "api_call": "PATCH /api/v2/incidents/{incident_id}/relationships/integrations/{integration_metadata_id}", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident.", "integration_metadata_id": "[REQUIRED] string: The UUID of the incident integration metadata."}, "functionality": "Update an existing incident integration metadata.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Get a list of an incident's todos", "api_call": "GET /api/v2/incidents/{incident_id}/relationships/todos", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident."}, "functionality": "Get all todos for an incident.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Create an incident todo", "api_call": "POST /api/v2/incidents/{incident_id}/relationships/todos", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident."}, "functionality": "Create an incident todo.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Delete an incident todo", "api_call": "DELETE /api/v2/incidents/{incident_id}/relationships/todos/{todo_id}", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident.", "todo_id": "[REQUIRED] string: The UUID of the incident todo."}, "functionality": "Delete an incident todo.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Get incident todo details", "api_call": "GET /api/v2/incidents/{incident_id}/relationships/todos/{todo_id}", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident.", "todo_id": "[REQUIRED] string: The UUID of the incident todo."}, "functionality": "Get incident todo details.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incidents: Update an incident todo", "api_call": "PATCH /api/v2/incidents/{incident_id}/relationships/todos/{todo_id}", "api_version": "v2", "api_arguments": {"incident_id": "[REQUIRED] string: The UUID of the incident.", "todo_id": "[REQUIRED] string: The UUID of the incident todo."}, "functionality": "Update an incident todo.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - GCP Integration: List all GCP STS-enabled service accounts", "api_call": "GET /api/v2/integration/gcp/accounts", "api_version": "v2", "api_arguments": {}, "functionality": "List all GCP STS-enabled service accounts configured in your Datadog account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - GCP Integration: Create a new entry for your service account", "api_call": "POST /api/v2/integration/gcp/accounts", "api_version": "v2", "api_arguments": {}, "functionality": "Create a new entry within Datadog for your STS enabled service account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - GCP Integration: Delete an STS enabled GCP Account", "api_call": "DELETE /api/v2/integration/gcp/accounts/{account_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Your GCP STS enabled service account's unique ID."}, "functionality": "Delete an STS enabled GCP account from within Datadog.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - GCP Integration: Update STS Service Account", "api_call": "PATCH /api/v2/integration/gcp/accounts/{account_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Your GCP STS enabled service account's unique ID."}, "functionality": "Update an STS enabled service account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - GCP Integration: List delegate account", "api_call": "GET /api/v2/integration/gcp/sts_delegate", "api_version": "v2", "api_arguments": {}, "functionality": "List your Datadog-GCP STS delegate account configured in your Datadog account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - GCP Integration: Create a Datadog GCP principal", "api_call": "POST /api/v2/integration/gcp/sts_delegate", "api_version": "v2", "api_arguments": {}, "functionality": "Create a Datadog GCP principal.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Opsgenie Integration: Get all service objects", "api_call": "GET /api/v2/integration/opsgenie/services", "api_version": "v2", "api_arguments": {}, "functionality": "Get a list of all services from the Datadog Opsgenie integration.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Opsgenie Integration: Create a new service object", "api_call": "POST /api/v2/integration/opsgenie/services", "api_version": "v2", "api_arguments": {}, "functionality": "Create a new service object in the Opsgenie integration.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Opsgenie Integration: Delete a single service object", "api_call": "DELETE /api/v2/integration/opsgenie/services/{integration_service_id}", "api_version": "v2", "api_arguments": {"integration_service_id": "[REQUIRED] string: The UUID of the service."}, "functionality": "Delete a single service object in the Datadog Opsgenie integration.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Opsgenie Integration: Get a single service object", "api_call": "GET /api/v2/integration/opsgenie/services/{integration_service_id}", "api_version": "v2", "api_arguments": {"integration_service_id": "[REQUIRED] string: The UUID of the service."}, "functionality": "Get a single service from the Datadog Opsgenie integration.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Opsgenie Integration: Update a single service object", "api_call": "PATCH /api/v2/integration/opsgenie/services/{integration_service_id}", "api_version": "v2", "api_arguments": {"integration_service_id": "[REQUIRED] string: The UUID of the service."}, "functionality": "Update a single service object in the Datadog Opsgenie integration.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloudflare Integration: List Cloudflare accounts", "api_call": "GET /api/v2/integrations/cloudflare/accounts", "api_version": "v2", "api_arguments": {}, "functionality": "List Cloudflare accounts.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloudflare Integration: Add Cloudflare account", "api_call": "POST /api/v2/integrations/cloudflare/accounts", "api_version": "v2", "api_arguments": {}, "functionality": "Create a Cloudflare account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloudflare Integration: Delete Cloudflare account", "api_call": "DELETE /api/v2/integrations/cloudflare/accounts/{account_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string"}, "functionality": "Delete a Cloudflare account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloudflare Integration: Get Cloudflare account", "api_call": "GET /api/v2/integrations/cloudflare/accounts/{account_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string"}, "functionality": "Get a Cloudflare account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloudflare Integration: Update Cloudflare account", "api_call": "PATCH /api/v2/integrations/cloudflare/accounts/{account_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string"}, "functionality": "Update a Cloudflare account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Confluent Cloud: List Confluent accounts", "api_call": "GET /api/v2/integrations/confluent-cloud/accounts", "api_version": "v2", "api_arguments": {}, "functionality": "List Confluent accounts.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Confluent Cloud: Add Confluent account", "api_call": "POST /api/v2/integrations/confluent-cloud/accounts", "api_version": "v2", "api_arguments": {}, "functionality": "Create a Confluent account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Confluent Cloud: Delete Confluent account", "api_call": "DELETE /api/v2/integrations/confluent-cloud/accounts/{account_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Confluent Account id."}, "functionality": "Delete a Confluent account with the provided account ID.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Confluent Cloud: Get Confluent account", "api_call": "GET /api/v2/integrations/confluent-cloud/accounts/{account_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Confluent Account id."}, "functionality": "Get the Confluent account with the provided account ID.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Confluent Cloud: Update Confluent account", "api_call": "PATCH /api/v2/integrations/confluent-cloud/accounts/{account_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Confluent Account id."}, "functionality": "Update the Confluent account with the provided account ID.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Confluent Cloud: List Confluent Account resources", "api_call": "GET /api/v2/integrations/confluent-cloud/accounts/{account_id}/resources", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Confluent Account id."}, "functionality": "Get a Confluent resource for the account associated with the provided ID.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Confluent Cloud: Add resource to Confluent account", "api_call": "POST /api/v2/integrations/confluent-cloud/accounts/{account_id}/resources", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Confluent Account id."}, "functionality": "Create a Confluent resource for the account associated with the provided ID.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Confluent Cloud: Delete resource from Confluent account", "api_call": "DELETE /api/v2/integrations/confluent-cloud/accounts/{account_id}/resources/{resource_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Confluent Account id.", "resource_id": "[REQUIRED] string: Confluent Account Resource ID."}, "functionality": "Delete a Confluent resource with the provided resource id for the account associated with the provided account ID.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Confluent Cloud: Get resource from Confluent account", "api_call": "GET /api/v2/integrations/confluent-cloud/accounts/{account_id}/resources/{resource_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Confluent Account id.", "resource_id": "[REQUIRED] string: Confluent Account Resource ID."}, "functionality": "Get a Confluent resource with the provided resource id for the account associated with the provided account ID.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Confluent Cloud: Update resource in Confluent account", "api_call": "PATCH /api/v2/integrations/confluent-cloud/accounts/{account_id}/resources/{resource_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Confluent Account id.", "resource_id": "[REQUIRED] string: Confluent Account Resource ID."}, "functionality": "Update a Confluent resource with the provided resource id for the account associated with the provided account ID.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Fastly Integration: List Fastly accounts", "api_call": "GET /api/v2/integrations/fastly/accounts", "api_version": "v2", "api_arguments": {}, "functionality": "List Fastly accounts.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Fastly Integration: Add Fastly account", "api_call": "POST /api/v2/integrations/fastly/accounts", "api_version": "v2", "api_arguments": {}, "functionality": "Create a Fastly account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Fastly Integration: Delete Fastly account", "api_call": "DELETE /api/v2/integrations/fastly/accounts/{account_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Fastly Account id."}, "functionality": "Delete a Fastly account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Fastly Integration: Get Fastly account", "api_call": "GET /api/v2/integrations/fastly/accounts/{account_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Fastly Account id."}, "functionality": "Get a Fastly account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Fastly Integration: Update Fastly account", "api_call": "PATCH /api/v2/integrations/fastly/accounts/{account_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Fastly Account id."}, "functionality": "Update a Fastly account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Fastly Integration: List Fastly services", "api_call": "GET /api/v2/integrations/fastly/accounts/{account_id}/services", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Fastly Account id."}, "functionality": "List Fastly services for an account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Fastly Integration: Add Fastly service", "api_call": "POST /api/v2/integrations/fastly/accounts/{account_id}/services", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Fastly Account id."}, "functionality": "Create a Fastly service for an account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Fastly Integration: Delete Fastly service", "api_call": "DELETE /api/v2/integrations/fastly/accounts/{account_id}/services/{service_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Fastly Account id.", "service_id": "[REQUIRED] string: Fastly Service ID."}, "functionality": "Delete a Fastly service for an account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Fastly Integration: Get Fastly service", "api_call": "GET /api/v2/integrations/fastly/accounts/{account_id}/services/{service_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Fastly Account id.", "service_id": "[REQUIRED] string: Fastly Service ID."}, "functionality": "Get a Fastly service for an account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Fastly Integration: Update Fastly service", "api_call": "PATCH /api/v2/integrations/fastly/accounts/{account_id}/services/{service_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string: Fastly Account id.", "service_id": "[REQUIRED] string: Fastly Service ID."}, "functionality": "Update a Fastly service for an account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Okta Integration: List Okta accounts", "api_call": "GET /api/v2/integrations/okta/accounts", "api_version": "v2", "api_arguments": {}, "functionality": "List Okta accounts.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Okta Integration: Add Okta account", "api_call": "POST /api/v2/integrations/okta/accounts", "api_version": "v2", "api_arguments": {}, "functionality": "Create an Okta account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Okta Integration: Delete Okta account", "api_call": "DELETE /api/v2/integrations/okta/accounts/{account_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string"}, "functionality": "Delete an Okta account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Okta Integration: Get Okta account", "api_call": "GET /api/v2/integrations/okta/accounts/{account_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string"}, "functionality": "Get an Okta account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Okta Integration: Update Okta account", "api_call": "PATCH /api/v2/integrations/okta/accounts/{account_id}", "api_version": "v2", "api_arguments": {"account_id": "[REQUIRED] string"}, "functionality": "Update an Okta account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - IP Allowlist: Get IP Allowlist", "api_call": "GET /api/v2/ip_allowlist", "api_version": "v2", "api_arguments": {}, "functionality": "Returns the IP allowlist and its enabled or disabled state.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - IP Allowlist: Update IP Allowlist", "api_call": "PATCH /api/v2/ip_allowlist", "api_version": "v2", "api_arguments": {}, "functionality": "Edit the entries in the IP allowlist, and enable or disable it.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs: Send logs", "api_call": "POST /api/v2/logs", "api_version": "v2", "api_arguments": {"Content-Encoding": "string: HTTP header used to compress the media-type.", "ddtags": "string: Log tags can be passed as query parameters with `text/plain` content type."}, "functionality": "Send your logs to your Datadog platform over HTTP. Limits per HTTP request are:\n\n- Maximum content size per payload (uncompressed): 5MB\n- Maximum size for a single log: 1MB\n- Maximum array size if sending multiple logs in an array: 1000 entries\n\nAny log exceeding 1MB is accepted and truncated by Datadog:\n- For a single log request, the API truncates the log at 1MB and returns a 2xx.\n- For a multi-logs request, the API processes all logs, truncates only logs larger than 1MB, and returns a 2xx.\n\nDatadog recommends sending your logs compressed.\nAdd the `Content-Encoding: gzip` header to the request when sending compressed logs.\nLog events can be submitted with a timestamp that is up to 18 hours in the past.\n\nThe status codes answered by the HTTP API are:\n- 202: Accepted: the request has been accepted for processing\n- 400: Bad request (likely an issue in the payload formatting)\n- 401: Unauthorized (likely a missing API Key)\n- 403: Permission issue (likely using an invalid API Key)\n- 408: Request Timeout, request should be retried after some time\n- 413: Payload too large (batch is above 5MB uncompressed)\n- 429: Too Many Requests, request should be retried after some time\n- 500: Internal Server Error, the server encountered an unexpected condition that prevented it from fulfilling the request, request should be retried after some time\n- 503: Service Unavailable, the server is not ready to handle the request probably because it is overloaded, request should be retried after some time", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs: Aggregate events", "api_call": "POST /api/v2/logs/analytics/aggregate", "api_version": "v2", "api_arguments": {}, "functionality": "The API endpoint to aggregate events into buckets and compute metrics and timeseries.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Archives: Get archive order", "api_call": "GET /api/v2/logs/config/archive-order", "api_version": "v2", "api_arguments": {}, "functionality": "Get the current order of your archives.\nThis endpoint takes no JSON arguments.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Archives: Update archive order", "api_call": "PUT /api/v2/logs/config/archive-order", "api_version": "v2", "api_arguments": {}, "functionality": "Update the order of your archives. Since logs are processed sequentially, reordering an archive may change\nthe structure and content of the data processed by other archives.\n\n**Note**: Using the `PUT` method updates your archive's order by replacing the current order\nwith the new one.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Archives: Get all archives", "api_call": "GET /api/v2/logs/config/archives", "api_version": "v2", "api_arguments": {}, "functionality": "Get the list of configured logs archives with their definitions.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Archives: Create an archive", "api_call": "POST /api/v2/logs/config/archives", "api_version": "v2", "api_arguments": {}, "functionality": "Create an archive in your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Archives: Delete an archive", "api_call": "DELETE /api/v2/logs/config/archives/{archive_id}", "api_version": "v2", "api_arguments": {"archive_id": "[REQUIRED] string: The ID of the archive."}, "functionality": "Delete a given archive from your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Archives: Get an archive", "api_call": "GET /api/v2/logs/config/archives/{archive_id}", "api_version": "v2", "api_arguments": {"archive_id": "[REQUIRED] string: The ID of the archive."}, "functionality": "Get a specific archive from your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Archives: Update an archive", "api_call": "PUT /api/v2/logs/config/archives/{archive_id}", "api_version": "v2", "api_arguments": {"archive_id": "[REQUIRED] string: The ID of the archive."}, "functionality": "Update a given archive configuration.\n\n**Note**: Using this method updates your archive configuration by **replacing**\nyour current configuration with the new one sent to your Datadog organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Archives: Revoke role from an archive", "api_call": "DELETE /api/v2/logs/config/archives/{archive_id}/readers", "api_version": "v2", "api_arguments": {"archive_id": "[REQUIRED] string: The ID of the archive."}, "functionality": "Removes a role from an archive. ([Roles API](https://docs.datadoghq.com/api/v2/roles/))", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Archives: List read roles for an archive", "api_call": "GET /api/v2/logs/config/archives/{archive_id}/readers", "api_version": "v2", "api_arguments": {"archive_id": "[REQUIRED] string: The ID of the archive."}, "functionality": "Returns all read roles a given archive is restricted to.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Archives: Grant role to an archive", "api_call": "POST /api/v2/logs/config/archives/{archive_id}/readers", "api_version": "v2", "api_arguments": {"archive_id": "[REQUIRED] string: The ID of the archive."}, "functionality": "Adds a read role to an archive. ([Roles API](https://docs.datadoghq.com/api/v2/roles/))", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Metrics: Get all log-based metrics", "api_call": "GET /api/v2/logs/config/metrics", "api_version": "v2", "api_arguments": {}, "functionality": "Get the list of configured log-based metrics with their definitions.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Metrics: Create a log-based metric", "api_call": "POST /api/v2/logs/config/metrics", "api_version": "v2", "api_arguments": {}, "functionality": "Create a metric based on your ingested logs in your organization.\nReturns the log-based metric object from the request body when the request is successful.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Metrics: Delete a log-based metric", "api_call": "DELETE /api/v2/logs/config/metrics/{metric_id}", "api_version": "v2", "api_arguments": {"metric_id": "[REQUIRED] string: The name of the log-based metric."}, "functionality": "Delete a specific log-based metric from your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Metrics: Get a log-based metric", "api_call": "GET /api/v2/logs/config/metrics/{metric_id}", "api_version": "v2", "api_arguments": {"metric_id": "[REQUIRED] string: The name of the log-based metric."}, "functionality": "Get a specific log-based metric from your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Metrics: Update a log-based metric", "api_call": "PATCH /api/v2/logs/config/metrics/{metric_id}", "api_version": "v2", "api_arguments": {"metric_id": "[REQUIRED] string: The name of the log-based metric."}, "functionality": "Update a specific log-based metric from your organization.\nReturns the log-based metric object from the request body when the request is successful.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Restriction Queries: List restriction queries", "api_call": "GET /api/v2/logs/config/restriction_queries", "api_version": "v2", "api_arguments": {"page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[number]": "integer: Specific page number to return."}, "functionality": "Returns all restriction queries, including their names and IDs.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Restriction Queries: Create a restriction query", "api_call": "POST /api/v2/logs/config/restriction_queries", "api_version": "v2", "api_arguments": {}, "functionality": "Create a new restriction query for your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Restriction Queries: Get restriction query for a given role", "api_call": "GET /api/v2/logs/config/restriction_queries/role/{role_id}", "api_version": "v2", "api_arguments": {"role_id": "[REQUIRED] string: The ID of the role."}, "functionality": "Get restriction query for a given role.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Restriction Queries: Get all restriction queries for a given user", "api_call": "GET /api/v2/logs/config/restriction_queries/user/{user_id}", "api_version": "v2", "api_arguments": {"user_id": "[REQUIRED] string: The ID of the user."}, "functionality": "Get all restriction queries for a given user.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Restriction Queries: Delete a restriction query", "api_call": "DELETE /api/v2/logs/config/restriction_queries/{restriction_query_id}", "api_version": "v2", "api_arguments": {"restriction_query_id": "[REQUIRED] string: The ID of the restriction query."}, "functionality": "Deletes a restriction query.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Restriction Queries: Get a restriction query", "api_call": "GET /api/v2/logs/config/restriction_queries/{restriction_query_id}", "api_version": "v2", "api_arguments": {"restriction_query_id": "[REQUIRED] string: The ID of the restriction query."}, "functionality": "Get a restriction query in the organization specified by the restriction query's `restriction_query_id`.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Restriction Queries: Update a restriction query", "api_call": "PATCH /api/v2/logs/config/restriction_queries/{restriction_query_id}", "api_version": "v2", "api_arguments": {"restriction_query_id": "[REQUIRED] string: The ID of the restriction query."}, "functionality": "Edit a restriction query.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Restriction Queries: Revoke role from a restriction query", "api_call": "DELETE /api/v2/logs/config/restriction_queries/{restriction_query_id}/roles", "api_version": "v2", "api_arguments": {"restriction_query_id": "[REQUIRED] string: The ID of the restriction query."}, "functionality": "Removes a role from a restriction query.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Restriction Queries: List roles for a restriction query", "api_call": "GET /api/v2/logs/config/restriction_queries/{restriction_query_id}/roles", "api_version": "v2", "api_arguments": {"restriction_query_id": "[REQUIRED] string: The ID of the restriction query.", "page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[number]": "integer: Specific page number to return."}, "functionality": "Returns all roles that have a given restriction query.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs Restriction Queries: Grant role to a restriction query", "api_call": "POST /api/v2/logs/config/restriction_queries/{restriction_query_id}/roles", "api_version": "v2", "api_arguments": {"restriction_query_id": "[REQUIRED] string: The ID of the restriction query."}, "functionality": "Adds a role to a restriction query.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs: Get a list of logs", "api_call": "GET /api/v2/logs/events", "api_version": "v2", "api_arguments": {"filter[query]": "string: Search query following logs syntax.", "filter[indexes]": "array: For customers with multiple indexes, the indexes to search.\nDefaults to '*' which means all indexes", "filter[from]": "string: Minimum timestamp for requested logs.", "filter[to]": "string: Maximum timestamp for requested logs.", "filter[storage_tier]": "string: Specifies the storage type to be used", "sort": "string: Order of logs in results.", "page[cursor]": "string: List following results with a cursor provided in the previous query.", "page[limit]": "integer: Maximum number of logs in the response."}, "functionality": "List endpoint returns logs that match a log search query.\n[Results are paginated][1].\n\nUse this endpoint to see your latest logs.\n\n**If you are considering archiving logs for your organization,\nconsider use of the Datadog archive capabilities instead of the log list API.\nSee [Datadog Logs Archive documentation][2].**\n\n[1]: /logs/guide/collect-multiple-logs-with-pagination\n[2]: https://docs.datadoghq.com/logs/archives", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Logs: Search logs", "api_call": "POST /api/v2/logs/events/search", "api_version": "v2", "api_arguments": {}, "functionality": "List endpoint returns logs that match a log search query.\n[Results are paginated][1].\n\nUse this endpoint to build complex logs filtering and search.\n\n**If you are considering archiving logs for your organization,\nconsider use of the Datadog archive capabilities instead of the log list API.\nSee [Datadog Logs Archive documentation][2].**\n\n[1]: /logs/guide/collect-multiple-logs-with-pagination\n[2]: https://docs.datadoghq.com/logs/archives", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Metrics: Get a list of metrics", "api_call": "GET /api/v2/metrics", "api_version": "v2", "api_arguments": {"filter[configured]": "boolean: Filter custom metrics that have configured tags.", "filter[tags_configured]": "string: Filter tag configurations by configured tags.", "filter[metric_type]": "string: Filter metrics by metric type.", "filter[include_percentiles]": "boolean: Filter distributions with additional percentile\naggregations enabled or disabled.", "filter[queried]": "boolean: (Beta) Filter custom metrics that have or have not been queried in the specified window[seconds].\nIf no window is provided or the window is less than 2 hours, a default of 2 hours will be applied.", "filter[tags]": "string: Filter metrics that have been submitted with the given tags. Supports boolean and wildcard expressions.\nCan only be combined with the filter[queried] filter.", "window[seconds]": "integer: The number of seconds of look back (from now) to apply to a filter[tag] or filter[queried] query.\nDefault value is 3600 (1 hour), maximum value is 2,592,000 (30 days)."}, "functionality": "Returns all metrics that can be configured in the Metrics Summary page or with Metrics without Limits™ (matching additional filters if specified).", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Metrics: Delete tags for multiple metrics", "api_call": "DELETE /api/v2/metrics/config/bulk-tags", "api_version": "v2", "api_arguments": {}, "functionality": "Delete all custom lists of queryable tag keys for a set of existing count, gauge, rate, and distribution metrics.\nMetrics are selected by passing a metric name prefix.\nResults can be sent to a set of account email addresses, just like the same operation in the Datadog web app.\nCan only be used with application keys of users with the `Manage Tags for Metrics` permission.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Metrics: Configure tags for multiple metrics", "api_call": "POST /api/v2/metrics/config/bulk-tags", "api_version": "v2", "api_arguments": {}, "functionality": "Create and define a list of queryable tag keys for a set of existing count, gauge, rate, and distribution metrics.\nMetrics are selected by passing a metric name prefix. Use the Delete method of this API path to remove tag configurations.\nResults can be sent to a set of account email addresses, just like the same operation in the Datadog web app.\nIf multiple calls include the same metric, the last configuration applied (not by submit order) is used, do not\nexpect deterministic ordering of concurrent calls. The `exclude_tags_mode` value will set all metrics that match the prefix to\nthe same exclusion state, metric tag configurations do not support mixed inclusion and exclusion for tags on the same metric.\nCan only be used with application keys of users with the `Manage Tags for Metrics` permission.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Metrics: List active tags and aggregations", "api_call": "GET /api/v2/metrics/{metric_name}/active-configurations", "api_version": "v2", "api_arguments": {"metric_name": "[REQUIRED] string: The name of the metric.", "window[seconds]": "integer: The number of seconds of look back (from now).\nDefault value is 604,800 (1 week), minimum value is 7200 (2 hours), maximum value is 2,630,000 (1 month)."}, "functionality": "List tags and aggregations that are actively queried on dashboards and monitors for a given metric name.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Metrics: List tags by metric name", "api_call": "GET /api/v2/metrics/{metric_name}/all-tags", "api_version": "v2", "api_arguments": {"metric_name": "[REQUIRED] string: The name of the metric."}, "functionality": "View indexed tag key-value pairs for a given metric name.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Metrics: Tag Configuration Cardinality Estimator", "api_call": "GET /api/v2/metrics/{metric_name}/estimate", "api_version": "v2", "api_arguments": {"metric_name": "[REQUIRED] string: The name of the metric.", "filter[groups]": "string: Filtered tag keys that the metric is configured to query with.", "filter[hours_ago]": "integer: The number of hours of look back (from now) to estimate cardinality with. Estimates are based on historical data, and unspecified fields default to the minimum 49 hours.", "filter[num_aggregations]": "integer: The number of aggregations that a `count`, `rate`, or `gauge` metric is configured to use. Max number of aggregation combos is 9.", "filter[pct]": "boolean: A boolean, for distribution metrics only, to estimate cardinality if the metric includes additional percentile aggregators.", "filter[timespan_h]": "integer: A window, in hours, from the look back to estimate cardinality with."}, "functionality": "Returns the estimated cardinality for a metric with a given tag, percentile and number of aggregations configuration using Metrics without Limits&trade;.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Metrics: Delete a tag configuration", "api_call": "DELETE /api/v2/metrics/{metric_name}/tags", "api_version": "v2", "api_arguments": {"metric_name": "[REQUIRED] string: The name of the metric."}, "functionality": "Deletes a metric's tag configuration. Can only be used with application\nkeys from users with the `Manage Tags for Metrics` permission.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Metrics: List tag configuration by name", "api_call": "GET /api/v2/metrics/{metric_name}/tags", "api_version": "v2", "api_arguments": {"metric_name": "[REQUIRED] string: The name of the metric."}, "functionality": "Returns the tag configuration for the given metric name.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Metrics: Update a tag configuration", "api_call": "PATCH /api/v2/metrics/{metric_name}/tags", "api_version": "v2", "api_arguments": {"metric_name": "[REQUIRED] string: The name of the metric."}, "functionality": "Update the tag configuration of a metric or percentile aggregations of a distribution metric or custom aggregations\nof a count, rate, or gauge metric. By setting `exclude_tags_mode` to true the behavior is changed\nfrom an allow-list to a deny-list, and tags in the defined list will not be queryable.\nCan only be used with application keys from users with the `Manage Tags for Metrics` permission. This endpoint requires\na tag configuration to be created first.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Metrics: Create a tag configuration", "api_call": "POST /api/v2/metrics/{metric_name}/tags", "api_version": "v2", "api_arguments": {"metric_name": "[REQUIRED] string: The name of the metric."}, "functionality": "Create and define a list of queryable tag keys for an existing count/gauge/rate/distribution metric.\nOptionally, include percentile aggregations on any distribution metric or configure custom aggregations\non any count, rate, or gauge metric. By setting `exclude_tags_mode` to true the behavior is changed\nfrom an allow-list to a deny-list, and tags in the defined list will not be queryable.\nCan only be used with application keys of users with the `Manage Tags for Metrics` permission.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Metrics: List distinct metric volumes by metric name", "api_call": "GET /api/v2/metrics/{metric_name}/volumes", "api_version": "v2", "api_arguments": {"metric_name": "[REQUIRED] string: The name of the metric."}, "functionality": "View distinct metrics volumes for the given metric name.\n\nCustom metrics generated in-app from other products will return `null` for ingested volumes.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Monitors: Get all monitor configuration policies", "api_call": "GET /api/v2/monitor/policy", "api_version": "v2", "api_arguments": {}, "functionality": "Get all monitor configuration policies.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Monitors: Create a monitor configuration policy", "api_call": "POST /api/v2/monitor/policy", "api_version": "v2", "api_arguments": {}, "functionality": "Create a monitor configuration policy.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Monitors: Delete a monitor configuration policy", "api_call": "DELETE /api/v2/monitor/policy/{policy_id}", "api_version": "v2", "api_arguments": {"policy_id": "[REQUIRED] string: ID of the monitor configuration policy."}, "functionality": "Delete a monitor configuration policy.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Monitors: Get a monitor configuration policy", "api_call": "GET /api/v2/monitor/policy/{policy_id}", "api_version": "v2", "api_arguments": {"policy_id": "[REQUIRED] string: ID of the monitor configuration policy."}, "functionality": "Get a monitor configuration policy by `policy_id`.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Monitors: Edit a monitor configuration policy", "api_call": "PATCH /api/v2/monitor/policy/{policy_id}", "api_version": "v2", "api_arguments": {"policy_id": "[REQUIRED] string: ID of the monitor configuration policy."}, "functionality": "Edit a monitor configuration policy.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Downtimes: Get active downtimes for a monitor", "api_call": "GET /api/v2/monitor/{monitor_id}/downtime_matches", "api_version": "v2", "api_arguments": {"monitor_id": "[REQUIRED] integer: The id of the monitor.", "page[offset]": "integer: Specific offset to use as the beginning of the returned page.", "page[limit]": "integer: Maximum number of downtimes in the response."}, "functionality": "Get all active downtimes for the specified monitor.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Roles: List permissions", "api_call": "GET /api/v2/permissions", "api_version": "v2", "api_arguments": {}, "functionality": "Returns a list of all permissions, including name, description, and ID.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: List findings", "api_call": "GET /api/v2/posture_management/findings", "api_version": "v2", "api_arguments": {"page[limit]": "integer: Limit the number of findings returned. Must be <= 1000.", "snapshot_timestamp": "integer: Return findings for a given snapshot of time (Unix ms).", "page[cursor]": "string: Return the next page of findings pointed to by the cursor.", "filter[tags]": "string: Return findings that have these associated tags (repeatable).", "filter[evaluation_changed_at]": "string: Return findings that have changed from pass to fail or vice versa on a specified date (Unix ms) or date range (using comparison operators).", "filter[muted]": "boolean: Set to `true` to return findings that are muted. Set to `false` to return unmuted findings.", "filter[rule_id]": "string: Return findings for the specified rule ID.", "filter[rule_name]": "string: Return findings for the specified rule.", "filter[resource_type]": "string: Return only findings for the specified resource type.", "filter[discovery_timestamp]": "string: Return findings that were found on a specified date (Unix ms) or date range (using comparison operators).", "filter[evaluation]": "string: Return only `pass` or `fail` findings.", "filter[status]": "string: Return only findings with the specified status."}, "functionality": "Get a list of CSPM findings.\n\n### Filtering\n\nFilters can be applied by appending query parameters to the URL.\n\n  - Using a single filter: `?filter[attribute_key]=attribute_value`\n  - Chaining filters: `?filter[attribute_key]=attribute_value&filter[attribute_key]=attribute_value...`\n  - Filtering on tags: `?filter[tags]=tag_key:tag_value&filter[tags]=tag_key_2:tag_value_2`\n\nHere, `attribute_key` can be any of the filter keys described further below.\n\nQuery parameters of type `integer` support comparison operators (`>`, `>=`, `<`, `<=`). This is particularly useful when filtering by `evaluation_changed_at` or `resource_discovery_timestamp`. For example: `?filter[evaluation_changed_at]=>20123123121`.\n\nYou can also use the negation operator on strings. For example, use `filter[resource_type]=-aws*` to filter for any non-AWS resources.\n\nThe operator must come after the equal sign. For example, to filter with the `>=` operator, add the operator after the equal sign: `filter[evaluation_changed_at]=>=1678809373257`.\n\nQuery parameters must be only among the documented ones and with values of correct types. Duplicated query parameters (e.g. `filter[status]=low&filter[status]=info`) are not allowed.\n\n### Response\n\nThe response includes an array of finding objects, pagination metadata, and a count of items that match the query.\n\nEach finding object contains the following:\n\n- The finding ID that can be used in a `GetFinding` request to retrieve the full finding details.\n- Core attributes, including status, evaluation, high-level resource details, muted state, and rule details.\n- `evaluation_changed_at` and `resource_discovery_date` time stamps.\n- An array of associated tags.\n", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Mute or unmute a batch of findings", "api_call": "PATCH /api/v2/posture_management/findings", "api_version": "v2", "api_arguments": {}, "functionality": "Mute or unmute findings.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Get a finding", "api_call": "GET /api/v2/posture_management/findings/{finding_id}", "api_version": "v2", "api_arguments": {"finding_id": "[REQUIRED] string: The ID of the finding.", "snapshot_timestamp": "integer: Return the finding for a given snapshot of time (Unix ms)."}, "functionality": "Returns a single finding with message and resource configuration.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Powerpack: Get all powerpacks", "api_call": "GET /api/v2/powerpacks", "api_version": "v2", "api_arguments": {"page[limit]": "integer: Maximum number of powerpacks in the response.", "page[offset]": "integer: Specific offset to use as the beginning of the returned page."}, "functionality": "Get a list of all powerpacks.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Powerpack: Create a new powerpack", "api_call": "POST /api/v2/powerpacks", "api_version": "v2", "api_arguments": {}, "functionality": "Create a powerpack.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Powerpack: Delete a powerpack", "api_call": "DELETE /api/v2/powerpacks/{powerpack_id}", "api_version": "v2", "api_arguments": {"powerpack_id": "[REQUIRED] string: Powerpack id"}, "functionality": "Delete a powerpack.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Powerpack: Get a Powerpack", "api_call": "GET /api/v2/powerpacks/{powerpack_id}", "api_version": "v2", "api_arguments": {"powerpack_id": "[REQUIRED] string: ID of the powerpack."}, "functionality": "Get a powerpack.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Powerpack: Update a powerpack", "api_call": "PATCH /api/v2/powerpacks/{powerpack_id}", "api_version": "v2", "api_arguments": {"powerpack_id": "[REQUIRED] string: ID of the powerpack."}, "functionality": "Update a powerpack.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Processes: Get all processes", "api_call": "GET /api/v2/processes", "api_version": "v2", "api_arguments": {"search": "string: String to search processes by.", "tags": "string: Comma-separated list of tags to filter processes by.", "from": "integer: Unix timestamp (number of seconds since epoch) of the start of the query window.\nIf not provided, the start of the query window will be 15 minutes before the `to` timestamp. If neither\n`from` nor `to` are provided, the query window will be `[now - 15m, now]`.", "to": "integer: Unix timestamp (number of seconds since epoch) of the end of the query window.\nIf not provided, the end of the query window will be 15 minutes after the `from` timestamp. If neither\n`from` nor `to` are provided, the query window will be `[now - 15m, now]`.", "page[limit]": "integer: Maximum number of results returned.", "page[cursor]": "string: String to query the next page of results.\nThis key is provided with each valid response from the API in `meta.page.after`."}, "functionality": "Get all processes for your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Metrics: Query scalar data across multiple products", "api_call": "POST /api/v2/query/scalar", "api_version": "v2", "api_arguments": {}, "functionality": "Query scalar values (as seen on Query Value, Table, and Toplist widgets).\nMultiple data sources are supported with the ability to\nprocess the data using formulas and functions.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Metrics: Query timeseries data across multiple products", "api_call": "POST /api/v2/query/timeseries", "api_version": "v2", "api_arguments": {}, "functionality": "Query timeseries data across various data sources and\nprocess the data by applying formulas and functions.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Restriction Policies: Delete a restriction policy", "api_call": "DELETE /api/v2/restriction_policy/{resource_id}", "api_version": "v2", "api_arguments": {"resource_id": "[REQUIRED] string: Identifier, formatted as `type:id`. Supported types: `connection`, `dashboard`, `notebook`, `security-rule`, `slo`."}, "functionality": "Deletes the restriction policy associated with a specified resource.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Restriction Policies: Get a restriction policy", "api_call": "GET /api/v2/restriction_policy/{resource_id}", "api_version": "v2", "api_arguments": {"resource_id": "[REQUIRED] string: Identifier, formatted as `type:id`. Supported types: `connection`, `dashboard`, `notebook`, `security-rule`, `slo`."}, "functionality": "Retrieves the restriction policy associated with a specified resource.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Restriction Policies: Update a restriction policy", "api_call": "POST /api/v2/restriction_policy/{resource_id}", "api_version": "v2", "api_arguments": {"resource_id": "[REQUIRED] string: Identifier, formatted as `type:id`. Supported types: `connection`, `dashboard`, `notebook`, `security-rule`, `slo`."}, "functionality": "Updates the restriction policy associated with a resource.\n\n#### Supported resources\nRestriction policies can be applied to the following resources:\n- Connections: `connection`\n- Dashboards: `dashboard`\n- Notebooks: `notebook`\n- Security Rules: `security-rule`\n- Service Level Objectives: `slo`\n\n#### Supported relations for resources\nResource Type            | Supported Relations\n-------------------------|--------------------------\nConnections              | `viewer`, `editor`, `resolver`\nDashboards               | `viewer`, `editor`\nNotebooks                | `viewer`, `editor`\nSecurity Rules           | `viewer`, `editor`\nService Level Objectives | `viewer`, `editor`", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Roles: List roles", "api_call": "GET /api/v2/roles", "api_version": "v2", "api_arguments": {"page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[number]": "integer: Specific page number to return.", "sort": "string: Sort roles depending on the given field. Sort order is **ascending** by default.\nSort order is **descending** if the field is prefixed by a negative sign, for example:\n`sort=-name`.", "filter": "string: Filter all roles by the given string.", "filter[id]": "string: Filter all roles by the given list of role IDs."}, "functionality": "Returns all roles, including their names and their unique identifiers.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Roles: Create role", "api_call": "POST /api/v2/roles", "api_version": "v2", "api_arguments": {}, "functionality": "Create a new role for your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Roles: Delete role", "api_call": "DELETE /api/v2/roles/{role_id}", "api_version": "v2", "api_arguments": {"role_id": "[REQUIRED] string: The unique identifier of the role."}, "functionality": "Disables a role.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Roles: Get a role", "api_call": "GET /api/v2/roles/{role_id}", "api_version": "v2", "api_arguments": {"role_id": "[REQUIRED] string: The unique identifier of the role."}, "functionality": "Get a role in the organization specified by the role’s `role_id`.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Roles: Update a role", "api_call": "PATCH /api/v2/roles/{role_id}", "api_version": "v2", "api_arguments": {"role_id": "[REQUIRED] string: The unique identifier of the role."}, "functionality": "Edit a role. Can only be used with application keys belonging to administrators.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Roles: Create a new role by cloning an existing role", "api_call": "POST /api/v2/roles/{role_id}/clone", "api_version": "v2", "api_arguments": {"role_id": "[REQUIRED] string: The unique identifier of the role."}, "functionality": "Clone an existing role", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Roles: Revoke permission", "api_call": "DELETE /api/v2/roles/{role_id}/permissions", "api_version": "v2", "api_arguments": {"role_id": "[REQUIRED] string: The unique identifier of the role."}, "functionality": "Removes a permission from a role.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Roles: List permissions for a role", "api_call": "GET /api/v2/roles/{role_id}/permissions", "api_version": "v2", "api_arguments": {"role_id": "[REQUIRED] string: The unique identifier of the role."}, "functionality": "Returns a list of all permissions for a single role.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Roles: Grant permission to a role", "api_call": "POST /api/v2/roles/{role_id}/permissions", "api_version": "v2", "api_arguments": {"role_id": "[REQUIRED] string: The unique identifier of the role."}, "functionality": "Adds a permission to a role.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Roles: Remove a user from a role", "api_call": "DELETE /api/v2/roles/{role_id}/users", "api_version": "v2", "api_arguments": {"role_id": "[REQUIRED] string: The unique identifier of the role."}, "functionality": "Removes a user from a role.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Roles: Get all users of a role", "api_call": "GET /api/v2/roles/{role_id}/users", "api_version": "v2", "api_arguments": {"role_id": "[REQUIRED] string: The unique identifier of the role.", "page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[number]": "integer: Specific page number to return.", "sort": "string: User attribute to order results by. Sort order is **ascending** by default.\nSort order is **descending** if the field is prefixed by a negative sign,\nfor example `sort=-name`. Options: `name`, `email`, `status`.", "filter": "string: Filter all users by the given string. Defaults to no filtering."}, "functionality": "Gets all users of a role.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Roles: Add a user to a role", "api_call": "POST /api/v2/roles/{role_id}/users", "api_version": "v2", "api_arguments": {"role_id": "[REQUIRED] string: The unique identifier of the role."}, "functionality": "Adds a user to a role.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - RUM: Aggregate RUM events", "api_call": "POST /api/v2/rum/analytics/aggregate", "api_version": "v2", "api_arguments": {}, "functionality": "The API endpoint to aggregate RUM events into buckets of computed metrics and timeseries.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - RUM: List all the RUM applications", "api_call": "GET /api/v2/rum/applications", "api_version": "v2", "api_arguments": {}, "functionality": "List all the RUM applications in your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - RUM: Create a new RUM application", "api_call": "POST /api/v2/rum/applications", "api_version": "v2", "api_arguments": {}, "functionality": "Create a new RUM application in your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - RUM: Delete a RUM application", "api_call": "DELETE /api/v2/rum/applications/{id}", "api_version": "v2", "api_arguments": {"id": "[REQUIRED] string: RUM application ID."}, "functionality": "Delete an existing RUM application in your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - RUM: Get a RUM application", "api_call": "GET /api/v2/rum/applications/{id}", "api_version": "v2", "api_arguments": {"id": "[REQUIRED] string: RUM application ID."}, "functionality": "Get the RUM application with given ID in your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - RUM: Update a RUM application", "api_call": "PATCH /api/v2/rum/applications/{id}", "api_version": "v2", "api_arguments": {"id": "[REQUIRED] string: RUM application ID."}, "functionality": "Update the RUM application with given ID in your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - RUM: Get a list of RUM events", "api_call": "GET /api/v2/rum/events", "api_version": "v2", "api_arguments": {"filter[query]": "string: Search query following RUM syntax.", "filter[from]": "string: Minimum timestamp for requested events.", "filter[to]": "string: Maximum timestamp for requested events.", "sort": "string: Order of events in results.", "page[cursor]": "string: List following results with a cursor provided in the previous query.", "page[limit]": "integer: Maximum number of events in the response."}, "functionality": "List endpoint returns events that match a RUM search query.\n[Results are paginated][1].\n\nUse this endpoint to see your latest RUM events.\n\n[1]: https://docs.datadoghq.com/logs/guide/collect-multiple-logs-with-pagination", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - RUM: Search RUM events", "api_call": "POST /api/v2/rum/events/search", "api_version": "v2", "api_arguments": {}, "functionality": "List endpoint returns RUM events that match a RUM search query.\n[Results are paginated][1].\n\nUse this endpoint to build complex RUM events filtering and search.\n\n[1]: https://docs.datadoghq.com/logs/guide/collect-multiple-logs-with-pagination", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Organizations: Upload IdP metadata", "api_call": "POST /api/v2/saml_configurations/idp_metadata", "api_version": "v2", "api_arguments": {}, "functionality": "Endpoint for uploading IdP metadata for SAML setup.\n\nUse this endpoint to upload or replace IdP metadata for SAML login configuration.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Scorecards: List all rule outcomes", "api_call": "GET /api/v2/scorecard/outcomes", "api_version": "v2", "api_arguments": {"page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[offset]": "integer: Specific offset to use as the beginning of the returned page.", "include": "string: Include related rule details in the response.", "fields[outcome]": "string: Return only specified values in the outcome attributes.", "fields[rule]": "string: Return only specified values in the included rule details.", "filter[outcome][service_name]": "string: Filter the outcomes on a specific service name.", "filter[outcome][state]": "string: Filter the outcomes by a specific state.", "filter[rule][enabled]": "boolean: Filter outcomes on whether a rule is enabled/disabled.", "filter[rule][id]": "string: Filter outcomes based on rule ID.", "filter[rule][name]": "string: Filter outcomes based on rule name."}, "functionality": "Fetches all rule outcomes.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Scorecards: Create outcomes batch", "api_call": "POST /api/v2/scorecard/outcomes/batch", "api_version": "v2", "api_arguments": {}, "functionality": "Sets multiple service-rule outcomes in a single batched request.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Scorecards: List all rules", "api_call": "GET /api/v2/scorecard/rules", "api_version": "v2", "api_arguments": {"page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[offset]": "integer: Specific offset to use as the beginning of the returned page.", "include": "string: Include related scorecard details in the response.", "filter[rule][id]": "string: Filter the rules on a rule ID.", "filter[rule][enabled]": "boolean: Filter for enabled rules only.", "filter[rule][custom]": "boolean: Filter for custom rules only.", "filter[rule][name]": "string: Filter rules on the rule name.", "filter[rule][description]": "string: Filter rules on the rule description.", "fields[rule]": "string: Return only specific fields in the response for rule attributes.", "fields[scorecard]": "string: Return only specific fields in the included response for scorecard attributes."}, "functionality": "Fetch all rules.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Scorecards: Create a new rule", "api_call": "POST /api/v2/scorecard/rules", "api_version": "v2", "api_arguments": {}, "functionality": "Creates a new rule.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Scorecards: Delete a rule", "api_call": "DELETE /api/v2/scorecard/rules/{rule_id}", "api_version": "v2", "api_arguments": {"rule_id": "[REQUIRED] string: The ID of the rule/scorecard."}, "functionality": "Deletes a single rule.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Workload Security: Get the latest Cloud Workload Security policy", "api_call": "GET /api/v2/security/cloud_workload/policy/download", "api_version": "v2", "api_arguments": {}, "functionality": "The download endpoint generates a Cloud Workload Security policy file from your currently active\nCloud Workload Security rules, and downloads them as a .policy file. This file can then be deployed to\nyour Agents to update the policy running in your environment.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Workload Security: Get all Cloud Workload Security Agent rules", "api_call": "GET /api/v2/security_monitoring/cloud_workload_security/agent_rules", "api_version": "v2", "api_arguments": {}, "functionality": "Get the list of Agent rules.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Workload Security: Create a Cloud Workload Security Agent rule", "api_call": "POST /api/v2/security_monitoring/cloud_workload_security/agent_rules", "api_version": "v2", "api_arguments": {}, "functionality": "Create a new Agent rule with the given parameters.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Workload Security: Delete a Cloud Workload Security Agent rule", "api_call": "DELETE /api/v2/security_monitoring/cloud_workload_security/agent_rules/{agent_rule_id}", "api_version": "v2", "api_arguments": {"agent_rule_id": "[REQUIRED] string: The ID of the Agent rule."}, "functionality": "Delete a specific Agent rule.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Workload Security: Get a Cloud Workload Security Agent rule", "api_call": "GET /api/v2/security_monitoring/cloud_workload_security/agent_rules/{agent_rule_id}", "api_version": "v2", "api_arguments": {"agent_rule_id": "[REQUIRED] string: The ID of the Agent rule."}, "functionality": "Get the details of a specific Agent rule.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Cloud Workload Security: Update a Cloud Workload Security Agent rule", "api_call": "PATCH /api/v2/security_monitoring/cloud_workload_security/agent_rules/{agent_rule_id}", "api_version": "v2", "api_arguments": {"agent_rule_id": "[REQUIRED] string: The ID of the Agent rule."}, "functionality": "Update a specific Agent rule.\nReturns the Agent rule object when the request is successful.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Get all security filters", "api_call": "GET /api/v2/security_monitoring/configuration/security_filters", "api_version": "v2", "api_arguments": {}, "functionality": "Get the list of configured security filters with their definitions.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Create a security filter", "api_call": "POST /api/v2/security_monitoring/configuration/security_filters", "api_version": "v2", "api_arguments": {}, "functionality": "Create a security filter.\n\nSee the [security filter guide](https://docs.datadoghq.com/security_platform/guide/how-to-setup-security-filters-using-security-monitoring-api/)\nfor more examples.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Delete a security filter", "api_call": "DELETE /api/v2/security_monitoring/configuration/security_filters/{security_filter_id}", "api_version": "v2", "api_arguments": {"security_filter_id": "[REQUIRED] string: The ID of the security filter."}, "functionality": "Delete a specific security filter.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Get a security filter", "api_call": "GET /api/v2/security_monitoring/configuration/security_filters/{security_filter_id}", "api_version": "v2", "api_arguments": {"security_filter_id": "[REQUIRED] string: The ID of the security filter."}, "functionality": "Get the details of a specific security filter.\n\nSee the [security filter guide](https://docs.datadoghq.com/security_platform/guide/how-to-setup-security-filters-using-security-monitoring-api/)\nfor more examples.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Update a security filter", "api_call": "PATCH /api/v2/security_monitoring/configuration/security_filters/{security_filter_id}", "api_version": "v2", "api_arguments": {"security_filter_id": "[REQUIRED] string: The ID of the security filter."}, "functionality": "Update a specific security filter.\nReturns the security filter object when the request is successful.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Get all suppression rules", "api_call": "GET /api/v2/security_monitoring/configuration/suppressions", "api_version": "v2", "api_arguments": {}, "functionality": "Get the list of all suppression rules.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Create a suppression rule", "api_call": "POST /api/v2/security_monitoring/configuration/suppressions", "api_version": "v2", "api_arguments": {}, "functionality": "Create a new suppression rule.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Delete a suppression rule", "api_call": "DELETE /api/v2/security_monitoring/configuration/suppressions/{suppression_id}", "api_version": "v2", "api_arguments": {"suppression_id": "[REQUIRED] string: The ID of the suppression rule"}, "functionality": "Delete a specific suppression rule.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Get a suppression rule", "api_call": "GET /api/v2/security_monitoring/configuration/suppressions/{suppression_id}", "api_version": "v2", "api_arguments": {"suppression_id": "[REQUIRED] string: The ID of the suppression rule"}, "functionality": "Get the details of a specific suppression rule.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Update a suppression rule", "api_call": "PATCH /api/v2/security_monitoring/configuration/suppressions/{suppression_id}", "api_version": "v2", "api_arguments": {"suppression_id": "[REQUIRED] string: The ID of the suppression rule"}, "functionality": "Update a specific suppression rule.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: List rules", "api_call": "GET /api/v2/security_monitoring/rules", "api_version": "v2", "api_arguments": {"page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[number]": "integer: Specific page number to return."}, "functionality": "List rules.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Create a detection rule", "api_call": "POST /api/v2/security_monitoring/rules", "api_version": "v2", "api_arguments": {}, "functionality": "Create a detection rule.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Delete an existing rule", "api_call": "DELETE /api/v2/security_monitoring/rules/{rule_id}", "api_version": "v2", "api_arguments": {"rule_id": "[REQUIRED] string: The ID of the rule."}, "functionality": "Delete an existing rule. Default rules cannot be deleted.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Get a rule's details", "api_call": "GET /api/v2/security_monitoring/rules/{rule_id}", "api_version": "v2", "api_arguments": {"rule_id": "[REQUIRED] string: The ID of the rule."}, "functionality": "Get a rule's details.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Update an existing rule", "api_call": "PUT /api/v2/security_monitoring/rules/{rule_id}", "api_version": "v2", "api_arguments": {"rule_id": "[REQUIRED] string: The ID of the rule."}, "functionality": "Update an existing rule. When updating `cases`, `queries` or `options`, the whole field\nmust be included. For example, when modifying a query all queries must be included.\nDefault rules can only be updated to be enabled and to change notifications.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Get a quick list of security signals", "api_call": "GET /api/v2/security_monitoring/signals", "api_version": "v2", "api_arguments": {"filter[query]": "string: The search query for security signals.", "filter[from]": "string: The minimum timestamp for requested security signals.", "filter[to]": "string: The maximum timestamp for requested security signals.", "sort": "string: The order of the security signals in results.", "page[cursor]": "string: A list of results using the cursor provided in the previous query.", "page[limit]": "integer: The maximum number of security signals in the response."}, "functionality": "The list endpoint returns security signals that match a search query.\nBoth this endpoint and the POST endpoint can be used interchangeably when listing\nsecurity signals.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Get a list of security signals", "api_call": "POST /api/v2/security_monitoring/signals/search", "api_version": "v2", "api_arguments": {}, "functionality": "Returns security signals that match a search query.\nBoth this endpoint and the GET endpoint can be used interchangeably for listing\nsecurity signals.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Get a signal's details", "api_call": "GET /api/v2/security_monitoring/signals/{signal_id}", "api_version": "v2", "api_arguments": {"signal_id": "[REQUIRED] string: The ID of the signal."}, "functionality": "Get a signal's details.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Modify the triage assignee of a security signal", "api_call": "PATCH /api/v2/security_monitoring/signals/{signal_id}/assignee", "api_version": "v2", "api_arguments": {"signal_id": "[REQUIRED] string: The ID of the signal."}, "functionality": "Modify the triage assignee of a security signal.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Change the related incidents of a security signal", "api_call": "PATCH /api/v2/security_monitoring/signals/{signal_id}/incidents", "api_version": "v2", "api_arguments": {"signal_id": "[REQUIRED] string: The ID of the signal."}, "functionality": "Change the related incidents for a security signal.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Security Monitoring: Change the triage state of a security signal", "api_call": "PATCH /api/v2/security_monitoring/signals/{signal_id}/state", "api_version": "v2", "api_arguments": {"signal_id": "[REQUIRED] string: The ID of the signal."}, "functionality": "Change the triage state of a security signal.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Sensitive Data Scanner: List Scanning Groups", "api_call": "GET /api/v2/sensitive-data-scanner/config", "api_version": "v2", "api_arguments": {}, "functionality": "List all the Scanning groups in your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Sensitive Data Scanner: Reorder Groups", "api_call": "PATCH /api/v2/sensitive-data-scanner/config", "api_version": "v2", "api_arguments": {}, "functionality": "Reorder the list of groups.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Sensitive Data Scanner: Create Scanning Group", "api_call": "POST /api/v2/sensitive-data-scanner/config/groups", "api_version": "v2", "api_arguments": {}, "functionality": "Create a scanning group.\nThe request MAY include a configuration relationship.\nA rules relationship can be omitted entirely, but if it is included it MUST be\nnull or an empty array (rules cannot be created at the same time).\nThe new group will be ordered last within the configuration.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Sensitive Data Scanner: Delete Scanning Group", "api_call": "DELETE /api/v2/sensitive-data-scanner/config/groups/{group_id}", "api_version": "v2", "api_arguments": {"group_id": "[REQUIRED] string: The ID of a group of rules."}, "functionality": "Delete a given group.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Sensitive Data Scanner: Update Scanning Group", "api_call": "PATCH /api/v2/sensitive-data-scanner/config/groups/{group_id}", "api_version": "v2", "api_arguments": {"group_id": "[REQUIRED] string: The ID of a group of rules."}, "functionality": "Update a group, including the order of the rules.\nRules within the group are reordered by including a rules relationship. If the rules\nrelationship is present, its data section MUST contain linkages for all of the rules\ncurrently in the group, and MUST NOT contain any others.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Sensitive Data Scanner: Create Scanning Rule", "api_call": "POST /api/v2/sensitive-data-scanner/config/rules", "api_version": "v2", "api_arguments": {}, "functionality": "Create a scanning rule in a sensitive data scanner group, ordered last.\nThe posted rule MUST include a group relationship.\nIt MUST include either a standard_pattern relationship or a regex attribute, but not both.\nIf included_attributes is empty or missing, we will scan all attributes except\nexcluded_attributes. If both are missing, we will scan the whole event.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Sensitive Data Scanner: Delete Scanning Rule", "api_call": "DELETE /api/v2/sensitive-data-scanner/config/rules/{rule_id}", "api_version": "v2", "api_arguments": {"rule_id": "[REQUIRED] string: The ID of the rule."}, "functionality": "Delete a given rule.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Sensitive Data Scanner: Update Scanning Rule", "api_call": "PATCH /api/v2/sensitive-data-scanner/config/rules/{rule_id}", "api_version": "v2", "api_arguments": {"rule_id": "[REQUIRED] string: The ID of the rule."}, "functionality": "Update a scanning rule.\nThe request body MUST NOT include a standard_pattern relationship, as that relationship\nis non-editable. Trying to edit the regex attribute of a rule with a standard_pattern\nrelationship will also result in an error.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Sensitive Data Scanner: List standard patterns", "api_call": "GET /api/v2/sensitive-data-scanner/config/standard-patterns", "api_version": "v2", "api_arguments": {}, "functionality": "Returns all standard patterns.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Metrics: Submit metrics", "api_call": "POST /api/v2/series", "api_version": "v2", "api_arguments": {"Content-Encoding": "string: HTTP header used to compress the media-type."}, "functionality": "The metrics end-point allows you to post time-series data that can be graphed on Datadog’s dashboards.\nThe maximum payload size is 500 kilobytes (512000 bytes). Compressed payloads must have a decompressed size of less than 5 megabytes (5242880 bytes).\n\nIf you’re submitting metrics directly to the Datadog API without using DogStatsD, expect:\n\n- 64 bits for the timestamp\n- 64 bits for the value\n- 20 bytes for the metric names\n- 50 bytes for the timeseries\n- The full payload is approximately 100 bytes.\n\nHost name is one of the resources in the Resources field.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Accounts: Create a service account", "api_call": "POST /api/v2/service_accounts", "api_version": "v2", "api_arguments": {}, "functionality": "Create a service account for your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Accounts: List application keys for this service account", "api_call": "GET /api/v2/service_accounts/{service_account_id}/application_keys", "api_version": "v2", "api_arguments": {"service_account_id": "[REQUIRED] string: The ID of the service account.", "page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[number]": "integer: Specific page number to return.", "sort": "string: Application key attribute used to sort results. Sort order is ascending\nby default. In order to specify a descending sort, prefix the\nattribute with a minus sign.", "filter": "string: Filter application keys by the specified string.", "filter[created_at][start]": "string: Only include application keys created on or after the specified date.", "filter[created_at][end]": "string: Only include application keys created on or before the specified date."}, "functionality": "List all application keys available for this service account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Accounts: Create an application key for this service account", "api_call": "POST /api/v2/service_accounts/{service_account_id}/application_keys", "api_version": "v2", "api_arguments": {"service_account_id": "[REQUIRED] string: The ID of the service account."}, "functionality": "Create an application key for this service account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Accounts: Delete an application key for this service account", "api_call": "DELETE /api/v2/service_accounts/{service_account_id}/application_keys/{app_key_id}", "api_version": "v2", "api_arguments": {"service_account_id": "[REQUIRED] string: The ID of the service account.", "app_key_id": "[REQUIRED] string: The ID of the application key."}, "functionality": "Delete an application key owned by this service account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Accounts: Get one application key for this service account", "api_call": "GET /api/v2/service_accounts/{service_account_id}/application_keys/{app_key_id}", "api_version": "v2", "api_arguments": {"service_account_id": "[REQUIRED] string: The ID of the service account.", "app_key_id": "[REQUIRED] string: The ID of the application key."}, "functionality": "Get an application key owned by this service account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Accounts: Edit an application key for this service account", "api_call": "PATCH /api/v2/service_accounts/{service_account_id}/application_keys/{app_key_id}", "api_version": "v2", "api_arguments": {"service_account_id": "[REQUIRED] string: The ID of the service account.", "app_key_id": "[REQUIRED] string: The ID of the application key."}, "functionality": "Edit an application key owned by this service account.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incident Services: Get a list of all incident services", "api_call": "GET /api/v2/services", "api_version": "v2", "api_arguments": {"include": "string: Specifies which types of related objects should be included in the response.", "page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[offset]": "integer: Specific offset to use as the beginning of the returned page.", "filter": "string: A search query that filters services by name."}, "functionality": "Get all incident services uploaded for the requesting user's organization. If the `include[users]` query parameter is provided, the included attribute will contain the users related to these incident services.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incident Services: Create a new incident service", "api_call": "POST /api/v2/services", "api_version": "v2", "api_arguments": {}, "functionality": "Creates a new incident service.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Definition: Get all service definitions", "api_call": "GET /api/v2/services/definitions", "api_version": "v2", "api_arguments": {"page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[number]": "integer: Specific page number to return.", "schema_version": "string: The schema version desired in the response."}, "functionality": "Get a list of all service definitions from the Datadog Service Catalog.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Definition: Create or update service definition", "api_call": "POST /api/v2/services/definitions", "api_version": "v2", "api_arguments": {}, "functionality": "Create or update service definition in the Datadog Service Catalog.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Definition: Delete a single service definition", "api_call": "DELETE /api/v2/services/definitions/{service_name}", "api_version": "v2", "api_arguments": {"service_name": "[REQUIRED] string: The name of the service."}, "functionality": "Delete a single service definition in the Datadog Service Catalog.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Service Definition: Get a single service definition", "api_call": "GET /api/v2/services/definitions/{service_name}", "api_version": "v2", "api_arguments": {"service_name": "[REQUIRED] string: The name of the service.", "schema_version": "string: The schema version desired in the response."}, "functionality": "Get a single service definition from the Datadog Service Catalog.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incident Services: Delete an existing incident service", "api_call": "DELETE /api/v2/services/{service_id}", "api_version": "v2", "api_arguments": {"service_id": "[REQUIRED] string: The ID of the incident service."}, "functionality": "Deletes an existing incident service.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incident Services: Get details of an incident service", "api_call": "GET /api/v2/services/{service_id}", "api_version": "v2", "api_arguments": {"service_id": "[REQUIRED] string: The ID of the incident service.", "include": "string: Specifies which types of related objects should be included in the response."}, "functionality": "Get details of an incident service. If the `include[users]` query parameter is provided,\nthe included attribute will contain the users related to these incident services.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incident Services: Update an existing incident service", "api_call": "PATCH /api/v2/services/{service_id}", "api_version": "v2", "api_arguments": {"service_id": "[REQUIRED] string: The ID of the incident service."}, "functionality": "Updates an existing incident service. Only provide the attributes which should be updated as this request is a partial update.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Spans: Aggregate spans", "api_call": "POST /api/v2/spans/analytics/aggregate", "api_version": "v2", "api_arguments": {}, "functionality": "The API endpoint to aggregate spans into buckets and compute metrics and timeseries.\nThis endpoint is rate limited to `300` requests per hour.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Spans: Get a list of spans", "api_call": "GET /api/v2/spans/events", "api_version": "v2", "api_arguments": {"filter[query]": "string: Search query following spans syntax.", "filter[from]": "string: Minimum timestamp for requested spans. Supports date-time ISO8601, date math, and regular timestamps (milliseconds).", "filter[to]": "string: Maximum timestamp for requested spans. Supports date-time ISO8601, date math, and regular timestamps (milliseconds).", "sort": "string: Order of spans in results.", "page[cursor]": "string: List following results with a cursor provided in the previous query.", "page[limit]": "integer: Maximum number of spans in the response."}, "functionality": "List endpoint returns spans that match a span search query.\n[Results are paginated][1].\n\nUse this endpoint to see your latest spans.\nThis endpoint is rate limited to `300` requests per hour.\n\n[1]: /logs/guide/collect-multiple-logs-with-pagination?tab=v2api", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Spans: Search spans", "api_call": "POST /api/v2/spans/events/search", "api_version": "v2", "api_arguments": {}, "functionality": "List endpoint returns spans that match a span search query.\n[Results are paginated][1].\n\nUse this endpoint to build complex spans filtering and search.\nThis endpoint is rate limited to `300` requests per hour.\n\n[1]: /logs/guide/collect-multiple-logs-with-pagination?tab=v2api", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Synthetics: Get the on-demand concurrency cap", "api_call": "GET /api/v2/synthetics/settings/on_demand_concurrency_cap", "api_version": "v2", "api_arguments": {}, "functionality": "Get the on-demand concurrency cap.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Synthetics: Save new value for on-demand concurrency cap", "api_call": "POST /api/v2/synthetics/settings/on_demand_concurrency_cap", "api_version": "v2", "api_arguments": {}, "functionality": "Save new value for on-demand concurrency cap.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Get all teams", "api_call": "GET /api/v2/team", "api_version": "v2", "api_arguments": {"page[number]": "integer: Specific page number to return.", "page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "sort": "string: Specifies the order of the returned teams", "include": "array: Included related resources optionally requested. Allowed enum values: `team_links, user_team_permissions`", "filter[keyword]": "string: Search query. Can be team name, team handle, or email of team member", "filter[me]": "boolean: When true, only returns teams the current user belongs to", "fields[team]": "array: List of fields that need to be fetched."}, "functionality": "Get all teams.\nCan be used to search for teams using the `filter[keyword]` and `filter[me]` query parameters.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Create a team", "api_call": "POST /api/v2/team", "api_version": "v2", "api_arguments": {}, "functionality": "Create a new team.\nUser IDs passed through the `users` relationship field are added to the team.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Remove a team", "api_call": "DELETE /api/v2/team/{team_id}", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string"}, "functionality": "Remove a team using the team's `id`.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Get a team", "api_call": "GET /api/v2/team/{team_id}", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string"}, "functionality": "Get a single team using the team's `id`.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Update a team", "api_call": "PATCH /api/v2/team/{team_id}", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string"}, "functionality": "Update a team using the team's `id`.\nIf the `team_links` relationship is present, the associated links are updated to be in the order they appear in the array, and any existing team links not present are removed.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Get links for a team", "api_call": "GET /api/v2/team/{team_id}/links", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string"}, "functionality": "Get all links for a given team.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Create a team link", "api_call": "POST /api/v2/team/{team_id}/links", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string"}, "functionality": "Add a new link to a team.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Remove a team link", "api_call": "DELETE /api/v2/team/{team_id}/links/{link_id}", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string", "link_id": "[REQUIRED] string"}, "functionality": "Remove a link from a team.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Get a team link", "api_call": "GET /api/v2/team/{team_id}/links/{link_id}", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string", "link_id": "[REQUIRED] string"}, "functionality": "Get a single link for a team.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Update a team link", "api_call": "PATCH /api/v2/team/{team_id}/links/{link_id}", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string", "link_id": "[REQUIRED] string"}, "functionality": "Update a team link.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Get team memberships", "api_call": "GET /api/v2/team/{team_id}/memberships", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string", "page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[number]": "integer: Specific page number to return.", "sort": "string: Specifies the order of returned team memberships", "filter[keyword]": "string: Search query, can be user email or name"}, "functionality": "Get a paginated list of members for a team", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Add a user to a team", "api_call": "POST /api/v2/team/{team_id}/memberships", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string"}, "functionality": "Add a user to a team.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Remove a user from a team", "api_call": "DELETE /api/v2/team/{team_id}/memberships/{user_id}", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string", "user_id": "[REQUIRED] string"}, "functionality": "Remove a user from a team.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Update a user's membership attributes on a team", "api_call": "PATCH /api/v2/team/{team_id}/memberships/{user_id}", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string", "user_id": "[REQUIRED] string"}, "functionality": "Update a user's membership attributes on a team.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Get permission settings for a team", "api_call": "GET /api/v2/team/{team_id}/permission-settings", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string"}, "functionality": "Get all permission settings for a given team.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Update permission setting for team", "api_call": "PUT /api/v2/team/{team_id}/permission-settings/{action}", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string", "action": "[REQUIRED] string"}, "functionality": "Update a team permission setting for a given team.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incident Teams: Get a list of all incident teams", "api_call": "GET /api/v2/teams", "api_version": "v2", "api_arguments": {"include": "string: Specifies which types of related objects should be included in the response.", "page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[offset]": "integer: Specific offset to use as the beginning of the returned page.", "filter": "string: A search query that filters teams by name."}, "functionality": "Get all incident teams for the requesting user's organization. If the `include[users]` query parameter is provided, the included attribute will contain the users related to these incident teams.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incident Teams: Create a new incident team", "api_call": "POST /api/v2/teams", "api_version": "v2", "api_arguments": {}, "functionality": "Creates a new incident team.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incident Teams: Delete an existing incident team", "api_call": "DELETE /api/v2/teams/{team_id}", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string: The ID of the incident team."}, "functionality": "Deletes an existing incident team.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incident Teams: Get details of an incident team", "api_call": "GET /api/v2/teams/{team_id}", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string: The ID of the incident team.", "include": "string: Specifies which types of related objects should be included in the response."}, "functionality": "Get details of an incident team. If the `include[users]` query parameter is provided,\nthe included attribute will contain the users related to these incident teams.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Incident Teams: Update an existing incident team", "api_call": "PATCH /api/v2/teams/{team_id}", "api_version": "v2", "api_arguments": {"team_id": "[REQUIRED] string: The ID of the incident team."}, "functionality": "Updates an existing incident team. Only provide the attributes which should be updated as this request is a partial update.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Usage Metering: Get hourly usage for application security", "api_call": "GET /api/v2/usage/application_security", "api_version": "v2", "api_arguments": {"start_hr": "[REQUIRED] string: Datetime in ISO-8601 format, UTC, precise to hour: `[YYYY-MM-DDThh]` for usage beginning at this hour.", "end_hr": "string: Datetime in ISO-8601 format, UTC, precise to hour: `[YYYY-MM-DDThh]` for usage ending\n**before** this hour."}, "functionality": "Get hourly usage for application security .\n**Note:** hourly usage data for all products is now available in the [Get hourly usage by product family API](https://docs.datadoghq.com/api/latest/usage-metering/#get-hourly-usage-by-product-family)", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Usage Metering: Get cost across multi-org account", "api_call": "GET /api/v2/usage/cost_by_org", "api_version": "v2", "api_arguments": {"start_month": "[REQUIRED] string: Datetime in ISO-8601 format, UTC, precise to month: `[YYYY-MM]` for cost beginning this month.", "end_month": "string: Datetime in ISO-8601 format, UTC, precise to month: `[YYYY-MM]` for cost ending this month."}, "functionality": "Get cost across multi-org account.\nCost by org data for a given month becomes available no later than the 16th of the following month.\n**Note:** This endpoint has been deprecated. Please use the new endpoint\n[`/historical_cost`](https://docs.datadoghq.com/api/latest/usage-metering/#get-historical-cost-across-your-account)\ninstead.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Usage Metering: Get estimated cost across your account", "api_call": "GET /api/v2/usage/estimated_cost", "api_version": "v2", "api_arguments": {"view": "string: String to specify whether cost is broken down at a parent-org level or at the sub-org level. Available views are `summary` and `sub-org`. Defaults to `summary`.", "start_month": "string: Datetime in ISO-8601 format, UTC, precise to month: `[YYYY-MM]` for cost beginning this month. Either start_month or start_date should be specified, but not both. (start_month cannot go beyond two months in the past). Provide an `end_month` to view month-over-month cost.", "end_month": "string: Datetime in ISO-8601 format, UTC, precise to month: `[YYYY-MM]` for cost ending this month.", "start_date": "string: Datetime in ISO-8601 format, UTC, precise to day: `[YYYY-MM-DD]` for cost beginning this day. Either start_month or start_date should be specified, but not both. (start_date cannot go beyond two months in the past). Provide an `end_date` to view day-over-day cumulative cost.", "end_date": "string: Datetime in ISO-8601 format, UTC, precise to day: `[YYYY-MM-DD]` for cost ending this day."}, "functionality": "Get estimated cost across multi-org and single root-org accounts.\nEstimated cost data is only available for the current month and previous month\nand is delayed by up to 72 hours from when it was incurred.\nTo access historical costs prior to this, use the `/historical_cost` endpoint.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Usage Metering: Get historical cost across your account", "api_call": "GET /api/v2/usage/historical_cost", "api_version": "v2", "api_arguments": {"view": "string: String to specify whether cost is broken down at a parent-org level or at the sub-org level. Available views are `summary` and `sub-org`.  Defaults to `summary`.", "start_month": "[REQUIRED] string: Datetime in ISO-8601 format, UTC, precise to month: `[YYYY-MM]` for cost beginning this month.", "end_month": "string: Datetime in ISO-8601 format, UTC, precise to month: `[YYYY-MM]` for cost ending this month."}, "functionality": "Get historical cost across multi-org and single root-org accounts.\nCost data for a given month becomes available no later than the 16th of the following month.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Usage Metering: Get hourly usage by product family", "api_call": "GET /api/v2/usage/hourly_usage", "api_version": "v2", "api_arguments": {"filter[timestamp][start]": "[REQUIRED] string: Datetime in ISO-8601 format, UTC, precise to hour: [YYYY-MM-DDThh] for usage beginning at this hour.", "filter[timestamp][end]": "string: Datetime in ISO-8601 format, UTC, precise to hour: [YYYY-MM-DDThh] for usage ending **before** this hour.", "filter[product_families]": "[REQUIRED] string: Comma separated list of product families to retrieve. Available families are `all`, `analyzed_logs`,\n`application_security`, `audit_trail`, `serverless`, `ci_app`, `cloud_cost_management`,\n`csm_container_enterprise`, `csm_host_enterprise`, `cspm`, `custom_events`, `cws`, `dbm`, `error_tracking`,\n`fargate`, `infra_hosts`, `incident_management`, `indexed_logs`, `indexed_spans`, `ingested_spans`, `iot`,\n`lambda_traced_invocations`, `logs`, `network_flows`, `network_hosts`, `netflow_monitoring`, `observability_pipelines`,\n`online_archive`, `profiling`, `rum`, `rum_browser_sessions`, `rum_mobile_sessions`, `sds`, `snmp`,\n`synthetics_api`, `synthetics_browser`, `synthetics_mobile`, `synthetics_parallel_testing`, and `timeseries`.\nThe following product family has been **deprecated**: `audit_logs`.", "filter[include_descendants]": "boolean: Include child org usage in the response. Defaults to false.", "filter[include_breakdown]": "boolean: Include breakdown of usage by subcategories where applicable (for product family logs only). Defaults to false.", "filter[versions]": "string: Comma separated list of product family versions to use in the format `product_family:version`. For example,\n`infra_hosts:1.0.0`. If this parameter is not used, the API will use the latest version of each requested\nproduct family. Currently all families have one version `1.0.0`.", "page[limit]": "integer: Maximum number of results to return (between 1 and 500) - defaults to 500 if limit not specified.", "page[next_record_id]": "string: List following results with a next_record_id provided in the previous query."}, "functionality": "Get hourly usage by product family.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Usage Metering: Get hourly usage for Lambda traced invocations", "api_call": "GET /api/v2/usage/lambda_traced_invocations", "api_version": "v2", "api_arguments": {"start_hr": "[REQUIRED] string: Datetime in ISO-8601 format, UTC, precise to hour: `[YYYY-MM-DDThh]` for usage beginning at this hour.", "end_hr": "string: Datetime in ISO-8601 format, UTC, precise to hour: `[YYYY-MM-DDThh]` for usage ending\n**before** this hour."}, "functionality": "Get hourly usage for Lambda traced invocations.\n**Note:** hourly usage data for all products is now available in the [Get hourly usage by product family API](https://docs.datadoghq.com/api/latest/usage-metering/#get-hourly-usage-by-product-family)", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Usage Metering: Get hourly usage for observability pipelines", "api_call": "GET /api/v2/usage/observability_pipelines", "api_version": "v2", "api_arguments": {"start_hr": "[REQUIRED] string: Datetime in ISO-8601 format, UTC, precise to hour: `[YYYY-MM-DDThh]` for usage beginning at this hour.", "end_hr": "string: Datetime in ISO-8601 format, UTC, precise to hour: `[YYYY-MM-DDThh]` for usage ending\n**before** this hour."}, "functionality": "Get hourly usage for observability pipelines.\n**Note:** hourly usage data for all products is now available in the [Get hourly usage by product family API](https://docs.datadoghq.com/api/latest/usage-metering/#get-hourly-usage-by-product-family)", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Usage Metering: Get projected cost across your account", "api_call": "GET /api/v2/usage/projected_cost", "api_version": "v2", "api_arguments": {"view": "string: String to specify whether cost is broken down at a parent-org level or at the sub-org level. Available views are `summary` and `sub-org`. Defaults to `summary`."}, "functionality": "Get projected cost across multi-org and single root-org accounts.\nProjected cost data is only available for the current month and becomes available around the 12th of the month.\nThis endpoint requires the usage_read authorization scope.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Users: Send invitation emails", "api_call": "POST /api/v2/user_invitations", "api_version": "v2", "api_arguments": {}, "functionality": "Sends emails to one or more users inviting them to join the organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Users: Get a user invitation", "api_call": "GET /api/v2/user_invitations/{user_invitation_uuid}", "api_version": "v2", "api_arguments": {"user_invitation_uuid": "[REQUIRED] string: The UUID of the user invitation."}, "functionality": "Returns a single user invitation by its UUID.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Users: List all users", "api_call": "GET /api/v2/users", "api_version": "v2", "api_arguments": {"page[size]": "integer: Size for a given page. The maximum allowed value is 100.", "page[number]": "integer: Specific page number to return.", "sort": "string: User attribute to order results by. Sort order is ascending by default.\nSort order is descending if the field\nis prefixed by a negative sign, for example `sort=-name`. Options: `name`,\n`modified_at`, `user_count`.", "sort_dir": "string: Direction of sort. Options: `asc`, `desc`.", "filter": "string: Filter all users by the given string. Defaults to no filtering.", "filter[status]": "string: Filter on status attribute.\nComma separated list, with possible values `Active`, `Pending`, and `Disabled`.\nDefaults to no filtering."}, "functionality": "Get the list of all users in the organization. This list includes\nall users even if they are deactivated or unverified.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Users: Create a user", "api_call": "POST /api/v2/users", "api_version": "v2", "api_arguments": {}, "functionality": "Create a user for your organization.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Users: Disable a user", "api_call": "DELETE /api/v2/users/{user_id}", "api_version": "v2", "api_arguments": {"user_id": "[REQUIRED] string: The ID of the user."}, "functionality": "Disable a user. Can only be used with an application key belonging\nto an administrator user.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Users: Get user details", "api_call": "GET /api/v2/users/{user_id}", "api_version": "v2", "api_arguments": {"user_id": "[REQUIRED] string: The ID of the user."}, "functionality": "Get a user in the organization specified by the user’s `user_id`.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Users: Update a user", "api_call": "PATCH /api/v2/users/{user_id}", "api_version": "v2", "api_arguments": {"user_id": "[REQUIRED] string: The ID of the user."}, "functionality": "Edit a user. Can only be used with an application key belonging\nto an administrator user.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Users: Get a user organization", "api_call": "GET /api/v2/users/{user_id}/orgs", "api_version": "v2", "api_arguments": {"user_id": "[REQUIRED] string: The ID of the user."}, "functionality": "Get a user organization. Returns the user information and all organizations\njoined by this user.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Users: Get a user permissions", "api_call": "GET /api/v2/users/{user_id}/permissions", "api_version": "v2", "api_arguments": {"user_id": "[REQUIRED] string: The ID of the user."}, "functionality": "Get a user permission set. Returns a list of the user’s permissions\ngranted by the associated user's roles.", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Datadog API - Teams: Get user memberships", "api_call": "GET /api/v2/users/{user_uuid}/memberships", "api_version": "v2", "api_arguments": {"user_uuid": "[REQUIRED] string"}, "functionality": "Get a list of memberships for a user", "env_requirements": ["datadog_api_client"], "metadata": {"documentation_link": "https://docs.datadoghq.com/api/latest/"}}]