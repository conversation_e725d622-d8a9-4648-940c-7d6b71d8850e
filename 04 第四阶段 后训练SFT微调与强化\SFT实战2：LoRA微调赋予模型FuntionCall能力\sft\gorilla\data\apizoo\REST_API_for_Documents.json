[{"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Configuration: Get Collaboration Configuration", "api_call": "GET /documents/api/1.2/config/collaboration", "api_version": "2023.11.27", "api_arguments": {}, "functionality": "<p>Retrieve Collaboration configurations.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Configuration: Get CDN Configuration", "api_call": "GET /documents/api/1.2/config/sites/deliverycdn", "api_version": "2023.11.27", "api_arguments": {}, "functionality": "<p>Retrieve the Content Delivery Network URL.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Configuration: Get Email Notification Configuration", "api_call": "GET /documents/api/1.2/config/notification/email", "api_version": "2023.11.27", "api_arguments": {}, "functionality": "<p>Retrieve the email notification configuration for this service instance.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Configuration: Set Email Notification Configuration", "api_call": "PUT /documents/api/1.2/config/notification/email", "api_version": "2023.11.27", "api_arguments": {"payload": "[REQUIRED] : The request body defines the details of setting email notification in this service instance."}, "functionality": "<p>Update email notification configuration for this service instance. To update this configuration, you must log in as an administrator.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Client Applications: Create a Client Application", "api_call": "POST /documents/api/1.2/config/clientApplications", "api_version": "2023.11.27", "api_arguments": {"file": "[REQUIRED] : <p>File(JSON payload) to upload.</p><p>Use <code>Content-Type: application/json</code> to describe this information as a JSON object.</p>", "metadata": "[REQUIRED] : <p>You can use this parameter to pass the input params <code>name</code> and <code>isEnabled</code> for the new client application.</p>Use <code>Content-Type: application/json</code> to describe this information as a JSON object.</p><p><b>The field <code>isEnabled</code> is optional. The default value for this field is <code>true</code></b></p><p>For example:</p><code>&#123;<p> &#34;name&#34;:&#32;&#32;&#32;&#32;&#34;testApp&#34;,</p><p> &#34;isEnabled&#34;:&#32;&#32;&#32;&#32;&#34;false&#34;</p>&#125;</code>"}, "functionality": "<p>Create a new custom actions client application. A single configuration file in JSON format containing one or more custom action definitions is required.</p><p> You must be an administrator to perform this operation.</p><p> The request is a multipart HTTP request where one part is a JSON object (<code>metadata</code>) with the attributes <code>name</code> , <code>isEnabled</code> and the other part is the content of the file itself (<code>file</code>).</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Client Applications: Get All Client Applications", "api_call": "GET /documents/api/1.2/config/clientApplications", "api_version": "2023.11.27", "api_arguments": {"links": ": <p>Indicate if links for the client applications should be returned in the response. The value <code>links=true</code> will return all available links for each client application. Any other value or not sending <code>links</code> will not return links.</p>"}, "functionality": "<p>Get metadata of all the available custom actions client applications.</p><p> You must be an administrator to perform this operation.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Client Applications: Get a Client Application", "api_call": "GET /documents/api/1.2/config/clientApplications/{id}", "api_version": "2023.11.27", "api_arguments": {"id": "[REQUIRED] : <p>Globally unique identifier (GUID) of the client application.</p>", "links": ": <p>Indicate if links for the specified client application should be returned in the response. The value <code>links=true</code> will return all available links for the specified client application. Any other value or not sending <code>links</code> will not return links.</p>"}, "functionality": "<p>Get metadata for a specific custom actions client application.</p><p> You must be an administrator, to perform this operation.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Client Applications: Update a Client Application", "api_call": "PUT /documents/api/1.2/config/clientApplications/{id}", "api_version": "2023.11.27", "api_arguments": {"id": "[REQUIRED] : <p>Globally unique identifier (GUID) of the client application.</p>", "file": "[REQUIRED] : <p>File(json payload) to upload.</p><p>Use <code>Content-Type: application/json</code> to describe this information as a JSON object.</p>", "metadata": "[REQUIRED] : <p>You can use this parameter to update isEnabled flag value of an existing client application.</p>Use <code>Content-Type: application/json</code> to describe this information as a JSON object.</p><p>For example:</p><code>&#123;<p> &#34;isEnabled&#34;:&#32;&#32;&#32;&#32;&#34;true&#34;</p>&#125;"}, "functionality": "<p>Update either or both of the following fields of an existing custom actions client application : <code>file</code> and <code>isEnabled</code> attribute of <code>metadata</code>. </p><p> You must be an administrator to perform this operation.</p><p>The request is a multipart HTTP request where one part is a JSON object (<code>metadata</code>) with the attribute <code>isEnabled</code> and the other part is the content of the file itself (<code>file</code>).</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Client Applications: Delete a Client Application", "api_call": "DELETE /documents/api/1.2/config/clientApplications/{id}", "api_version": "2023.11.27", "api_arguments": {"id": "[REQUIRED] : <p>Globally unique identifier (GUID) of the client application</p>"}, "functionality": "<p>Delete the specified custom actions client application. You must be an administrator to perform this operation.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Client Applications: Download a Client Application File", "api_call": "GET /documents/api/1.2/config/clientApplications/{id}/data", "api_version": "2023.11.27", "api_arguments": {"id": "[REQUIRED] : <p>Globally unique identifier (GUID) of the client application.</p>"}, "functionality": "<p>Download the JSON Playload of an existing custom actions client application. You must be an administrator to perform this operation.</p><p><b>Note:</p><p>Range requests are not supported</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Sites: Create Site from Site", "api_call": "POST /documents/api/1.2/sites/{siteId}/site", "api_version": "2023.11.27", "api_arguments": {"siteId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the source site.</p>", "payload": "[REQUIRED] : <p>This parameter includes information such as the <code>name</code> parameter, which is the name of the site. Other parameters supported are <code>description</code> and <code>copyUpdates</code>.</p>"}, "functionality": "<p>Create a site from another site.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Templates: Create Site from Template", "api_call": "POST /documents/api/1.2/templates/{templateId}/site", "api_version": "2023.11.27", "api_arguments": {"templateId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the source template.</p>", "payload": "[REQUIRED] : <p>This parameter includes information such as the <code>name</code> parameter, which is the name of the site. Other parameters supported are <code>description</code> and <code>copyUpdates</code>.</p>"}, "functionality": "<p>Create a site from an existing template.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Change Folder Ownership", "api_call": "POST /documents/api/1.2/folders/{folderId}/changeOwnership", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder to change.</p>", "payload": "[REQUIRED] : <p>The request body defines details of the change ownership of the folder request. All parameters are required.</p>"}, "functionality": "<p>Change the ownership of a specified folder and gives the requester Manager access to the specified folder.<p><b>Note:</p><p>To change the ownership of a folder, the requester must have admin privileges for the folder. That is, the requester must be the owner or have the manager role.</b></p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Bulk Create Folders", "api_call": "POST /documents/api/1.2/folders/{folderId}/_bulkCreate", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. It is the parent folder for all sub-folders created in this request. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "payload": "[REQUIRED] : The request body defines details of the sub-folders to be created.", "links": ": <p>Indicate if links for the created folders should be returned in the response. The value <code>links=true</code> will return all available links for each created folder. Any other value or not sending <code>links</code> will not return links.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Requests bulk creation of folders under a single parent folder.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Bulk Download Folders and Files", "api_call": "POST /documents/api/1.2/folders/{folderId}/_download", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>. This files in the zip must all be children of this folder.</p>", "payload": "[REQUIRED] : The request body defines details of the files to be zipped."}, "functionality": "<p>Requests a bulk download of the specified globally unique identifiers (GUIDs) of the files under the specified folder.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Get Status of a Bulk Download Job", "api_call": "GET /documents/api/1.2/folders/{folderId}/_download/{jobId}", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "jobId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the bulk download job.</p>"}, "functionality": "<p>Returns the status of a bulk download job.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Abort a Bulk Download Job", "api_call": "DELETE /documents/api/1.2/folders/{folderId}/_download/{jobId}", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "jobId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the bulk download job.</p>"}, "functionality": "<p>Abort a bulk download job.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Download a Bulk Download Job File", "api_call": "GET /documents/api/1.2/folders/{folderId}/_download/{jobId}/package", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "jobId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the bulk download job.</p>", "Range": ": <p>The standard HTTP <code>Range</code>  header allows clients to stream parts of the file rather than the entire file. The value of the <code>Range</code> header can be one of the following formats: <ul><li>bytes=<i>startByte</i> - <i>endByte</i></br> For example to receive the first 500 bytes use <code>bytes=0-500</code><li>bytes=<i>skipBytes</i>-</br>For example to skip the first 500 bytes use <code>bytes=500-</code></li><li>bytes=-<i>lastBytes</i></br>For example to receive the last 500 bytes use <code>bytes=-500</code</li></ul> </p>"}, "functionality": "<p>Download a bulk download zip.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Get Folder", "api_call": "GET /documents/api/1.2/folders/{folderId}", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Get folder information for the specified folder.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Create Folder", "api_call": "POST /documents/api/1.2/folders/{folderId}", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "payload": "[REQUIRED] : The request body defines details of the create folder request. <b>Bold</b> indicates a required value.", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Create a new subfolder in the specified destination folder.</p><p><b>Note:</b><p>Two folders cannot have the same name in a given folder. Folder names are not case-sensitive; that is, <code>Folder_Name</code> and <code>folder_name</code> are considered identical. If a folder with the same name exists in the target destination, the name of the new folder is given a numeric increment. For example, if the folder <code>my_folder</code> already exists, the new folder is named <code>my_folder(2)</code>.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Edit Folder", "api_call": "PUT /documents/api/1.2/folders/{folderId}", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder.</p>", "payload": ": The request body defines details of the edit folder request. There are no required attributes.", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Change the name or the description of the specified folder.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Delete Folder", "api_call": "DELETE /documents/api/1.2/folders/{folderId}", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Delete the specified folder.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Copy Folder", "api_call": "POST /documents/api/1.2/folders/{folderId}/copy", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder.</p>", "payload": "[REQUIRED] : The request body defines details of the copy folder request. <b>Bold</b> indicates a required value.", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Copy a specified folder and its contents to a specified destination folder.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Move Folder", "api_call": "POST /documents/api/1.2/folders/{folderId}/move", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder.</p>", "payload": "[REQUIRED] : <p>The request body defines details of the move folder request. <b>Bold</b> indicates a required value.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Move a specified folder and its contents to a specified destination folder. The moved folder retains its globally unique identifier and all date and ownership information.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Get Home Folder Contents", "api_call": "GET /documents/api/1.2/folders/items", "api_version": "2023.11.27", "api_arguments": {"orderby": ": <p>Order the resulting items using the specified field and sort order. You can use the <code>name</code>, <code>createdTime</code>, and <code>modifiedTime</code> fields, and a sort order of ascending (<code>asc</code>) or descending (<code>desc</code>). For example, this is the default: <pre>orderby=name:asc</pre></p>", "limit": ": <p>Specify the maximum number of items to return. Use this parameter to specify how many items to include on a single page of results. The default is <code>50</code> and maximum is <code>10000</code>. The following example limits the number of returned items to 10: <pre>limit=10</pre></p>", "offset": ": <p>Specify the point at which to begin the list of items from the complete set of items returned for the action. If you do not specify an offset, the returned items begin with the first item in the item list (<code>offset=0</code>).</p> <p>Use this parameter to specify the starting point for a given page of results from the complete set of returned items. The following example limits the number of items per page to 10 and displays the second page of results (items 11-20):<pre>limit=10&offset=10</pre></p>"}, "functionality": "<p>Get a collection of all items (folders and files) that the user has access to, including folders that others have shared with that user. The <code>type</code> field indicates whether an item is a folder or a file.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Get Specific Folder Contents", "api_call": "GET /documents/api/1.2/folders/{folderId}/items", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "filterName": ": <p>Filter the results to only those folders and files whose name matches the specified string exactly. If there is no exact match, the call returns an empty list. For example, the following parameter restricts the response to folders (or files) named <code>TestFolder</code>: <pre>filterName=TestFolder</pre></p><p>The filter changes the behavior of the following response parameters:<ul><li>count: The number of items that match the <code>filterName</code> parameter.</li><li>totalResults: The number of items that match the <code>filterName</code> parameter.</li></ul></p>", "orderby": ": <p>Order the resulting items using the specified field and sort order. You can use the <code>name</code>, <code>createdTime</code>, and <code>modifiedTime</code> fields, and a sort order of ascending (<code>asc</code>) or descending (<code>desc</code>). For example, this is the default: <pre>orderby=name:asc</pre></p>", "limit": ": <p>Specify the maximum number of items to return. Use this parameter to specify how many items to include on a single page of results. The default is <code>50</code> and maximum is <code>10000</code>. The following example limits the number of returned items to 10: <pre>limit=10</pre></p>", "offset": ": <p>Specify the point at which to begin the list of items from the complete set of items returned for the action. If you do not specify an offset, the returned items begin with the first item in the item list (<code>offset=0</code>).</p> <p>Use this parameter to specify the starting point for a given page of results from the complete set of returned items. The following example limits the number of items per page to 10 and displays the second page of results (items 11-20):<pre>limit=10&offset=10</pre></p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Get a collection of child items (folders and files) in the specified folder. The <code>type</code> field indicates whether an item is a folder or a file.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Search Folders or Files", "api_call": "GET /documents/api/1.2/folders/search/items", "api_version": "2023.11.27", "api_arguments": {"fulltext": "[REQUIRED] : <p>Search string used to match folders or files. It will search these locations: <code>itemName</code>, <code>contents</code>, <code>extension</code>, <code>ownerName</code>, <code>lastModifiedName</code>, and <code>folderDescription</code>.</p><p>Either <code>fulltext</code> or <code>querytext</code> must be provided in the search API.</p>", "querytext": "[REQUIRED] : <p>Search string used to search for folders or files using the targets listed below. It can replace <code>fulltext</code>, taking advantage of multiple targets at the same time.</p><p>Either the <code>fulltext</code> or <code>querytext</code> parameter must be provided in the search API.</p> <p>The <code>querytext</code> parameter can target searches against the following string fields or number field. The search query is in the form:</p> <pre>searchField&ltoperation&gtsearchValue</pre> <p>Multiple searches can be combined with &ltAND&gt and &ltOR&GT. Searches are not case-sensitive.</p></br>The following string fields are supported:\n<ul> <li>xTags: search for tags</li> <li>fItemType: search by item type, either <code>File</code> to return only file items or <code>Folder</code> to return only folder items.</li> <li>fItemName: search for an item's name.</br>In the response this is the <code>name</code> field.</li> <li>fCreator: search by the ID of the item's creator.<br/>In the response this is the <code>createdBy.id</code> field.</li> <li>fCreatorFullName: search by the full display name of the item's creator. <br/>In the response this is the <code>createdBy.displayName</code> field.</li> <li>fCreatorLoginName: search by the login name of the item's creator. <br/>In the response this is the <code>createdBy.loginName</code> field.</li> <li>fOwner: search by the ID of the item's owner. <br/>In the response this is the <code>ownedBy.id</code> field.</li> <li>fOwnerFullName: search by the full display name of the item's owner.<br/>In the response this is the <code>ownedBy.displayName</code> field.</li> <li>fOwnerLoginName: search by the login name of the item's owner.<br/>In the response this is the <code>ownedBy.loginName</code> field.</li> <li>fLastModifier: search by the ID of the user to last modify the item. <br/>In the response this is the <code>modifiedBy.id</code> field.</li> <li>fLastModifierFullName: search by the full display name of the user to last modify the item.<br/>In the response this is the <code>modifiedBy.displayName</code> field.</li> <li>fLastModifierLoginName: search by the login name of the user to last modify the item. <br/>In the response this is the <code>modifiedBy.loginName</code> field.</li> <li>Searchable metadata fields can also be searched as string search targets, but metadata field values are not returned by the search. When you search by metadata fields, the target is <code>MetadataCollectionName.metadataFieldName</code>. For more information about metadata, see <a href='api-metadata-collection.html'>Metadata Collection REST Endpoints</a></li></ul> <p>The string search supports <code>&ltCONTAINS&gt</code> and <code>&ltMATCHES&gt</code> operations. <ul> <li><code>&ltCONTAINS&gt</code> operator instructs search to look for text fields being searched to contain specified \"words\". The words must be separated by tokens like whitespace\nand periods.</li><li><code>&ltMATCHES&gt</code> operator instructs search to look for an exact (albeit, case-insensitive) match of the field value, including whitespaces and periods.</li></ul></p></br>\n The following date fields are supported:\n <ul> <li>fCreateDate: search by the created timestamp. Should be in the <code>yyyy-mm-ddThh:mm:ssZ</code> format, e.g. <code>2020-03-25T10:10:10Z</code>. <br/>In the response this is the <code>createdTime</code> field.</li> <li>fLastModifiedDate: search by the last modified timestamp. Should be in the <code>yyyy-mm-ddThh:mm:ssZ</code> format, e.g. <code>2020-03-25T10:10:10Z</code>. <br/>In the response this is the <code>modifiedTime</code> field.</li> <li>Searchable metadata fields can also be searched as date search targets, but metadata field values are not returned by the search. When you search by metadata fields, the target is <code>MetadataCollectionName.metadataFieldName</code>. For more information about metadata, see <a href='api-metadata-collection.html'>Metadata Collection REST Endpoints</a></li></ul> <br/>\nThe following number field is supported:\n <ul><li>dSize: search by the size in bytes of an item.</li></ul> </p> <p> The number and date search targets support the following operations.\n <ul>\n <li> Use &lt to search for values less that the search value.</li>\n <li> Use = to search for values equal to the search value.</li>\n <li> Use &gt to search for values greater than the search value.</li>\n <li> Use &lt= to search for values less than or equal to the search value.</li>\n <li> Use &gt= to search for values greater than or equal to the search value.</li>\n</ul>", "orderby": ": <p>Order the resulting items using the specified field and sort order. You can use the <code>name</code>, <code>size</code>, <code>lastModifiedName</code>, and <code>lastModifiedDate</code> fields, and a sort order of ascending (<code>asc</code>) or descending (<code>desc</code>). For example, this is the default: <pre>orderby=name:asc</pre></p>", "limit": ": <p>Specify the maximum number of items to return. Use this parameter to specify how many items to include on a single page of results. The default is <code>20</code> and maximum is <code>10000</code>. The following example limits the number of returned items to 10: <pre>limit=10</pre></p>", "offset": ": <p>Specify the point at which to begin the list of items from the complete set of items returned for the action. If you do not specify an offset, the returned items begin with the first item in the item list (<code>offset=0</code>).</p> <p>Use this parameter to specify the starting point for a given page of results from the complete set of returned items. The following example limits the number of items per page to 10 and displays the second page of results (items 11-20):<pre>limit=10&offset=10</pre></p>", "fields": ": <p>Specify the additional fields to be returned. Currently, the only value supported is <code>metadata</code>, which would result in metadata associated with items returned by the search results being added to the response. The default is not to return metadata in the search results. In order to have metadata returned, use <pre>fields=metadata</pre></p>"}, "functionality": "Search for a folder or file by <code>fulltext</code> or <code>querytext</code> in the user&#39;s home directory. This will search the entire directory tree under the home directory. This will also search shared folders. The search has a limit of 10000 items (folders and files).", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Search Folders or Files Under Specific Folder ID", "api_call": "GET /documents/api/1.2/folders/{folderId}/search/items", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "fulltext": "[REQUIRED] : <p>Search string used to match folders or files. It will search these locations: <code>itemName</code>, <code>contents</code>, <code>extension</code>, <code>ownerName</code>, <code>lastModifiedName</code>, and <code>folderDescription</code>.</p><p>Either <code>fulltext</code> or <code>querytext</code> must be provided in the search API.</p>", "querytext": "[REQUIRED] : <p>Search string used to search for folders or files using the targets listed below. It can replace <code>fulltext</code>, taking advantage of multiple targets at the same time.</p><p>Either the <code>fulltext</code> or <code>querytext</code> parameter must be provided in the search API.</p> <p>The <code>querytext</code> parameter can target searches against the following string fields or number field. The search query is in the form:</p> <pre>searchField&ltoperation&gtsearchValue</pre> <p>Multiple searches can be combined with &ltAND&gt and &ltOR&GT. Searches are not case-sensitive.</p></br>The following string fields are supported:\n<ul> <li>xTags: search for tags</li> <li>fItemType: search by item type, either <code>File</code> to return only file items or <code>Folder</code> to return only folder items.</li> <li>fItemName: search for an item's name.</br>In the response this is the <code>name</code> field.</li> <li>fCreator: search by the ID of the item's creator.<br/>In the response this is the <code>createdBy.id</code> field.</li> <li>fCreatorFullName: search by the full display name of the item's creator. <br/>In the response this is the <code>createdBy.displayName</code> field.</li> <li>fCreatorLoginName: search by the login name of the item's creator. <br/>In the response this is the <code>createdBy.loginName</code> field.</li> <li>fOwner: search by the ID of the item's owner. <br/>In the response this is the <code>ownedBy.id</code> field.</li> <li>fOwnerFullName: search by the full display name of the item's owner.<br/>In the response this is the <code>ownedBy.displayName</code> field.</li> <li>fOwnerLoginName: search by the login name of the item's owner.<br/>In the response this is the <code>ownedBy.loginName</code> field.</li> <li>fLastModifier: search by the ID of the user to last modify the item. <br/>In the response this is the <code>modifiedBy.id</code> field.</li> <li>fLastModifierFullName: search by the full display name of the user to last modify the item.<br/>In the response this is the <code>modifiedBy.displayName</code> field.</li> <li>fLastModifierLoginName: search by the login name of the user to last modify the item. <br/>In the response this is the <code>modifiedBy.loginName</code> field.</li> <li>Searchable metadata fields can also be searched as string search targets, but metadata field values are not returned by the search. When you search by metadata fields, the target is <code>MetadataCollectionName.metadataFieldName</code>. For more information about metadata, see <a href='api-metadata-collection.html'>Metadata Collection REST Endpoints</a></li></ul> <p>The string search supports <code>&ltCONTAINS&gt</code> and <code>&ltMATCHES&gt</code> operations. <ul> <li><code>&ltCONTAINS&gt</code> operator instructs search to look for text fields being searched to contain specified \"words\". The words must be separated by tokens like whitespace\nand periods.</li><li><code>&ltMATCHES&gt</code> operator instructs search to look for an exact (albeit, case-insensitive) match of the field value, including whitespaces and periods.</li></ul></p></br>\n The following date fields are supported:\n <ul> <li>fCreateDate: search by the created timestamp. Should be in the <code>yyyy-mm-ddThh:mm:ssZ</code> format, e.g. <code>2020-03-25T10:10:10Z</code>. <br/>In the response this is the <code>createdTime</code> field.</li> <li>fLastModifiedDate: search by the last modified timestamp. Should be in the <code>yyyy-mm-ddThh:mm:ssZ</code> format, e.g. <code>2020-03-25T10:10:10Z</code>. <br/>In the response this is the <code>modifiedTime</code> field.</li> <li>Searchable metadata fields can also be searched as date search targets, but metadata field values are not returned by the search. When you search by metadata fields, the target is <code>MetadataCollectionName.metadataFieldName</code>. For more information about metadata, see <a href='api-metadata-collection.html'>Metadata Collection REST Endpoints</a></li></ul> <br/>\nThe following number field is supported:\n <ul><li>dSize: search by the size in bytes of an item.</li></ul> </p> <p> The number and date search targets support the following operations.\n <ul>\n <li> Use &lt to search for values less that the search value.</li>\n <li> Use = to search for values equal to the search value.</li>\n <li> Use &gt to search for values greater than the search value.</li>\n <li> Use &lt= to search for values less than or equal to the search value.</li>\n <li> Use &gt= to search for values greater than or equal to the search value.</li>\n</ul>", "orderby": ": <p>Order the resulting items using the specified field and sort order. You can use the <code>name</code>, <code>size</code>, <code>lastModifiedName</code>, and <code>lastModifiedDate</code> fields, and a sort order of ascending (<code>asc</code>) or descending (<code>desc</code>). For example, this is the default: <pre>orderby=name:asc</pre></p>", "limit": ": <p>Specify the maximum number of items to return. Use this parameter to specify how many items to include on a single page of results. The default is <code>20</code> and maximum is <code>10000</code>. The following example limits the number of returned items to 10: <pre>limit=10</pre></p>", "offset": ": <p>Specify the point at which to begin the list of items from the complete set of items returned for the action. If you do not specify an offset, the returned items begin with the first item in the item list (<code>offset=0</code>).</p> <p>Use this parameter to specify the starting point for a given page of results from the complete set of returned items. The following example limits the number of items per page to 10 and displays the second page of results (items 11-20):<pre>limit=10&offset=10</pre></p>", "fields": ": <p>Specify the additional fields to be returned. Currently, the only value supported is <code>metadata</code>, which would result in metadata associated with items returned by the search results being added to the response. The default is not to return metadata in the search results. In order to have metadata returned, use <pre>fields=metadata</pre></p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "Search for a folder or file by <code>fulltext</code> or <code>querytext</code> in the specific folder ID or the <code>self</code> folder directory. This will search the entire directory tree under the specific folder ID or the <code>self</code> folder directory. This will also search shared folders. The search has a limit of 10000 items (folders and files).", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Get Information on Multiple Folders", "api_call": "GET /documents/api/1.2/folders/lists", "api_version": "2023.11.27", "api_arguments": {"idList": "[REQUIRED] : <p>A comma-separated list of globally unique identifiers (GUIDs) for folders. To reference the user&#39;s home folder,  the value for <code>idList</code> can include <code>self</code>. There is a limit of 100 folder IDs</p>", "checkStatus": ": <p>Parameter used to check the current status of any identifier listed in <code>idList</code>. Any missing identifier or any identifier in trash listed in <code>idList</code> will cause this API to return error. If <code>checkStatus=1</code>, API will report if items are missing (<code>missing</code>) or if items are in trash (<code>inTrash</code>) or if items exist (<code>exists</code>) instead of returning error. If <code>checkStatus=0</code> or not specified at all, the current API behavior remains the same.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Get folder information for a list of folders.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Get Folder Tags", "api_call": "GET /documents/api/1.2/folders/{folderId}/tags", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "directAssigned": ": <p>Specify if just directly assigned tags should be retrieved (<code>1</code>) or if all inherited tags should be included as well (<code>0</code>).</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "Get tags assigned to a particular folder.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Set Folder Tags", "api_call": "POST /documents/api/1.2/folders/{folderId}/tags", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "payload": "[REQUIRED] : The request body defines details of the set tags values request. There are no required attributes.", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Set all tags values to a particular folder. This API will set tags and it will also replace any previously set values. In case replacing tag values is not the desired result, see the Edit Folder Tags API.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Edit Folder Tags", "api_call": "PUT /documents/api/1.2/folders/{folderId}/tags", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "payload": "[REQUIRED] : The request body defines details of the edit tags values request. There are no required attributes.", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Edit tag values assigned to a particular folder. This API adds new tags without replacing existing ones or removes specific tags, or both.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Delete All Folder Tags", "api_call": "DELETE /documents/api/1.2/folders/{folderId}/tags", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Delete all tags directly assigned to this folder. It is important to observe that inherited tags will not be deleted and, therefore, they will still be retrieved by the Get Folder Tags API.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Assign a Metadata Collection to a Folder", "api_call": "POST /documents/api/1.2/folders/{folderId}/metadata/{collectionName}", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "collectionName": "[REQUIRED] : Name of the metadata collection to assign."}, "functionality": "<p>Associate an existing metadata collection with a specified folder.</p><p><b>Note: To assign a global collection to a folder, you must be the owner or have the manager role for the folder. If it is a personal collection, you can assign only if you are the folder&#39;s owner.</b></p><p>You must assign a collection to a particular folder before you can assign values to the fields for the particular folder. You can assign a collection to one or more folders, and any folder can have one or more collections assigned to it.</p><p><b>Metadata Inheritance: </p><p>Metadata fields assigned to a folder are available to all folders and files in the hierarchy beneath that folder. Similarly, the values you assign to those fields are inherited by all folders and files nested beneath that folder unless explicitly defined for a nested folder or file. Metadata values specified for a folder or file replace the inherited value for that folder and, by the same rules of inheritance, any folders or files in the hierarchy nested beneath that folder.</b></p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Assign Values to a Folder Metadata Collection", "api_call": "POST /documents/api/1.2/folders/{folderId}/metadata", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "payload": "[REQUIRED] : The request body defines details of the assign values to metadata collection request. There are no required attributes.", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Assign values to the fields in a specified folder metadata collection. You must assign a collection to a particular folder before you can assign values to the fields for the particular folder.</p><p><b>Note: To set a metadata value on a folder, you must have the contributor or manager role for that folder.</b></p><p></p><p><b>Metadata Inheritance: </p><p>Metadata fields assigned to a folder are available to all folders and files in the hierarchy beneath that folder. Similarly, the values you assign to those fields are inherited by all folders and files nested beneath that folder unless explicitly defined for a nested folder or file. Metadata values specified for a folder or file replace the inherited value for that folder and, by the same rules of inheritance, any folders or files in the hierarchy nested beneath that folder.</b></p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Get Folder Metadata Collection", "api_call": "GET /documents/api/1.2/folders/{folderId}/metadata", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "directAssigned": ": <p>Specify if just directly assigned collection values should be retrieved (<code>1</code>) or if values from all inherited collections should be included as well (<code>0</code>).</p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "Get values assigned to fields in a specified metadata collection for a particular folder.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Delete Values in a Folder Metadata Collection", "api_call": "DELETE /documents/api/1.2/folders/{folderId}/metadata", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "collection": "[REQUIRED] : Collection name. For a user personal collection, it must start with <code>Personal.</code>, such as <code>Personal.MyCollection</code>.", "fields": "[REQUIRED] : One or more field names, separated by commas, whose values you want to delete.", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Reset (delete) values in specified fields in a metadata collection.</p><p><b>Note: To remove a metadata value from a folder, you must be the owner or have the manager role for the folder.</b></p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Get Folder Assigned Metadata Collections", "api_call": "GET /documents/api/1.2/folders/{folderId}/metadataFields", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "directAssigned": ": <p>Specify if just a directly assigned collection should be retrieved (<code>1</code>) or if all inherited collections should be included as well (<code>0</code>).</p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "Get field definitions of all fields in a specified metadata collection assigned to a particular folder.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Folders: Create Folder Conversation", "api_call": "POST /documents/api/1.2/folders/{folderId}/conversation", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder.</p>", "payload": "[REQUIRED] : The request body defines details of the create folder conversation request.", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access this folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access this folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Create a new conversation and associate it with the specified folder. It will also generate a conversation applink for the creator.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Applinks: Create Folder Applink", "api_call": "POST /documents/api/1.2/applinks/folder/{folderId}", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder.</p>", "payload": "[REQUIRED] : <p>The request body defines the details of the create applink request. <b>Bold</b> indicates a required value.</p>"}, "functionality": "<p>Create an applink for a specified folder and a specified user.<p><p>You can grant the specified user the following roles:</p><ul><li><strong>Viewer:</strong> Viewers can look at files and folders, but can&#39;t change things.</li><li><strong>Downloader:</strong> Downloaders can also download files and save them to their own computers.</li><li><strong>Contributor:</strong> Contributors can also modify files, update files, upload new files, and delete files.</li><li><strong>Manager:</strong> Managers can also modify files, update files, upload new files, delete, and share files and folders.</li></ul><p><b>Note:</p><p>To create an applink, the requester must have admin privileges for the folder. That is, the requester must be the owner or have the manager role.</b></p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Applinks: Create File Applink", "api_call": "POST /documents/api/1.2/applinks/file/{fileId}", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder.</p>", "payload": "[REQUIRED] : <p>The request body defines the details of the create applink request. <b>Bold</b> indicates a required value.</p>"}, "functionality": "<p>Create an applink for a specified file and a specified user.<p><p>You can grant the specified user the following roles:</p><ul><li><strong>Viewer:</strong> Viewers can look at files and folders, but can&#39;t change things.</li><li><strong>Downloader:</strong> Downloaders can also download files and save them to their own computers.</li><li><strong>Contributor:</strong> Contributors can also modify files, update files, upload new files, and delete files.</li><li><strong>Manager:</strong> Managers can also modify files, update files, upload new files, delete, and share files.</li></ul><p><b>Note:</p><p>To create an applink, the requester must have admin privileges for the file. That is, the requester must be the owner or have the manager role.</b></p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Applinks: Refresh Applink Token", "api_call": "PUT /documents/api/1.2/applinks/token", "api_version": "2023.11.27", "api_arguments": {"appLinkID": "[REQUIRED] : <p>This element uniquely identifies the resource.</p>", "accessToken": "[REQUIRED] : <p>This element provides access to the resource and expires after 15 minutes.</p>", "refreshToken": "[REQUIRED] : <p>This element enables you to request a new access token when the current access token expires. The refreshed token expires after 24 hours.</p>"}, "functionality": "<p>Refresh an expired access token.<p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Extract ZIP File into a Folder", "api_call": "POST /documents/api/1.2/files/{fileId}/_extract", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) of the ZIP file to be extracted into a folder", "payload": ": The request body defines details of how the files will be extracted from the ZIP file."}, "functionality": "Requests that a ZIP file be extracted to a folder.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get Status of an Extraction Job", "api_call": "GET /documents/api/1.2/files/{fileId}/_extract/{jobId}", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) of the ZIP file to be extracted into a folder", "jobId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the extraction job.</p>", "includeIgnoredItemDetails": ": <p>An optional boolean value that defaults to <code>true</code>. If <code>false</code> the <code>ignoredItems</code> array is not included in the response</p>", "includeSkippedItemDetails": ": <p>An optional boolean value that defaults to <code>true</code>. If <code>false</code> the <code>skippedItems</code> array is not included in the response</p>"}, "functionality": "<p>Returns the status of an extraction job</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Abort an Extraction Job", "api_call": "DELETE /documents/api/1.2/files/{fileId}/_extract/{jobId}", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) of the ZIP file to be extracted into a folder.</p>", "jobId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the extraction job.</p>"}, "functionality": "<p>Abort an extraction job.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get Information on Multiple Files", "api_call": "GET /documents/api/1.2/files/lists", "api_version": "2023.11.27", "api_arguments": {"idList": "[REQUIRED] : <p>A comma-separated list of globally unique identifiers (GUIDs) for files. There is a limit of 100 file IDs</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this folder. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Get file information for a list of files.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get File Information", "api_call": "GET /documents/api/1.2/files/{fileId}", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "version": ": <p>Specify the version number of the file to query. If the version is not specified, the latest version is used.</p>", "includeRenditions": ": <p>When true, detailed information about existing renditions associated with the file is returned. The default is false.</p>", "includeOnlyExistingRenditions": ": <p>This parameter is ignored unless <code>includeRenditions</code> is true. By default, only information about existing renditions is returned. When false, detailed information about all supported renditions with the file is returned. The <code>exists</code> field will indicate if a rendition has been created. The default is true.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Get file information for the specified file.<p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Edit File", "api_call": "PUT /documents/api/1.2/files/{fileId}", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "payload": ": The request body defines details of the edit file request. There are no required attributes.", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Change the name of the specified file.<p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Delete File", "api_call": "DELETE /documents/api/1.2/files/{fileId}", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "version": ": <p>Specify the version number of the file to delete. If the version is not specified, all versions of the file are deleted (moved to trash). If the version is specified, that version of the file is deleted (moved to trash).</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Delete (move to trash) the specified file.<p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Upload File", "api_call": "POST /documents/api/1.2/files/data", "api_version": "2023.11.27", "api_arguments": {"jsonInputParameters": "[REQUIRED] : <p>You can use this parameter to include the <code>parentID</code> parameter and the <code>duplicateResolution</code> parameter as a JSON payload with the request. This parameter must come <b>before</b> the <code>primaryFile</code> parameter. <p>Set <code>parentID</code> to the globally unique identifier (GUID) of the folder to upload the file to. <b><p>The <code>parentID</code> parameter must be sent as a part of this JSON payload. This parameter is required.</p></b> For example:</p><code>&#123;<p>&#34;parentID&#34;:&#32;&#32;&#32;&#32;&#34;FB4CD874EF94CD2CC1B60B72T&#34;</p>&#125;</code></p><p>Also, to resolve any conflict with duplicate file names, you can set <code>duplicateResolution</code> to <code>TimeStampSuffix</code>. <p><b>The <code>duplicateResolution</code> parameter can be sent as a part of this JSON payload. This parameter is optional.</b></p> For example:</p> <code>&#123;<p> &#34;parentID&#34;:&#32;&#32;&#32;&#32;&#34;FB4CD874EF94CD2CC1B60B72T&#34;,</p><p> &#34;duplicateResolution&#34;:&#32;&#32;&#32;&#32;&#34;TimeStampSuffix&#34;</p>&#125;</code></p>", "primaryFile": "[REQUIRED] : File to upload. This must come <b>after</b> the <code>jsonInputParameters</code> parameter.", "metadataValues": ": <p>You can use this parameter to set metadata values to a collection already assigned to any parent folder. The rules are the same as those applied to the set metadata values REST API. Use <code>Content-Type: application/json</code> to describe this information as a JSON object.</p><p>For example:</p><code>&#123;<p> &#34;collection&#34;:&#32;&#32;&#32;&#32;&#34;CollectionA&#34;,</p><p> &#34;fieldA&#34;:&#32;&#32;&#32;&#32;&#34;valueA&#34;,</p><p> &#34;fieldB&#34;:&#32;&#32;&#32;&#32;&#34;valueB&#34;,</p><p> &#34;fieldC&#34;:&#32;&#32;&#32;&#32;&#34;valueC&#34;</p>&#125;</code>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Upload a new file using a multipart request to specify destination and file information.</p><p><b>Note:</p><p>File names are not case-sensitive; that is, <code>File_Name</code> and <code>file_name</code> are considered identical. Two files cannot have the same name in a given folder. </p><p>By default, if a file with the same name exists in the target destination, the uploaded file replaces the existing file as a new revision.</b></p><p>The request is a multipart HTTP request where one part is a JSON object (<code>jsonInputParameters</code>) with the <code>parentID</code> and the other part is the content of the file itself (<code>primaryFile</code>). It is also possible to set metadata values using another JSON object (<code>metadataValues</code>).</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Upload File Version", "api_call": "POST /documents/api/1.2/files/{fileId}/data", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "primaryFile": "[REQUIRED] : File to upload.", "metadataValues": ": <p>You can use this parameter to set metadata values to a collection already assigned to any parent folder. The rules are the same as those applied to the set metadata values REST API. Use <code>Content-Type: application/json</code> to describe this information as a JSON object.</p><p>For example:</p><code>&#123;<p> &#34;collection&#34;:&#32;&#32;&#32;&#32;&#34;CollectionA&#34;,</p><p> &#34;fieldA&#34;:&#32;&#32;&#32;&#32;&#34;valueA&#34;,</p><p> &#34;fieldB&#34;:&#32;&#32;&#32;&#32;&#34;valueB&#34;,</p><p> &#34;fieldC&#34;:&#32;&#32;&#32;&#32;&#34;valueC&#34;</p>&#125;</code>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Upload a new revision to a file using a multipart request to specify information for the target file and input file.</p><p><b>Note:</p><p>Two files cannot have the same name in a given folder. File names are not case-sensitive; that is, <code>File_Name</code> and <code>file_name</code> are considered identical. If a file with the same name exists in the target destination, the name of the new file is given a numeric increment. For example, if the file <code>my_file</code> already exists, the new file is named <code>my_file(2)</code>.</b></p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Download File", "api_call": "GET /documents/api/1.2/files/{fileId}/data", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "version": ": <p>Specify the version number of the file to download. If the version is not specified, the latest version is used.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. To work, this public link must have at least the downloader role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the downloader role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>", "Range": ": <p>The standard HTTP <code>Range</code>  header allows clients to stream parts of the file rather than the entire file. The value of the <code>Range</code> header can be one of the following formats: <ul><li>bytes=<i>startByte</i> - <i>endByte</i></br> For example to receive the first 500 bytes use <code>bytes=0-500</code><li>bytes=<i>skipBytes</i>-</br>For example to skip the first 500 bytes use <code>bytes=500-</code></li><li>bytes=-<i>lastBytes</i></br>For example to receive the last 500 bytes use <code>bytes=-500</code</li></ul> </p>"}, "functionality": "<p>Get file content for the specified file as a stream.<p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Copy File", "api_call": "POST /documents/api/1.2/files/{fileId}/copy", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "payload": "[REQUIRED] : The request body defines the details of the copy file request. <b>Bold</b> indicates a required value.", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Copy a specified file to a specified destination folder.<p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Move File", "api_call": "POST /documents/api/1.2/files/{fileId}/move", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "payload": "[REQUIRED] : <p>The request body defines details of the move file request. <b>Bold</b> indicates a required value.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Move a specified file and its versions to a specified destination folder. The moved file retains its globally unique identifier and all date and ownership information.<p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get File Versions", "api_call": "GET /documents/api/1.2/files/{fileId}/versions", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Get version information for the specified file.<p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Reserve File", "api_call": "POST /documents/api/1.2/files/{fileId}/reserve", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>"}, "functionality": "<p>Reserve the specified file. A file reservation lets other users (or processes) know that the file is in use.<p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Unreserve File", "api_call": "POST /documents/api/1.2/files/{fileId}/unreserve", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "payload": "[REQUIRED] : <p>The request body defines details of the unreserve file request. <b>Bold</b> indicates a required value.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>"}, "functionality": "<p>Remove the reservation from the specified file. A file reservation lets other users (or processes) know that the file is in use.<p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get Responsive Thumbnail Image", "api_call": "GET /documents/api/1.2/files/{fileId}/data/thumbnailImage", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file. The GUID must be for a jpeg, png, gif or tiff image.</p>", "format": ": <p>Specify the image type to return. The following types are supported:<ul><li><code>jpeg</code> or <code>jpg</code> returns a jpeg image. This is the default value.</li><li><code>webp</code> returns a WebP image.</li></ul></p>", "version": ": <p>Specify the version number of the file to use. If the version is not specified, the latest version is used.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Get a thumbnail-sized rendition of the file. The image returned is not wider than 150 pixels. If the rendition has not been created, this call will generate the rendition.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get Responsive Small Image", "api_call": "GET /documents/api/1.2/files/{fileId}/data/smallImage", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file. The GUID must be for a jpeg, png, gif or tiff image.</p>", "format": ": <p>Specify the image type to return. The following types are supported:<ul><li><code>jpeg</code> or <code>jpg</code> returns a jpeg image. This is the default value.</li><li><code>webp</code> returns a WebP image.</li></ul></p>", "version": ": <p>Specify the version number of the file to use. If the version is not specified, the latest version is used.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Get a small-sized rendition of the file. The image returned is not wider than 300 pixels. If the rendition has not been created, this call will generate the rendition.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get Responsive Medium Image", "api_call": "GET /documents/api/1.2/files/{fileId}/data/mediumImage", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file. The GUID must be a for jpeg, png, gif or tiff image.</p>", "format": ": <p>Specify the image type to return. The following types are supported:<ul><li><code>jpeg</code> or <code>jpg</code> returns a jpeg image. This is the default value.</li><li><code>webp</code> returns a WebP image.</li></ul></p>", "version": ": <p>Specify the version number of the file to use. If the version is not specified, the latest version is used.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Get a medium-sized rendition of the file. The image returned is not wider than 1024 pixels. If the rendition has not been created, this call will generate the rendition.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get Responsive Large Image", "api_call": "GET /documents/api/1.2/files/{fileId}/data/largeImage", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file. The GUID must be for a jpeg, png, gif or tiff image.</p>", "format": ": <p>Specify the image type to return. The following types are supported:<ul><li><code>jpeg</code> or <code>jpg</code> returns a jpeg image. This is the default value.</li><li><code>webp</code> returns a WebP image.</li></ul></p>", "version": ": <p>Specify the version number of the file to use. If the version is not specified, the latest version is used.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Get a large-sized rendition of the file. The image returned is not wider than 2048 pixels. If the rendition has not been created, this call will generate the rendition.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get Thumbnail", "api_call": "GET /documents/api/1.2/files/{fileId}/data/thumbnail", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "version": ": <p>Specify the version number of the file to use. If the version is not specified, the latest version is used.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>", "Range": ": <p>The standard HTTP <code>Range</code>  header allows clients to stream parts of the file rather than the entire file. The value of the <code>Range</code> header can be one of the following formats: <ul><li>bytes=<i>startByte</i> - <i>endByte</i></br> For example to receive the first 500 bytes use <code>bytes=0-500</code><li>bytes=<i>skipBytes</i>-</br>For example to skip the first 500 bytes use <code>bytes=500-</code></li><li>bytes=-<i>lastBytes</i></br>For example to receive the last 500 bytes use <code>bytes=-500</code</li></ul> </p>"}, "functionality": "<p>Get a thumbnail image for the front page of the document. If document renditions have not been generated, this call generates the thumbnail image for the front page of the document.<p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get Rendition", "api_call": "GET /documents/api/1.2/files/{fileId}/data/rendition", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "rendition": "[REQUIRED] : <p>Specify the thumbnail number or page number in the following format.</p><p><ul><li>page&#60;&#35;&#62; <pre>For example: rendition=page3</pre></li><li>thumbnail&#60;&#35;&#62; <pre>For example: rendition=thumbnail3</pre></li><li>uploaded rendition name <pre>For example: rendition=small</pre></li></ul></p>", "version": ": <p>Specify the version number of the file to use. If the version is not specified, the latest version is used. It is important to notice that system renditions and custom renditions can exist for a specific file version only.</p>", "renditionType": ": <p>Specify the rendition type to retrieve. This parameter needs to be used together with a <code>rendition</code> parameter: thumbnails or page renditions require <code>renditionType=system</code>; custom uploaded renditions require <code>renditionType=uploaded</code>. If <code>renditionType</code> is not specified, the <code>system</code> rendition type will be assumed.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>", "Range": ": <p>The standard HTTP <code>Range</code>  header allows clients to stream parts of the file rather than the entire file. The value of the <code>Range</code> header can be one of the following formats: <ul><li>bytes=<i>startByte</i> - <i>endByte</i></br> For example to receive the first 500 bytes use <code>bytes=0-500</code><li>bytes=<i>skipBytes</i>-</br>For example to skip the first 500 bytes use <code>bytes=500-</code></li><li>bytes=-<i>lastBytes</i></br>For example to receive the last 500 bytes use <code>bytes=-500</code</li></ul> </p>"}, "functionality": "<p>Get the thumbnail or page rendition for a specified file and page. You can also get any custom uploaded rendition for a specified file. A rendition is a viewable representation of the file contents.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Upload Custom Rendition", "api_call": "POST /documents/api/1.2/files/{fileId}/data/rendition", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "jsonInputParameters": "[REQUIRED] : <p>You can use this parameter to include the <code>name</code> parameter and the <code>description</code> parameter as a JSON payload with the request. <p>Set <code>name</code> as a unique identifier for this custom rendition within the file&#39;s latest revision scope. <b><p>The <code>name</code> parameter must be sent as a part of this JSON payload. This parameter is required.</p></b> For example: </p><code>&#123;<p> &#34;name&#34;:&#32;&#32;&#32;&#32;&#34;smallCustomRendition&#34;</p>&#125;</code></p><p>The following restrictions apply to <code>name</code>:<ul><li>The length of <code>name</code> can&#39;t exceed 28 characters.</li><li>The <code>name</code> parameter isn&#39;t case-sensitive; that is, <code>smallRendition</code> and <code>smallrendition</code> are considered identical.</li><li>The <code>name</code> parameter is limited to English letters, numbers, the hyphen character, and the underscore character only. Use of other characters will cause failure.</li></ul></p> <p>       The <code>description</code> parameter can be sent as a part of this JSON payload. This parameter is optional.</b></p><p>For example:</p> <code>&#123;<p> &#34;name&#34;:&#32;&#32;&#32;&#32;&#34;smallCustomRendition&#34;,</p><p> &#34;description&#34;:&#32;&#32;&#32;&#32;&#34;This is a small rendition version for this file&#34;</p>&#125;</code>", "file": "[REQUIRED] : Custom rendition file to upload.", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Upload a custom rendition file to the latest file version, using a multipart request. The given name will be used to uniquely identify this rendition within a file version.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Delete Custom Rendition", "api_call": "DELETE /documents/api/1.2/files/{fileId}/data/rendition", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "rendition": "[REQUIRED] : <p>Rendition <code>name</code> given to a custom rendition in upload.</p>", "version": ": <p>Specify the version number of the file to use. If the version is not specified, the latest version is used. It is important to notice that custom renditions can exist for a specific file version only.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Delete the specified custom rendition.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: List Renditions", "api_call": "GET /documents/api/1.2/files/{fileId}/data/renditions", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "renditionType": ": <p>Specify the rendition type to retrieve. Use <code>renditionType=system</code> for thumbnail and page renditions; use <code>renditionType=uploaded</code> for custom uploaded renditions.</p>", "version": ": <p>Specify the version number of the file to use. If the version is not specified, the latest version is used. It is important to notice that system renditions and custom renditions can exist for a specific file version only.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>List system-generated renditions or user-uploaded renditions for a file version.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get Rendition Page Count", "api_call": "GET /documents/api/1.2/files/{fileId}/pages", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "version": ": <p>Specify the version number of the file to use. If the version is not specified, the latest version is used.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Get the number of renditions (pages) created for the specified file that has an image based preview (rendition) created image. An image based preview (or rendition) is a viewable representation of the file contents where each page of the file is converted to an image.<p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Generate File Renditions", "api_call": "POST /documents/api/1.2/files/{fileId}/pages", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "version": ": <p>Specify the version number of the file to use. If the version is not specified, the latest version is used.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Generate an image based preview (page renditions) for a specified file that does not have an image based preview (page renditions). An image based preview (or rendition) is a viewable representation of the file contents where each page of the file is converted to an image. Not all file types support image based preview (page renditions).<p>The types of files that can have renditions include (similar formats may also be supported):<p><ul><li>Most images (bmp,jpg,tif,gif,png)</li><li>Office files (doc, docx, dot, dotx, xls, xlsx, xls, xlst, pot, potx, ppt, pptx)</li><li>Text files (txt, scripts, source code)</li><li>AutoCAD drawings</li><li>Adobe PDF files</li><li>Internet files (html, email)</li></ul><p>Note that previews (page renditions) cannot be generated for video formats.<p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get File HTML5 Preview", "api_call": "GET /documents/api/1.2/files/{fileId}/previewPath", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "version": ": <p>Specify the version number of the file to use. If the version is not specified, the latest version is used.</p>", "waitForCompletePreview": ": <p>When true, this call will not return until the HTML5 preview has been created. Using this parameter will delay the return of this call but will ensure the preview is fully available to the system. The default is false.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Generate an HTML5 preview of the given file and return a path that can be used to access it.<p> <br> The types of files that can be previewed include (similar formats may also be supported):<p><ul><li>Most images (bmp,jpg,tif,gif,png)</li><li>Office files (doc, docx, dot, dotx, xls, xlsx, xls, xlst, pot, potx, ppt, pptx)</li><li>Text files (txt, scripts, source code)</li><li>AutoCAD drawings</li><li>Adobe PDF files</li><li>Internet files (html, email)</li></ul><p>Note that previews cannot be generated for video formats.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get File Tags", "api_call": "GET /documents/api/1.2/files/{fileId}/tags", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "directAssigned": ": <p>Specify if just directly assigned tags should be retrieved (<code>1</code>) or if all inherited tags should be included as well (<code>0</code>).</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "Get tags assigned to a particular file.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Set File Tags", "api_call": "POST /documents/api/1.2/files/{fileId}/tags", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "payload": "[REQUIRED] : The request body defines details of the set tag values request. There are no required attributes.", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Set all tag values to a particular file. This API will set tags and it will also replace any previously set values. In case replacing tags values is not the desired result, see the Edit File Tags API.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Edit File Tags", "api_call": "PUT /documents/api/1.2/files/{fileId}/tags", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "payload": "[REQUIRED] : The request body defines details of the edit tags values request. There are no required attributes.", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Edit tag values assigned to a particular file. This API adds new tags without replacing existing ones or removes specific tags or both.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Delete All File Tags", "api_call": "DELETE /documents/api/1.2/files/{fileId}/tags", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Delete all tags directly assigned to this file. It is important to observe that inherited tags will not be deleted and, therefore, they will still be retrieved by the Get File Tags API.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Assign a Metadata Collection to a File", "api_call": "POST /documents/api/1.2/files/{fileId}/metadata/{collectionName}", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "collectionName": "[REQUIRED] : Name of the metadata collection to assign. For a user personal collection, it must start with <code>Personal.</code>, such as <code>Personal.MyCollection</code>."}, "functionality": "<p>Associate an existing metadata collection with a specified file.</p><p><b>Note: To assign a global collection to a file, you must be the owner or have the manager role for the file. If it is a personal collection, you can assign only if you are the file&#39;s owner.</b></p><p>You must assign a collection to a particular file before you can assign values to the fields for the particular file. You can assign a collection to one or more files, and any file can have one or more collections assigned to it.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Assign Values to a File Metadata Collection", "api_call": "POST /documents/api/1.2/files/{fileId}/metadata", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "payload": "[REQUIRED] : The request body defines details of the assign values to metadata collection request. There are no required attributes.", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Assign values to the fields in a specified file metadata collection. You must assign a collection to a particular file before you can assign values to the fields for the particular file.</p><p><b>Note: To set a metadata value on a file, you must have the contributor or manager role for that file.</b></p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get File Metadata Collection", "api_call": "GET /documents/api/1.2/files/{fileId}/metadata", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "directAssigned": ": <p>Specify if just directly assigned collection values should be retrieved (<code>1</code>) or if values from all inherited collections should be included as well (<code>0</code>).</p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "Get values assigned to fields in a specified metadata collection for a particular file.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Delete Values in File Metadata Collection", "api_call": "DELETE /documents/api/1.2/files/{fileId}/metadata", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "collection": "[REQUIRED] : Collection name. For a user personal collection, it must start with <code>Personal.</code>, such as <code>Personal.MyCollection</code>.", "fields": "[REQUIRED] : One or more field names, separated by commas, whose values you want to delete.", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Reset (delete) values in specified fields in a metadata collection.</p><p><b>Note: To remove a metadata value from a file, you must be the owner or have the manager role for the file.</b></p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get File Assigned Metadata Collections", "api_call": "GET /documents/api/1.2/files/{fileId}/metadataFields", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "directAssigned": ": <p>Specify if just directly assigned collections should be retrieved (<code>1</code>) or if all inherited collections should be included as well (<code>0</code>).</p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "Get field definitions of all fields in a specified metadata collection assigned to a particular file or its own parent folder hierarchy.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Create File Conversation", "api_call": "POST /documents/api/1.2/files/{fileId}/conversation", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "payload": "[REQUIRED] : The request body defines details of the create file conversation request. <b>Bold</b> indicates a required value.", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. To work, this public link must have the contributor role granted. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Create a new conversation and associate it with the specified file. This request will also generate a conversation applink for the creator.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Files: Get File Accesses", "api_call": "GET /documents/api/1.2/files/{fileId}/accesses", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the file.</p>", "limit": ": <p> Specify the maximum number of accesses to return. Use this parameter to specify how many items to include on a single page of results. The default is 200.</p>", "offset": ": <p>Specify the point at which to begin the list of items from the complete set of items returned for the action. If you do not specify an offset, the returned items begin with the first item in the item list (offset=0). </p>", "orderBy": ": <p>The results can be sorted by the fields below. The sort order can be defined as ascending (asc) or descending (desc). The default sort order is ascending. <ul> <li><code>accessedBy</code> sorts by the user who initiated the activity.</li> <li><code>version</code> sorts by version of the item on which activity occurred.</li> <li><code>accessType</code> sorts by the type of the access (for example: preview, download).</li> <li><code>size</code> sorts by the size of the item accesses.</li> <li><code>accessedTime</code> sorts by the time of the access.</li> <li><code>clientIP</code> sorts by IP address of the client on which this activity was initiated.</li> <li><code>errorCode</code> sorts by the status code of the activity.</li> </ul> </p>", "linkID": ": <p>Public link ID of a public link authorizing the current user to access this file. It can be used as linkID or LinkID.</p>", "dAccessCode": ": <p>Access code needed to use protected public links. It needs to be sent as part of a Cookie header in the following format: <code>dAccessCode-&#60;linkID&#62;=&#60;passcodeValue&#62;</code></p>", "appLinkID": ": <p>Applink ID authorizing the current user to access the parent folder or this file. Any time the parameter <code>appLinkID</code> is used, a parameter <code>accessToken</code> must be provided as well. To work, this applink must have at least the contributor role granted. It can be used as appLinkID or AppLinkID.</p>", "accessToken": ": <p>Applink access token authorizing the current user to access the parent folder or this file. This parameter is mandatory if <code>appLinkID</code> is used. It can be used as accessToken or AccessToken.</p>"}, "functionality": "<p>Get information on which users have accessed the specified file.<p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Catalog: Get All Versions", "api_call": "GET /documents/api/", "api_version": "2023.11.27", "api_arguments": {}, "functionality": "Get information for all versions of the API. The version resource provides information about all available versions or about a specific version of the API itself.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Catalog: Get Specific Version", "api_call": "GET /documents/api/{version}", "api_version": "2023.11.27", "api_arguments": {"version": "[REQUIRED] : Version value"}, "functionality": "Get information for the specified version of the API. The version resource provides information about all available versions or about a specific version of the API itself.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Catalog: Get API Catalog", "api_call": "GET /documents/api/{version}/metadata-catalog", "api_version": "2023.11.27", "api_arguments": {"version": "[REQUIRED] : Version value"}, "functionality": "<p>Get available resources in the specified versions of the API.</p><p><b>Note:</p><p>In version 1.1 of the Content Management REST API, the Catalog resource was renamed Metadata-Catalog. Catalog will continue to be supported as an alias for Metadata-Catalog.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Catalog: Get Folder API Catalog", "api_call": "GET /documents/api/{version}/metadata-catalog/folders", "api_version": "2023.11.27", "api_arguments": {"version": "[REQUIRED] : Version value"}, "functionality": "Return metadata information for the folders resource, including supported services.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Catalog: Get File API Catalog", "api_call": "GET /documents/api/{version}/metadata-catalog/files", "api_version": "2023.11.27", "api_arguments": {"version": "[REQUIRED] : Version value"}, "functionality": "Return metadata information for the files resource, including supported services.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Catalog: Get User API Catalog", "api_call": "GET /documents/api/{version}/metadata-catalog/users", "api_version": "2023.11.27", "api_arguments": {"version": "[REQUIRED] : Version value"}, "functionality": "Return metadata information for the users resource, including supported services.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Catalog: Get Applink API Catalog", "api_call": "GET /documents/api/{version}/metadata-catalog/applinks", "api_version": "2023.11.27", "api_arguments": {"version": "[REQUIRED] : Version value"}, "functionality": "Return metadata information for the applinks resource, including supported services.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Catalog: Get Share API Catalog", "api_call": "GET /documents/api/{version}/metadata-catalog/shares", "api_version": "2023.11.27", "api_arguments": {"version": "[REQUIRED] : Version value"}, "functionality": "Return metadata information for the shares resource, including supported services.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Catalog: Get Public Links API Catalog", "api_call": "GET /documents/api/{version}/metadata-catalog/publiclinks", "api_version": "2023.11.27", "api_arguments": {"version": "[REQUIRED] : Version value"}, "functionality": "Return metadata information for the publiclinks resource, including supported services.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Catalog: Get Metadata API Catalog", "api_call": "GET /documents/api/{version}/metadata-catalog/metadata", "api_version": "2023.11.27", "api_arguments": {"version": "[REQUIRED] : Version value"}, "functionality": "Return API catalog information for the metadata resource, including supported services.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Catalog: Get Configuration API Catalog", "api_call": "GET /documents/api/{version}/metadata-catalog/configuration", "api_version": "2023.11.27", "api_arguments": {"version": "[REQUIRED] : Version value"}, "functionality": "Return API catalog information for the configuration resource, including supported services.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Catalog: Get Sites API Catalog", "api_call": "GET /documents/api/{version}/metadata-catalog/sites", "api_version": "2023.11.27", "api_arguments": {"version": "[REQUIRED] : Version value"}, "functionality": "Return API catalog information for the sites resource, including supported services.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Catalog: Get Templates API Catalog", "api_call": "GET /documents/api/{version}/metadata-catalog/templates", "api_version": "2023.11.27", "api_arguments": {"version": "[REQUIRED] : Version value"}, "functionality": "Return API catalog information for the templates resource, including supported services.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Users: Get Users", "api_call": "GET /documents/api/1.2/users/items", "api_version": "2023.11.27", "api_arguments": {"info": "[REQUIRED] : <p>(Required) Specify a search string used to return matching users. Note that wildcard characters are not supported and an empty parameter value is treated as a missing parameter.</p>"}, "functionality": "<p>Get information about one or more users for folder and file sharing purposes.</p><p>The service uses search text provided with the call to match possible users. The search uses a &#34;fuzzy&#34; search of user names and email addresses to return matching users with the most likely matches listed first.</b><p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Users: Get User with Email Address", "api_call": "GET /documents/api/1.2/users/search/items", "api_version": "2023.11.27", "api_arguments": {"email": "[REQUIRED] : <p>(Required) Specify an email address as a search string used to return a specific matching user. Wildcard characters are not supported.</p>"}, "functionality": "<p>The service uses an email address as search text provided with the call to match a specific user. The search returns the matching user, if any.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Users: Transfer User Content", "api_call": "POST /documents/api/1.2/users/{userID}/transferContent", "api_version": "2023.11.27", "api_arguments": {"userID": "[REQUIRED] : <p>Specify the source <code>userID</code> from where all content will be transferred. User GUID or login can be used.</p>", "payload": "[REQUIRED] : The request body defines details of what folder will be transferred."}, "functionality": "<p>Transfer either all user (<code>userID</code>) content or a specific folder to another user (<code>targetUserID</code>). During transfer, a new folder is created at the destination user&#39;s home folder (named 'Documents from {sourceUsername}') and all content is moved in there. After the transfer is finished, this new folder will be automatically shared with the source user.</p><p>To transfer user content, you must log in as an administrator.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Shares: Share Folder", "api_call": "POST /documents/api/1.2/shares/{folderId}", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. For sharing the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code></p>.", "payload": "[REQUIRED] : The request body defines details of the share request. <b>Bold</b> indicates a required value."}, "functionality": "Share a specified folder with specified user(s) and/or group(s). When you share folders, you control the permissions each person/group has for the folder and its files by assigning a role to the person/group. The different roles determine what a person/group can do with a shared folder.<ul><li><b>Viewer:</b> Viewers can look at the contents of a folders but can&#39;t change files.</li><li><b>Downloader:</b> Downloaders can also download files and save them to their own computer.</li><li><b>Contributor:</b> Contributors can also modify, update, upload, and delete files. Contributors can also get metadata values, get metadata definitions, and set values for metadata already assigned by the folder owner.</li><li><b>Manager:</b> Managers have all the privileges of the other roles and can add or remove other people as members.</li></ul>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Shares: Unshare Folder", "api_call": "DELETE /documents/api/1.2/shares/{folderId}", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code></p>.", "message": ": Optional message sent to the user."}, "functionality": "Remove all shares from a specified folder.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Shares: Get Shared Folder Users", "api_call": "GET /documents/api/1.2/shares/{folderId}/items", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code></p>.", "payload": "[REQUIRED] : The request body defines details of the get shared folder users request. There are no required attributes."}, "functionality": "Get information about a shared folder and the users who share it.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Shares: Edit Shared Folder User Role", "api_call": "PUT /documents/api/1.2/shares/{folderId}/role", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code></p>.", "payload": "[REQUIRED] : The request body defines details of the edit shared folder user role request. <b>Bold</b> indicates a required value."}, "functionality": "Change the role of a user/group for a shared folder.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Shares: Revoke User", "api_call": "DELETE /documents/api/1.2/shares/{folderId}/user", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>", "userID": "[REQUIRED] : Globally unique identifier (GUID) for the user, login name of the user, GUID for the group or a comma-separated list of the above (no more than 1000 total IDs).", "message": ": Optional message sent to the user."}, "functionality": "<p>Delete a user&#39;s or group&#39;s access to a shared folder.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Shares: Revoke Current User", "api_call": "DELETE /documents/api/1.2/shares/{folderId}/myuser", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : <p>Globally unique identifier (GUID) for the folder. If the referenced folder is the user&#39;s home folder, the value for <code>folderId</code> is <code>self</code>.</p>"}, "functionality": "<p>Delete the current user&#39;s access to a shared folder.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Metadata Collection: Create Metadata Collection", "api_call": "POST /documents/api/1.2/metadata/{collectionName}", "api_version": "2023.11.27", "api_arguments": {"collectionName": "[REQUIRED] : <p>Global collection names must be unique in the instance where the collection is defined and used. User personal collection names must start with <code>Personal.</code>, such as <code>Personal.MyCollection</code>, to be user scoped.</p><p><b>Note:</p><p>You cannot change the name of a collection after you create it.</b></p><p>The following restrictions apply to collection and field names:<ul><li>Collection and field names can&#39;t start or end with a space.</li><li>The combined length of the collection and field name can&#39;t exceed 240 characters.</li><li>Collection and file names aren&#39;t case-sensitive; that is, <code>Field Name</code> and <code>field name</code> are considered identical.</li></ul></p><p>Don&#39;t use the following characters in collection or field names:</p><p><table> <tr><td>&nbsp;&#47;</td><td>&nbsp;&#92;</td><td>&nbsp;&#60;</td><td>&nbsp;&#62;</td><td>&nbsp;&#91;</td><td>&nbsp;&#93;</td><td>&nbsp;&#123;</td><td>&nbsp;&#125;</td><td>&nbsp;&#61;</td><td>&nbsp;$</td><td>&nbsp;%</td></tr><tr><td>&nbsp;&#39;</td><td>&nbsp;&#42;</td><td>&nbsp;&#34;</td><td>&nbsp;&#63;</td><td>&nbsp;&#58;</td><td>&nbsp;&#35;</td><td>&nbsp;&#38;</td><td>&nbsp;&#94;</td><td>&nbsp;&#46;</td><td>&nbsp;&#44;</td><td>&nbsp;&#124;</td></tr></table></p><p>The following strings are also not allowed in collection or field names:</p></p><ul><li>NUL</li><li>NULL</li><li>xSystem</li><li>xUser</li><li>xAF</li><li>dMetadataUnitID</li><li>dParentMetadataUnitID</li><li>dMetadataInheritLevel</li><li>dAssignedMetaCollection</li><li>dMetaCollection</li><li>dMetaCollections</li><li>dMetadataCollectionID</li><li>dMetadataID</li></ul></p>", "payload": "[REQUIRED] : The request body defines details of the create metadata collection request."}, "functionality": "<p>Create a named collection to store field values as strings. To create a global collection, you must log in as an administrator. A global collection can be used by all tenant users.</p><p>Before you can assign field values, you must assign the collection to a folder or file.</p><b><p>New:</p><p>User personal collections can be created by prefixing the collection name with <code>Personal.</code>. A personal collection will be visible and accessible only to the user who created it; therefore, no naming conflict will exist if two different users define their own personal collections using the same name.</p></b><p></p><p><b>Note:</p><p>Global collection names must be unique in the instance where the collection is defined, and you cannot change the collection name after it is created. For these reasons, consider implementing naming conventions if you plan to make extensive use of metadata collections.</b></p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Metadata Collection: Edit Metadata Collection", "api_call": "PUT /documents/api/1.2/metadata/{collectionName}", "api_version": "2023.11.27", "api_arguments": {"collectionName": "[REQUIRED] : Collection name. For a user personal collection, it must start with <code>Personal.</code>, such as <code>Personal.MyCollection</code>.", "payload": "[REQUIRED] : The request body defines the details of edit fields or the enable/disable status in a metadata collection request."}, "functionality": "<p>Add or remove fields in a specified metadata collection.</p><p>Enable or disable a metadata collection.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Metadata Collection: Get Metadata Collection Definition", "api_call": "GET /documents/api/1.2/metadata/{collectionName}", "api_version": "2023.11.27", "api_arguments": {"collectionName": "[REQUIRED] : Collection name. For a user personal collection, it must start with <code>Personal.</code>, such as <code>Personal.MyCollection</code>."}, "functionality": "<p>Retrieve metadata collection definition information along with metadata fields information.</p><p>Enable (<code>1</code>) or disable (<code>0</code>) status will be displayed individually for a metadata collection and all its fields.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Metadata Collection: Delete Metadata Collection", "api_call": "DELETE /documents/api/1.2/metadata/{collectionName}", "api_version": "2023.11.27", "api_arguments": {"collectionName": "[REQUIRED] : Collection name. For a user personal collection, it must start with <code>Personal.</code>, such as <code>Personal.MyCollection</code>."}, "functionality": "<p>Delete a metadata collection and all of its values assigned to files or folders.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Metadata Collection: Edit Fields in a Metadata Collection", "api_call": "PUT /documents/api/1.2/metadata/{collectionName}/field", "api_version": "2023.11.27", "api_arguments": {"collectionName": "[REQUIRED] : Collection name. For a user personal collection, it must start with <code>Personal.</code>, such as <code>Personal.MyCollection</code>.", "payload": "[REQUIRED] : The request body defines the details of the enable/disable status for fields in a metadata collection request."}, "functionality": "<p>Edit field definitions or status in a metadata collection.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Metadata Collection: Get Searchable Metadata Fields", "api_call": "GET /documents/api/1.2/metadata/searchFields", "api_version": "2023.11.27", "api_arguments": {}, "functionality": "<p>Retrieve all metadata fields currently available for searching content. The result list includes all metadata fields prefixed with their respective global metadata collection for that tenant.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Metadata Collection: Set Searchable Metadata Fields", "api_call": "POST /documents/api/1.2/metadata/searchFields", "api_version": "2023.11.27", "api_arguments": {"payload": "[REQUIRED] : The request body defines the details of setting searchable metadata fields."}, "functionality": "<p>Turn search on for a list of metadata fields. Only metadata fields from the same global collection can be processed in the same request.</p><p>Currently, there is a limit of 200 searchable metadata fields, and after that point no extra fields can be indexed. These metadata fields and metadata collections will be removed from the search index if an administrator deletes them. This action will lower the number of indexed metadata fields allowing users to index new ones.</p> <p>You must log in as an administrator. Searchable metadata fields can be used by all tenant users.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Metadata Collection: Get Available Metadata Collections", "api_call": "GET /documents/api/1.2/metadata", "api_version": "2023.11.27", "api_arguments": {"payload": ": The request body defines the details of retrieving metadata collections available for the current user. It is not required, and it will retrieve only metadata collections by default. If fields need to be retrieved as well, <code>retrieveFields</code> can be used."}, "functionality": "<p>Retrieve all metadata collections available for the current user. The result list includes all global metadata collections for that tenant as well as all personal metadata collections created by the current user. By default, only metadata collection definitions will be retrieved, but all respective field definitions can be obtained as well, if needed.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Publiclinks: Create Folder Public Link", "api_call": "POST /documents/api/1.2/publiclinks/folder/{folderId}", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : Globally unique identifier (GUID) for the folder.", "payload": "[REQUIRED] : The request body defines details of the create public link request. <b>Bold</b> indicates a required value."}, "functionality": "<p>Create a public link for a specified folder.</p><p>A public link allows specific users access to the requested folder, whether they have an account or not.</p><p><b>Note:</p><p>To create a public link, the requester must have admin privileges for the folder or file. That is, the requester must be the owner or have the manager role.</b></p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Publiclinks: Get Folder Public Link", "api_call": "GET /documents/api/1.2/publiclinks/folder/{folderId}", "api_version": "2023.11.27", "api_arguments": {"folderId": "[REQUIRED] : Globally unique identifier (GUID) for the folder."}, "functionality": "<p>Return a list of the public links for a specified folder.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Publiclinks: Create File Public Link", "api_call": "POST /documents/api/1.2/publiclinks/file/{fileId}", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : Globally unique identifier (GUID) for the file.", "payload": "[REQUIRED] : The request body defines details of the create public link request. <b>Bold</b> indicates a required value."}, "functionality": "<p>Create a public link for a specified file.</p><p>A public link allows specific users access to the requested file, whether they have an account or not.</p><p><b>Note:</p><p>To create a public link, the requester must have admin privileges for the folder or file. That is, the requester must be the owner or have the manager role.</b></p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Publiclinks: Get File Public Link", "api_call": "GET /documents/api/1.2/publiclinks/file/{fileId}", "api_version": "2023.11.27", "api_arguments": {"fileId": "[REQUIRED] : Globally unique identifier (GUID) for the file."}, "functionality": "<p>Return a list of the public links for a specified file.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Publiclinks: Get Public Link", "api_call": "GET /documents/api/1.2/publiclinks/{linkId}", "api_version": "2023.11.27", "api_arguments": {"linkId": "[REQUIRED] : Globally unique identifier (GUID) for the public link."}, "functionality": "<p>Return information about a specific public link.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Publiclinks: Edit Public Link", "api_call": "PUT /documents/api/1.2/publiclinks/{linkId}", "api_version": "2023.11.27", "api_arguments": {"linkId": "[REQUIRED] : Globally unique identifier (GUID) for the public link.", "payload": "[REQUIRED] : The request body defines details of the edit public link request. There are no required attributes."}, "functionality": "<p>Edit the available public link parameters.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Documents - Publiclinks: Delete Public Link", "api_call": "DELETE /documents/api/1.2/publiclinks/{linkId}", "api_version": "2023.11.27", "api_arguments": {"linkId": "[REQUIRED] : Globally unique identifier (GUID) for the public link."}, "functionality": "<p>Delete a specific public link.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/content-cloud/rest-api-documents/"}}]