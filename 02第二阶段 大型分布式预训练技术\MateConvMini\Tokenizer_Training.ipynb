{"cells": [{"cell_type": "markdown", "id": "42922531-0bdb-40f3-b54e-de5fa31f7e18", "metadata": {}, "source": ["# MateConv mini tokenizer训练"]}, {"cell_type": "markdown", "id": "5e902c1a-9a4a-442e-998d-b7883ce17005", "metadata": {}, "source": ["- Step 1.导入必要的库"]}, {"cell_type": "code", "execution_count": 1, "id": "88c752cd-3a37-43ba-bed5-4c9151abfc14", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import random\n", "from tqdm import tqdm\n", "from transformers import AutoTokenizer\n", "import json\n", "from datasets import load_dataset\n", "from tokenizers import (\n", "    decoders,\n", "    models,\n", "    normalizers,\n", "    pre_tokenizers,\n", "    processors,\n", "    trainers,\n", "    <PERSON><PERSON><PERSON>,\n", ")\n", "import os"]}, {"cell_type": "markdown", "id": "a52fdcb6-0937-4270-ab66-e9dd1ac3d97a", "metadata": {}, "source": ["- Step 2.读取 tokenizer_train.jsonl 文件"]}, {"cell_type": "code", "execution_count": 5, "id": "eac525e8-543d-4c01-91d4-c79d80f7b1c2", "metadata": {}, "outputs": [], "source": ["def read_texts_from_jsonl(file_path):\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        for line in f:\n", "            data = json.loads(line)\n", "            yield data['text']\n", "\n", "# 测试读取数据，你可以更换成你建立的目录\n", "data_path = '/root/autodl-tmp/MateConv/Data/tokenizer_train.jsonl'\n", "texts = read_texts_from_jsonl(data_path)"]}, {"cell_type": "markdown", "id": "0539c9f7-4811-449a-9559-e597438a0995", "metadata": {}, "source": ["- Step 3.初始化分词器"]}, {"cell_type": "code", "execution_count": 3, "id": "ebe6e3bd-a912-4f22-bfa9-f6db03a2d527", "metadata": {}, "outputs": [], "source": ["# 初始化tokenizer\n", "tokenizer = Tokenizer(models.BPE())\n", "tokenizer.pre_tokenizer = pre_tokenizers.ByteLevel(add_prefix_space=False)\n", "\n", "# 定义特殊token\n", "special_tokens = [\"<unk>\", \"<s>\", \"</s>\"]\n", "\n", "# 设置训练器并添加特殊token\n", "trainer = trainers.<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    vocab_size=6400,\n", "    special_tokens=special_tokens,  # 确保这三个token被包含\n", "    show_progress=True,\n", "    initial_alphabet=pre_tokenizers.ByteLevel.alphabet()\n", ")"]}, {"cell_type": "markdown", "id": "ae2ae8c2-198c-455f-a5f8-198ecdd58bcc", "metadata": {}, "source": ["- Step 4.训练分词器"]}, {"cell_type": "code", "execution_count": 6, "id": "268dd2a1-79b9-4199-a02d-ecbae5dd748e", "metadata": {}, "outputs": [], "source": ["# 读取文本数据\n", "texts = read_texts_from_jsonl(data_path)"]}, {"cell_type": "code", "execution_count": 7, "id": "74c45cda-ecf9-4544-a52e-1ca952e6e9e7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\n"]}], "source": ["# 训练tokenizer\n", "tokenizer.train_from_iterator(texts, trainer=trainer)"]}, {"cell_type": "markdown", "id": "37f4147c-ade4-4d9c-ad22-111815075599", "metadata": {}, "source": ["- Step 5.保存分词器"]}, {"cell_type": "markdown", "id": "39bd2d95-8fc4-4bfa-96f4-39b328290b29", "metadata": {}, "source": ["在训练完毕之后，还需要设置解码器 (`tokenizer.decoder = decoders.ByteLevel()`) ，这是为了在生成文本时正确地将分词器产生的 token 序列还原回原始文本。"]}, {"cell_type": "code", "execution_count": 8, "id": "a991ac12-ccc8-478e-87fd-26442ec70e42", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tokenizer 保存成功！\n"]}], "source": ["# 设置解码器\n", "tokenizer.decoder = decoders.ByteLevel()\n", "\n", "# 保存tokenizer\n", "tokenizer_dir = \"/root/autodl-tmp/MateConv/model/mateconv_tokenizer\"\n", "os.makedirs(tokenizer_dir, exist_ok=True)\n", "tokenizer.save(os.path.join(tokenizer_dir, \"tokenizer.json\"))\n", "tokenizer.model.save(\"/root/autodl-tmp/MateConv/model/mateconv_tokenizer\")\n", "\n", "# 手动创建配置文件\n", "config = {\n", "    \"add_bos_token\": <PERSON>alse,\n", "    \"add_eos_token\": False,\n", "    \"add_prefix_space\": True,\n", "    \"added_tokens_decoder\": {\n", "        \"0\": {\n", "            \"content\": \"<unk>\",\n", "            \"lstrip\": <PERSON><PERSON><PERSON>,\n", "            \"normalized\": <PERSON><PERSON><PERSON>,\n", "            \"rstrip\": <PERSON><PERSON><PERSON>,\n", "            \"single_word\": <PERSON><PERSON><PERSON>,\n", "            \"special\": <PERSON>\n", "            },\n", "        \"1\": {\n", "            \"content\": \"<s>\",\n", "            \"lstrip\": <PERSON><PERSON><PERSON>,\n", "            \"normalized\": <PERSON><PERSON><PERSON>,\n", "            \"rstrip\": <PERSON><PERSON><PERSON>,\n", "            \"single_word\": <PERSON><PERSON><PERSON>,\n", "            \"special\": <PERSON>\n", "            },\n", "        \"2\": {\n", "            \"content\": \"</s>\",\n", "            \"lstrip\": <PERSON><PERSON><PERSON>,\n", "            \"normalized\": <PERSON><PERSON><PERSON>,\n", "            \"rstrip\": <PERSON><PERSON><PERSON>,\n", "            \"single_word\": <PERSON><PERSON><PERSON>,\n", "            \"special\": <PERSON>\n", "            }\n", "    },\n", "    \"bos_token\": \"<s>\",\n", "    \"clean_up_tokenization_spaces\": False,\n", "    \"eos_token\": \"</s>\",\n", "    \"legacy\": True,\n", "    \"model_max_length\": 1000000000000000019884624838656,\n", "    \"pad_token\": None,\n", "    \"sp_model_kwargs\": {},\n", "    \"spaces_between_special_tokens\": <PERSON>alse,\n", "    \"tokenizer_class\": \"PreTrainedTokenizerFast\",\n", "    \"unk_token\": \"<unk>\",\n", "    \"use_default_system_prompt\": False,\n", "    \"chat_template\": \"{% if messages[0]['role'] == 'system' %}{% set system_message = messages[0]['content'] %}{% endif %}{% if system_message is defined %}{{ system_message }}{% endif %}{% for message in messages %}{% set content = message['content'] %}{% if message['role'] == 'user' %}{{ '<s>user\\\\n' + content + '</s>\\\\n<s>assistant\\\\n' }}{% elif message['role'] == 'assistant' %}{{ content + '</s>' + '\\\\n' }}{% endif %}{% endfor %}\"\n", "}\n", "\n", "# 保存配置文件\n", "with open(os.path.join(tokenizer_dir, \"tokenizer_config.json\"), \"w\", encoding=\"utf-8\") as config_file:\n", "    json.dump(config, config_file, ensure_ascii=False, indent=4)\n", "\n", "print(\"Tokenizer 保存成功！\")"]}, {"cell_type": "markdown", "id": "90c4362c-b8bd-491c-a83a-8fc4cd8498c6", "metadata": {}, "source": ["- Step 6.评估分词器"]}, {"cell_type": "code", "execution_count": 9, "id": "7ff976cd-2b53-4b0d-a317-dbd1cab9c715", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[608, 1589, 4835, 269, 4833, 954, 4725, 270, 1170, 345, 4584, 5204, 1273, 648, 2207, 1, 320, 275, 201, 345, 1390, 258, 3852, 1081, 269, 2, 201, 1, 1078, 538, 501, 201, 22, 23, 24, 2, 201, 1, 320, 275, 201, 22, 23, 24, 2, 201, 1, 1078, 538, 501, 201, 25, 26, 27, 2, 201]\n"]}], "source": ["from transformers import AutoTokenizer\n", "\n", "# 加载预训练的tokenizer\n", "tokenizer = AutoTokenizer.from_pretrained(\"./model/mateconv_tokenizer\")\n", "\n", "# 测试一段对话\n", "messages = [\n", "    {\"role\": \"system\", \"content\": \"你是一个优秀的聊天机器人，总是给我正确的回应！\"},\n", "    {\"role\": \"user\", \"content\": '是椭圆形的'},\n", "    {\"role\": \"assistant\", \"content\": '456'},\n", "    {\"role\": \"user\", \"content\": '456'},\n", "    {\"role\": \"assistant\", \"content\": '789'}\n", "]\n", "\n", "# 使用模板进行文本处理\n", "new_prompt = tokenizer.apply_chat_template(messages, tokenize=True)\n", "print(new_prompt)"]}, {"cell_type": "code", "execution_count": null, "id": "61b573bc-9649-4844-8ad0-a7569b916b0a", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}