[{"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - acl.delete", "api_call": "service.acl().delete(calendarId: str, ruleId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "ruleId": "[REQUIRED] string: ACL rule identifier."}, "functionality": "Deletes an access control rule.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - acl.get", "api_call": "service.acl().get(calendarId: str, ruleId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "ruleId": "[REQUIRED] string: ACL rule identifier."}, "functionality": "Returns an access control rule.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - acl.insert", "api_call": "service.acl().insert(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "sendNotifications": "boolean: Whether to send notifications about the calendar sharing change. Optional. The default is True."}, "functionality": "Creates an access control rule.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - acl.list", "api_call": "service.acl().list(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "maxResults": "integer: Maximum number of entries returned on one result page. By default the value is 100 entries. The page size can never be larger than 250 entries. Optional.", "pageToken": "string: Token specifying which result page to return. Optional.", "showDeleted": "boolean: Whether to include deleted ACLs in the result. Deleted ACLs are represented by role equal to \"none\". Deleted ACLs will always be included if syncToken is provided. Optional. The default is False.", "syncToken": "string: Token obtained from the nextSyncToken field returned on the last page of results from the previous list request. It makes the result of this list request contain only entries that have changed since then. All entries deleted since the previous list request will always be in the result set and it is not allowed to set showDeleted to False.\nIf the syncToken expires, the server will respond with a 410 GONE response code and the client should clear its storage and perform a full synchronization without any syncToken.\nLearn more about incremental synchronization.\nOptional. The default is to return all entries."}, "functionality": "Returns the rules in the access control list for the calendar.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - acl.patch", "api_call": "service.acl().patch(calendarId: str, ruleId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "ruleId": "[REQUIRED] string: ACL rule identifier.", "sendNotifications": "boolean: Whether to send notifications about the calendar sharing change. Note that there are no notifications on access removal. Optional. The default is True."}, "functionality": "Updates an access control rule. This method supports patch semantics.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - acl.update", "api_call": "service.acl().update(calendarId: str, ruleId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "ruleId": "[REQUIRED] string: ACL rule identifier.", "sendNotifications": "boolean: Whether to send notifications about the calendar sharing change. Note that there are no notifications on access removal. Optional. The default is True."}, "functionality": "Updates an access control rule.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - acl.watch", "api_call": "service.acl().watch(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "maxResults": "integer: Maximum number of entries returned on one result page. By default the value is 100 entries. The page size can never be larger than 250 entries. Optional.", "pageToken": "string: Token specifying which result page to return. Optional.", "showDeleted": "boolean: Whether to include deleted ACLs in the result. Deleted ACLs are represented by role equal to \"none\". Deleted ACLs will always be included if syncToken is provided. Optional. The default is False.", "syncToken": "string: Token obtained from the nextSyncToken field returned on the last page of results from the previous list request. It makes the result of this list request contain only entries that have changed since then. All entries deleted since the previous list request will always be in the result set and it is not allowed to set showDeleted to False.\nIf the syncToken expires, the server will respond with a 410 GONE response code and the client should clear its storage and perform a full synchronization without any syncToken.\nLearn more about incremental synchronization.\nOptional. The default is to return all entries."}, "functionality": "Watch for changes to ACL resources.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - calendarList.delete", "api_call": "service.calendarList().delete(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword."}, "functionality": "Removes a calendar from the user's calendar list.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - calendarList.get", "api_call": "service.calendarList().get(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword."}, "functionality": "Returns a calendar from the user's calendar list.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - calendarList.insert", "api_call": "service.calendarList().insert().execute()", "api_version": "v3", "api_arguments": {"colorRgbFormat": "boolean: Whether to use the foregroundColor and backgroundColor fields to write the calendar colors (RGB). If this feature is used, the index-based colorId field will be set to the best matching option automatically. Optional. The default is False."}, "functionality": "Inserts an existing calendar into the user's calendar list.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - calendarList.list", "api_call": "service.calendarList().list().execute()", "api_version": "v3", "api_arguments": {"maxResults": "integer: Maximum number of entries returned on one result page. By default the value is 100 entries. The page size can never be larger than 250 entries. Optional.", "minAccessRole": "string: The minimum access role for the user in the returned entries. Optional. The default is no restriction.", "pageToken": "string: Token specifying which result page to return. Optional.", "showDeleted": "boolean: Whether to include deleted calendar list entries in the result. Optional. The default is False.", "showHidden": "boolean: Whether to show hidden entries. Optional. The default is False.", "syncToken": "string: Token obtained from the nextSyncToken field returned on the last page of results from the previous list request. It makes the result of this list request contain only entries that have changed since then. If only read-only fields such as calendar properties or ACLs have changed, the entry won't be returned. All entries deleted and hidden since the previous list request will always be in the result set and it is not allowed to set showDeleted neither showH<PERSON><PERSON> to False.\nTo ensure client state consistency minAccessRole query parameter cannot be specified together with nextSyncToken.\nIf the syncToken expires, the server will respond with a 410 GONE response code and the client should clear its storage and perform a full synchronization without any syncToken.\nLearn more about incremental synchronization.\nOptional. The default is to return all entries."}, "functionality": "Returns the calendars on the user's calendar list.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - calendarList.patch", "api_call": "service.calendarList().patch(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "colorRgbFormat": "boolean: Whether to use the foregroundColor and backgroundColor fields to write the calendar colors (RGB). If this feature is used, the index-based colorId field will be set to the best matching option automatically. Optional. The default is False."}, "functionality": "Updates an existing calendar on the user's calendar list. This method supports patch semantics.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - calendarList.update", "api_call": "service.calendarList().update(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "colorRgbFormat": "boolean: Whether to use the foregroundColor and backgroundColor fields to write the calendar colors (RGB). If this feature is used, the index-based colorId field will be set to the best matching option automatically. Optional. The default is False."}, "functionality": "Updates an existing calendar on the user's calendar list.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - calendarList.watch", "api_call": "service.calendarList().watch().execute()", "api_version": "v3", "api_arguments": {"maxResults": "integer: Maximum number of entries returned on one result page. By default the value is 100 entries. The page size can never be larger than 250 entries. Optional.", "minAccessRole": "string: The minimum access role for the user in the returned entries. Optional. The default is no restriction.", "pageToken": "string: Token specifying which result page to return. Optional.", "showDeleted": "boolean: Whether to include deleted calendar list entries in the result. Optional. The default is False.", "showHidden": "boolean: Whether to show hidden entries. Optional. The default is False.", "syncToken": "string: Token obtained from the nextSyncToken field returned on the last page of results from the previous list request. It makes the result of this list request contain only entries that have changed since then. If only read-only fields such as calendar properties or ACLs have changed, the entry won't be returned. All entries deleted and hidden since the previous list request will always be in the result set and it is not allowed to set showDeleted neither showH<PERSON><PERSON> to False.\nTo ensure client state consistency minAccessRole query parameter cannot be specified together with nextSyncToken.\nIf the syncToken expires, the server will respond with a 410 GONE response code and the client should clear its storage and perform a full synchronization without any syncToken.\nLearn more about incremental synchronization.\nOptional. The default is to return all entries."}, "functionality": "Watch for changes to CalendarList resources.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - calendars.clear", "api_call": "service.calendars().clear(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword."}, "functionality": "Clears a primary calendar. This operation deletes all events associated with the primary calendar of an account.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - calendars.delete", "api_call": "service.calendars().delete(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword."}, "functionality": "Deletes a secondary calendar. Use calendars.clear for clearing all events on primary calendars.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - calendars.get", "api_call": "service.calendars().get(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword."}, "functionality": "Returns metadata for a calendar.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - calendars.insert", "api_call": "service.calendars().insert().execute()", "api_version": "v3", "api_arguments": {}, "functionality": "Creates a secondary calendar.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - calendars.patch", "api_call": "service.calendars().patch(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword."}, "functionality": "Updates metadata for a calendar. This method supports patch semantics.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - calendars.update", "api_call": "service.calendars().update(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword."}, "functionality": "Updates metadata for a calendar.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - channels.stop", "api_call": "service.channels().stop().execute()", "api_version": "v3", "api_arguments": {}, "functionality": "Stop watching resources through this channel", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - colors.get", "api_call": "service.colors().get().execute()", "api_version": "v3", "api_arguments": {}, "functionality": "Returns the color definitions for calendars and events.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - events.delete", "api_call": "service.events().delete(calendarId: str, eventId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "eventId": "[REQUIRED] string: Event identifier.", "sendNotifications": "boolean: Deprecated. Please use sendUpdates instead.\n\nWhether to send notifications about the deletion of the event. Note that some emails might still be sent even if you set the value to false. The default is false.", "sendUpdates": "string: Guests who should receive notifications about the deletion of the event."}, "functionality": "Deletes an event.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - events.get", "api_call": "service.events().get(calendarId: str, eventId: str).execute()", "api_version": "v3", "api_arguments": {"alwaysIncludeEmail": "boolean: Deprecated and ignored. A value will always be returned in the email field for the organizer, creator and attendees, even if no real email address is available (i.e. a generated, non-working value will be provided).", "calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "eventId": "[REQUIRED] string: Event identifier.", "maxAttendees": "integer: The maximum number of attendees to include in the response. If there are more than the specified number of attendees, only the participant is returned. Optional.", "timeZone": "string: Time zone used in the response. Optional. The default is the time zone of the calendar."}, "functionality": "Returns an event based on its Google Calendar ID. To retrieve an event using its iCalendar ID, call the events.list method using the iCalUID parameter.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - events.import", "api_call": "service.events().import(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "conferenceDataVersion": "integer: Version number of conference data supported by the API client. Version 0 assumes no conference data support and ignores conference data in the event's body. Version 1 enables support for copying of ConferenceData as well as for creating new conferences using the createRequest field of conferenceData. The default is 0.", "supportsAttachments": "boolean: Whether API client performing operation supports event attachments. Optional. The default is False."}, "functionality": "Imports an event. This operation is used to add a private copy of an existing event to a calendar.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - events.insert", "api_call": "service.events().insert(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "conferenceDataVersion": "integer: Version number of conference data supported by the API client. Version 0 assumes no conference data support and ignores conference data in the event's body. Version 1 enables support for copying of ConferenceData as well as for creating new conferences using the createRequest field of conferenceData. The default is 0.", "maxAttendees": "integer: The maximum number of attendees to include in the response. If there are more than the specified number of attendees, only the participant is returned. Optional.", "sendNotifications": "boolean: Deprecated. Please use sendUpdates instead.\n\nWhether to send notifications about the creation of the new event. Note that some emails might still be sent even if you set the value to false. The default is false.", "sendUpdates": "string: Whether to send notifications about the creation of the new event. Note that some emails might still be sent. The default is false.", "supportsAttachments": "boolean: Whether API client performing operation supports event attachments. Optional. The default is False."}, "functionality": "Creates an event.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - events.instances", "api_call": "service.events().instances(calendarId: str, eventId: str).execute()", "api_version": "v3", "api_arguments": {"alwaysIncludeEmail": "boolean: Deprecated and ignored. A value will always be returned in the email field for the organizer, creator and attendees, even if no real email address is available (i.e. a generated, non-working value will be provided).", "calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "eventId": "[REQUIRED] string: Recurring event identifier.", "maxAttendees": "integer: The maximum number of attendees to include in the response. If there are more than the specified number of attendees, only the participant is returned. Optional.", "maxResults": "integer: Maximum number of events returned on one result page. By default the value is 250 events. The page size can never be larger than 2500 events. Optional.", "originalStart": "string: The original start time of the instance in the result. Optional.", "pageToken": "string: Token specifying which result page to return. Optional.", "showDeleted": "boolean: Whether to include deleted events (with status equals \"cancelled\") in the result. Cancelled instances of recurring events will still be included if singleEvents is False. Optional. The default is False.", "timeMax": "string: Upper bound (exclusive) for an event's start time to filter by. Optional. The default is not to filter by start time. Must be an RFC3339 timestamp with mandatory time zone offset.", "timeMin": "string: Lower bound (inclusive) for an event's end time to filter by. Optional. The default is not to filter by end time. Must be an RFC3339 timestamp with mandatory time zone offset.", "timeZone": "string: Time zone used in the response. Optional. The default is the time zone of the calendar."}, "functionality": "Returns instances of the specified recurring event.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - events.list", "api_call": "service.events().list(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"alwaysIncludeEmail": "boolean: Deprecated and ignored.", "calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "eventTypes": "string: Event types to return. Optional. This parameter can be repeated multiple times to return events of different types. The default is [\"default\", \"focusTime\", \"outOfOffice\"].", "iCalUID": "string: Specifies an event ID in the iCalendar format to be provided in the response. Optional. Use this if you want to search for an event by its iCalendar ID.", "maxAttendees": "integer: The maximum number of attendees to include in the response. If there are more than the specified number of attendees, only the participant is returned. Optional.", "maxResults": "integer: Maximum number of events returned on one result page. The number of events in the resulting page may be less than this value, or none at all, even if there are more events matching the query. Incomplete pages can be detected by a non-empty nextPageToken field in the response. By default the value is 250 events. The page size can never be larger than 2500 events. Optional.", "orderBy": "string: The order of the events returned in the result. Optional. The default is an unspecified, stable order.", "pageToken": "string: Token specifying which result page to return. Optional.", "privateExtendedProperty": "string: Extended properties constraint specified as propertyName=value. Matches only private properties. This parameter might be repeated multiple times to return events that match all given constraints.", "q": "string: Free text search terms to find events that match these terms in the following fields:\n\n- summary \n- description \n- location \n- attendee's displayName \n- attendee's email \n- organizer's displayName \n- organizer's email \n- workingLocationProperties.officeLocation.buildingId \n- workingLocationProperties.officeLocation.deskId \n- workingLocationProperties.officeLocation.label \n- workingLocationProperties.customLocation.label \nThese search terms also match predefined keywords against all display title translations of working location, out-of-office, and focus-time events. For example, searching for \"Office\" or \"Bureau\" returns working location events of type officeLocation, whereas searching for \"Out of office\" or \"Abwesend\" returns out-of-office events. Optional.", "sharedExtendedProperty": "string: Extended properties constraint specified as propertyName=value. Matches only shared properties. This parameter might be repeated multiple times to return events that match all given constraints.", "showDeleted": "boolean: Whether to include deleted events (with status equals \"cancelled\") in the result. Cancelled instances of recurring events (but not the underlying recurring event) will still be included if showDeleted and singleEvents are both False. If showDeleted and singleEvents are both True, only single instances of deleted events (but not the underlying recurring events) are returned. Optional. The default is False.", "showHiddenInvitations": "boolean: Whether to include hidden invitations in the result. Optional. The default is False.", "singleEvents": "boolean: Whether to expand recurring events into instances and only return single one-off events and instances of recurring events, but not the underlying recurring events themselves. Optional. The default is False.", "syncToken": "string: Token obtained from the nextSyncToken field returned on the last page of results from the previous list request. It makes the result of this list request contain only entries that have changed since then. All events deleted since the previous list request will always be in the result set and it is not allowed to set showDeleted to False.\nThere are several query parameters that cannot be specified together with nextSyncToken to ensure consistency of the client state.\n\nThese are: \n- iCalUID \n- orderBy \n- privateExtendedProperty \n- q \n- sharedExtendedProperty \n- timeMin \n- timeMax \n- updatedMin All other query parameters should be the same as for the initial synchronization to avoid undefined behavior. If the syncToken expires, the server will respond with a 410 GONE response code and the client should clear its storage and perform a full synchronization without any syncToken.\nLearn more about incremental synchronization.\nOptional. The default is to return all entries.", "timeMax": "string: Upper bound (exclusive) for an event's start time to filter by. Optional. The default is not to filter by start time. Must be an RFC3339 timestamp with mandatory time zone offset, for example, 2011-06-03T10:00:00-07:00, 2011-06-03T10:00:00Z. Milliseconds may be provided but are ignored. If timeMin is set, timeMax must be greater than timeMin.", "timeMin": "string: Lower bound (exclusive) for an event's end time to filter by. Optional. The default is not to filter by end time. Must be an RFC3339 timestamp with mandatory time zone offset, for example, 2011-06-03T10:00:00-07:00, 2011-06-03T10:00:00Z. Milliseconds may be provided but are ignored. If timeMax is set, timeMin must be smaller than timeMax.", "timeZone": "string: Time zone used in the response. Optional. The default is the time zone of the calendar.", "updatedMin": "string: Lower bound for an event's last modification time (as a RFC3339 timestamp) to filter by. When specified, entries deleted since this time will always be included regardless of showDeleted. Optional. The default is not to filter by last modification time."}, "functionality": "Returns events on the specified calendar.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - events.move", "api_call": "service.events().move(calendarId: str, eventId: str, destination: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier of the source calendar where the event currently is on.", "destination": "[REQUIRED] string: Calendar identifier of the target calendar where the event is to be moved to.", "eventId": "[REQUIRED] string: Event identifier.", "sendNotifications": "boolean: Deprecated. Please use sendUpdates instead.\n\nWhether to send notifications about the change of the event's organizer. Note that some emails might still be sent even if you set the value to false. The default is false.", "sendUpdates": "string: Guests who should receive notifications about the change of the event's organizer."}, "functionality": "Moves an event to another calendar, i.e. changes an event's organizer. Note that only default events can be moved; outOfOffice, focusTime and workingLocation events cannot be moved.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - events.patch", "api_call": "service.events().patch(calendarId: str, eventId: str).execute()", "api_version": "v3", "api_arguments": {"alwaysIncludeEmail": "boolean: Deprecated and ignored. A value will always be returned in the email field for the organizer, creator and attendees, even if no real email address is available (i.e. a generated, non-working value will be provided).", "calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "conferenceDataVersion": "integer: Version number of conference data supported by the API client. Version 0 assumes no conference data support and ignores conference data in the event's body. Version 1 enables support for copying of ConferenceData as well as for creating new conferences using the createRequest field of conferenceData. The default is 0.", "eventId": "[REQUIRED] string: Event identifier.", "maxAttendees": "integer: The maximum number of attendees to include in the response. If there are more than the specified number of attendees, only the participant is returned. Optional.", "sendNotifications": "boolean: Deprecated. Please use sendUpdates instead.\n\nWhether to send notifications about the event update (for example, description changes, etc.). Note that some emails might still be sent even if you set the value to false. The default is false.", "sendUpdates": "string: Guests who should receive notifications about the event update (for example, title changes, etc.).", "supportsAttachments": "boolean: Whether API client performing operation supports event attachments. Optional. The default is False."}, "functionality": "Updates an event. This method supports patch semantics.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - events.quickAdd", "api_call": "service.events().quickAdd(calendarId: str, text: str).execute()", "api_version": "v3", "api_arguments": {"calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "sendNotifications": "boolean: Deprecated. Please use sendUpdates instead.\n\nWhether to send notifications about the creation of the event. Note that some emails might still be sent even if you set the value to false. The default is false.", "sendUpdates": "string: Guests who should receive notifications about the creation of the new event.", "text": "[REQUIRED] string: The text describing the event to be created."}, "functionality": "Creates an event based on a simple text string.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - events.update", "api_call": "service.events().update(calendarId: str, eventId: str).execute()", "api_version": "v3", "api_arguments": {"alwaysIncludeEmail": "boolean: Deprecated and ignored. A value will always be returned in the email field for the organizer, creator and attendees, even if no real email address is available (i.e. a generated, non-working value will be provided).", "calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "conferenceDataVersion": "integer: Version number of conference data supported by the API client. Version 0 assumes no conference data support and ignores conference data in the event's body. Version 1 enables support for copying of ConferenceData as well as for creating new conferences using the createRequest field of conferenceData. The default is 0.", "eventId": "[REQUIRED] string: Event identifier.", "maxAttendees": "integer: The maximum number of attendees to include in the response. If there are more than the specified number of attendees, only the participant is returned. Optional.", "sendNotifications": "boolean: Deprecated. Please use sendUpdates instead.\n\nWhether to send notifications about the event update (for example, description changes, etc.). Note that some emails might still be sent even if you set the value to false. The default is false.", "sendUpdates": "string: Guests who should receive notifications about the event update (for example, title changes, etc.).", "supportsAttachments": "boolean: Whether API client performing operation supports event attachments. Optional. The default is False."}, "functionality": "Updates an event.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - events.watch", "api_call": "service.events().watch(calendarId: str).execute()", "api_version": "v3", "api_arguments": {"alwaysIncludeEmail": "boolean: Deprecated and ignored.", "calendarId": "[REQUIRED] string: Calendar identifier. To retrieve calendar IDs call the calendarList.list method. If you want to access the primary calendar of the currently logged in user, use the \"primary\" keyword.", "eventTypes": "string: Event types to return. Optional. This parameter can be repeated multiple times to return events of different types. The default is [\"default\", \"focusTime\", \"outOfOffice\"].", "iCalUID": "string: Specifies an event ID in the iCalendar format to be provided in the response. Optional. Use this if you want to search for an event by its iCalendar ID.", "maxAttendees": "integer: The maximum number of attendees to include in the response. If there are more than the specified number of attendees, only the participant is returned. Optional.", "maxResults": "integer: Maximum number of events returned on one result page. The number of events in the resulting page may be less than this value, or none at all, even if there are more events matching the query. Incomplete pages can be detected by a non-empty nextPageToken field in the response. By default the value is 250 events. The page size can never be larger than 2500 events. Optional.", "orderBy": "string: The order of the events returned in the result. Optional. The default is an unspecified, stable order.", "pageToken": "string: Token specifying which result page to return. Optional.", "privateExtendedProperty": "string: Extended properties constraint specified as propertyName=value. Matches only private properties. This parameter might be repeated multiple times to return events that match all given constraints.", "q": "string: Free text search terms to find events that match these terms in the following fields:\n\n- summary \n- description \n- location \n- attendee's displayName \n- attendee's email \n- organizer's displayName \n- organizer's email \n- workingLocationProperties.officeLocation.buildingId \n- workingLocationProperties.officeLocation.deskId \n- workingLocationProperties.officeLocation.label \n- workingLocationProperties.customLocation.label \nThese search terms also match predefined keywords against all display title translations of working location, out-of-office, and focus-time events. For example, searching for \"Office\" or \"Bureau\" returns working location events of type officeLocation, whereas searching for \"Out of office\" or \"Abwesend\" returns out-of-office events. Optional.", "sharedExtendedProperty": "string: Extended properties constraint specified as propertyName=value. Matches only shared properties. This parameter might be repeated multiple times to return events that match all given constraints.", "showDeleted": "boolean: Whether to include deleted events (with status equals \"cancelled\") in the result. Cancelled instances of recurring events (but not the underlying recurring event) will still be included if showDeleted and singleEvents are both False. If showDeleted and singleEvents are both True, only single instances of deleted events (but not the underlying recurring events) are returned. Optional. The default is False.", "showHiddenInvitations": "boolean: Whether to include hidden invitations in the result. Optional. The default is False.", "singleEvents": "boolean: Whether to expand recurring events into instances and only return single one-off events and instances of recurring events, but not the underlying recurring events themselves. Optional. The default is False.", "syncToken": "string: Token obtained from the nextSyncToken field returned on the last page of results from the previous list request. It makes the result of this list request contain only entries that have changed since then. All events deleted since the previous list request will always be in the result set and it is not allowed to set showDeleted to False.\nThere are several query parameters that cannot be specified together with nextSyncToken to ensure consistency of the client state.\n\nThese are: \n- iCalUID \n- orderBy \n- privateExtendedProperty \n- q \n- sharedExtendedProperty \n- timeMin \n- timeMax \n- updatedMin All other query parameters should be the same as for the initial synchronization to avoid undefined behavior. If the syncToken expires, the server will respond with a 410 GONE response code and the client should clear its storage and perform a full synchronization without any syncToken.\nLearn more about incremental synchronization.\nOptional. The default is to return all entries.", "timeMax": "string: Upper bound (exclusive) for an event's start time to filter by. Optional. The default is not to filter by start time. Must be an RFC3339 timestamp with mandatory time zone offset, for example, 2011-06-03T10:00:00-07:00, 2011-06-03T10:00:00Z. Milliseconds may be provided but are ignored. If timeMin is set, timeMax must be greater than timeMin.", "timeMin": "string: Lower bound (exclusive) for an event's end time to filter by. Optional. The default is not to filter by end time. Must be an RFC3339 timestamp with mandatory time zone offset, for example, 2011-06-03T10:00:00-07:00, 2011-06-03T10:00:00Z. Milliseconds may be provided but are ignored. If timeMax is set, timeMin must be smaller than timeMax.", "timeZone": "string: Time zone used in the response. Optional. The default is the time zone of the calendar.", "updatedMin": "string: Lower bound for an event's last modification time (as a RFC3339 timestamp) to filter by. When specified, entries deleted since this time will always be included regardless of showDeleted. Optional. The default is not to filter by last modification time."}, "functionality": "Watch for changes to Events resources.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - freebusy.query", "api_call": "service.freebusy().query().execute()", "api_version": "v3", "api_arguments": {}, "functionality": "Returns free/busy information for a set of calendars.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - settings.get", "api_call": "service.settings().get(setting: str).execute()", "api_version": "v3", "api_arguments": {"setting": "[REQUIRED] string: The id of the user setting."}, "functionality": "Returns a single user setting.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - settings.list", "api_call": "service.settings().list().execute()", "api_version": "v3", "api_arguments": {"maxResults": "integer: Maximum number of entries returned on one result page. By default the value is 100 entries. The page size can never be larger than 250 entries. Optional.", "pageToken": "string: Token specifying which result page to return. Optional.", "syncToken": "string: Token obtained from the nextSyncToken field returned on the last page of results from the previous list request. It makes the result of this list request contain only entries that have changed since then.\nIf the syncToken expires, the server will respond with a 410 GONE response code and the client should clear its storage and perform a full synchronization without any syncToken.\nLearn more about incremental synchronization.\nOptional. The default is to return all entries."}, "functionality": "Returns all user settings for the authenticated user.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Calendar API - settings.watch", "api_call": "service.settings().watch().execute()", "api_version": "v3", "api_arguments": {"maxResults": "integer: Maximum number of entries returned on one result page. By default the value is 100 entries. The page size can never be larger than 250 entries. Optional.", "pageToken": "string: Token specifying which result page to return. Optional.", "syncToken": "string: Token obtained from the nextSyncToken field returned on the last page of results from the previous list request. It makes the result of this list request contain only entries that have changed since then.\nIf the syncToken expires, the server will respond with a 410 GONE response code and the client should clear its storage and perform a full synchronization without any syncToken.\nLearn more about incremental synchronization.\nOptional. The default is to return all entries."}, "functionality": "Watch for changes to Settings resources.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/calendar/api/v3/reference"}}]