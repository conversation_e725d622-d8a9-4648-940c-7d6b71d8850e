[{"user_name": "felixzhu555", "api_name": "Google Maps - Address Validation", "api_call": "Client.addressvalidation(addressLines, regionCode=region_code, locality=locality, enableUspsCass=boolean)", "api_version": "4.10.0", "api_arguments": {}, "functionality": "Validate an address and its components, standardize the address for mailing, and determine the best known geocode for it.", "env_requirements": ["googlemaps"], "example_code": "client = googlemaps.Client(key='YOUR_API_KEY')\nresponse = client.addressvalidation('1600 Amphitheatre Pk', regionCode='US', locality='Mountain View', enableUspsCass=True)", "meta_data": {"description": "The googlemaps python client is an abstraction for the Google Maps API that requires python 3.5+. Each Google Maps web service request requires an API key or client ID. API keys are generated in the 'Credentials' page of the 'APIs & Services' tab of Google Cloud console. This key should be kept secret on your server."}, "questions": ["Is this address valid?", "I need the geocode information for an address.", "I want to get the most current and accurate information for an American address."]}, {"user_name": "felixzhu555", "api_name": "Google Maps - Directions", "api_call": "Client.directions(origin, destination, mode=transport_mode, waypoints=location_arr, alternatives=want_alts, avoid=avoid, language=language, units=units, region=region_code, departure_time=depart_time, arrival_time=arrive_time, optimize_waypoints=boolean, transit_mode=transit_mode, transit_routing_preference=routing_pref, traffic_model=traffic_model)", "api_version": "4.10.0", "api_arguments": {"mode": ["driving", "walking", "bicycling", "transit"], "units": ["metric", "imperial"], "transit_mode": ["bus", "subway", "train", "tram", "rail"], "transit_routing_preference": ["less_walking", "fewer_transfers"], "traffic_model": ["best_guess", "optimistic", "pessimistic"]}, "functionality": "Get directions between locations, for driving, cycling, transit, and walking.", "env_requirements": ["googlemaps"], "example_code": "client = googlemaps.Client(key='YOUR_API_KEY')\nresponse = client.directions('Soda Hall', 'Chase Center', mode='transit', alternatives=True, transit_mode=['bus', 'tram'], traffic_model='best_guess')", "meta_data": {"description": "The googlemaps python client is an abstraction for the Google Maps API that requires python 3.5+. Each Google Maps web service request requires an API key or client ID. API keys are generated in the 'Credentials' page of the 'APIs & Services' tab of Google Cloud console. This key should be kept secret on your server."}, "questions": ["I want to find the fastest way to get from Berkeley to Los Angeles.", "What public transit routes are there to get to work?", "How can I plan my walking route to pass by the park, grocery store, and library?"]}, {"user_name": "felixzhu555", "api_name": "Google Maps - Distance Matrix", "api_call": "Client.distance_matrix(origins, destinations, mode=transport_mode, language=language, avoid=avoid, units=units, departure_time=depart_time, arrival_time=arrive_time, transit_mode=transit_mode, transit_routing_preference=routing_pref, traffic_model=traffic_model, region=region_code)", "api_version": "4.10.0", "api_arguments": {"mode": ["driving", "walking", "bicycling", "transit"], "avoid": ["tolls", "highways", "ferries"], "units": ["metric", "imperial"], "transit_mode": ["bus", "subway", "train", "tram", "rail"], "transit_routing_preference": ["less_walking", "fewer_transfers"], "traffic_model": ["best_guess", "optimistic", "pessimistic"]}, "functionality": "Get the travel distance and time for a matrix of origins and destinations.", "env_requirements": ["googlemaps"], "example_code": "client = googlemaps.Client(key='YOUR_API_KEY')\nresponse = client.distance_matrix(['New York', 'Seattle'], ['San Francisco', 'Chicago'], mode='driving', avoid='tolls', traffic_model='optimistic')", "meta_data": {"description": "The googlemaps python client is an abstraction for the Google Maps API that requires python 3.5+. Each Google Maps web service request requires an API key or client ID. API keys are generated in the 'Credentials' page of the 'APIs & Services' tab of Google Cloud console. This key should be kept secret on your server."}, "questions": ["How long will it take to drive from San Francisco to Los Angeles?", "What is the distance between Yellowstone and Yosemite?", "How many miles will I have to walk in a full lap around the lake?"]}, {"user_name": "felixzhu555", "api_name": "Google Maps - Elevation", "api_call": "Client.elevation(locations_arr)", "api_version": "4.10.0", "api_arguments": {}, "functionality": "Get elevation data for a location on the earth, or sampled elevation data along paths.", "env_requirements": ["googlemaps"], "example_code": "client = googlemaps.Client(key='YOUR_API_KEY')\nresponse = client.elevation([(40.714728, -73.998672), (-34.397, 150.644)])", "meta_data": {"description": "The googlemaps python client is an abstraction for the Google Maps API that requires Python 3.5+. Each Google Maps web service request requires an API key or client ID. API keys are generated in the 'Credentials' page of the 'APIs & Services' tab of Google Cloud console. This key should be kept secret on your server."}, "questions": ["What is the elevation of Twin Peaks in San Francisco?", "How much elevation gain is there walking across the Berkeley campus?"]}, {"user_name": "felixzhu555", "api_name": "Google Maps - Geocoding", "api_call": "Client.geocode(address=str_address, place_id=id, components=comp_filter, bounds=bbox_dict, region=region_code, language=language)", "api_version": "4.10.0", "api_arguments": {}, "functionality": "Convert addresses or Place IDs to latitude/longitude coordinates and vice-versa.", "env_requirements": ["googlemaps"], "example_code": "client = googlemaps.Client(key='YOUR_API_KEY')\nres1 = client.geocode('San Antonio', components={'country': 'CL'}, bounds={'northeast': (-33.568623, -71.588242), 'southwest': (-33.614257, -71.629524)})\nres2 = client.reverse_geocode((37.423021, -122.083739), result_type='street_address')", "meta_data": {"description": "The googlemaps python client is an abstraction for the Google Maps API that requires python 3.5+. Each Google Maps web service request requires an API key or client ID. API keys are generated in the 'Credentials' page of the 'APIs & Services' tab of Google Cloud console. This key should be kept secret on your server."}, "questions": ["What are the map coordinates of the Statue of Liberty?", "What is located at latitude 37.423021 and longitude -122.083739?"]}, {"user_name": "felixzhu555", "api_name": "Google Maps - Geolocation", "api_call": "Client.geolocate(home_mobile_country_code=mcc, home_mobile_network_code=mnc, radio_type=radio_type, carrier=carrier_name, consider_ip=boolean, cell_towers=cell_tower_arr, wifi_access_points=access_point_arr)", "api_version": "4.10.0", "api_arguments": {"radio_type": ["lte", "gsm", "cdma", "wcdma"]}, "functionality": "Locate mobile devices that do not provide native geolocation features.", "env_requirements": ["googlemaps"], "example_code": "client = googlemaps.Client(key='YOUR_API_KEY')\nresponse = client.geolocate(radio_type='lte', carrier='T-Mobile')", "meta_data": {"description": "The googlemaps python client is an abstraction for the Google Maps API that requires python 3.5+. Each Google Maps web service request requires an API key or client ID. API keys are generated in the 'Credentials' page of the 'APIs & Services' tab of Google Cloud console. This key should be kept secret on your server."}, "questions": ["My phone's GPS does not work, how can I geolocate myself?", "I want a web service that can find locations of users without geolocation abilities on their device."]}, {"user_name": "felixzhu555", "api_name": "Google Maps - Time Zone", "api_call": "Client.timezone(location=coords, timestamp=timestamp, language=language)", "api_version": "4.10.0", "api_arguments": {}, "functionality": "Get time zone data for given map coordinates.", "env_requirements": ["googlemaps"], "example_code": "client = googlemaps.Client(key='YOUR_API_KEY')\nresponse = client.timezone((39.603481, -119.682251))", "meta_data": {"description": "The googlemaps python client is an abstraction for the Google Maps API that requires python 3.5+. Each Google Maps web service request requires an API key or client ID. API keys are generated in the 'Credentials' page of the 'APIs & Services' tab of Google Cloud console. This key should be kept secret on your server."}, "questions": ["Given some coordinates, how can I find the time zone of this location?", "I want to find the UTC and daylight savings offsets for a location in the middle of the US."]}, {"user_name": "felixzhu555", "api_name": "Google Maps - Roads", "api_call": "Client.nearest_roads(points=locations)", "api_version": "4.10.0", "api_arguments": {}, "functionality": "Identify the roads a vehicle is traveling along, and get metadata about those roads.", "env_requirements": ["googlemaps"], "example_code": "client = googlemaps.Client(key='YOUR_API_KEY')\ncoords = (40.714728, -73.998672)\nres1 = client.nearest_roads(coords)\nres2 = client.snap_to_roads(coords)\nres3 = client.snapped_speed_limits(coords)", "meta_data": {"description": "The googlemaps python client is an abstraction for the Google Maps API that requires python 3.5+. Each Google Maps web service request requires an API key or client ID. API keys are generated in the 'Credentials' page of the 'APIs & Services' tab of Google Cloud console. This key should be kept secret on your server."}, "questions": ["What are the roads closest to these specific coordinates?", "What is the speed limit for this particular road?", "I want to find the most likely roads a car drove on given these locations it passed through."]}, {"user_name": "felixzhu555", "api_name": "Google Maps - Places", "api_call": "Client.places(query=text_search, location=coords, radius=int_radius, min_price=zero_to_four, max_price=zero_to_four, open_now=is_open, type=str_type, region=region_code, page_token=token)", "api_version": "4.10.0", "api_arguments": {"min_price": [0, 1, 2, 3, 4], "max_price": [0, 1, 2, 3, 4]}, "functionality": "Search and get up-to-date information for over 200 million places.", "env_requirements": ["googlemaps"], "example_code": "client = googlemaps.Client(key='YOUR_API_KEY')\ncoords = (-33.86746, 151.207090)\nres1 = client.places('restaurant', location=coords, radius=1000, min_price=1, max_price=3, open_now=True, type='restaurant')\nres2 = client.find_place('restaurant', 'textquery', fields=['address_components', 'name', 'opening_hours', 'website', 'rating'])\nres3 = client.places_nearby(coords, radius=1000, keyword='restaurant', min_price=1, max_price=3, name='pizza', rank_by='distance')", "meta_data": {"description": "The googlemaps python client is an abstraction for the Google Maps API that requires python 3.5+. Each Google Maps web service request requires an API key or client ID. API keys are generated in the 'Credentials' page of the 'APIs & Services' tab of Google Cloud console. This key should be kept secret on your server."}, "questions": ["Give me all the expensive Italian restuarants within a 1 mile radius from my location.", "What are all the stores that close before 6pm?", "Where are the best barber shops nearby?"]}, {"user_name": "felixzhu555", "api_name": "Google Maps - Static Map", "api_call": "Client.static_marker(size=(width, height), center=some_location, zoom=zoom_int, scale=scale_int, format=img_format, maptype=maptype_str, language=language, region=region, markers=static_map_markers, path=static_map_path, visible=visible_locations, style=style)", "api_version": "4.10.0", "api_arguments": {"maptype": ["roadmap", "satellite", "hybrid", "terrain"]}, "functionality": "Get a simple, embeddable map image with minimal code.", "env_requirements": ["googlemaps"], "example_code": "from googlemaps import maps\nclient = googlemaps.Client(key='YOUR_API_KEY')\npath = maps.StaticMapPath(points=[(62.107733, -145.541936), 'Delta+Junction,AK'], weight=5, color='red',)\nm1 = maps.StaticMapMarker(locations=[(62.107733, -145.541936)], color='blue', label='S')\nm2 = maps.StaticMapMarker(locations=['Delta+Junction,AK'], size='tiny', color='green')\nm3 = maps.StaticMapMarker(locations=['Tok,AK'], size='mid', color='0xFFFF00', label='C')\nresponse = client.static_marker(size=(400, 400), zoom=6, center=(63.259591, -144.667969), maptype='hybrid', format='png', scale=2, visible=['Tok,AK'], path=path, markers=[m1, m2, m3])", "meta_data": {"description": "The googlemaps python client is an abstraction for the Google Maps API that requires python 3.5+. Each Google Maps web service request requires an API key or client ID. API keys are generated in the 'Credentials' page of the 'APIs & Services' tab of Google Cloud console. This key should be kept secret on your server."}, "questions": ["I want an image of the map of San Francisco with all the notable landmarks highlighted.", "How can I get a static image from Google maps?", "Create an image of the United States map with the capitals of each state marked with a blue dot."]}]