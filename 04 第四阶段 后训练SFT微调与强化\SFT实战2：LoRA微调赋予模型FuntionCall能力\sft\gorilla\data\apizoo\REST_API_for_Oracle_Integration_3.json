[{"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Connections: Retrieve Connections", "api_call": "GET /ic/api/integration/v1/connections", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Filters connections by name, status, and role.</p><p>Valid parameters:</p><ul><li>name: Name of the connection. Supports exact matches or contains. For example: <ul><li>To retrieve connections that are an exact match in the connection name, specify: <pre>q={name:'My Connection Name'}</pre>.</li><li>To retrieve all connections that contain the specified string in the connection name, specify: <pre>q={name: /Oracle/}</pre></li></ul></li><li>status: Status of connection. Valid values: CONFIGURED, INPROGRESS. INPROGRESS is displayed as Draft in the graphical user interface.<p>For example, to retrieve all connections that contain RN and have the status configured, specify:</p> <pre>{name : /RN/, status : 'CONFIGURED'}</pre></li><li>role: role of the connection. <p>Valid values: SOURCE, SOURCE_AND_TARGET, TARGET. Values are case-sensitive. In the graphical user interface: SOURCE is displayed as trigger, SOURCE_AND_TARGET is displayed as trigger and invoke, and TARGET is displayed as invoke.</p></li></ul>", "orderBy": "string: <p>Lists connections ordered by name, last updated time, or adapter type.</p><p>Valid values:</p><ul><li>name: Order connections by connection name. Example: orderBy=name.</li><li>time: Order connections by the last updated time. Example: orderBy=time.</li><li>type: Order connections by the adapter type which are PREINSTALLED, PRIVATE, or  MARKETPLACE. Example: orderBy=type</li></ul>", "expand": "string: Includes additional details about the adapter in the response. Valid value: adapter", "return": "string: API returns a minimal or full view of the response.", "offset": "integer: Use with the limit parameter for paginating through the returned results. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list connections starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list connections starting at the 4th item, and the list will contain 16 items.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves information about all connections ordered by the last updated time.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Connections: Retrieve a Connection", "api_call": "GET /ic/api/integration/v1/connections/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Connection identifier", "expand": "string: Includes additional details about the adapter in the response. Valid value: adapter", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves detailed information about the connection with the specified ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Connections: Update a Connection", "api_call": "POST /ic/api/integration/v1/connections/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Connection identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field.", "X-HTTP-Method-Override": "[REQUIRED] string: HTTP Method override in the header"}, "functionality": "<p>Updates the security properties, connection properties, agentGroupId, or securityPolicy of a connection. Other properties are ignored.</p><p>Supported security properties depend on the selected securityPolicy and the connection type. The connection type also determines the supported securityPolicy values.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Connections: Delete a Connection", "api_call": "DELETE /ic/api/integration/v1/connections/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Connection identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Deletes the connection with the specified ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Connections: Retrieve Connection Property Attachment", "api_call": "GET /ic/api/integration/v1/connections/{id}/attachments/{connPropName}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Connection identifier", "connPropName": "[REQUIRED] string: Connection property name. For example: targetWSDLURL", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Downloads the attachment associated with the specified connection property. This operation returns a byte stream that is output to the display by default. In cURL, use the -o option to specify the file in which to save the response.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Connections: Upload Connection Property Attachment", "api_call": "POST /ic/api/integration/v1/connections/{id}/attachments/{connPropName}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Connection identifier", "connPropName": "[REQUIRED] string: Connection property name. For example: targetWSDLURL", "serviceWSDL": "string: Path and name of the WSDL file in the zip file. For example: serviceWSDL=/path/source.wsdl", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Uploads an attachment (such as a WSDL file) to a connection and associates the attachment with a specific connection property. If you are using cURL, use the -F option to specify the file to upload.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Connections: Refresh Metadata for a Connection", "api_call": "POST /ic/api/integration/v1/connections/{id}/metadata", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Connection identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Refreshes the metadata of the adapter associated with the connection. Only applies to adapters that have implemented metadata caching.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Connections: Test a Connection", "api_call": "POST /ic/api/integration/v1/connections/{id}/test", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Connection identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "<p>Tests a connection by doing a ping test. Can be used to check the status of the connection.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Connections: Validate and Test a Connection", "api_call": "POST /ic/api/integration/v1/connections/{id}/testWithAttachments", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Connection identifier", "patchAttachments": "boolean: Indicates whether it is a partial or a full update of the attachments. Set patchAttachments=true to send in the request payload only attachments that have changed. Set patchAttachments=false to send in the request payload all attachments (changed and unchanged).", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field.", "X-HTTP-Method-Override": "[REQUIRED] string: HTTP Method override in the header", "action": "string: When action=validate performs a full validation of the WSDL, including processing of the imported schemas and WSDLs."}, "functionality": "Validates and tests the connection for certain adapters. For example, for the FTP adapter performs a full validation of the WSDL including imported schemas and WSDLs, and then tests the connection.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Connections: Validate a Connection", "api_call": "POST /ic/api/integration/v1/connections/{id}/validate", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Connection identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field.", "X-HTTP-Method-Override": "[REQUIRED] string: HTTP Method override in the header", "patchAttachments": "boolean: Param to determine whether this partial update of attachments case."}, "functionality": "Validates a connection.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Environment: Retrieve List of Domains Allowed for CORS Processing", "api_call": "GET /ic/api/integration/v1/environment/corsdomains", "api_version": "2024.02.14", "api_arguments": {"integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves the list of domains allowed for processing of cross-origin resource sharing (CORS) sites that are created and updated by the user.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Environment: Update the List of Allowed Domains for CORS Processing", "api_call": "PUT /ic/api/integration/v1/environment/corsdomains", "api_version": "2024.02.14", "api_arguments": {"integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "To modify the existing list of domains, use this API function to replace the existing list. Use the following format in the JSON payload: scheme, hostname, and port (optional). For example, \"corsDomains\": [\"https://abc.com\", \"http://xyz.com:123\"].", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Environment: Create a List of Domains Allowed for CORS Processing", "api_call": "POST /ic/api/integration/v1/environment/corsdomains", "api_version": "2024.02.14", "api_arguments": {"integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "<p>Creates a list of allowed domains for CORS processing. In the JSON payload, ensure that the list is in the following format: scheme, hostname, and port (optional). For example, \"corsDomains\": [\"https://abc.com\", \"http://xyz.com:123\"]. </p><p>Note: Oracle Integration applications allow authenticated access from Oracle domains like oracle.com, oraclecorp.com, oracleonsrcloud.com, oc.scloud, oci.scloud, oracleiaas.com, and oraclecloud.com. All domains belonging to arbitrary origins need to be registered in the allowed list of sites for cross-origin resource sharing (CORS) processing. Use this API only if you want to allow access from non-Oracle cloud domains. You need Administrator permissions to use CORS related API functions.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Environment: Delete the List of Domains Supported for CORS Processing", "api_call": "DELETE /ic/api/integration/v1/environment/corsdomains", "api_version": "2024.02.14", "api_arguments": {"integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Deletes all the allowed domains for CORS processing.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Event Types: Retrieve Event Types", "api_call": "GET /ic/api/integration/v1/eventTypes", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Filters event types by name and code.</p><p>Valid parameters:</p><ul><li>name: Name of the event type. Supports full match and exact matches. For example: <ul><li>To retrieve event type that is an exact match in the name, specify: <pre>q={name:'My Event Type'}</pre></li></ul></li> <li>code: Code of the event type. Supports full match and exact matches. For example: <ul><li>To retrieve event type that is an exact match in the code, specify: <pre>q={name:'My_Event_Type'}</pre></li></ul></li></ul>", "orderBy": "string: <p>Orders results by event type name or created time.</p><p>Valid values:</p><ul><li>name: Order event types by name in the ascending order. Example: orderBy=name.</li><li>time: Order event types by the created time in the descending order. Example: orderBy=time.</li></ul>", "next-page-token": "string: Use for paginating through the returned results. This is not a mandatory parameter. This parameter can be skipped in the first request. For the subsequent requests, obtain the value for the parameter from the response header named 'nextpagetoken' in the previous request.", "limit": "integer", "return": "string", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves information about all the event types ordered by the last updated time. Retrieves the latest revision of each event type.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Event Types: Create an Event Type", "api_call": "POST /ic/api/integration/v1/eventTypes", "api_version": "2024.02.14", "api_arguments": {"integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "<p>An event type describes the schema or structure of data contained in an event. This API function creates an event type. The request body must contain:</p> <ul><li>name: Defines the name of the event type. </li> <li>code: Defines the code that is used as the unique identifier for the event type.</li> <li>description: Describes the event type.</li> <li>schema type: The type of schema associated with the event type. For example: json</li> <li>schema: The schema associated with the event type.</li></ul> <p>If the resource creation is successful, the response header contains the Location header that includes the location URL for the new resource created.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Event Types: Delete an Event Type", "api_call": "DELETE /ic/api/integration/v1/eventTypes/{code}", "api_version": "2024.02.14", "api_arguments": {"code": "[REQUIRED] string: Event type code", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Deletes all the revisions of the event type with the specified code.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Event Types: Retrieve an Event Type", "api_call": "GET /ic/api/integration/v1/eventTypes/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Event type composite identifier. The ID consists of the code and the revision separated by the | (vertical line) character. Format: code%7Crevision. Example: MY_EVENT_TYPE%7C1. When using cURL, substitute the %7C code for the vertical line.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves detailed information about the event type with the specified code and revision.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Event Types: Clone an Event Type", "api_call": "POST /ic/api/integration/v1/eventTypes/{id}/clone", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Event type composite identifier. The ID consists of the code and the revision separated by the | (vertical line) character. Format: code%7Crevision. Example: MY_EVENT_TYPE%7C1. When using cURL, substitute the %7C code for the vertical line.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Clones the event type with the specified code and revision. <p>If the resource creation is successful, the response header contains the Location header that includes the location URL for the new resource created.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Event Types: Update an Event Type", "api_call": "POST /ic/api/integration/v1/eventTypes/{id}/update", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Event type composite identifier. The ID consists of the code and the revision separated by the | (vertical line) character. Format: code%7Crevision. Example: MY_EVENT_TYPE%7C1. When using cURL, substitute the %7C code for the vertical line.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "<p>Updates an event type with the code and revision specified in the request URL. The request body must contain:</p> <ul><li>name: Defines the name of the event type. </li> <li>code: Defines the code which is the unique identifier for the event type. </li> <li>description: Describes the event type.</li> <li>schema type: Type of schema associated with the event type. For example: json</li> <li>schema: Schema associated with the event type.</li></ul><p>If the resource creation is successful, the response header contains the Location header that includes the location URL for the new resource created.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations: Retrieve Integrations", "api_call": "GET /ic/api/integration/v1/integrations", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Filters integrations by integration name, status, type, and integration style.</p><p>Valid parameters:</p><ul><li>name: Name of the integration. Supports exact matches or contains. For example: <ul><li>To retrieve integrations that are an exact match in the integration name, specify: <pre>q={name:'My Integration Name'}</pre></li><li>To retrieve all integrations that contain the specified string in the integration name, specify: <pre>q={name: /Oracle/}</pre></li></ul></li><li>status: Status of the integration. Valid values: ACTIVATED, CONFIGURED, INPROGRESS, FAILEDACTIVATION.<p>For example, to retrieve all integrations that contain SC2RN and have the status configured, specify:</p> <pre>{name : /SC2RN/, status : 'CONFIGURED'}</pre></li><li>type: type of integration. <p>Valid values: PREBUILT, CUSTOM, DEVELOPED. </p></li><li>style: Style of the integration. Valid values: <ul><li><pre>freeform_mapmydata</pre> - Application-Driven Orchestrated Integration</li><li><pre>freeform_scheduled</pre> - Scheduled Orchestration Integration</li><li><pre>template_mapmydata</pre> - Basic Routing Map My Data Integration</li><li><pre>template_subscribetooic</pre> - Subscribe to OIC Integration</li><li><pre>template_publishtooic</pre> - Publish to OIC Integration</li></ul></li></ul>", "orderBy": "string: <p>Orders results by integration name or last updated time.</p><p>Valid values:</p><ul><li>name: Order integrations by integration name. Example: orderBy=name.</li><li>time: Order integrations by the last updated time. Example: orderBy=time.</li></ul>", "expand": "string: Includes additional details in the response about connections in the integration or about the adapters for the connections. Valid values: connection, connection.adapter.", "offset": "integer: Use with the limit parameter for paginating through the returned results. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list integrations starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list integrations starting at the 4th item, and the list will contain 16 items.", "return": "string: Return parameter", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves information about all integrations ordered by the last updated time.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations: Import(Replace) an Integration", "api_call": "PUT /ic/api/integration/v1/integrations/archive", "api_version": "2024.02.14", "api_arguments": {"includeRecordingFlag": "string: Include recording flag", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Updates an existing integration with the same name that was exported previously. If you are using cURL, use the -F option to specify the file to import. If this command returns with HTTP 404, use <a href=\"op-ic-api-integration-v1-integrations-archive-post.html\">Import(Add) an Integration</a> instead.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations: Import(Add) an Integration", "api_call": "POST /ic/api/integration/v1/integrations/archive", "api_version": "2024.02.14", "api_arguments": {"includeRecordingFlag": "string: Include recording flag", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "<p>Imports a new integration that was previously exported. To submit an import request, the integration must not be present in the environment. If the integration was imported earlier, use <a href=\"op-ic-api-integration-v1-integrations-archive-put.html\">Import(Replace) an Integration</a> instead.</p><p>Importing an integration with a different user name or version than what was exported is not supported.</p><p>If you are using cURL, use the -F option to specify the file to import.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations: Retrieve an Integration", "api_call": "GET /ic/api/integration/v1/integrations/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "expand": "string: Includes additional details in the response about connections in the integration or about the adapters for the connections. Valid values: connection, connection.adapter.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves detailed information about the integration with the specified ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations: Update(Activate/Deactivate) an Integration", "api_call": "POST /ic/api/integration/v1/integrations/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "X-HTTP-Method-Override": "string: X-HTTP-Method-Override request header", "deleteEventSubscriptionFlag": "string: Delete Event Subscription Flag", "enableAsyncActivationMode": "string: Activates/Deactivates an integration in asynchronous mode. The cURL request does not wait for the update process to finish and returns with success, while the update finishes in the background.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "<p>Activates or deactivates an integration. To modify an active integration, you need to deactivate it first.</p> <ul><li>Activate: Once you create an integration and it has the status CONFIGURED, you can activate the integration to the runtime environment.</p><p>Note that the version number matters when activating integrations:</p><ul><li> If integration XYZ/01.00.0000 is activated and you activate XZY/01.00.0001, XYZ/01.00.0000 will be automatically deactivated before XYZ/01.00.0001 is activated. You will have only one activated integration in this case (XZY/01.00.0001).  Automatic deactivation behaves the same as a manual deactivation.</li><li>If integration XYZ/01.00.0000 is currently activated and you activate XYZ/02.00.0000, you will have two activated integrations.</li></ul><li>Deactivate: You can deactivate an integration to stop it from processing any new messages. If there are pending requests unprocessed, they are lost after deactivation.</li></ul>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations: Delete an Integration Version", "api_call": "DELETE /ic/api/integration/v1/integrations/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Deletes the integration with the specified ID. Make sure the integration you want to delete is not active. To deactivate an integration, use <a href=\"op-ic-api-integration-v1-integrations-id-post.html\">Update(Activate/Deactivate) an Integration</a>.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations: Retrieve Integration Activation Errors", "api_call": "GET /ic/api/integration/v1/integrations/{id}/activationErrors", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves integration activation errors for the integration with the specified ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations: Retrieve Integration Activation Status", "api_call": "GET /ic/api/integration/v1/integrations/{id}/activationStatus", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves integration activation progress. Possible responses:<ul><li>CONFIGURED: Activation has not yet started</li><li>ACTIVATION_INPROGRESS: Activation is still in progress</li><li>ACTIVATED: Activation completed successfully</li><li>FAILEDACTIVATION: Activation failed.</li></ul>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations: Export an Integration", "api_call": "GET /ic/api/integration/v1/integrations/{id}/archive", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "includeRecordingFlag": "string: Include recording flag", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Exports an integration for use in other Oracle Integration environments to the location specified with the -o option.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations: Clone an Integration", "api_call": "POST /ic/api/integration/v1/integrations/{id}/clone", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Creates a new copy of an integration with identical connections and data mappings. The request body must contain at least the code, version, and name fields.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations/Scheduled Integrations: Retrieve a Schedule", "api_call": "GET /ic/api/integration/v1/integrations/{id}/schedule", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line."}, "functionality": "Retrieves schedule for a scheduled integration.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations/Scheduled Integrations: Update a Schedule", "api_call": "PUT /ic/api/integration/v1/integrations/{id}/schedule", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Updates the schedule for a scheduled orchestration.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations/Scheduled Integrations: Add a Schedule", "api_call": "POST /ic/api/integration/v1/integrations/{id}/schedule", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Adds a schedule to a scheduled orchestration.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations/Scheduled Integrations: Run a Scheduled Integration Now", "api_call": "POST /ic/api/integration/v1/integrations/{id}/schedule/jobs", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field.", "X-Oracle-IntegrationSuite-Parent-Ctx": "string"}, "functionality": "Creates an ad-hoc integration run for a scheduled integration. This is useful for when you want to test a scheduled integration.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations/Scheduled Integrations: Update Scheduled Integration Parameters", "api_call": "PATCH /ic/api/integration/v1/integrations/{id}/schedule/parameters", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Update scheduled integration parameters.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations/Scheduled Integrations: Pause an Integration Schedule", "api_call": "POST /ic/api/integration/v1/integrations/{id}/schedule/pause", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "async": "boolean: Pause the schedule in asynchronous manner. Link in the location header can be used to monitor the state of the schedule.", "retry": "boolean: Retry pause schedule if it's stuck in PAUSING state. Link in the location header can be used to monitor the state of the schedule.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Pauses an integration schedule. Existing runs are allowed to complete, but no new runs are submitted.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations/Scheduled Integrations: Resume an Integration Schedule", "api_call": "POST /ic/api/integration/v1/integrations/{id}/schedule/resume", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "async": "boolean: Resume the schedule in asynchronous manner. Link in the location header can be used to monitor the state of the schedule.", "retry": "boolean: Retry resume schedule if it's stuck in RESUMING state. Link in the location header can be used to monitor the state of the schedule.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Resumes an integration schedule.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations/Scheduled Integrations: Start an Integration Schedule", "api_call": "POST /ic/api/integration/v1/integrations/{id}/schedule/start", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "async": "boolean: Starts the schedule in asynchronous manner. Link in the location header can be used to monitor the state of the schedule.", "retry": "boolean: Retry start schedule if it's stuck in STARTING state. Link in the location header can be used to monitor the state of the schedule.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Start the integration schedule for the integration with the specified ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Integrations/Scheduled Integrations: Stop an Integration Schedule", "api_call": "POST /ic/api/integration/v1/integrations/{id}/schedule/stop", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "async": "boolean: Stop the schedule in asynchronous manner. Link in the location header can be used to monitor the state of the schedule.", "retry": "boolean: Retry stop schedule if it's stuck in STOPPING state. Link in the location header can be used to monitor the state of the schedule.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Stops an integration schedule. Cancels all existing runs and no new runs are submitted.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Libraries: Retrieve Libraries", "api_call": "GET /ic/api/integration/v1/libraries", "api_version": "2024.02.14", "api_arguments": {"q": "string: Filters libraries by name and type.<p>Valid parameters:</p><ul><li>name: Name of the the library. Supports contains only. For example: <ul><li>To retrieve all libraries that contain the specified string in the library name, specify: <pre>q={name: /library/}</pre></li></ul></li><li>type: Type of the library. Valid values: preinstalled, private. <p>For example, to retrieve all libraries that contain the string myLibrary and have the status preinstalled, specify:</p> <pre>q={name : /myLibrary/, type : 'preinstalled'}</pre></li></ul>", "orderBy": "string: <p>Lists libraries ordered by name or last updated time.</p><p>Valid value:</p><ul><li>name: Order libraries by library name. Example: orderBy=name.</li><li>time: Order libraries by the last updated time. Example: orderBy=time.</li></ul>", "offset": "integer: Use with the limit parameter for paginating through the returned results. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list libraries starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list libraries starting at the 4th item, and the list will contain 16 items.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves information about all libraries ordered by the last updated time.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Libraries: Import(Register) a Library", "api_call": "POST /ic/api/integration/v1/libraries/archive", "api_version": "2024.02.14", "api_arguments": {"integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Imports an API library jar file.  If you are using cURL, use the -F option to specify the file to import. The request must contain the following attributes added with the -F option: name, code, version, type, and file. Description is optional.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Libraries: Retrieve a Library", "api_call": "GET /ic/api/integration/v1/libraries/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Library identifier. The id must be added to the request in the following form: code%7Cversion. For example: JSLIB%7C01.00.0000. The %7C is the encoded | (vertical line).", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves detailed information about the library with the specified ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Libraries: Delete a Library Version", "api_call": "DELETE /ic/api/integration/v1/libraries/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Library identifier. The id must be added to the request in the following form: code%7Cversion. For example: JSLIB%7C01.00.0000. The %7C is the encoded | (vertical line).", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Deletes the library with the specified ID. The ID consists of the code and the version separated by the | (vertical line) character. When using cURL, substitute the %7C code for the vertical line.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Libraries: Export a Library", "api_call": "GET /ic/api/integration/v1/libraries/{id}/archive", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Library identifier. The id must be added to the request in the following form: code%7Cversion. For example: JSLIB%7C01.00.0000. The %7C is the encoded | (vertical line).", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Exports the library jar file containing the JS files to the location specified with the -o option. This operation returns a byte stream that is output to the display by default. If you are using cURL, use the -o option to save the response to a file.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Libraries: Update a Library", "api_call": "POST /ic/api/integration/v1/libraries/{id}/archive", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Library identifier. The id must be added to the request in the following form: code%7Cversion. For example: JSLIB%7C01.00.0000. The %7C is the encoded | (vertical line).", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Replace the existing jar/js file.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Libraries: Export Library Metadata (Deprecated)", "api_call": "GET /ic/api/integration/v1/libraries/{id}/metadata", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Library identifier. The id must be added to the request in the following form: code%7Cversion. For example: JSLIB%7C01.00.0000. The %7C is the encoded | (vertical line).", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Exports the metadata XML file for the library with the specified ID to a location specified with the -o option. This operation returns a byte stream that is output to the display by default. If you are using cURL, use the -o option to save the response to a file. Deprecated in Oracle Integration 3.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Libraries: Import Library Metadata (Deprecated)", "api_call": "POST /ic/api/integration/v1/libraries/{id}/metadata", "api_version": "2024.02.14", "api_arguments": {"integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Imports the metadata XML file for the library with the specified ID. If you are using cURL, use the -F option to specify the file to import. Deprecated in Oracle Integration 3.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Lookups: Retrieve Lookups", "api_call": "GET /ic/api/integration/v1/lookups", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Filters lookups by name and status.</p><p>Valid parameters:</p><ul><li>name: Name of the the lookup. Supports exact matches or contains. For example: <ul><li>To retrieve lookups that are an exact match in the lookup name, specify: <pre>q={name:'<PERSON>Look<PERSON>'}</pre></li><li>To retrieve all lookups that contain the specified string in the lookup name, specify: <pre>q={name: /MyLookup/}</pre></li></ul></li><li>status: Status of the lookup. Valid values: CONFIGURED, LOCKED. <p>For example, to retrieve all lookups that contain Lookup and have the status LOCKED, specify:</p> <pre>q={name : /Lookup/, status : 'LOCKED'}</pre></li></ul>", "orderBy": "string: <p>Lists lookups ordered by name.</p><p>Valid value:</p><ul><li>name: Order lookups by lookup name. Example: orderBy=name.</li></ul>", "expand": "string: Includes additional details in the response about the adapters used in the lookups. Valid value: adapter", "offset": "integer: Use with the limit parameter for paginating through the returned results. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list lookups starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list lookups starting at the 4th item, and the list will contain 16 items.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves information about all lookups ordered by the last updated time.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Lookups: Create a Lookup", "api_call": "POST /ic/api/integration/v1/lookups", "api_version": "2024.02.14", "api_arguments": {"integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "<p>Creates a lookup. The request body must contain:</p> <ul><li>name: defines the name of the lookup. For example: <pre>\"name\":\"myLookup\"</pre></li> <li>columns: defines the domains or adapters to map. The request body can contain two or more columns. For example: <pre>\"columns\":[\"rightnow\",\"soap\"]</pre></li><li>rowData: specifies the mappings to each adapter or domain. For example:<pre>\"rows\":[{\"rowData\":[\"RN1\",\"SOAP1\"]},{\"rowData\":[\"RN2\",\"SOAP2\"]}]</pre></li></ul>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Lookups: Import(Replace) a Lookup", "api_call": "PUT /ic/api/integration/v1/lookups/archive", "api_version": "2024.02.14", "api_arguments": {"integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Updates an existing lookup. If you are using cURL, use the -F option to specify the file to import. If this command returns with HTTP 404, use <a href=\"op-ic-api-integration-v1-lookups-archive-post.html\">Import(Add) a Lookup</a> instead.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Lookups: Import(Add) a Lookup", "api_call": "POST /ic/api/integration/v1/lookups/archive", "api_version": "2024.02.14", "api_arguments": {"integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Imports a new lookup file that was previously exported from another environment. To submit an import request, the lookup must not be present in the environment. If the lookup was imported earlier, use <a href=\"op-ic-api-integration-v1-lookups-archive-put.html\">Import(Replace) a Lookup</a> instead.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Lookups: Retrieve a Lookup", "api_call": "GET /ic/api/integration/v1/lookups/{name}", "api_version": "2024.02.14", "api_arguments": {"name": "[REQUIRED] string: Lookup name", "expand": "string: Expand the lookup resource by adding related resources.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves detailed information about the lookup with the specified name.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Lookups: Update a Lookup", "api_call": "PUT /ic/api/integration/v1/lookups/{name}", "api_version": "2024.02.14", "api_arguments": {"name": "[REQUIRED] string: Lookup name", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "<p>Updates a lookup with the name and values specified in the request body. The request body must contain:</p> <ul><li>name: defines the name of the lookup. For example: <pre>\"name\":\"myLookup\"</pre></li> <li>columns: defines the domains or adapters to map. The request body can contain two or more columns. For example: <pre>\"columns\":[\"rightnow\",\"soap\"]</pre></li><li>rowData: specifies the mappings to each adapter or domain. For example:<pre>\"rows\":[{\"rowData\":[\"RN1\",\"SOAP1\"]},{\"rowData\":[\"RN2\",\"SOAP2\"]}]</pre></li></ul>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Lookups: Delete a Lookup", "api_call": "DELETE /ic/api/integration/v1/lookups/{name}", "api_version": "2024.02.14", "api_arguments": {"name": "[REQUIRED] string: Lookup name", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Deletes the lookup with the specified name.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Lookups: Export a Lookup", "api_call": "GET /ic/api/integration/v1/lookups/{name}/archive", "api_version": "2024.02.14", "api_arguments": {"name": "[REQUIRED] string: Lookup name", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Exports a lookup. This operation returns a byte stream that is output to the display by default. If you are using cURL, use the -o option to save the response to a file.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Lookups: Clone a Lookup", "api_call": "POST /ic/api/integration/v1/lookups/{name}/clone", "api_version": "2024.02.14", "api_arguments": {"name": "[REQUIRED] string: Lookup name", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Clones the lookup with the specified name. The request must contain a JSON file with the new lookup's details attached with the -d option. The JSON file must contain the name attribute.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Lookups: Retrieve Usage of the Lookup", "api_call": "GET /ic/api/integration/v1/lookups/{name}/usage", "api_version": "2024.02.14", "api_arguments": {"name": "[REQUIRED] string: Lookup name", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Lists all the integrations which use the specified lookup.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve Status of Agent Groups", "api_call": "GET /ic/api/integration/v1/monitoring/agentgroups", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Filters results by agent group name.</p>\n<p>\n     <b>Example:</b>\n</p>\n<pre>{name:'Execution Agents Group'}</pre>", "orderBy": "string: <p>Orders results by name or last updated time.</p>\n<p>\n     <b>Examples:</b>\n</p>\n<pre>/agentgroups?orderBy=time</pre>\n<pre>/agentgroups?orderBy=name</pre>", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves monitoring data for all agent groups including current availability status.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve Status of an Agent Group", "api_call": "GET /ic/api/integration/v1/monitoring/agentgroups/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: <p>Agent group identifier.</p>\n<p>\n     <b>Example:</b>\n</p>\n<pre>agentgroups/EXECUTION_AGENT_GROUP</pre>", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves monitoring data including current availability status for the agent group with the specified ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve Status of Agents in an Agent Group", "api_call": "GET /ic/api/integration/v1/monitoring/agentgroups/{id}/agents", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: <p>Agent group identifier.</p>\n<p>\n     <b>Example:</b>\n</p>\n<pre>agentgroups/EXECUTION_AGENT_GROUP/agents</pre>", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves monitoring data for agents in the specified agent group including availability status.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve Status of an Agent", "api_call": "GET /ic/api/integration/v1/monitoring/agentgroups/{id}/agents/{key}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Agent group identifier.", "key": "[REQUIRED] string: Agent identifier.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves monitoring data including current availability status for an agent in the specified agent group.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve Audit Records", "api_call": "GET /ic/api/integration/v1/monitoring/auditRecords", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Filter parameters.</p>\n<p>\n   <ul>\n       <li>limit: Number of records to fetch. Default, 15 records are fetched.</li>\n       <li>code: Integration identifier.</li>\n   <li>version: Integration version.</li>\n  <li>timewindow: 1h, 6h, 1d, 2d, 3d, RETENTIONPERIOD. Default value is 1h.</li>\n  <li>type: '<string>' Exact or like match of resource type. Default, records of all types of resources are fetched.</li>\n   <li>distinct: '<string>' Boolean to indicate, fetch distinct records of a resource. Default, distinct is false.</li>\n   <li>Type values: ICS_AppInstance, ICS_DVM, ICS_ProjectV2, ICS_AgentDefinition, ICS_AgentInstance, ICS_Schedule, API_LIBRARY, ICS_PACKAGE.</li>\n       </ul>\n</p> \n<p>\n<b>Example:</b> \n</p> \n<pre>{limit: '10', type:/ICS_AppInstance/, distinct: 'true'}</pre>", "offset": "integer: Use with the limit parameter for paginating through the returned results. Default is 0. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list connections starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list error integration instances starting at the 4th item, and the list will contain 16 items.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves audit records based on query.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve Errored Instances", "api_call": "GET /ic/api/integration/v1/monitoring/errors", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Filter parameters.</p>\n<p>\n\t<ul>\n\t    <li>timewindow: 1h, 6h, 1d, 2d, 3d, RETENTIONPERIOD. Default value is 1h.</li>\n           <li>code: Integration identifier.</li>\n\t  <li>orderBy: Sorts by creation date and last updated date. Valid values are lastupdateddate and creationdate. Default value is lastupdateddate.</li> \n\t  <li>version: Integration version.</li> \n           <li>startdate: Custom time range start date/time in UTC format for filtering instances. You can select any date within the 32 days range of Oracle Integration Instance data retention period.</li>\n           <li>enddate:  Custom time range end date/time in UTC format for filtering instances. You can select any date within the 32 days range of Oracle Integration Instance data retention period.</li>\n\t    <li>id: Integration instance identifier.</li> \n   <li>instanceId: Integration instance identifier</li>\n        <li>primaryValue: Searches across primary variable values. To perform multi-word searches, encapsulate the search values in double quotation marks. To perform exact match searches, encapsulate the search values in square brackets followed by single quotation marks. For example, '[Test-instance1]', and '[Testinginstance2]'.</li> \n           <li>secondaryValue: Searches across secondary and tertiary variable values. To perform multi-word searches, encapsulate the search values in double quotation marks. To perform exact match searches, encapsulate the search values in square brackets followed by single quotation marks. For example, '[Test-instance1]', and '[Testinginstance2]'. To search for an exact secondary tracking variable value, use the secondaryName in addition to the secondaryValue.</li> \n\t    <li>tertiaryValue: Searches across secondary and tertiary variable values. To perform multi-word searches, encapsulate the search values in double quotation marks. To perform exact match searches, encapsulate the search values in square brackets followed by single quotation marks. For example, '[Test-instance1]', and '[Testinginstance2]'. To search for an exact tertiary tracking variable value, use the tertiaryName in addition to the tertiaryValue.</li> \n\t  <li>primaryName: Primary variable name.</li> \n\t   <li>secondaryName: Secondary variable name.</li> \n\t <li>tertiaryName: Tertiary variable name.</li> \n\t <li>businessIDValue: Searches across the primary, secondary and tertiary variables values. To perform multi-word searches, encapsulate the search values in double quotation marks followed by single quotation marks. For example, '\"uyt-atz\"'. To perform exact match searches, encapsulate the search values in square brackets followed by single quotation marks. For example, '[Test-instance1]', and '[Testinginstance2]'.</li> \n\t <li>recoverable: Indicates whether to fetch recoverable or non-recoverable instances. Valid values are true/false.</li> \n\t \n\t    <li>includePurged: Indicates whether to fetch purged instances. Valid values are yes, no, and onlyPurged.</li> \n <li>projectCode: Project identifier.</li>\n <li>connectionCode: Connection identifier.</li>\n <li>integration-style: Integration style identifier. Valid values are appdriven/scheduled.</li>\n\t</ul>\n</p> \n<p>\n\t<b>Example:</b> \n</p> \n<pre>{startdate:'2020-07-09 07:00:00', enddate:'2020-07-09 21:25:00', code:'ERROR', version:'01.00.0000', recoverable:'true', id:'118'}</pre> ", "groupBy": "string: <p>\n\t<ul>\n\t\t<li>Groups results by messages, integration name, or connection name. Valid values: messages, integration, connection.</li>\n\t</ul>\n<p>\n", "expand": "string: <p>\n\t<ul>\n\t\t<li>Includes additional details in the response about integrations, or about connections. Valid values: integration, connection.</li>\n\t</ul>\n</p> \n", "offset": "integer: Use with the limit parameter for paginating through the returned results. Default is 0. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list error instances starting at the 4th item, and the list will contain 16 items.", "return": "string: Controls the response data. Valid value is minimal. For example, return=minimal. When this value is specified, minimal information of the instance data is added in the response thus making the response time quicker than the default API call. In this response, the attributes integration.name and integrationDeleted are set to their default and should not be relied upon. This URL parameter is recommended if performance is preferred instead of the availability of complete instance data.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves information about all integration instances that have a errored status in the past hour, ordered by integration instance last updated time. Retrieves information such as integration instance identifiers, fault identifiers, integration details, error details and retry count. It includes connection information if the error occurred in the connection. You can perform multi-word value search using the businessIDValue, primaryValue, secondaryValue, and tertiaryValue attributes in query filter parameters. To perform multi-word searches, encapsulate the search values in double quotation marks followed by single quotation marks. Search includes error messages and detailed error messages. For example, '\"Test-instance1\"', and '\"Testing instance2\"'. You can also search primaryValue, secondaryValue, and tertiaryValue values against the specified primaryName, secondaryName, and tertiaryName variable names.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Discard Errored Integration Instances", "api_call": "POST /ic/api/integration/v1/monitoring/errors/discard", "api_version": "2024.02.14", "api_arguments": {"return": "string: Return Param to distinguish source of request", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Discards errored integration instances with the specified integration instance identifiers.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve Error Recovery Jobs", "api_call": "GET /ic/api/integration/v1/monitoring/errors/recoveryJobs", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Filter parameters.</p>\n<p>\n<ul>\n\t<li>timewindow: 1h, 6h, 1d, 2d, 3d, RETENTIONPERIOD. Default value is 1h.</li>\n       <li>startdate: Custom time range start date/time in UTC format for filtering instances.</li>\n\t<li>enddate: Custom time range end date/time in UTC format for filtering instances.</li> \n\t<li>primaryValue: Full or partial recovery job identifier.</li> \n</ul>\n</p> \n<p>\n\t<b>Example:</b> \n</p> \n<pre>{primaryValue:'4', startdate:'2020-07-15 07:00:00', enddate:'2020-07-08 21:10:00'}</pre>", "expand": "string: <p>\n\t<ul>\n\t\t<li>Includes additional details in the response about integrations, or about connections. Valid values: integration, connection.</li>\n\t</ul>\n</p> \n", "offset": "integer: Use with the limit parameter for paginating through the returned results. Default is 0. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list error instances starting at the 4th item, and the list will contain 16 items.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves a list of recovery jobs created when errored integration instances are resubmitted in a batch.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve an Error Recovery Job", "api_call": "GET /ic/api/integration/v1/monitoring/errors/recoveryJobs/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Recovery job identifier.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves an error recovery job based on the specified job identifier.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Resubmit Errored Integration Instances", "api_call": "POST /ic/api/integration/v1/monitoring/errors/resubmit", "api_version": "2024.02.14", "api_arguments": {"integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Resubmit multiple errored instances based on a list of given integration instance identifiers.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve an Errored Integration Instance", "api_call": "GET /ic/api/integration/v1/monitoring/errors/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Errored integration instance identifier.", "return": "string: Controls the response data. Valid value is minimal. For example, return=minimal. When this value is specified, minimal information of the instance data is added in the response thus making the response time quicker than the default API call. Response does not contain connection details like appTypeDisplayName, appTypeName, and appTypeVersion and in this response, the attributes integration.name and integrationDeleted are set to their default and should not be relied upon. This URL parameter is recommended if performance is preferred instead of the availability of complete instance data.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves details about the errored integration instance with the specified errored instance identifier.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Discard an Errored Integration Instance", "api_call": "POST /ic/api/integration/v1/monitoring/errors/{id}/discard", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Errored integration instance identifier.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Discard the errored integration instance with the specified errored instance identifier.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Resubmit an Errored Integration Instance", "api_call": "POST /ic/api/integration/v1/monitoring/errors/{id}/resubmit", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Errored instance identifier.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Resubmits the errored integration instance with the specified identifier. Additionally, to change the trace level of the resubmission, use the \"recoveryTraceLevel\" attribute in the request body. For example, \"recoveryTraceLevel\" : \"Audit\".", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve Historical Tracking Metrics", "api_call": "GET /ic/api/integration/v1/monitoring/history", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Filter parameter.</p>\n<p>\n<ul>\n    <li>timewindow: 1h, 6h, 1d, 2d, 3d, RETENTIONPERIOD.</li>\n    <li>startdate: Custom time range start date/time in UTC format for filtering metrics. If the start date is not mentioned and end date is specified, then it considers 30 days before the end date as the start date.</li>\n    <li>enddate:  Custom time range end date/time in UTC format for filtering metrics. If the end date is not mentioned and start date is specified, then it considers current date as the end date.</li>\n</ul>\n</p> \n", "frequency": "string: <p>Time frequency by which the metrics are to be grouped by. Valid values: hourly and daily.</p>\n<p>\n<b>Example:</b> \n<p>Retrieves hourly metrics for past 24 hours.</p> \n</p> \n<pre>/history?frequency=hourly</pre>\n<p>\n<p>Retrieves daily metrics for past 30 days.</p> \n</p> \n<pre>/history?frequency=daily</pre>", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "This API fetches historical tracking metrics. These are the metrics collected since the environment was created.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve Integration Instances", "api_call": "GET /ic/api/integration/v1/monitoring/instances", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Filter parameters.</p>\n<p>\n\t<ul>\n\t<li>timewindow: 1h, 6h, 1d, 2d, 3d, RETENTIONPERIOD. Default value is 1h.</li>\n  \t<li>code: Integration identifier.</li>\n <li>orderBy: Sorts by execution time, creation date, and last updated date. Valid values are lastupdateddate, creationdate, and executiontime. Default value is lastupdateddate.</li>\n     \t <li>version: Integration version.</li>\n   \t <li>minDuration: Retrieve instances that ran for a minimum duration specified in milliseconds.</li>\n  \t <li>maxDuration: Retrieve instances that ran for a maximum duration specified in milliseconds .</li>\n  <li>status: Status of the integration instance. Valid values are COMPLETED, FAILED and ABORTED.</li>\n       <li>startdate: Custom time range start date/time in UTC format for filtering instances. You can select any date within the 32 days range of Oracle Integration Instance data retention period.</li>\n       <li>enddate: Custom time range end date/time in UTC format for filtering instances. You can select any date within the 32 days range of Oracle Integration Instance data retention period.</li>\n       <li>primaryValue: Searches across primary variable values. To perform multi-word searches, encapsulate the search values in double quotation marks. To perform exact match searches, encapsulate the search values in square brackets followed by single quotation marks. For example, '[Test-instance1]', and '[Testinginstance2]'.</li>\n       <li>secondaryValue: Searches across secondary and tertiary variable values. To perform multi-word searches, encapsulate the search values in double quotation marks followed by single quotation marks. To perform exact match searches, encapsulate the search values in square brackets followed by single quotation marks. For example, '[Test-instance1]', and '[Testinginstance2]'. To search for an exact secondary tracking variable value, use the secondaryName in addition to the secondaryValue.</li>\n       <li>tertiaryValue: Searches across secondary and tertiary variable values. To perform multi-word searches, encapsulate the search values in double quotation marks followed by single quotation marks. To perform exact match searches, encapsulate the search values in square brackets followed by single quotation marks. For example, '[Test-instance1]', and '[Testinginstance2]'. To search for an exact tertiary tracking variable value, use the tertiaryName in addition to the tertiaryValue.</li>\n   <li>primaryName: Primary variable name.</li> \n\t   <li>secondaryName: Secondary variable name.</li> \n\t <li>tertiaryName: Tertiary variable name.</li> \n\t <li>businessIDValue: Searches across the primary, secondary and tertiary variables values. To perform multi-word searches, encapsulate the search values in double quotation marks followed by single quotation marks. For example, '\"uyt-atz\"'. To perform exact match searches, encapsulate the search values in square brackets followed by single quotation marks. For example, '[Test-instance1]', and '[Testinginstance2]'.</li> \n\t    <li>jobid: Recovery job identifier corresponding to bulk resubmission of errored integration instances.</li>\n       <li>runId: Run identifier of the scheduled integration instance.</li>\n       <li>requestId: Applies only to scheduled orchestrations. ID of the request that triggered the integration instance.</li>\n       <li>id: Integration instance identifier</li>\n \t <li>instanceId: Integration instance identifier</li>\n \t <li>includePurged: Indicates whether to fetch purged instances. Valid values are yes, no, and onlyPurged.</li>  \n <li>parentInstanceId: Integration instance identifier of a parent integration to search for locally invoked child integration instances.</li>\n <li>projectCode: Project identifier.</li>\n <li>integration-style: Integration style identifier. Valid values are appdriven/scheduled.</li>\n\t</ul>\n</p> \n<p>\n\t<b>Example:</b> \n</p> \n<pre>{timewindow:'1h', id:'118', status:'FAILED', code:'ERROR', version:'01.00.0000', requestId:'2432', jobid:'18'}</pre>", "groupBy": "string: <p>\n\t<ul>\n\t\t<li>Groups results by integration name. Valid values: integration.</li>\n\t</ul>\n<p>\n", "fields": "string: Limit query results to a few fields. Valid values are runId, id and all.", "offset": "integer: Use with the limit parameter for paginating through the returned results. The offset is the starting point from which to return records. Default value is 0. For example, offset=3&limit=16 indicates to list results at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list results starting at the 4th item, and the list will contain 16 items.", "return": "string: Controls the response data. Valid URL parameter values are:\n<p>\n\t<ul>\n\t<li>summary: Response does not contain non-primary tracking variables.</li>\n  \t<li>minimal: Response does not contain integration name and project name. When this value is specified, minimal information of the instance data is added in the response thus making the response time faster than the default API call. In this response, the attributes integrationName and projectFound are set to their default and should not be relied upon. This URL parameter is recommended if performance is preferred instead of the availability of complete instance data.</li>\n \t<li>metadata: Response contains metadata of the instance, and integration name and project name if available. Metadata comprises of details such as instanceId, integrationId, integrationVersion, and status.</li>\n  \t<li>metadataminimal: It's functionality is the same as metadata, however the response does not include integration name and project name.</li>\n", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieve information about integration instances for the past hour ordered by last updated time. You can perform multi-word value search using the businessIDValue, primaryValue, secondaryValue, and tertiaryValue attributes in query filter parameters. To perform multi-word searches, encapsulate the search values in double quotation marks followed by single quotation marks. For example, '\"Test-instance1\"', and '\"Testing instance2\"'. You can also search primaryValue, secondaryValue, and tertiaryValue values against the specified primaryName, secondaryName, and tertiaryName variable names.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve an Integration Instance", "api_call": "GET /ic/api/integration/v1/monitoring/instances/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration instance identifier.", "return": "string: Controls the response data. Valid value is minimal. For example, return=minimal. When this value is specified, minimal information of the instance data is added in the response thus making the response time quicker than the default API call. In this response, the attributes integrationName and projectFound are set to their default and should not be relied upon. This URL parameter is recommended if performance is preferred instead of the availability of complete instance data.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves detailed information about the integration instance with the specified instance ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Abort an Integration Instance", "api_call": "POST /ic/api/integration/v1/monitoring/instances/{id}/abort", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration instance identifier.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Abort a waiting or paused scheduled integration instance or any in-progress asynchronous integration instance with the specified identifier.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve Integration Instance Activity Stream (Deprecated)", "api_call": "GET /ic/api/integration/v1/monitoring/instances/{id}/activityStream", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: <p>Integration instance identifier.</p>\n<p>\n<b>Example:</b> \n</p> \n<pre>100001</pre>", "order": "string: <p>Order identifier.</p>\n<p>\n<b>Examples:</b> \n</p> \n<pre>asc, desc</pre>", "timezone": "string: <p>Timezone identifier.</p>\n<p>\n<b>Examples:</b> \n</p> \n<pre>America/New_York, Asia/Calcutta, Europe/London</pre>", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves activity stream data of an integration instance with specified identifier. The response includes the sequence of actions, invokes and error messages if any, along with their timestamps. It's recommended to not use this API but its alternative /ic/api/integration/v1/monitoring/instances/{id}/activityStreamDetails.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve Integration Instance Activity Stream Details", "api_call": "GET /ic/api/integration/v1/monitoring/instances/{id}/activityStreamDetails", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: <p>Integration instance identifier.</p>\n<p>\n<b>Example:</b> \n</p> \n<pre>XkDtR_wnEeypSFfBhM5b3Q</pre>", "timezone": "string: <p>Timezone identifier.</p>\n<p>\n<b>Examples:</b> \n</p> \n<pre>America/New_York, Asia/Calcutta, Europe/London</pre>", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves activity stream data of an integration instance with specified identifier. The response includes the sequence of actions, invokes and error messages if any, along with their timestamps.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Download Large Payload in an Integration Instance", "api_call": "GET /ic/api/integration/v1/monitoring/instances/{id}/activityStreamDetails/{+key}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: <p>Integration instance identifier.</p>\n<p>\n<b>Example:</b> \n</p> \n<pre>XkDtR_wnEeypSFfBhM5b3Q</pre>", "key": "[REQUIRED] string: <p>Large payload identifier.</p>\n<p>\n<b>Example:</b> \n</p> \n<pre>tracking-data/debug/X5YdCvwnEeypSFfBhM5b3Q</pre>", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Downloads a large payload (payload size is more than 32 KB) in an integration instance with a specified identifier. The response is a payload with the specified identifier.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve Monitoring Data for Integrations", "api_call": "GET /ic/api/integration/v1/monitoring/integrations", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Filter parameters.</p>\n<p>\n\t<ul>\n\t\t<li>timewindow: 1h, 6h, 1d, 2d, 3d, RETENTIONPERIOD. Default value is 1h.</li>\n               <li>status: CONFIGURED, ACTIVATION_INPROGRESS, FAILEDACTIVATION, ACTIVATED, ALL. Default value is ACTIVATED.</li>\n               <li>startdate: Custom time range start date/time in UTC format for filtering integrations.</li>\n               <li>enddate:  Custom time range end date/time in UTC format for filtering integrations.</li>\n\t\t<li>name: Integration name - Both complete and partial name search is supported.</li> \n\t\t<li>style: Style of integration\n\t\t\t<ul>\n\t\t\t\t<li>'freeform_mapmydata' - App Driven Orchestration </li>\n\t\t\t\t<li>'freeform_scheduled' - Scheduled Orchestration </li>\n\t\t\t\t<li>'template_mapmydata' - Basic Routing  </li>\n\t\t\t\t<li>'template_subscribetooic' - Subscribe to OIC </li>\n\t\t\t\t<li>'template_publishtooic' - Publish to OIC </li>\n\t\t\t</ul>\n\t\t</li> \n\t</ul>\n</p> \n<p>\n\t<b>Example:</b> \n</p> \n<pre>{timewindow:'2d', status:'CONFIGURED', style:'freeform_scheduled'}</pre>", "orderBy": "string: <p>Valid values:</p>\n<p>\n\t<ul>\n\t\t<li>'name': Results will be ordered by Integration name.</li>\n\t\t<li>'time': Results will be ordered by last updated time.</li>\n\t</ul>\n</p> \n<p>\n\t<b>Example:</b>\n\t<p>To order the records by name</p> \n</p>\n<pre>orderBy='name'</pre>", "offset": "integer: Start index for pagination support. Default value is 0.", "limit": "integer: Number of integrations to fetch.", "return": "string: Type of records to return.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves a list of integrations along with respective count for total, processed, succeeded, errored and aborted messages within the specified time window.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve Message Count Summary", "api_call": "GET /ic/api/integration/v1/monitoring/integrations/messages/summary", "api_version": "2024.02.14", "api_arguments": {"integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves summary of total, processed, succeeded, errored and aborted messages(instances) currently present in tracking runtime.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Monitoring: Retrieve Monitoring Data for an Integration", "api_call": "GET /ic/api/integration/v1/monitoring/integrations/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Integration identifier and version.", "q": "string: <p>Filter parameter.</p>\n<p>\n<ul>\n    <li>timewindow: 1h, 6h, 1d, 2d, 3d, RETENTIONPERIOD. Default value is 1h.</li>\n</ul>\n</p> \n", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves a monitoring integration with the specified identifier and version.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Packages: Retrieve Packages", "api_call": "GET /ic/api/integration/v1/packages", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Returns a filtered list of packages based on the specified parameters.</p><p>Valid parameters:</p><ul><li>name: Name of the package. Supports exact matches or contains. For example: <ul><li>To retrieve packages that are an exact match in the package name, specify: <pre>q={name:'PackageName'}</pre>.</li><li>To retrieve all packages that contain the specified string in the package name, specify: <pre>q={name: /pack/}</pre></li></ul></li><li>type: type of package. Valid values: PREBUILT, DEVELOPED.</li></ul>", "offset": "integer: Use with the limit parameter for paginating through the returned results. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list packages starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list packages starting at the 4th item, and the list will contain 16 items.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves information about all packages ordered by the last updated time.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Packages: Import(Replace) a Package", "api_call": "PUT /ic/api/integration/v1/packages/archive", "api_version": "2024.02.14", "api_arguments": {"includeRecordingFlag": "string: Include recording flag", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Updates an existing package with the same name that was exported previously. If you are using cURL, use the -F option to specify the file to import. If this command returns with HTTP 404, use <a href=\"op-ic-api-integration-v1-packages-archive-post.html\">Import(Add) a Package</a> instead.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Packages: Import(Add) a Package", "api_call": "POST /ic/api/integration/v1/packages/archive", "api_version": "2024.02.14", "api_arguments": {"includeRecordingFlag": "string: Include recording flag", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Imports a new package file. To submit an import request, the package must not be present in the environment. If the package was imported earlier, use <a href=\"op-ic-api-integration-v1-packages-archive-put.html\">Import(Replace) a Package</a> instead.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Packages: Import Sample Packages (Deprecated)", "api_call": "POST /ic/api/integration/v1/packages/loadSamples", "api_version": "2024.02.14", "api_arguments": {"integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Imports sample packages. If you already have sample integrations, getting new samples will overwrite existing integrations with the same name and version. If a sample integration is active or locked, it will not be overwritten. This API has been deprecated in Oracle Integration 3. Instead, use the Recipes and Accelerators page to Install the desired Recipes and Accelerators.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Packages: Retrieve a Package", "api_call": "GET /ic/api/integration/v1/packages/{packagename}", "api_version": "2024.02.14", "api_arguments": {"packagename": "[REQUIRED] string: Package name", "includeDependencies": "boolean: Include integration dependency information in the integration section of the response.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves detailed information about the package with the specified name.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Packages: Update Package Dependencies", "api_call": "POST /ic/api/integration/v1/packages/{packagename}", "api_version": "2024.02.14", "api_arguments": {"packagename": "[REQUIRED] string: Package name", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field.", "X-HTTP-Method-Override": "[REQUIRED] string: X-HTTP-Method-Override request header"}, "functionality": "Update a package by name.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Packages: Delete a Package", "api_call": "DELETE /ic/api/integration/v1/packages/{packagename}", "api_version": "2024.02.14", "api_arguments": {"packagename": "[REQUIRED] string: Package name", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Deletes a package.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Packages: Export a Package", "api_call": "GET /ic/api/integration/v1/packages/{packagename}/archive", "api_version": "2024.02.14", "api_arguments": {"packagename": "[REQUIRED] string: Package name", "includeRecordingFlag": "string: Include recording flag", "prebuilt": "boolean: Exports the package as prebuilt", "bartatype": "string: Accelerator type BA (Business Accelerator) | R (Recipe) | TA (Technical Accelerator)", "builtby": "string: Built By", "isView": "boolean: is View Allowed", "isClone": "boolean: is <PERSON><PERSON>owed", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Exports a package. This operation returns a byte stream that is output to the display by default. If you are using cURL, use the -o option to save the response to a file.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects: Retrieve Projects", "api_call": "GET /ic/api/integration/v1/projects", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Returns a filtered list of projects based on the specified parameters.</p><p>Valid parameters:</p><ul><li>name: Name of the project. Supports exact matches or contains. For example: <ul><li>To retrieve projects that are an exact match in the project name, specify: <pre>q={name:'ProjectName'}</pre>.</li><li>To retrieve all projects that contain the specified string in the project name, specify: <pre>q={name: /pack/}</pre></li></ul></li><li>type: type of project. Valid values: ACCELERATOR, DEVELOPED.</li></ul>", "orderBy": "string: <p>Orders results by project name or last updated time.</p><p>Valid values:</p><ul><li>name: Order projects by project name. Example: orderBy=name.</li><li>time: Order projects by the last updated time. Example: orderBy=time.</li></ul>", "offset": "integer: Use with the limit parameter for paginating through the returned results. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list projects starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list projects starting at the 4th item, and the list will contain 16 items.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves information about all projects ordered by the last updated time.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects: Import(Add) a Project", "api_call": "POST /ic/api/integration/v1/projects/archive", "api_version": "2024.02.14", "api_arguments": {"integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "<p>Imports a new project that was previously exported. To submit an import request, the project must not be present in the environment.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects: Retrieve a Project", "api_call": "GET /ic/api/integration/v1/projects/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Project identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves detailed information about the project.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects: Delete a Project", "api_call": "DELETE /ic/api/integration/v1/projects/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Project identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Deletes a project.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/RBAC: Retrieve Project RBAC Details", "api_call": "GET /ic/api/integration/v1/projects/{id}/acl", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Project identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves Role-Based Access Control information about the project.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/RBAC: Update Project RBAC Details", "api_call": "PUT /ic/api/integration/v1/projects/{id}/acl", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Project identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "<p>Updates Role-Based Access Control details of the project. The request body must contain:</p> <ul> <li>administrators: specifies the owners of the project. For example:  <pre>\"administrators\":{\"allowAll\":false,\"allowed\":[{\"id\":\"123456abcdef\",\"displayName\":\"test admin user\",\"type\":\"user\"},{\"id\":\"654321abcdef\",\"displayName\":\"ServiceAdministrator\",\"type\": \"group\"}]}</pre></li> <li>editors: specifies who can edit the project. For example:   <pre>\"editors\":{\"allowAll\":false,\"allowed\":[{\"id\":\"234561abcdef\",\"displayName\":\"test developer user\",\"type\":\"user\"},{\"id\":\"123456abcdef\",\"displayName\":\"test admin user\",\"type\": \"user\"}]}</pre></li> <li>viewers: specifies who can view the project. If everyone can view the project allowAll can be set to true and allowed list can be empty. For example: <pre>\"viewers\":{\"allowAll\":true,\"allowed\":[]}</pre></li> <li>monitors: specifies who can monitor the project. For example: <pre>\"monitors\":{\"allowAll\":true,\"allowed\":[]}</pre></li> </ul>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects: Export a Project", "api_call": "POST /ic/api/integration/v1/projects/{id}/archive", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Project identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Exports a project. This operation returns a byte stream that is output to the display by default. If you are using cURL, use the -o option to save the response to a file.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Connections: Retrieve Connections in a Project", "api_call": "GET /ic/api/integration/v1/projects/{projectId}/connections", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "q": "string: <p>Filters connections by name, status, and role.</p><p>Valid parameters:</p><ul><li>name: Name of the connection. Supports exact matches or contains. For example: <ul><li>To retrieve connections that are an exact match in the connection name, specify: <pre>q={name:'My Connection Name'}</pre>.</li><li>To retrieve all connections that contain the specified string in the connection name, specify: <pre>q={name: /Oracle/}</pre></li></ul></li><li>status: Status of connection. Valid values: CONFIGURED, INPROGRESS. INPROGRESS is displayed as Draft in the graphical user interface.<p>For example, to retrieve all connections that contain RN and have the status configured, specify:</p> <pre>{name : /RN/, status : 'CONFIGURED'}</pre></li><li>role: role of the connection. <p>Valid values: SOURCE, SOURCE_AND_TARGET, TARGET. Values are case-sensitive. In the graphical user interface: SOURCE is displayed as trigger, SOURCE_AND_TARGET is displayed as trigger and invoke, and TARGET is displayed as invoke.</p></li></ul>", "orderBy": "string: <p>Lists connections ordered by name, last updated time, or adapter type.</p><p>Valid values:</p><ul><li>name: Order connections by connection name. Example: orderBy=name.</li><li>time: Order connections by the last updated time. Example: orderBy=time.</li><li>type: Order connections by the adapter type which are PREINSTALLED, PRIVATE, or  MARKETPLACE. Example: orderBy=type</li></ul>", "offset": "integer: Use with the limit parameter for paginating through the returned results. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list connections starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list connections starting at the 4th item, and the list will contain 16 items.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves information about all connections in a project ordered by the last updated time.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Connections: Retrieve a Connection in a Project", "api_call": "GET /ic/api/integration/v1/projects/{projectId}/connections/{id}", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "id": "[REQUIRED] string: Connection identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves detailed information about the connection in a project.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Connections: Update a Connection in a Project", "api_call": "POST /ic/api/integration/v1/projects/{projectId}/connections/{id}", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "id": "[REQUIRED] string: Connection identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field.", "X-HTTP-Method-Override": "[REQUIRED] string: HTTP Method override in the header"}, "functionality": "<p>Updates the security properties, connection properties, agentGroupId, or securityPolicy of a connection in a project. Other properties are ignored.</p><p>Supported security properties depend on the selected securityPolicy and the connection type. The connection type also determines the supported securityPolicy values.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Connections: Test a Connection in a Project", "api_call": "POST /ic/api/integration/v1/projects/{projectId}/connections/{id}/test", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "id": "[REQUIRED] string: Connection identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "<p>Tests a connection in a project by doing a ping test. Can be used to check the status of the connection.</p>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Deployments: Retrieve Deployments in a Project", "api_call": "GET /ic/api/integration/v1/projects/{projectId}/deployments", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "offset": "integer: Use with the limit parameter for paginating through the returned results. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list deployments starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list deployments starting at the 4th item, and the list will contain 16 items.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves information about all the deployments in a project.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Deployments: Create a Deployment in a Project", "api_call": "POST /ic/api/integration/v1/projects/{projectId}/deployments", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "<p>Creates a deployment in a project. The request body must contain name and code.</p>  <ul><li>name: defines the name of the deployment.</li>  <li>code: defines the code which is the unique identifier for the deployment. </li>  <li>description: describes the deployment.</li>  <li>integrations: specifies the integrations associated with the deployment. For example:<pre>\"integrations\":[{\"code\":\"TEST_INT1\",\"version\":\"01.00.0000\"},  {\"code\":\"TEST_INT2\",\"version\":\"02.00.0000\"}]</pre></li></ul>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Deployments: Retrieve a Deployment in a Project", "api_call": "GET /ic/api/integration/v1/projects/{projectId}/deployments/{id}", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "id": "[REQUIRED] string: Deployment identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves detailed information about the deployment in a project.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Deployments: Update a Deployment in a Project", "api_call": "POST /ic/api/integration/v1/projects/{projectId}/deployments/{id}", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "id": "[REQUIRED] string: Deployment identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field.", "X-HTTP-Method-Override": "[REQUIRED] string: HTTP Method override in the header"}, "functionality": "<p>Updates a deployment using the ID and values specified in the request body. The request body must contain name and code.</p>  <ul><li>name: defines the name of the deployment.</li>  <li>code: defines the code which is the unique identifier for the deployment. </li>  <li>description: describes the deployment.</li>  <li>integrations: specifies the integrations associated with the deployment. For example:<pre>\"integrations\":[{\"code\":\"TEST_INT1\",\"version\":\"01.00.0000\"},    {\"code\":\"TEST_INT2\",\"version\":\"02.00.0000\"}]</pre></li></ul>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Deployments: Delete a Deployment in a Project", "api_call": "DELETE /ic/api/integration/v1/projects/{projectId}/deployments/{id}", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "id": "[REQUIRED] string: Deployment identifier", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Deletes the deployment with the specified ID in a project.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Integrations: Retrieve Integrations in a Project", "api_call": "GET /ic/api/integration/v1/projects/{projectId}/integrations", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "q": "string: <p>Filters integrations by integration name, status, type, and integration style.</p><p>Valid parameters:</p><ul><li>name: Name of the integration. Supports exact matches or contains. For example: <ul><li>To retrieve integrations that are an exact match in the integration name, specify: <pre>q={name:'My Integration Name'}</pre></li><li>To retrieve all integrations that contain the specified string in the integration name, specify: <pre>q={name: /Oracle/}</pre></li></ul></li><li>status: Status of the integration. Valid values: ACTIVATED, CONFIGURED, INPROGRESS, FAILEDACTIVATION.<p>For example, to retrieve all integrations that contain SC2RN and have the status configured, specify:</p> <pre>{name : /SC2RN/, status : 'CONFIGURED'}</pre></li><li>type: type of integration. <p>Valid values: PREBUILT, CUSTOM, DEVELOPED. </p></li><li>style: Style of the integration. Valid values: <ul><li><pre>freeform_mapmydata</pre> - Application-Driven Orchestrated Integration</li><li><pre>freeform_scheduled</pre> - Scheduled Orchestration Integration</li><li><pre>template_mapmydata</pre> - Basic Routing Map My Data Integration</li><li><pre>template_subscribetooic</pre> - Subscribe to OIC Integration</li><li><pre>template_publishtooic</pre> - Publish to OIC Integration</li></ul></li></ul>", "orderBy": "string: <p>Orders results by integration name or last updated time.</p><p>Valid values:</p><ul><li>name: Order integrations by integration name. Example: orderBy=name.</li><li>time: Order integrations by the last updated time. Example: orderBy=time.</li></ul>", "offset": "integer: Use with the limit parameter for paginating through the returned results. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list integrations starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list integrations starting at the 4th item, and the list will contain 16 items.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves information about all integrations in a project ordered by the last updated time.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Integrations: Retrieve an Integration in a Project", "api_call": "GET /ic/api/integration/v1/projects/{projectId}/integrations/{id}", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves detailed information about the integration in a project.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Integrations: Update(Activate/Deactivate) an Integration in a Project", "api_call": "POST /ic/api/integration/v1/projects/{projectId}/integrations/{id}", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "id": "[REQUIRED] string: Integration composite identifier. The ID consists of the code and the version separated by the | (vertical line) character. Format: code%7Cversion. Example: SC2RN%7C01.00.0000. When using cURL, substitute the %7C code for the vertical line.", "X-HTTP-Method-Override": "string: X-HTTP-Method-Override request header", "deleteEventSubscriptionFlag": "string: Delete Event Subscription Flag", "enableAsyncActivationMode": "string: Activates/Deactivates an integration in asynchronous mode. The cURL request does not wait for the update process to finish and returns with success, while the update finishes in the background.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "<p>Activates or deactivates an integration in a project. To modify an active integration, you need to deactivate it first.</p> <ul><li>Activate: Once you create an integration and it has the status CONFIGURED, you can activate the integration to the runtime environment.</p><p>Note that the version number matters when activating integrations:</p><ul><li> If integration XYZ/01.00.0000 is activated and you activate XZY/01.00.0001, XYZ/01.00.0000 will be automatically deactivated before XYZ/01.00.0001 is activated. You will have only one activated integration in this case (XZY/01.00.0001).  Automatic deactivation behaves the same as a manual deactivation.</li><li>If integration XYZ/01.00.0000 is currently activated and you activate XYZ/02.00.0000, you will have two activated integrations.</li></ul><li>Deactivate: You can deactivate an integration to stop it from processing any new messages. If there are pending requests unprocessed, they are lost after deactivation.</li></ul>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Lookups: Retrieve Lookups in a Project", "api_call": "GET /ic/api/integration/v1/projects/{projectId}/lookups", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "q": "string: <p>Filters lookups by name and status.</p><p>Valid parameters:</p><ul><li>name: Name of the the lookup. Supports exact matches or contains. For example: <ul><li>To retrieve lookups that are an exact match in the lookup name, specify: <pre>q={name:'<PERSON>Look<PERSON>'}</pre></li><li>To retrieve all lookups that contain the specified string in the lookup name, specify: <pre>q={name: /MyLookup/}</pre></li></ul></li><li>status: Status of the lookup. Valid values: CONFIGURED, LOCKED. <p>For example, to retrieve all lookups that contain Lookup and have the status LOCKED, specify:</p> <pre>q={name : /Lookup/, status : 'LOCKED'}</pre></li></ul>", "orderBy": "string: <p>Lists lookups ordered by name.</p><p>Valid value:</p><ul><li>name: Order lookups by lookup name. Example: orderBy=name.</li></ul>", "offset": "integer: Use with the limit parameter for paginating through the returned results. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list lookups starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list lookups starting at the 4th item, and the list will contain 16 items.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves information about all lookups in a project ordered by the last updated time.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Lookups: Retrieve a Lookup in a Project", "api_call": "GET /ic/api/integration/v1/projects/{projectId}/lookups/{name}", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "name": "[REQUIRED] string: Lookup name", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves detailed information about the lookup in a project.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Lookups: Update a Lookup in a Project", "api_call": "PUT /ic/api/integration/v1/projects/{projectId}/lookups/{name}", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "name": "[REQUIRED] string: Lookup name", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "<p>Updates a lookup in a project with the name and values specified in the request body. The request body must contain:</p> <ul><li>name: defines the name of the lookup. For example: <pre>\"name\":\"myLookup\"</pre></li> <li>columns: defines the domains or adapters to map. The request body can contain two or more columns. For example: <pre>\"columns\":[\"rightnow\",\"soap\"]</pre></li><li>rowData: specifies the mappings to each adapter or domain. For example:<pre>\"rows\":[{\"rowData\":[\"RN1\",\"SOAP1\"]},{\"rowData\":[\"RN2\",\"SOAP2\"]}]</pre></li></ul>", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Libraries: Retrieve Libraries in a Project", "api_call": "GET /ic/api/integration/v1/projects/{projectId}/libraries", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "q": "string: Filters libraries by name and type.<p>Valid parameters:</p><ul><li>name: Name of the the library. Supports contains only. For example: <ul><li>To retrieve all libraries that contain the specified string in the library name, specify: <pre>q={name: /library/}</pre></li></ul></li><li>type: Type of the library. Valid values: preinstalled, private. <p>For example, to retrieve all libraries that contain the string myLibrary and have the status preinstalled, specify:</p> <pre>q={name : /myLibrary/, type : 'preinstalled'}</pre></li></ul>", "orderBy": "string: <p>Lists libraries ordered by name or last updated time.</p><p>Valid value:</p><ul><li>name: Order libraries by library name. Example: orderBy=name.</li><li>time: Order libraries by the last updated time. Example: orderBy=time.</li></ul>", "offset": "integer: Use with the limit parameter for paginating through the returned results. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list libraries starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list libraries starting at the 4th item, and the list will contain 16 items.", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves information about all the libraries in a project ordered by the last updated time.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Libraries: Retrieve a Library in a Project", "api_call": "GET /ic/api/integration/v1/projects/{projectId}/libraries/{id}", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "id": "[REQUIRED] string: Library identifier. The id must be added to the request in the following form: code%7Cversion. For example: JSLIB%7C01.00.0000. The %7C is the encoded | (vertical line).", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves detailed information about the library in a project.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Projects/Libraries: Update a Library in a Project", "api_call": "POST /ic/api/integration/v1/projects/{projectId}/libraries/{id}/archive", "api_version": "2024.02.14", "api_arguments": {"projectId": "[REQUIRED] string: Project identifier", "id": "[REQUIRED] string: Library identifier. The id must be added to the request in the following form: code%7Cversion. For example: JSLIB%7C01.00.0000. The %7C is the encoded | (vertical line).", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Replaces the existing jar or js file of a library in a project.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Integrations/Usages: Retrieve Integration Usages", "api_call": "GET /ic/api/integration/v1/usage", "api_version": "2024.02.14", "api_arguments": {"fields": "string", "excludeFields": "string", "integrationInstance": "[REQUIRED] string: This is the name of the service instance. You can get this value from the About page where it is specified in the Service instance field."}, "functionality": "Retrieves information about integration resources and usages. This function provides usage metrics of domain value maps, application instances, integrations, integration designs, packages, messages, agent, adpaters, API libraries, message system, and schedules.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Clone Instance: Export Design-Time Metadata of a Service Instance", "api_call": "POST ic/api/common/v1/exportServiceInstanceArchive", "api_version": "2024.02.14", "api_arguments": {}, "functionality": "Exports an integration's design-time metadata to another integration instance. This operation asynchronously creates an archive.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Clone Instance: Retrieve the Status of an Export Operation", "api_call": "GET ic/api/common/v1/exportServiceInstanceArchive/{jobId}", "api_version": "2024.02.14", "api_arguments": {"jobId": "[REQUIRED] string"}, "functionality": "Retrieves the status of the export operation for a given job id.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Clone Instance: Import Design-Time Metadata of a Service Instance", "api_call": "POST ic/api/common/v1/importServiceInstanceArchive", "api_version": "2024.02.14", "api_arguments": {}, "functionality": "Imports an integration's design-time metadata to the current integration instance. This action asynchronously imports an archive.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - Clone Instance: Retrieve the Status of an Import Operation", "api_call": "GET ic/api/common/v1/importServiceInstanceArchive/{jobId}", "api_version": "2024.02.14", "api_arguments": {"jobId": "[REQUIRED] string"}, "functionality": "Retrieves the status of the import operation for a given job id.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Retrieve B2B Documents", "api_call": "GET /ic/api/b2b/v1/b2bdocuments", "api_version": "2024.02.14", "api_arguments": {}, "functionality": "Retrieves available B2B documents.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Create a B2B Document", "api_call": "POST /ic/api/b2b/v1/b2bdocuments", "api_version": "2024.02.14", "api_arguments": {}, "functionality": "Creates a B2B document with the specified ID, name, description, standard, txId and version. Examples of standard values are X12, EDIFACT. Examples of version for EDIFACT are D00A, D00B, etc. and examples for X12 are 4010. 4011, etc. Examples of txId (or transaction type) for EDIFACT are APERAK, AUTHOR, etc. and examples for X12 are 100, 850, etc.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Retrieve a B2B Document", "api_call": "GET /ic/api/b2b/v1/b2bdocuments/{documentId}", "api_version": "2024.02.14", "api_arguments": {"documentId": "[REQUIRED] string: <p>B2B document ID.</p>\n<p>\n <b>Example:</b> \n</p> \n<pre>/b2b/v1/b2bdocuments/DOC123</pre>"}, "functionality": "Retrieves detailed information about the B2B Document with the specified ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Update a B2B Document", "api_call": "PUT /ic/api/b2b/v1/b2bdocuments/{documentId}", "api_version": "2024.02.14", "api_arguments": {"documentId": "[REQUIRED] string: <p>B2B document ID.</p>\n<p>\n     <b>Example:</b> \n</p> \n<pre>/b2b/v1/b2bdocuments/DOC123</pre>"}, "functionality": "Updates a B2B Document with the specified document ID. To modify a document used by an active agreement, you need to deactivate it first.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Delete a B2B Document", "api_call": "DELETE /ic/api/b2b/v1/b2bdocuments/{documentId}", "api_version": "2024.02.14", "api_arguments": {"documentId": "[REQUIRED] string: B2B Document ID."}, "functionality": "Deletes the B2B Document with the specified ID. Ensure that the B2B Document you want to delete is not in use by any integration or trading partner. Before deleting it, remove any references to the document.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Clone a B2B Document", "api_call": "POST /ic/api/b2b/v1/b2bdocuments/{documentId}/clone", "api_version": "2024.02.14", "api_arguments": {"documentId": "[REQUIRED] string: <p>B2B document ID.</p>\n<p>\n     <b>Example:</b> \n</p> \n<pre>/b2b/v1/b2bdocuments/DOC123/clone</pre>"}, "functionality": "Creates a copy of a B2B document with identical metadata. The request body must contain at least the name and identifier fields.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Retrieve Integrations Using a B2B Document", "api_call": "GET /ic/api/b2b/v1/b2bdocuments/{documentId}/integrations", "api_version": "2024.02.14", "api_arguments": {"documentId": "[REQUIRED] string: <p>Integrations associated with b2b document.</p>\n<p>\n   <b>Example:</b> \n</p> \n<pre>/b2b/v1/b2bdocuments/DOC123/integrations</pre>"}, "functionality": "Retrieves available integrations that are using the specified B2B Document.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Retrieve Trading Partners using a B2B Document", "api_call": "GET /ic/api/b2b/v1/b2bdocuments/{documentId}/partners", "api_version": "2024.02.14", "api_arguments": {"documentId": "[REQUIRED] string: <p>Trading partners using the B2B document.</p>\n<p>\n     <b>Example:</b> \n</p> \n<pre>/b2b/v1/b2bdocuments/DOC123/partners</pre>"}, "functionality": "Retrieves the available trading partners that are using the specified B2B Document.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Create a B2B schema from an SEF File", "api_call": "POST /ic/api/b2b/v1/import", "api_version": "2024.02.14", "api_arguments": {}, "functionality": "Creates a B2B schema by importing a Standard Exchange Format (SEF) file. The SEF file format is used to exchange EDI implementation guidelines in a machine-readable format.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Monitoring: Retrieve Business Messages", "api_call": "GET /ic/api/b2b/v1/monitoring/businessmessages", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Filter parameter.</p>\n<p>\n\t<ul>\n\t     <li>timewindow: 1h, 6h, 1d, 2d, 3d, RETENTIONPERIOD. Default value is 1h.</li>\n       <li>direction: Message direction. Valid values are INBOUND and OUTBOUND.</li>\n\t     <li>id: Business message identifier.</li> \n       <li>startdate: Custom time range start date/time in UTC format for filtering messages.</li>\n       <li>enddate:  Custom time range end date/time in UTC format for filtering messages.</li>\n\t     <li>status: Message status.</li> \n       <li>instanceId: Integration instance identifier.</li> \n\t     <li>errorcode: Value of error code.</li> \n\t     <li>tradingpartnerid: Trading partner identifier.</li> \n\t     <li>messagetype: Message type. Valid values are MSG and FA.</li> \n\t     <li>userdefinedid: User defined Identifier for business message is the value for Application Message Identifier field for an outbound business message. This field is available for mapping in your outbound integration. If a value is set in the mapping, it is returned with the business message details.</li> \n\t     <li>documenttype: Document type.</li> \n\t     <li>documentstandard: Document standard.</li> \n\t     <li>documentversion: Document version.</li> \n\t     <li>identifiervalue: Value of business identifier.</li> \n\t     <li>parentmessageid: Value of functional acknowledgement identifier. This will be helpful to fetch all business messages correlated to a given functional acknowledgement identifier.</li> \n\t     <li>interchangecontrolnumber: Value of interchange control number.</li> \n\t     <li>groupcontrolnumber: Value of group control number.</li> \n\t     <li>transactioncontrolnumber: Value of transaction set control number.</li> \n\t</ul>\n</p> \n<p>\n\t<b>Example:</b> \n</p> \n<pre>{startdate:'2020-07-09 07:00:00', enddate:'2020-07-09 21:25:00', id:'0AC415941776251CB5E0000026D27C45', direction:'INBOUND', messagetype:'FA'}</pre> ", "offset": "integer: Use with the limit parameter for paginating through the returned results. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list business messages starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. Default value is 0. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list business messages starting at the 4th item, and the list will contain 16 items."}, "functionality": "Retrieves a list of business messages with information such as message type, state, direction and trading partner. Business messages represent individual documents or business transactions such as a purchase order.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Monitoring: Retrieve Business Message Details", "api_call": "GET /ic/api/b2b/v1/monitoring/businessmessages/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Business message identifier."}, "functionality": "Retrieves detailed information about the business message with the specified identifier. Retrieves information such as message type, state, direction and properties.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Monitoring: View Business Message Payload", "api_call": "GET /ic/api/b2b/v1/monitoring/businessmessages/{id}/payload", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Business message identifier.", "type": "string: Business message payload type.", "payloadId": "string: Payload ID."}, "functionality": "Displays the business message payload up to a maximum size of 500 KB. To view the complete content, use the Download B2B Business Message Payload API function to download the complete payload, and then view it.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Monitoring: Download B2B Business Message Payload", "api_call": "GET /ic/api/b2b/v1/monitoring/businessmessages/{id}/payload/download", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Business message identifier.", "type": "string: Message payload type.", "payloadId": "string: Payload identifier."}, "functionality": "Downloads the payload file of the B2B business message using the message identifier.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Monitoring: Resubmit a Business Message", "api_call": "POST /ic/api/b2b/v1/monitoring/businessmessages/{id}/resubmit", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Business message identifier."}, "functionality": "Resubmits a B2B business message using the message identifier. If you are resubmitting an outbound business message then instanceId is required in ResubmitReq payload in the body. instanceId is the integration instance ID of the outbound backend integration which processed the business message you want to resubmit and is available in correlations of the B2B business message.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Monitoring: Retrieve Wire Messages", "api_call": "GET /ic/api/b2b/v1/monitoring/wiremessages", "api_version": "2024.02.14", "api_arguments": {"q": "string: <p>Filter parameters.</p>\n<p>\n\t<ul>\n\t     <li>timewindow: 1h, 6h, 1d, 2d, 3d, RETENTIONPERIOD. Default value is 1h.</li>\n       <li>direction: Message direction. Valid values are INBOUND and OUTBOUND.</li>\n\t     <li>id: Wire message identifier.</li> \n       <li>startdate: Custom time range start date/time in UTC format for filtering messages.</li>\n       <li>enddate:  Custom time range end date/time in UTC format for filtering messages.</li>\n\t     <li>status: Message status.</li> \n       <li>transportprotocol: Message transport protocol. Valid values are AS2 and FTP.</li> \n       <li>instanceId: Integration instance identifier.</li> \n\t     <li>errorcode: Value of b2b error code.</li> \n\t     <li>tradingpartnerid: Trading partner identifier.</li> \n\t     <li>messagetype: Message type. Valid values are WIRE and MDN.</li> \n\t     <li>userdefinedid: A message identifier at the wire protocol level, if one is defined for the transport type. For AS2 transport, this is set to the AS2 message Identifier. For FTP transport this field is not available.</li> \n\t     <li>parentmessageid: Value of wire message identifier. This helps in fetching all acknowledgements correlated to a given wire message identifier.</li> \n\t</ul>\n</p> \n<p>\n\t<b>Example:</b> \n</p> \n<pre>{startdate:'2020-07-09 07:00:00', enddate:'2020-07-09 21:25:00', id:'0AC415941776251CB5E0000026D27C45', direction:'INBOUND', messagetype:'WIRE'}</pre> ", "offset": "integer: Use with the limit parameter for paginating through the returned results. The offset is the starting point from which to return records. For example, offset=3&limit=16 indicates to list wire messages starting at the 4th item, and the list will contain 16 items.", "limit": "integer: Use with the offset parameter for paginating through the returned results. Default value is 0. The limit is the maximum number of items to return at one time. For example, offset=3&limit=16 indicates to list wire messages starting at the 4th item, and the list will contain 16 items."}, "functionality": "Retrieves a list of wire messages with information like message type, state, direction, protocol and trading partner. Wire messages represent the technical exchange of messages between the host company and trading partners. These messages can be encrypted and signed AS2 message.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Monitoring: Retrieve Wire Message Details", "api_call": "GET /ic/api/b2b/v1/monitoring/wiremessages/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Wire message identifier."}, "functionality": "Retrieves detailed information about the wire message with the specified identifier. Retrieves information such as message type, state, direction, protocol and properties.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Monitoring: Retrieve the Wire Message Payload", "api_call": "GET /ic/api/b2b/v1/monitoring/wiremessages/{id}/payload", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Message identifier.", "payloadId": "string: Payload identifier."}, "functionality": "Displays the wire message payload up to a maximum size of 500 KB. To view the complete content, use the Download B2B Wire Message Payload API function to download the complete payload, and then view it.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Monitoring: Download B2B Wire Message Payload", "api_call": "GET /ic/api/b2b/v1/monitoring/wiremessages/{id}/payload/download", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Message identifier.", "type": "string: Message payload type. Valid values are packed and unpacked.", "payloadId": "string: Payload identifier."}, "functionality": "Retrieves the payload file for a given B2B wire message identifier.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Monitoring: Resubmit a Wire Message", "api_call": "POST /ic/api/b2b/v1/monitoring/wiremessages/{id}/resubmit", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: Wire message identifier."}, "functionality": "Resubmits a B2B wire message using the message identifier. If you are resubmitting an inbound wire message then instanceId is required in ResubmitReq payload in the body. instanceId is the integration instance ID of the transport's auto-generated inbound receive integration which processed the wire message you want to resubmit and is available in correlations of the B2B wire message.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Retrieve B2B Schemas", "api_call": "GET /ic/api/b2b/v1/schemas", "api_version": "2024.02.14", "api_arguments": {}, "functionality": "Retrieves all the available B2B schemas.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Create a B2B Schema", "api_call": "POST /ic/api/b2b/v1/schemas", "api_version": "2024.02.14", "api_arguments": {}, "functionality": "Creates a B2B schema with the specified ID, name, description, standard, txId and version. Examples of standard values are X12, EDIFACT etc. Examples of version for EDIFACT are D00A, D00B, etc. and the examples for X12 are 4010, 4011, etc. Examples of txId (or transaction type) for EDIFACT are APERAK, AUTHOR, etc. and the examples for X12 are 100, 850, etc.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Retrieve a B2B Schema", "api_call": "GET /ic/api/b2b/v1/schemas/{schemaId}", "api_version": "2024.02.14", "api_arguments": {"schemaId": "[REQUIRED] string: B2B schema ID.", "standard": "[REQUIRED] string: Document standard.", "version": "[REQUIRED] string: Document version.", "transactionId": "[REQUIRED] string: <p>Document transaction ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/schemas/SCH2</pre>"}, "functionality": "Retrieves detailed information about the B2B Schema with the specified ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Update a B2B Schema", "api_call": "PUT /ic/api/b2b/v1/schemas/{schemaId}", "api_version": "2024.02.14", "api_arguments": {"schemaId": "[REQUIRED] string: <p>B2B schema ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/schemas/SCH123</pre>"}, "functionality": "Updates a B2B schema with the specified schema ID. Before you modify a schema used by an active agreement, deactivate it.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Delete a B2B Schema", "api_call": "DELETE /ic/api/b2b/v1/schemas/{schemaId}", "api_version": "2024.02.14", "api_arguments": {"schemaId": "[REQUIRED] string: <p>Delete B2B schema.</p>\n<p>\n<b>Example:</b> \n</p> \n<pre>/b2b/v1/b2bdocuments/SCH123</pre>"}, "functionality": "Deletes the B2B Schema with the specified ID. Ensure that the B2B schema that you want to delete is not in use by any B2B document. Before deleting the B2B schema, remove any references to it.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Clone a B2B Schema", "api_call": "POST /ic/api/b2b/v1/schemas/{schemaId}/clone", "api_version": "2024.02.14", "api_arguments": {"schemaId": "[REQUIRED] string: <p>B2B schema ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/schemas/SCH123/clone</pre>"}, "functionality": "Creates a copy of a B2B schema with identical metadata. The request body must contain at least the name and identifier fields.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Retrieve Documents for a B2B Schema", "api_call": "GET /ic/api/b2b/v1/schemas/{schemaId}/documents", "api_version": "2024.02.14", "api_arguments": {"schemaId": "[REQUIRED] string: <p>Schema ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/schemas/SCH123/documents</pre>"}, "functionality": "Retrieves documents associated with the B2B schema.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Generate a Schema Implementation Guide", "api_call": "GET /ic/api/b2b/v1/schemas/{schemaId}/guide", "api_version": "2024.02.14", "api_arguments": {"schemaId": "[REQUIRED] string: B2B schema ID.", "format": "[REQUIRED] string: Document format.", "standardId": "[REQUIRED] string: Document standard ID.", "versionId": "[REQUIRED] string: Document version ID.", "transactionId": "[REQUIRED] string: <p>Document transaction ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/schemas/STANDARD/guide?format=html&standardId=X12&versionId=4010&transactionId=100</pre>"}, "functionality": "Generates implementation guide for the schema.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Retrieve an Element's Code List", "api_call": "GET /ic/api/b2b/v1/schemas/{schemaId}/{elementId}/codes", "api_version": "2024.02.14", "api_arguments": {"schemaId": "[REQUIRED] string: B2B schema ID.", "standardId": "[REQUIRED] string: Document standard ID.", "versionId": "[REQUIRED] string: Document version ID.", "transactionId": "[REQUIRED] string: Document transaction ID.", "elementId": "[REQUIRED] string: <p>Element ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/schemas/SCH2/92/codes</pre>"}, "functionality": "Retrieves the code list of an element using the element ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Retrieve B2B Document Standards", "api_call": "GET /ic/api/b2b/v1/standards", "api_version": "2024.02.14", "api_arguments": {}, "functionality": "Retrieves the available document standards supported by B2B.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Retrieve Document Versions", "api_call": "GET /ic/api/b2b/v1/standards/{standardId}", "api_version": "2024.02.14", "api_arguments": {"standardId": "[REQUIRED] string: <p>Standard ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/standards/X12</pre>"}, "functionality": "Retrieves available document versions with the specified document standard.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Retrieve Document Transactions", "api_call": "GET /ic/api/b2b/v1/standards/{standardId}/{versionId}", "api_version": "2024.02.14", "api_arguments": {"standardId": "[REQUIRED] string: Standard ID.", "versionId": "[REQUIRED] string: <p>Version ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/standards/X12/4010</pre>"}, "functionality": "Retrieves available document transactions with the specified document standard and version.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Document Customization: Retrieve B2B Document Types", "api_call": "GET /ic/api/b2b/v1/standards/{standardId}/{versionId}/{transactionId}", "api_version": "2024.02.14", "api_arguments": {"standardId": "[REQUIRED] string: B2B document standard ID.", "versionId": "[REQUIRED] string: Document version ID.", "transactionId": "[REQUIRED] string: Document transaction ID.", "isDocument": "[REQUIRED] boolean: <p>isDocument Flag</p>\n<p>\n      <b>Example:</b> \n</p> \n<pre>/b2b/v1/standards/X12/4010/100?isDocument=true</pre>"}, "functionality": "Retrieve all B2B document types.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Retrieve the Host Profile", "api_call": "GET /ic/api/b2b/v1/tpm/host", "api_version": "2024.02.14", "api_arguments": {}, "functionality": "Retrieves the host company name and host Identifiers.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Create/Update Host Profile", "api_call": "POST /ic/api/b2b/v1/tpm/host", "api_version": "2024.02.14", "api_arguments": {}, "functionality": "Add new or update existing host identifier with the specified host company name.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Delete a Host Identifier", "api_call": "DELETE /ic/api/b2b/v1/tpm/host/{id}", "api_version": "2024.02.14", "api_arguments": {"id": "[REQUIRED] string: <p>Host identifier ID.</p>\n<p>\n     <b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/host/11678035855650825</pre>"}, "functionality": "Deletes a host identifier only when there are no associations with agreements and transports.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Retrieve Trading Partners", "api_call": "GET /ic/api/b2b/v1/tpm/partners", "api_version": "2024.02.14", "api_arguments": {}, "functionality": "Retrieve all available trading partners.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Create a Trading Partner", "api_call": "POST /ic/api/b2b/v1/tpm/partners", "api_version": "2024.02.14", "api_arguments": {}, "functionality": "Creates a trading partner with the specified name and description.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Import a Trading Partner", "api_call": "POST /ic/api/b2b/v1/tpm/partners/import", "api_version": "2024.02.14", "api_arguments": {"isOverwrite": "[REQUIRED] boolean: <p>Overwrite flag to overwrite components in the zip file. Default value is false.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>ic/api/b2b/v1/tpm/partners/import?isOverwrite=true</pre>"}, "functionality": "Imports a trading partner with the associated agreements, documents, schemas, and host identifiers from the previously exported trading partner zip file.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Retrieve a Trading Partner", "api_call": "GET /ic/api/b2b/v1/tpm/partners/{tpId}", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: <p>Trading partner ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123</pre>"}, "functionality": "Retrieves a trading partner with the specified trading partner ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Update a Trading Partner", "api_call": "PUT /ic/api/b2b/v1/tpm/partners/{tpId}", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID."}, "functionality": "Updates a trading partner with the specified trading partner ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Delete a Trading Partner", "api_call": "DELETE /ic/api/b2b/v1/tpm/partners/{tpId}", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID."}, "functionality": "Deletes a trading partner with the specified trading partner ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Retrieve Trading Partner Contact Details", "api_call": "GET /ic/api/b2b/v1/tpm/partners/{tpId}/contacts", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: <p>Trading partner ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/contacts</pre>"}, "functionality": "Retrieves trading partner contact details with the specified trading partner ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Create/Update Trading Partner Contacts", "api_call": "POST /ic/api/b2b/v1/tpm/partners/{tpId}/contacts", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID."}, "functionality": "Adds a new contact or update an existing contact details with the specified trading partner ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Retrieve Control Numbers of a Trading Partner", "api_call": "GET /ic/api/b2b/v1/tpm/partners/{tpId}/controlNumbers", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID."}, "functionality": "Gets the control numbers of a trading partner.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Set Control Numbers for a Trading Partner", "api_call": "PUT /ic/api/b2b/v1/tpm/partners/{tpId}/controlNumbers", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID."}, "functionality": "Sets the control numbers for a trading partner.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Export a Trading Partner", "api_call": "GET /ic/api/b2b/v1/tpm/partners/{tpId}/export", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: <p>Trading partner ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP1/export</pre>"}, "functionality": "Exports a trading partner with the associated agreements, documents, schemas and host identifiers as a zipped file.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Retrieve B2B Identifiers", "api_call": "GET /ic/api/b2b/v1/tpm/partners/{tpId}/identifiers", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: <p>Trading partner ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/identifiers</pre>"}, "functionality": "Retrieves all available B2B identifiers with the specified trading partner ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Create/Update B2B Identifiers", "api_call": "POST /ic/api/b2b/v1/tpm/partners/{tpId}/identifiers", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID."}, "functionality": "Add a new or update existing B2B identifier with the specified trading partner ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Delete a B2B Identifier", "api_call": "DELETE /ic/api/b2b/v1/tpm/partners/{tpId}/identifiers/{identifierId}", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID.", "identifierId": "[REQUIRED] string: <p>Trading partner ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/identifiers/1990020336714597</pre>"}, "functionality": "Deletes a B2B identifier only when there are no associations with agreements and transports.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Retrieve Inbound Agreements", "api_call": "GET /ic/api/b2b/v1/tpm/partners/{tpId}/inboundagrs", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID."}, "functionality": "Retrieves all inbound agreements with the specified trading partner ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Create an Inbound Agreement", "api_call": "POST /ic/api/b2b/v1/tpm/partners/{tpId}/inboundagrs", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID."}, "functionality": "Creates an inbound agreement for trading partner. The request body must contain atleast name and docId fields.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Retrieve an Inbound Agreement", "api_call": "GET /ic/api/b2b/v1/tpm/partners/{tpId}/inboundagrs/{agrId}", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID.", "agrId": "[REQUIRED] string: <p>Trading partner inbound agreement ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/inboundagrs/INAGR1604824600539</pre>"}, "functionality": "Retrieves an inbound agreement with the specified trading partner ID and inbound agreement ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Update an Inbound Agreement", "api_call": "PUT /ic/api/b2b/v1/tpm/partners/{tpId}/inboundagrs/{agrId}", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID.", "agrId": "[REQUIRED] string: Inbound agreement ID."}, "functionality": "Updates an inbound agreement with the specified trading partner ID and inbound agreement ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Delete an Inbound Agreement", "api_call": "DELETE /ic/api/b2b/v1/tpm/partners/{tpId}/inboundagrs/{agrId}", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID.", "agrId": "[REQUIRED] string: Trading partner inbound agreement ID."}, "functionality": "Deletes an inbound agreement with the specified trading partner ID and inbound agreement ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Deploy an Inbound Agreement", "api_call": "POST /ic/api/b2b/v1/tpm/partners/{tpId}/inboundagrs/{agrId}/deploy", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: <p>Trading partner ID.</p>\n", "agrId": "[REQUIRED] string: <p>Inbound agreement ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/inboundagrs/INAGR12333344/deploy</pre>", "isProceedWithWarnings": "[REQUIRED] boolean"}, "functionality": "Deploys an inbound agreement with active backend integration and B2B document linked to it. The deployed agreements translates and processes messages.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Undeploy an Inbound Agreement", "api_call": "POST /ic/api/b2b/v1/tpm/partners/{tpId}/inboundagrs/{agrId}/undeploy", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: <p>Trading partner ID.</p>\n", "agrId": "[REQUIRED] string: <p>Inbound agreement ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/inboundagrs/INAGR12333344/undeploy</pre>", "isProceedWithWarnings": "[REQUIRED] boolean"}, "functionality": "Undeploys an inbound agreement to disallow acceptance of new messages by the agreement. However, all in-flight messages are processed successfully.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Retrieve Outbound Agreements", "api_call": "GET /ic/api/b2b/v1/tpm/partners/{tpId}/outboundagrs", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: <p>Trading partner ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/outboundagrs</pre>"}, "functionality": "Retrieves all available outbound agreements with the specified trading partner ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Create an Outbound Agreement", "api_call": "POST /ic/api/b2b/v1/tpm/partners/{tpId}/outboundagrs", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID."}, "functionality": "Creates an outbound agreement with the specified trading partner ID. The request body must contain at least the name and docId fields.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Retrieve an Outbound Agreement", "api_call": "GET /ic/api/b2b/v1/tpm/partners/{tpId}/outboundagrs/{agrId}", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID.", "agrId": "[REQUIRED] string: <p>Trading partner outbound agreement ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/outboundagrs/OUTAGR1604824600539</pre>"}, "functionality": "Retrieves an outbound agreement with the specified trading partner ID and agreement ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Update an Outbound Agreement", "api_call": "PUT /ic/api/b2b/v1/tpm/partners/{tpId}/outboundagrs/{agrId}", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID.", "agrId": "[REQUIRED] string: Trading partner outbound agreement ID."}, "functionality": "Updates an outbound agreement with the specified trading partner ID and agreement ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Delete an Outbound Agreement", "api_call": "DELETE /ic/api/b2b/v1/tpm/partners/{tpId}/outboundagrs/{agrId}", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID.", "agrId": "[REQUIRED] string: Trading partner outbound agreement ID."}, "functionality": "Deletes an outbound agreement with the specified trading partner ID and outbound agreement ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Deploy an Outbound Agreement", "api_call": "POST /ic/api/b2b/v1/tpm/partners/{tpId}/outboundagrs/{agrId}/deploy", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: <p>Trading partner ID.</p>\n", "agrId": "[REQUIRED] string: <p>Outbound agreement ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/outboundagrs/OUTAGR12333344/deploy</pre>", "isProceedWithWarnings": "[REQUIRED] boolean"}, "functionality": "Deploys an outbound agreement. The deployed agreements translates and processes messages.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Undeploy an Outbound Agreement", "api_call": "POST /ic/api/b2b/v1/tpm/partners/{tpId}/outboundagrs/{agrId}/undeploy", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: <p>Trading partner ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/outboundagrs/OUTAGR12333344/undeploy</pre>", "agrId": "[REQUIRED] string: <p>Outbound agreement ID.</p>\n", "isProceedWithWarnings": "[REQUIRED] boolean"}, "functionality": "Undeploys an outbound agreement to disallow acceptance of new messages by the agreement. However, all in-flight messages are processed successfully.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Retrieve Transports", "api_call": "GET /ic/api/b2b/v1/tpm/partners/{tpId}/transports", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: <p>Trading partner ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/transports</pre>"}, "functionality": "Retrieves all the available transports with the specified trading partner ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Create a Transport", "api_call": "POST /ic/api/b2b/v1/tpm/partners/{tpId}/transports", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID."}, "functionality": "Creates a transport with the specified trading partner ID. The request body must contain at least the name, transportType, connectionId, integrationNamePrefix, and integrationIdentifierPrefix fields.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Retrieve a Transport", "api_call": "GET /ic/api/b2b/v1/tpm/partners/{tpId}/transports/{transportId}", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID.", "transportId": "[REQUIRED] string: <p>Trading partner transport ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/transports/TRANSPORT1604824600539</pre>"}, "functionality": "Retrieves a Transport with the specified trading partner ID and transport ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Update a Transport", "api_call": "PUT /ic/api/b2b/v1/tpm/partners/{tpId}/transports/{transportId}", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID.", "transportId": "[REQUIRED] string: <p>Trading partner transport ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/transports/TRANSPORT1604824600539</pre>"}, "functionality": "Updates a transport with the specified trading partner ID and transport ID.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Delete a Transport", "api_call": "DELETE /ic/api/b2b/v1/tpm/partners/{tpId}/transports/{transportId}", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID.", "transportId": "[REQUIRED] string: <p>Trading partner transport ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/transports/TRANSPORT1604824600539</pre>"}, "functionality": "Deletes a Transport with the specified trading partner ID and transport ID. Ensure that the transport you want to delete is not in use by any outbound agreement. You may want to remove any reference it has before deleting.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Deploy a Transport", "api_call": "POST /ic/api/b2b/v1/tpm/partners/{tpId}/transports/{transportId}/deploy", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID.", "transportId": "[REQUIRED] string: <p>Trading partner transport ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/transports/TRANSPORT1604824600539/deploy</pre>"}, "functionality": "Deploys a transport. The deployed transport helps to generate messages.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "REST API for Oracle Integration 3 - B2B/Trading Partner: Undeploy a Transport", "api_call": "POST /ic/api/b2b/v1/tpm/partners/{tpId}/transports/{transportId}/undeploy", "api_version": "2024.02.14", "api_arguments": {"tpId": "[REQUIRED] string: Trading partner ID.", "transportId": "[REQUIRED] string: <p>Trading partner transport ID.</p>\n<p>\n\t<b>Example:</b> \n</p> \n<pre>/b2b/v1/tpm/partners/TP123/transports/TRANSPORT1604824600539/undeploy</pre>"}, "functionality": "Undeploys a transport to disallow acceptance of new messages. However, all in-flight messages are processed successfully.", "metadata": {"documentation_link": "https://docs.oracle.com/en/cloud/paas/application-integration/rest-api/"}}]