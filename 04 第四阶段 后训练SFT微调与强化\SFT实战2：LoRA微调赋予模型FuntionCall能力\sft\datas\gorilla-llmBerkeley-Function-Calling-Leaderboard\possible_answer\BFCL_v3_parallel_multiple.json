{"id": "parallel_multiple_0", "ground_truth": [{"math_toolkit.sum_of_multiples": {"lower_limit": [1], "upper_limit": [1000], "multiples": [[3, 5]]}}, {"math_toolkit.product_of_primes": {"count": [5]}}]}
{"id": "parallel_multiple_1", "ground_truth": [{"area_rectangle.calculate": {"length": [7.0], "breadth": [3.0]}}, {"area_circle.calculate": {"radius": [5.0]}}]}
{"id": "parallel_multiple_2", "ground_truth": [{"circle.calculate_area": {"radius": [5]}}, {"circle.calculate_circumference": {"diameter": [10]}}]}
{"id": "parallel_multiple_3", "ground_truth": [{"get_rectangle_property": {"perimeter": [14], "area": [15], "property": ["width"], "tolerance": [""]}}, {"get_rectangle_property": {"perimeter": [14], "area": [15], "property": ["length"], "tolerance": ["", 0.1]}}]}
{"id": "parallel_multiple_4", "ground_truth": [{"integral": {"function": ["x**2", "lambda x : x**2"], "a": [1.0], "b": [5.0]}}, {"derivative": {"function": ["x**2", "lambda x : x**2"], "x": [3.0]}}]}
{"id": "parallel_multiple_5", "ground_truth": [{"gcd": {"num1": [96], "num2": [128]}}, {"lcm": {"num1": [15], "num2": [25]}}]}
{"id": "parallel_multiple_6", "ground_truth": [{"find_prime_numbers": {"start": [50], "end": [150]}}, {"get_fibonacci_sequence": {"count": [150]}}]}
{"id": "parallel_multiple_7", "ground_truth": [{"kinematics.calculate_time": {"velocity": [50], "distance": [600]}}, {"kinematics.calculate_time": {"velocity": [400], "distance": [1000]}}]}
{"id": "parallel_multiple_8", "ground_truth": [{"kinematics.final_velocity": {"initial_velocity": [20.0], "acceleration": [5.0], "time": [6.0]}}, {"kinematics.distance_traveled": {"initial_velocity": [20.0], "acceleration": [5.0], "time": [6.0]}}]}
{"id": "parallel_multiple_9", "ground_truth": [{"flight_book": {"_from": ["Seattle"], "to": ["Boston"], "airlines": ["American Airlines"]}}, {"hotel_book": {"location": ["Boston", "Boston, Massachusetts", "Boston, MA", "Boston,MA"], "nights": [4]}}]}
{"id": "parallel_multiple_10", "ground_truth": [{"musical_ticket.buy": {"show": ["Mamma Mia"], "date": ["2023-06-30"]}}, {"train_ticket.buy": {"origin": ["New York"], "destination": ["Chicago"], "date": ["2023-06-30"]}}]}
{"id": "parallel_multiple_11", "ground_truth": [{"physics.electric_field": {"charge": [4.0], "distance": [3.0]}}, {"physics.magnetic_field": {"current": [0.5], "turnsPerMeter": [25.0], "length": [2.0]}}]}
{"id": "parallel_multiple_12", "ground_truth": [{"calculate_magnetic_field": {"current": [4.0], "distance": [2.0]}}, {"calculate_voltage_difference": {"electric_field": [5.0], "distance": [3.0], "charge": [0.0, ""], "permeability": ["", 0.1]}}]}
{"id": "parallel_multiple_13", "ground_truth": [{"energy_calculator.calculate": {"substance": ["water"], "mass": [100.0], "initial_temperature": [25.0], "final_temperature": [100.0], "unit": ["joules", ""]}}, {"energy_calculator.calculate": {"substance": ["Aluminium", "aluminium"], "mass": [100.0], "initial_temperature": [25.0], "final_temperature": [100.0], "unit": ["joules", ""]}}]}
{"id": "parallel_multiple_14", "ground_truth": [{"animal_population.get_history": {"country": ["Bangladesh"], "species": ["tigers", "tiger"], "years": [5]}}, {"animal_population.get_history": {"country": ["India"], "species": ["tigers", "tiger"], "years": [5]}}, {"animal_population.get_projection": {"country": ["Nepal"], "species": ["tigers", "tiger"], "years": [10]}}, {"animal_population.get_projection": {"country": ["Malaysia"], "species": ["tigers", "tiger"], "years": [10]}}]}
{"id": "parallel_multiple_15", "ground_truth": [{"restaurant.search": {"location": ["New York, NY"], "cuisine": ["Chinese"], "rating": [1.0, ""]}}, {"restaurant.search": {"location": ["Los Angeles, CA"], "cuisine": ["Italian"], "rating": [4.0]}}, {"flight.search": {"_from": ["New York", "New York, NY"], "to": ["Los Angeles", "Los Angeles, CA"], "type": ["round-trip", "round trip"]}}]}
{"id": "parallel_multiple_16", "ground_truth": [{"calculate_factorial": {"number": [8]}}, {"generate_prime": {"start": [1], "end": [50]}}]}
{"id": "parallel_multiple_17", "ground_truth": [{"steps_calorie_calculation": {"calorie": [500.0]}}, {"hydration_calculator": {"exercise_time": [2.0]}}]}
{"id": "parallel_multiple_18", "ground_truth": [{"currency_conversion": {"amount": [10.0], "from_currency": ["USD", "United States Dollar"], "to_currency": ["EUR", "Euro"]}}, {"banking_service": {"account_id": ["987654"], "amount": [10.0]}}]}
{"id": "parallel_multiple_19", "ground_truth": [{"math.gaussian_integral": {"function": ["exp(-x**2)", "lambda x: exp(-x**2)"], "lower_limit": [-2.0], "upper_limit": [2.0]}}, {"math.definite_integral": {"function": ["sin(x)", "lambda x: sin(x)"], "lower_limit": [0.0], "upper_limit": [3.1416]}}]}
{"id": "parallel_multiple_20", "ground_truth": [{"statistics.median": {"data": [[3, 4, 5, 2, 8, 5]]}}, {"statistics.variance": {"data": [[3, 4, 5, 2, 8, 5]], "population": [true, false, ""]}}, {"statistics.mode": {"data": [[3, 4, 5, 2, 8, 5]]}}]}
{"id": "parallel_multiple_21", "ground_truth": [{"data_loading": {"file_path": ["dataset.csv"], "delimiter": [",", ""]}}, {"linear_regression_fit": {"x": ["data['sales']"], "y": ["data['future_sales']"], "return_residuals": [true]}}]}
{"id": "parallel_multiple_22", "ground_truth": [{"financial_ratios.interest_coverage": {"company_name": ["XYZ"], "years": [3]}}, {"sales_growth.calculate": {"company": ["XYZ"], "years": [3]}}]}
{"id": "parallel_multiple_23", "ground_truth": [{"financial_ratio.net_profit_margin": {"net_income": [20000], "total_revenue": [100000]}}, {"financial_ratio.debt_ratio": {"total_liabilities": [10000], "total_assets": [30000]}}]}
{"id": "parallel_multiple_24", "ground_truth": [{"investment.invest": {"company": ["Google", "GOOG"], "amount": [2000.0]}}, {"investment.withdraw": {"company": ["Apple", "AAPL"], "amount": [1000.0]}}]}
{"id": "parallel_multiple_25", "ground_truth": [{"stock_invest.calculate_investment_cost": {"company": ["Apple", "AAPL"], "shares": [50]}}, {"stock_invest.calculate_dividend_payout": {"shares": [50], "dividend_per_share": [1.3]}}]}
{"id": "parallel_multiple_26", "ground_truth": [{"bank.get_transaction_history": {"account": ["********"], "days": [7]}}, {"bank.calculate_balance": {"account": ["********"], "transactions": [[], ""], "type": ["credit", ""], "starting_balance": ["", 0.0]}}]}
{"id": "parallel_multiple_27", "ground_truth": [{"bank_account.transfer": {"from_account": ["checking"], "to_account": ["saving"], "amount": [5000.0]}}, {"bank_account.calculate_interest": {"principal": [5000.0], "rate": [0.03], "time": [5]}}]}
{"id": "parallel_multiple_28", "ground_truth": [{"criminal_record.get_status": {"criminal_name": ["John Doe"], "region": ["New York", "NY"]}}, {"criminal_record.get_offense_nature": {"criminal_name": ["John Doe"], "optional_param": ["", false]}}]}
{"id": "parallel_multiple_29", "ground_truth": [{"court_records.search_cases": {"location": ["New York"], "query": ["Theft"], "year": [2021], "limit": [5, ""]}}, {"court_records.search_cases": {"location": ["San Francisco"], "query": ["Theft"], "year": [2021], "limit": [5, ""]}}]}
{"id": "parallel_multiple_30", "ground_truth": [{"legal_case.find_parties": {"party_name": ["Charles Dickens"], "city": ["Boston", "Boston, Massachusetts"]}}, {"legal_case.find_parties": {"party_name": ["University of California", "UC"], "city": ["Los Angeles", "Los Angeles, California", "LA"]}}]}
{"id": "parallel_multiple_31", "ground_truth": [{"lawsuit.fetch_details": {"company_name": ["Pacific Gas and Electric", "PG&E"]}}, {"lawsuit.judge": {"company_name": ["Pacific Gas and Electric", "PG&E"], "lawsuit_id": [123, ""]}}, {"lawsuit.fetch_details": {"company_name": ["Tesla Inc.", "Tesla"]}}, {"lawsuit.judge": {"company_name": ["Tesla Inc.", "Tesla"], "lawsuit_id": [123, ""]}}]}
{"id": "parallel_multiple_32", "ground_truth": [{"weather_forecast_temperature": {"location": ["Boston, USA"], "days": [10]}}, {"weather_forecast_humidity": {"location": ["Boston, USA"], "days": [10]}}, {"weather_forecast_precipitation": {"location": ["Rome, Italy"], "days": [10]}}]}
{"id": "parallel_multiple_33", "ground_truth": [{"supermarket.find_in_city": {"city": ["Los Angeles", "LA"], "state": ["California", "CA"], "openNow": ["", true]}}, {"sightseeing.popular_in_city": {"city": ["Miami"], "state": ["Florida", "FL"], "kidsFriendly": ["", true]}}]}
{"id": "parallel_multiple_34", "ground_truth": [{"translate_text": {"text": ["Hello World"], "from_lang": ["English", "EN"], "to_lang": ["Spanish", "ES"]}}, {"translate_text": {"text": ["Goodbye"], "from_lang": ["French", "FR"], "to_lang": ["English", "EN"]}}, {"get_current_time": {"location": ["Los Angeles"]}}, {"get_current_time": {"location": ["London"]}}]}
{"id": "parallel_multiple_35", "ground_truth": [{"image_processing.object_identification": {"image_url": ["my_backyard_image_url"]}}, {"text_analysis.sentiment_analysis": {"text": ["my_journal_entry_text"]}}]}
{"id": "parallel_multiple_36", "ground_truth": [{"euro_history.battle_details": {"battle_name": ["Battle of Waterloo", "Waterloo"], "specific_info": [["overview"]]}}, {"euro_history.treaty_info": {"treaty_name": ["Treaty of Tordesillas", "Tordesillas"], "info_requested": [["overview"]]}}]}
{"id": "parallel_multiple_37", "ground_truth": [{"history.get_timeline": {"event": ["World War 2", "WW2", "World War 2 in Europe"], "region": ["Europe", ""]}}, {"history.get_important_figures": {"event": ["World War 2", "WW2", "World War 2 in Europe"], "number": [1, ""]}}]}
{"id": "parallel_multiple_38", "ground_truth": [{"us_history.life_expectancy": {"year": [1900]}}, {"us_history.life_expectancy": {"year": [1950]}}, {"us_history.gdp": {"year": [1900]}}, {"us_history.gdp": {"year": [1950]}}]}
{"id": "parallel_multiple_39", "ground_truth": [{"scientist_info.get_birthdate": {"name": ["Nikola Tesla"]}}, {"scientist_info.get_famous_discovery": {"name": ["Nikola Tesla"], "discovery_order": [1, ""]}}]}
{"id": "parallel_multiple_40", "ground_truth": [{"scienceFacts.getWeight": {"particle": ["Neutron"], "unit": ["amu"]}}, {"scienceFacts.getWeight": {"particle": ["Proton"], "unit": ["amu"]}}, {"scienceFacts.getDiameter": {"particle": ["Proton"], "unit": ["femtometers"]}}, {"scienceFacts.getDiameter": {"particle": ["Neutron"], "unit": ["femtometers"]}}]}
{"id": "parallel_multiple_41", "ground_truth": [{"painting.create": {"shape": ["square"], "background_color": ["blue"], "dimensions": [[16, 16]]}}, {"display.set_screen_brightness": {"percentage": [70], "duration": [30]}}, {"painting.display": {"time": [30]}}]}
{"id": "parallel_multiple_42", "ground_truth": [{"artwork.find": {"museum": ["Modern Arts Museum, New York", "Modern Arts Museum"], "type": ["sculpture", "Sculpture"], "material": ["bronze", "Bronze"], "artist": [""]}}, {"artwork.find": {"museum": ["Louvre Museum, Paris", "Louvre Museum", "Paris"], "type": ["sculpture", "Sculpture"], "material": ["stone", "Stone"], "artist": [""]}}, {"artwork.find": {"museum": ["Metropolitan Museum of Art", "Metropolitan Museum"], "type": ["painting"], "artist": ["Picasso"], "material": [""]}}]}
{"id": "parallel_multiple_43", "ground_truth": [{"get_artwork_price": {"museum_location": ["Philadelphia"], "sculpture_material": ["marble"], "sculpture_size": [[4, 4]]}}, {"get_artwork_price": {"museum_location": ["New York"], "sculpture_material": ["bronze"], "sculpture_size": [[6, 3]]}}]}
{"id": "parallel_multiple_44", "ground_truth": [{"house_designer.design": {"bedrooms": [3], "bathrooms": [2], "garden": [true]}}, {"office_designer.design": {"rooms": [5], "meeting_room": ["large"]}}]}
{"id": "parallel_multiple_45", "ground_truth": [{"calcVolume.cuboid": {"height": [10.0], "width": [5.0], "depth": [8.0]}}, {"calcVolume.sphere": {"radius": [4.0]}}]}
{"id": "parallel_multiple_46", "ground_truth": [{"museum.get_hours": {"museum_name": ["Louvre Museum", "Louvre"]}}, {"museum.get_waiting_time": {"museum_name": ["Louvre Museum", "Louvre"], "day": ["", "Monday"]}}, {"location.get_travel_time": {"destination": ["Louvre Museum", "Louvre"], "mode": ["Driving", ""]}}]}
{"id": "parallel_multiple_47", "ground_truth": [{"lowest_price": {"city": ["Austin"], "product": ["Yamaha Acoustic Guitar"]}}, {"average_price": {"city": ["New York"], "product": ["Yamaha Acoustic Guitar"]}}, {"store_count": {"city": ["Austin"], "product": ["Yamaha Acoustic Guitar"]}}, {"store_count": {"city": ["New York"], "product": ["Yamaha Acoustic Guitar"]}}]}
{"id": "parallel_multiple_48", "ground_truth": [{"note_conversion.indian": {"note": ["C"]}}, {"frequency_to_wavelength": {"frequency": [440.0]}}]}
{"id": "parallel_multiple_49", "ground_truth": [{"beat_generator": {"genre": ["Hip Hop", "hip hop"], "bpm": [95], "scale": ["Major", "major", ""]}}, {"melody_generator": {"note_sequence": [["C4", "E4", "F4", "G4"]], "instrument": ["Bass", ""]}}]}
{"id": "parallel_multiple_50", "ground_truth": [{"sport_analysis.last_game_performance": {"team": ["L.A Lakers", "Los Angeles Lakers"], "details": [["field goal %", "free throw %"]]}}, {"sport_analysis.compare_ppg": {"team": ["L.A Lakers", "Los Angeles Lakers"], "seasons": [["2018-2019", "2019-2020"], ["18-19", "19-20"]]}}]}
{"id": "parallel_multiple_51", "ground_truth": [{"get_player_record": {"player": ["Michael Jordan"], "stat": ["highest_scoring_game"]}}, {"get_player_record": {"player": ["Michael Jordan"], "stat": ["total_championships"]}}]}
{"id": "parallel_multiple_52", "ground_truth": [{"game_of_life.play": {"rounds": [3], "start_board": [[]]}}, {"chess.play": {"moves": [["e4", "e5"]]}}]}
{"id": "parallel_multiple_53", "ground_truth": [{"board_game_search": {"complexity": [2.5], "player_count": [6]}}, {"trivia_game_search": {"duration": [60.0, 45.0, 30.0]}}]}
{"id": "parallel_multiple_54", "ground_truth": [{"BattleReignGameAPI.update_player_equipment": {"attribute": ["armor"], "level": [5], "playerID": [123, ""]}}, {"GameGuideAPI.search_guide": {"game": ["Battle Reign"], "condition": ["snowy weather"], "type": [""]}}, {"GameGuideAPI.search_guide": {"game": ["Shadow Fall"], "type": ["strategy"], "condition": [""]}}]}
{"id": "parallel_multiple_55", "ground_truth": [{"recipe_search": {"ingredient": ["spaghetti"], "dietary_requirements": [["gluten_free"]], "isHomemade": [true]}}, {"recipe_prep_time": {"recipe": ["spaghetti", "homemade healthy spaghetti", "Homemade healthy gluten free spaghetti", "homemade_spaghetti"]}}, {"recipe_nutrition_info": {"recipe": ["homemade_spaghetti", "homemade healthy spaghetti", "spaghetti", "Homemade healthy gluten free spaghetti"]}}]}
{"id": "parallel_multiple_56", "ground_truth": [{"time_zones.get_current_time": {"location": ["Beijing", "BJ"]}}, {"time_zones.get_current_time": {"location": ["Tokyo", "TYO"]}}, {"time_zones.get_time_difference": {"city_1": ["Beijing", "BJ"], "city_2": ["Tokyo", "TYO"]}}]}
{"id": "parallel_multiple_57", "ground_truth": [{"hotel.find": {"location": ["Paris", "Paris, France", "France"], "stars": [4], "amenities": [["Free WiFi", "Breakfast Included", "Gym"]]}}, {"hotel.find": {"location": ["New York", "New York, USA", "NY", "NY, USA", "USA"], "stars": [4], "amenities": [["Free WiFi", "Breakfast Included", "Gym"]]}}]}
{"id": "parallel_multiple_58", "ground_truth": [{"triangle_properties.get": {"side1": [5.0], "side2": [7.0], "side3": [9.0], "get_area": ["", true], "get_perimeter": ["", true], "get_angles": ["", true]}}, {"circle_properties.get": {"radius": [3.0], "get_area": ["", true], "get_circumference": ["", true]}}]}
{"id": "parallel_multiple_59", "ground_truth": [{"math.triangle_area_heron": {"side1": [7.0], "side2": [10.0], "side3": [5.0]}}, {"math.triangle_area_base_height": {"base": [8.0], "height": [6.0]}}, {"math.circle_area": {"radius": [4.0]}}]}
{"id": "parallel_multiple_60", "ground_truth": [{"country_info.capital": {"country": ["Australia"]}}, {"country_info.population": {"country": ["Canada"]}}, {"country_info.largest_city": {"country": ["Brazil"]}}]}
{"id": "parallel_multiple_61", "ground_truth": [{"EuclideanDistance.calculate": {"pointA": [[3, 2]], "pointB": [[7, 5]], "rounding": [2, ""]}}, {"angleToXAxis.calculate": {"pointA": [[3, 2]], "pointB": [[7, 5]], "rounding": [2, ""]}}, {"EuclideanDistance.calculate": {"pointA": [[10, 8]], "pointB": [[14, 12]], "rounding": [2, ""]}}, {"angleToXAxis.calculate": {"pointA": [[10, 8]], "pointB": [[14, 12]], "rounding": [2, ""]}}]}
{"id": "parallel_multiple_62", "ground_truth": [{"kinematics.calculate_displacement": {"initial_speed": [5.0], "acceleration": [2.0], "time": [10.0], "rounding": [2, ""]}}, {"kinematics.calculate_final_speed": {"initial_speed": [5.0], "acceleration": [2.0], "time": [10.0], "rounding": [2, ""]}}]}
{"id": "parallel_multiple_63", "ground_truth": [{"weather.get_by_coordinates_date": {"coordinates": [[40.7128, -74.006]], "date": ["2021-01-15", "01/15/2021", "Jan 15, 2021"]}}, {"weather.get_by_city_date": {"city": ["New York City", "New York City, NY"], "date": ["2020-12-25", "12/25/2020", "Dec 25, 2020"]}}, {"weather.get_by_city_date": {"city": ["New York City"], "date": ["2021-01-01", "01/01/2021", "Jan 1, 2021"]}}, {"weather.get_forecast_by_coordinates": {"coordinates": [[40.7128, -74.006]], "days_ahead": [10]}}]}
{"id": "parallel_multiple_64", "ground_truth": [{"wildlife_population.assess_growth": {"species": ["African Elephant"], "location": ["Serengeti", "Serengeti ecosystem"], "duration": [10]}}, {"ecological_impact.analyze": {"species": ["African Elephant"], "ecosystem": ["Serengeti", "Serengeti ecosystem"], "location": ["Serengeti"], "timeframe": [5, ""]}}, {"wildlife_population.assess_growth": {"species": ["Bengal Tiger"], "location": ["Sundarbans", "Sundarbans ecosystem"], "duration": [7]}}, {"ecological_impact.analyze": {"species": ["Bengal Tiger", "Tiger"], "ecosystem": ["Sundarbans", "Sundarbans ecosystem"], "location": ["Sundarbans"], "timeframe": [3]}}]}
{"id": "parallel_multiple_65", "ground_truth": [{"realestate.find_properties": {"location": ["San Francisco, CA", "SF, CA"], "propertyType": ["condo"], "bedrooms": [2], "budget": [{"min": [500000], "max": [800000]}]}}, {"property_valuation.get": {"location": ["Los Angeles, CA", "LA, CA"], "propertyType": ["villa"], "bedrooms": [3], "age": [5]}}, {"property_valuation.get": {"location": ["New York, NY", "NY, NY"], "propertyType": ["apartment"], "bedrooms": [1], "age": [10]}}]}
{"id": "parallel_multiple_66", "ground_truth": [{"calculate_average": {"gradeDict": [{"Math": [85], "English": [90], "Science": [88], "History": [92], "Art": [89]}]}}, {"calculate_standard_deviation": {"gradeDict": [{"Math": [85], "English": [90], "Science": [88], "History": [92], "Art": [89]}]}}, {"highest_grade": {"gradeDict": [{"Math": [85], "English": [90], "Science": [88], "History": [92], "Art": [89]}]}}]}
{"id": "parallel_multiple_67", "ground_truth": [{"math_roots.quadratic": {"a": [3.0], "b": [4.0], "c": [-7.0]}}, {"math.roots.cubic": {"a": [2.0], "b": [-5.0], "c": [3.0], "d": [-1.0]}}, {"math.roots.polynomial": {"coefficients": [[6.0, -3.0, 2.0, -1.0, 1.0]], "degree": [4.0, ""]}}]}
{"id": "parallel_multiple_68", "ground_truth": [{"corporate_finance.calculate_YOY_growth_rate": {"company_name": ["Tech Innovators"], "year1": [2018], "year1_revenue": [500000.0], "year2": [2019], "year2_revenue": [750000.0]}}, {"financial_ratios.calculate_ROE": {"net_income": [100000.0], "shareholder_equity": [200000.0]}}, {"financial_ratios.calculate_ROA": {"net_income": [100000.0], "total_assets": [1000000.0]}}]}
{"id": "parallel_multiple_69", "ground_truth": [{"finance.property_depreciation": {"initial_cost": [500000.0], "depreciation_rate": [0.02], "years": [5], "monthly": [""]}}, {"finance.inflation_adjustment": {"initial_sum": [200000.0], "years": [5], "inflation_rate": [0.03]}}, {"finance.loan_repayment": {"loan_amount": [300000.0], "interest_rate": [0.04], "loan_term": [10]}}, {"finance.property_depreciation": {"initial_cost": [500000.0], "depreciation_rate": [0.02], "years": [5], "monthly": [true]}}]}
{"id": "parallel_multiple_70", "ground_truth": [{"solarFarm.potential": {"coordinates": [[37.7749, -122.4194]], "panelArea": [50000.0], "month": ["July"]}}, {"windFarm.potential": {"coordinates": [[40.7128, -74.006]], "turbineCount": [100.0], "month": ["July"]}}]}
{"id": "parallel_multiple_71", "ground_truth": [{"sculpture_price.calculate": {"material": ["marble"], "size": [10], "complexity": ["high"]}}, {"sculptor_info.get": {"name": ["Auguste Rodin"]}}, {"sculpture_availability.check": {"sculpture_name": ["The Thinker"], "material": ["bronze"]}}]}
{"id": "parallel_multiple_72", "ground_truth": [{"generate_sound_wave": {"frequency": [440.0], "duration": [5], "wave_type": ["sine", ""]}}, {"generate_sound_wave": {"frequency": [880.0], "duration": [10], "wave_type": ["square"]}}, {"play_sound_wave": {"wave_file": ["test.wav"], "volume": [0.8]}}, {"play_sound_wave": {"wave_file": ["test2.wav"], "volume": [0.6]}}]}
{"id": "parallel_multiple_73", "ground_truth": [{"sports_data.basketball.most_points_single_game": {"league": ["NBA"]}}, {"sports_data.basketball.most_points_single_season": {"league": ["NBA"]}}, {"sports_data.basketball.most_points_career": {"league": ["NBA"]}}]}
{"id": "parallel_multiple_74", "ground_truth": [{"basketball.player_stats.get": {"player_name": ["LeBron James"], "stats_fields": [["points", "assists", "rebounds", "minutes"]]}}, {"basketball.team_stats.get": {"team_name": ["Los Angeles Lakers"], "stats_fields": [["total points", "total assists", "total rebounds", "win rate"]]}}, {"basketball.game_stats.get": {"team1": ["Los Angeles Lakers"], "team2": ["Golden State Warriors"], "date": ["2021-01-18", "01/18/2021", "Jan 18, 2021", "January 18, 2021"], "stats_fields": [["total points", "total assists", "total rebounds", "turnovers"]]}}]}
{"id": "parallel_multiple_75", "ground_truth": [{"route_planner.calculate_route": {"start": ["New York"], "destination": ["Boston"], "method": ["fastest", ""]}}, {"chess_club_details.find": {"name": ["Knight Gambit"], "city": ["Boston"], "event": ["null", ""]}}, {"route_planner.calculate_route": {"start": ["Boston"], "destination": ["Philadelphia"], "method": ["fastest", ""]}}, {"chess_club_details.find": {"name": ["Rook Corner"], "city": ["Philadelphia"]}}, {"route_planner.calculate_route": {"start": ["Philadelphia"], "destination": ["New York"], "method": ["shortest"]}}]}
{"id": "parallel_multiple_76", "ground_truth": [{"video_games.store_price": {"game_title": ["The Legend of Zelda: Breath of the Wild"], "platform": ["Nintendo Switch"], "region": ["United States", ""]}}, {"video_games.on_sale": {"game_title": ["Super Mario Odyssey"], "platform": ["Nintendo Switch"], "region": ["United States", ""]}}, {"video_games.store_currency": {"platform": ["PlayStation"], "region": ["United States", ""]}}, {"video_games.store_price": {"game_title": ["God of War"], "platform": ["PlayStation"], "region": ["United Kingdom"]}}]}
{"id": "parallel_multiple_77", "ground_truth": [{"game_rewards.get": {"game": ["Call of Duty"], "platform": ["Playstation"], "mission": [""], "trophy": [""]}}, {"game_rewards.get": {"game": ["Fortnite"], "platform": ["PC"], "trophy": ["Master"], "mission": [""]}}, {"game_scores.get": {"game": ["FIFA"], "platform": ["Xbox"], "level": [3], "player": [""]}}, {"game_missions.list": {"game": ["Assassin Creed"]}}]}
{"id": "parallel_multiple_78", "ground_truth": [{"maps.shortest_path": {"start_location": ["New York City"], "end_location": ["Metropolitan Museum of Art"], "mode": ["walk", ""]}}, {"maps.shortest_path": {"start_location": ["Metropolitan Museum of Art"], "end_location": ["Central Park"], "mode": ["bike"]}}, {"maps.route_times": {"route": ["New York City to Metropolitan Museum of Art"], "mode": ["walk", ""]}}, {"maps.route_times": {"route": ["Metropolitan Museum of Art to Central Park"], "mode": ["bike"]}}]}
{"id": "parallel_multiple_79", "ground_truth": [{"solve.quadratic_equation": {"a": [5], "b": [6], "c": [1]}}, {"convert.rgb_to_hex": {"r": [255], "g": [160], "b": [0]}}, {"perform.string_reverse": {"input_string": ["Hello, World!"]}}]}
{"id": "parallel_multiple_80", "ground_truth": [{"functions.intersect": {"function1": ["4x + 7", "lambda x: 4x + 7"], "function2": ["2x + 5", "lambda x: 2x + 5"]}}, {"functions.zero": {"function": ["3x + 9", "lambda x: 3x + 9"]}}]}
{"id": "parallel_multiple_81", "ground_truth": [{"geometry_rectangle.calculate": {"width": [30], "length": [50]}}, {"geometry_square.calculate": {"side": [5]}}, {"geometry_circle.calculate": {"radius": [3]}}]}
{"id": "parallel_multiple_82", "ground_truth": [{"geometry.calculate_cone_volume": {"radius": [10.0], "height": [30.0], "round_off": [2, ""]}}, {"physics.calculate_cone_mass": {"radius": [10.0], "height": [30.0], "density": [5.2]}}, {"physics.calculate_cone_mass": {"radius": [10.0], "height": [30.0], "density": [7.8]}}]}
{"id": "parallel_multiple_83", "ground_truth": [{"calculate_integral": {"func": ["3x**2 - 2x + 1", "lambda x: 3x**2 - 2x + 1"], "a": [1], "b": [4]}}, {"calculate_derivative": {"func": ["2x**3 - 3x**2 + 4x - 5", "lambda x: 2x**3 - 3x**2 + 4x - 5"], "x_value": [2], "order": ["", 1]}}, {"calculate_derivative": {"func": ["2*x**3 - 3*x**2 + 4*x - 5", "lambda x: 2*x**3 - 3*x**2 + 4*x - 5"], "x_value": [2], "order": [2]}}]}
{"id": "parallel_multiple_84", "ground_truth": [{"math.lcm": {"num1": [36], "num2": [48]}}, {"math.gcd": {"num1": [36], "num2": [48]}}]}
{"id": "parallel_multiple_85", "ground_truth": [{"calculate_gcd": {"num1": [56], "num2": [98], "algorithm": ["euclidean", ""]}}, {"calculate_gcd": {"num1": [81], "num2": [27], "algorithm": ["binary"]}}, {"calculate_lcm": {"num1": [15], "num2": [25], "method": ["standard", ""]}}, {"calculate_lcm": {"num1": [21], "num2": [14], "method": ["reduced"]}}]}
{"id": "parallel_multiple_86", "ground_truth": [{"kinematics.calculate_speed_from_rest": {"distance": [120.0], "time": [10.0], "initial_speed": [0.0, ""]}}, {"kinematics.calculate_acceleration": {"initial_speed": [12.0], "final_speed": [24.0], "time": [5.0], "distance": [""]}}]}
{"id": "parallel_multiple_87", "ground_truth": [{"kinematics.final_velocity": {"initial_velocity": [0.0], "time": [5.0], "acceleration": [3.0]}}, {"physics.wave_velocity": {"frequency": [50.0], "wavelength": [3.0]}}, {"kinematics.distance": {"initial_velocity": [0.0, ""], "time": [12.0], "acceleration": [3.0]}}]}
{"id": "parallel_multiple_88", "ground_truth": [{"library.search_book": {"book_name": ["To Kill a Mockingbird"], "city": ["New York", "NY"], "availability": [true], "genre": ["Fiction", ""]}}, {"library.reserve_book": {"book_id": ["123ABC"], "branch_id": ["XYZ789"], "return_date": ["2022-12-31", "12/31/2022", "Dec 31, 2022"]}}]}
{"id": "parallel_multiple_89", "ground_truth": [{"ride_hailing.get_rides": {"source": ["123 Main Street"], "destination": ["456 Park Avenue"], "max_cost": [30.0, ""]}}, {"grocery_delivery.order": {"location": ["789 Broadway"], "items": [["milk", "bread", "eggs", "apples"], ["milk", "bread", "apples", "eggs"], ["milk", "eggs", "bread", "apples"], ["milk", "eggs", "apples", "bread"], ["milk", "apples", "bread", "eggs"], ["milk", "apples", "eggs", "bread"], ["bread", "milk", "eggs", "apples"], ["bread", "milk", "apples", "eggs"], ["bread", "eggs", "milk", "apples"], ["bread", "eggs", "apples", "milk"], ["bread", "apples", "milk", "eggs"], ["bread", "apples", "eggs", "milk"], ["eggs", "milk", "bread", "apples"], ["eggs", "milk", "apples", "bread"], ["eggs", "bread", "milk", "apples"], ["eggs", "bread", "apples", "milk"], ["eggs", "apples", "milk", "bread"], ["eggs", "apples", "bread", "milk"], ["apples", "milk", "bread", "eggs"], ["apples", "milk", "eggs", "bread"], ["apples", "bread", "milk", "eggs"], ["apples", "bread", "eggs", "milk"], ["apples", "eggs", "milk", "bread"], ["apples", "eggs", "bread", "milk"]], "max_delivery_cost": [10.0, ""]}}, {"ride_hailing.get_rides": {"source": ["456 Park Avenue"], "destination": ["321 Elm Street"], "max_cost": [20.0]}}, {"ride_hailing.get_rides": {"source": ["321 Elm Street"], "destination": ["123 Main Street"], "max_cost": [25.0]}}]}
{"id": "parallel_multiple_90", "ground_truth": [{"calculate_final_temperature": {"quantity1": [5.0], "temperature1": [300.0], "quantity2": [3.0], "temperature2": [500.0]}}, {"calculate_mass": {"quantity": [4.0], "molar_mass": [16.0]}}]}
{"id": "parallel_multiple_91", "ground_truth": [{"biological.calc_energy": {"mols": [5.0], "substance": ["C6H12O6", "glucose"], "joules_per_mol": [2800.0, ""]}}, {"biological.calc_biomass": {"energy": [14000.0], "efficiency": [0.1, ""]}}, {"physical.calc_work": {"energy": [1400.0], "distance": [2.0]}}]}
{"id": "parallel_multiple_92", "ground_truth": [{"calculate.weight_in_space": {"weight_earth_kg": [75.0], "planet": ["Mars"]}}, {"currency_conversion": {"amount": [5000.0], "from_currency": ["USD", "US Dollars", "US Dollar"], "to_currency": ["JPY", "Japanese Yen"]}}, {"unit_conversion.convert": {"value": [24.0], "from_unit": ["in", "inch", "inches"], "to_unit": ["cm", "centimeter", "centimeters"]}}]}
{"id": "parallel_multiple_93", "ground_truth": [{"geology.get_era": {"era_name": ["Jurassic"], "calculate_years_ago": [true]}}, {"history.get_event_date": {"event_name": ["signing of the Magna Carta", "Magna Carta"], "calculate_years_ago": [true]}}]}
{"id": "parallel_multiple_94", "ground_truth": [{"sort_list": {"elements": [["apple", "banana", "cherry", "date", "elderberry"], ["elderberry", "cherry", "banana", "apple", "date"]], "order": ["desc", "descending"]}}, {"filter_list": {"elements": [["apple", "banana", "cherry", "date", "elderberry"]], "condition": ["startswith(b)", "startwith(b)"]}}, {"sum_elements": {"elements": [[5, 10, 15, 20, 25]]}}, {"sort_list": {"elements": [[35, 10, 25, 5, 15]], "order": ["asc", ""]}}]}
{"id": "parallel_multiple_95", "ground_truth": [{"cosine_similarity.calculate": {"vector1": [[1, 2, 3]], "vector2": [[4, 5, 6]], "rounding": [2]}}, {"correlation.calculate": {"array1": [[7, 8, 9]], "array2": [[10, 11, 12]], "type": ["pearson", ""]}}, {"correlation.calculate": {"array1": [[13, 14, 15]], "array2": [[16, 17, 18]], "type": ["spearman"]}}, {"cosine_similarity.calculate": {"vector1": [[19, 20, 21]], "vector2": [[22, 23, 24]], "rounding": [3]}}]}
{"id": "parallel_multiple_96", "ground_truth": [{"library.find_nearby": {"location": ["New York City", "New York City, NY"], "preferences": [["Pet-friendly", "Cafe Inside"]]}}, {"store.find_nearby": {"location": ["New York City", "New York City, NY"], "preferences": [["Disabled Access", "24 hours"]]}}]}
{"id": "parallel_multiple_97", "ground_truth": [{"calc_Simple_Interest": {"principle_amount": [5000.0], "duration": [5.0], "annual_rate": [0.04]}}, {"calc_Compound_Interest": {"principle_amount": [5000.0], "duration": [5.0], "annual_rate": [0.035], "compound_freq": [1, ""]}}, {"future_value": {"initial_investment": [3000.0], "interest_rate": [0.05], "time": [6], "num_compoundings": [2]}}]}
{"id": "parallel_multiple_98", "ground_truth": [{"currency_conversion": {"amount": [5000.0], "from_currency": ["Japanese Yen", "JPY"], "to_currency": ["US Dollars", "USD", "US Dollar"]}}, {"unit_conversion": {"value": [15.0], "from_unit": ["km", "kilometer", "kilometers"], "to_unit": ["mi", "mile", "miles"]}}]}
{"id": "parallel_multiple_99", "ground_truth": [{"corporate_finance.dividend_data": {"company": ["Microsoft", "MSFT"], "years": [5], "frequency": ["quarterly"]}}, {"corporate_finance.dividend_data": {"company": ["Microsoft"], "years": [5], "frequency": ["annually", ""]}}, {"stock_market_data": {"company": ["Microsoft", "MSFT"], "days": [60]}}, {"stock_market_data": {"company": ["Microsoft"], "days": [120]}}]}
{"id": "parallel_multiple_100", "ground_truth": [{"stock_forecast": {"company": ["Apple Inc.", "AAPL"], "days": [30], "model": ["ARIMA", ""]}}, {"stock_forecast": {"company": ["Microsoft Corporation", "MSFT"], "days": [45], "model": ["LSTM"]}}, {"weather_forecast": {"location": ["New York City", "NYC", "New York", "NY"], "days": [7]}}, {"weather_forecast": {"location": ["Los Angeles", "LA", "Los Angeles, California", "CA"], "days": [14]}}]}
{"id": "parallel_multiple_101", "ground_truth": [{"avg_closing_price": {"company": ["Microsoft", "MSFT"], "days": [30], "data_source": ["yahoo finance", "Yahoo Finance", ""]}}, {"total_revenue": {"company": ["Apple", "AAPL"], "days": [30], "data_source": ["google finance", "Google Finance", ""]}}, {"volume_traded": {"company": ["Microsoft", "MSFT"], "days": [30], "data_source": ["yahoo finance", "Yahoo Finance", ""]}}, {"volume_traded": {"company": ["Apple", "AAPL"], "days": [30], "data_source": ["yahoo finance", "Yahoo Finance", ""]}}]}
{"id": "parallel_multiple_102", "ground_truth": [{"financial.compound_interest": {"principle": [5000], "rate": [0.04], "time": [5], "n": [4]}}, {"financial.simple_interest": {"principle": [5000], "rate": [0.035], "time": [5]}}]}
{"id": "parallel_multiple_103", "ground_truth": [{"lawyer.search": {"location": ["New York, NY", "NY, New York", "NY"], "expertise": ["Divorce"]}}, {"lawyer.search": {"location": ["Los Angeles, CA", "CA, Los Angeles", "CA"], "expertise": ["Criminal"]}}, {"doctor.search": {"location": ["Chicago, IL", "IL, Chicago", "IL"], "specialization": ["Cardiology"]}}, {"doctor.search": {"location": ["Houston, TX", "TX, Houston", "TX"], "specialization": ["Orthopedics", "Orthopaedic"]}}]}
{"id": "parallel_multiple_104", "ground_truth": [{"air_quality_forecast": {"location": ["New York", "NY"], "days": [5]}}, {"weather_forecast": {"location": ["Los Angeles", "LA"], "days": [7]}}, {"news": {"topic": ["global warming"], "days": [3]}}, {"air_quality_forecast": {"location": ["Beijing"], "days": [2]}}]}
{"id": "parallel_multiple_105", "ground_truth": [{"geodistance.find": {"origin": ["New York", "NY"], "destination": ["London"], "unit": ["kilometers", "km"]}}, {"timezones.get_difference": {"city1": ["New York", "NY"], "city2": ["London"]}}, {"flights.search": {"from_city": ["New York", "NY"], "to_city": ["London"], "date": ["next friday", "2022-01-01", "01/01/2022", "Jan.1,2022"]}}, {"geodistance.find": {"origin": ["London"], "destination": ["Paris"], "unit": ["miles", "mi", ""]}}]}
{"id": "parallel_multiple_106", "ground_truth": [{"traffic_estimate": {"start_location": ["San Francisco", "SF"], "end_location": ["Palo Alto"], "time_period": ["weekday"]}}, {"calculate_distance": {"start_point": ["San Francisco", "SF"], "end_point": ["Palo Alto"]}}, {"traffic_estimate": {"start_location": ["Palo Alto"], "end_location": ["Los Angeles", "LA"], "time_period": ["weekend"]}}, {"weather_forecast": {"location": ["Los Angeles", "LA"], "days": [5]}}]}
{"id": "parallel_multiple_107", "ground_truth": [{"library.search_books": {"location": ["New York City", "NYC"], "genre": ["mystery"], "title": [""]}}, {"google.books_search": {"genre": ["mystery"], "title": [""]}}, {"openlibrary.books_search": {"genre": ["mystery"], "title": [""]}}]}
{"id": "parallel_multiple_108", "ground_truth": [{"five_factor_model.analyse": {"talkative": [true], "nervous": [false], "artistic_interests": [true], "lazy": [false], "forgiving": [true]}}, {"MBTI.analyse": {"thinking_vs_feeling": ["feeling", "F"], "introverted_vs_extroverted": ["extroverted", "E"], "judging_vs_perceiving": ["perceiving", "P"], "sensing_vs_intuition": ["intuition", "N"]}}]}
{"id": "parallel_multiple_109", "ground_truth": [{"european_history.get_monarchs": {"country": ["France"], "century": [17]}}, {"european_history.get_events": {"country": ["England"], "century": [18], "event_type": ["war", ""]}}, {"european_history.get_culture": {"country": ["Italy"], "century": [19], "aspect": ["art", ""]}}]}
{"id": "parallel_multiple_110", "ground_truth": [{"us_history.population_by_state_year": {"state": ["California", "CA"], "year": [1980]}}, {"us_history.population_by_state_year": {"state": ["California", "CA"], "year": [1990]}}, {"us_economy.gdp_by_state_year": {"state": ["California", "CA"], "year": [1980], "adjustment": ["Real"]}}, {"us_economy.gdp_by_state_year": {"state": ["California", "CA"], "year": [1990], "adjustment": ["Real"]}}]}
{"id": "parallel_multiple_111", "ground_truth": [{"religion.get_origin": {"religion": ["Buddhism"]}}, {"religion.get_origin": {"religion": ["Hinduism"]}}, {"religion.get_core_beliefs": {"religion": ["Hinduism"]}}, {"religion.get_core_beliefs": {"religion": ["Buddhism"]}}]}
{"id": "parallel_multiple_112", "ground_truth": [{"art_auction.fetch_artwork_price": {"artwork_name": ["Starry Night"], "artist": ["Vincent Van Gogh"], "platform": ["Sotheby"]}}, {"art_auction.fetch_artwork_price": {"artwork_name": ["The Scream"], "artist": ["Edvard Munch"], "platform": ["Christie"]}}, {"library.search_book": {"title": ["To Kill a Mockingbird"], "author": ["Harper Lee"], "platform": ["New York Public Library"]}}, {"library.search_book": {"title": ["1984"], "author": ["George Orwell"], "platform": ["British Library"]}}]}
{"id": "parallel_multiple_113", "ground_truth": [{"paint_color.trends": {"room": ["Living room"], "period": ["Monthly", ""]}}, {"weather_forecast": {"location": ["Seattle", "Seattle, WA"], "days": [5]}}, {"house_price_trends": {"location": ["San Francisco, CA", "San Francisco,CA", "San Francisco", "CA"], "period": ["Quarterly"]}}]}
{"id": "parallel_multiple_114", "ground_truth": [{"sculpture.create_custom": {"item": ["horse"], "material": ["Marble"], "size": [20]}}, {"sculpture.create_custom": {"item": ["dog"], "material": ["Wood"], "size": [15]}}, {"painting.create_custom": {"subject": ["sunset"], "color": ["Red"], "size": [30]}}, {"painting.create_custom": {"subject": ["cityscape"], "color": ["Blue"], "size": [25]}}]}
{"id": "parallel_multiple_115", "ground_truth": [{"artwork_search.find": {"type": ["installation"], "location": ["New York", "NY"], "era": ["modern", ""]}}, {"park_search.find": {"facilities": [["playground", "picnic area"]], "location": ["New York", "NY"]}}, {"tourist_attraction.find": {"attractionType": ["monument"], "location": ["New York", "NY"]}}]}
{"id": "parallel_multiple_116", "ground_truth": [{"exhibition_info": {"museum_name": ["Louvre", "Louvre museum"], "month": [3]}}, {"restaurant_info": {"location": ["Paris", "Paris area"], "food_type": ["Italian"]}}, {"restaurant_info": {"location": ["Paris", "Paris area"], "food_type": ["Chinese"]}}]}
{"id": "parallel_multiple_117", "ground_truth": [{"concert.book_ticket": {"artist": ["Taylor Swift"], "location": ["New York", "NY"], "add_ons": [["VIP Seating"], ""]}}, {"concert.book_ticket": {"artist": ["Ed Sheeran"], "location": ["Los Angeles", "LA"], "add_ons": [["Backstage Pass", "Parking Pass"]]}}, {"festival.book_ticket": {"festival": ["Coachella"], "location": ["Indio"], "add_ons": [["Camping Pass", "Parking Pass"]]}}]}
{"id": "parallel_multiple_118", "ground_truth": [{"music.generate": {"key": ["D Minor", "Dm"], "tempo": [120], "time_signature": ["4/4", ""]}}, {"audio.generate": {"frequency": [440], "amplitude": [0.5], "duration": [""]}}, {"music.generate": {"key": ["E Major", "EM"], "tempo": [90], "time_signature": ["3/4"]}}, {"audio.generate": {"frequency": [300], "amplitude": [0.7], "duration": [5.0]}}]}
{"id": "parallel_multiple_119", "ground_truth": [{"player_stats.get_all_time_goals": {"player_name": ["Cristiano Ronaldo"], "team_name": ["Manchester United"], "competition": ["Premier League", "PL", ""]}}, {"team_stats.get_top_scorer": {"team_name": ["Manchester United"], "competition": ["Premier League", "PL", ""]}}, {"league_stats.get_top_scorer": {"league_name": ["Premier League", "PL", ""], "season": ["2019-2020", "19-20", "2019/2020", "2019", "2020", ""]}}]}
{"id": "parallel_multiple_120", "ground_truth": [{"soccer_scores.get_scores": {"team": ["Manchester United"], "league": ["English Premier League", "EPL"], "rounds": [5]}}, {"basketball_scores.get_scores": {"team": ["Los Angeles Lakers", "Lakers"], "league": ["NBA", "National Basketball Association"], "rounds": [7]}}]}
{"id": "parallel_multiple_121", "ground_truth": [{"BoardGameGeek.recommend": {"numPlayers": [6], "category": ["strategy"], "difficulty": ["beginner", ""]}}, {"BoardGameGeek.recommend": {"numPlayers": [4], "category": ["party"], "difficulty": ["intermediate"]}}, {"AmazonGameStore.recommend": {"numOfPlayers": [6], "category": ["strategy"], "priceRange": ["$20-$30", "20-30 dollars"]}}, {"AmazonGameStore.recommend": {"numOfPlayers": [4], "category": ["party"], "priceRange": ["$20-$30", "20-30 dollars"]}}]}
{"id": "parallel_multiple_122", "ground_truth": [{"games.update.find": {"game": ["Call of Duty"], "platform": ["Playstation", "PS"], "region": ["European", "EU"]}}, {"games.price.find": {"game": ["Call of Duty"], "platform": ["Xbox"]}}, {"games.reviews.find": {"game": ["FIFA 21"], "region": ["American", "US", "USA"]}}]}
{"id": "parallel_multiple_123", "ground_truth": [{"video_games.get_player_count": {"game_title": ["Call of Duty: Modern Warfare"], "year": [2019], "platform": ["Playstation", "PS"]}}, {"video_games.get_player_count": {"game_title": ["Call of Duty: Modern Warfare"], "year": [2020], "platform": ["PC", "Personal Computer"]}}, {"video_games.get_sales": {"game_title": ["Call of Duty: Modern Warfare"], "year": [2019], "platform": ["Playstation", "PS"]}}, {"video_games.get_sales": {"game_title": ["Call of Duty: Modern Warfare"], "year": [2020], "platform": ["PC", "Personal Computer"]}}]}
{"id": "parallel_multiple_124", "ground_truth": [{"recipe_search": {"ingredients": [["eggs", "milk", "bread"]], "calories": [300], "meal": ["breakfast"]}}, {"restaurant_search": {"ingredients": [["chicken", "tomatoes", "lettuce"]], "calories": [500], "meal": ["lunch"]}}, {"ingredient_replace": {"original_ingredient": ["beef"], "replacement_ingredient": ["tofu"], "calories": [600]}}]}
{"id": "parallel_multiple_125", "ground_truth": [{"restaurant.find_group": {"location": ["Seattle, WA", "WA", "Seattle"], "cuisine": [["Seafood", "Italian"]], "group_size": [10]}}, {"events.find_event": {"location": ["Seattle, WA", "WA", "Seattle"], "event_type": [["Concert", "Sports"]], "group_size": [10]}}]}
{"id": "parallel_multiple_126", "ground_truth": [{"recipe.find": {"mainIngredient": ["chicken"], "ingredientLimit": [5]}}, {"restaurant.find": {"cuisine": ["Italian"], "price": [["mid"], ""]}}, {"recipe.find": {"mainIngredient": ["beef"], "ingredientLimit": [7]}}]}
{"id": "parallel_multiple_127", "ground_truth": [{"hotel.book": {"location": ["Paris"], "roomType": ["deluxe"], "nights": [5], "additional_services": [["breakfast", "spa"], ["spa", "breakfast"]]}}, {"car.rental": {"location": ["Paris"], "days": [7], "car_type": ["SUV"], "pick_up": ["airport", ""]}}, {"hotel.book": {"location": ["Rome"], "roomType": ["suite"], "nights": [3], "additional_services": [["airport transfer service"], ["airport transfer"]]}}, {"car.rental": {"location": ["Rome"], "days": [5], "car_type": ["compact"], "pick_up": ["hotel"]}}]}
{"id": "parallel_multiple_128", "ground_truth": [{"hotel_room_pricing.get": {"hotelName": ["Hilton New York"], "roomType": ["deluxe"], "nights": [5]}}, {"car_rental_pricing.get": {"rentalCompany": ["Enterprise"], "carType": ["sedan"], "days": [10]}}, {"flight_ticket_pricing.get": {"airline": ["Delta Airlines", "Delta"], "flightClass": ["business"], "passengers": [3]}}]}
{"id": "parallel_multiple_129", "ground_truth": [{"currency_exchange.convert": {"amount": [5000], "from_currency": ["Euros", "EUR"], "to_currency": ["US Dollars", "USD"], "live_conversion": [true, ""]}}, {"currency_exchange.convert": {"amount": [3000], "from_currency": ["Euros", "EUR"], "to_currency": ["British Pounds", "GBP"], "live_conversion": [false]}}, {"unit_conversion.convert": {"value": [100], "from_unit": ["kilometers", "km"], "to_unit": ["miles", "mi"]}}, {"unit_conversion.convert": {"value": [75], "from_unit": ["kilograms", "kg"], "to_unit": ["pounds", "lbs", "lb"]}}]}
{"id": "parallel_multiple_130", "ground_truth": [{"portfolio_future_value": {"stock": ["AAPL", "\"AAPL\""], "invested_amount": [5000], "expected_annual_return": [0.07], "years": [10]}}, {"get_stock_info": {"company_name": ["Microsoft", "\"Microsoft\""], "detail_level": ["detailed", "\"detailed\""], "market": ["NASDAQ", "\"NASDAQ\"", ""]}}, {"solve_quadratic_equation": {"a": [5], "b": [-20], "c": [15]}}]}
{"id": "parallel_multiple_131", "ground_truth": [{"geometry.area_circle": {"radius": [5.6], "units": ["feet", "ft"]}}, {"plot_sine_wave": {"start_range": [0], "end_range": [3.14], "frequency": [2], "amplitude": [1.5], "phase_shift": [0.5]}}]}
{"id": "parallel_multiple_132", "ground_truth": [{"calculus.derivative": {"function": ["3x**2 + 2x - 1", "lambda x: 3x**2 + 2x - 1"], "value": [2], "function_variable": ["x", ""]}}, {"calculus.derivative": {"function": ["5y**3 - 4y + 2", "lambda y: 5y**3 - 4y + 2"], "value": [3], "function_variable": ["y"]}}, {"get_personality_traits": {"type": ["INTJ"], "traits": [["strengths", "weaknesses"], ["weaknesses", "strengths"], ""]}}]}
{"id": "parallel_multiple_133", "ground_truth": [{"music_generator.generate_scale_progression": {"key": ["D"], "tempo": [120], "duration": [2], "scale_type": ["minor", "Minor"]}}, {"math.hcf": {"number1": [456], "number2": [123]}}]}
{"id": "parallel_multiple_134", "ground_truth": [{"get_top_cases": {"field_of_law": ["constitutional law"], "top_number": [5], "country": ["United Kingdom", "UK"]}}, {"math.gcd": {"num1": [36], "num2": [48]}}]}
{"id": "parallel_multiple_135", "ground_truth": [{"musical_scale": {"key": ["C"], "scale_type": ["major", ""]}}, {"poker_game_winner": {"players": [["John", "Sarah", "Mike"]], "cards": [{"John": [["2 of hearts", "3 of diamonds", "4 of spades", "5 of clubs", "6 of diamonds"]], "Sarah": [["3 of hearts", "4 of diamonds", "5 of spades", "6 of clubs", "7 of diamonds"]], "Mike": [["4 of hearts", "5 of diamonds", "6 of spades", "7 of clubs", "8 of diamonds"]]}], "type": ["Texas Holdem", ""]}}, {"calculate_displacement": {"initial_velocity": [10], "time": [5], "acceleration": [0, ""]}}]}
{"id": "parallel_multiple_136", "ground_truth": [{"court_case.search": {"docket_number": ["12345"], "location": ["Dallas, TX", "Dallas,TX", "Dallas, Texas"], "full_text": [false, ""]}}, {"chess.rating": {"player_name": ["Magnus Carlsen"], "variant": ["classical", ""]}}, {"get_event_date": {"event": ["Battle of Gettysburg"], "location": ["global", ""]}}, {"calculate_final_speed": {"initial_velocity": [0], "height": [100], "gravity": [9.8, ""]}}]}
{"id": "parallel_multiple_137", "ground_truth": [{"cell_biology.function_lookup": {"molecule": ["ATP"], "organelle": ["mitochondria"], "specific_function": [true]}}, {"get_shortest_driving_distance": {"origin": ["New York", "NY"], "destination": ["Los Angeles", "LA"], "unit": ["miles", ""]}}, {"get_scientist_for_discovery": {"discovery": ["theory of relativity"]}}, {"instrument_price.get": {"brand": ["Fender"], "model": ["Stratocaster"], "finish": ["sunburst"]}}]}
{"id": "parallel_multiple_138", "ground_truth": [{"calculate_magnetic_field": {"current": [5], "radius": [0.02], "permeability": [""]}}, {"concert_booking.book_ticket": {"artist": ["Taylor Swift"], "city": ["New York", "NY"], "num_tickets": [3]}}, {"lawsuit_details.find": {"company_name": ["Apple Inc.", "Apple"], "year": [2010], "case_type": ["Patent"]}}]}
{"id": "parallel_multiple_139", "ground_truth": [{"group_dynamics.pattern": {"total": [30], "extroverts": [15], "introverts": [15]}}, {"mix_paint_color": {"color1": ["blue"], "color2": ["yellow"], "lightness": [70]}}, {"cooking_conversion.convert": {"quantity": [2], "from_unit": ["cups", "c"], "to_unit": ["milliliters", "ml"], "item": ["flour"]}}, {"calculate_electric_field_strength": {"charge": [1e-06], "distance": [0.02], "medium": ["vacuum", ""]}}]}
{"id": "parallel_multiple_140", "ground_truth": [{"calculate_density": {"mass": [10], "volume": [2], "unit": ["kg/m\u00b3", "kilograms per cubic meter", ""]}}, {"mix_paint_color": {"color1": ["red"], "color2": ["blue"], "lightness": [70]}}, {"calculate_density": {"mass": [5], "volume": [1], "unit": ["g/cm\u00b3", "grams per cubic centimeter"]}}, {"mix_paint_color": {"color1": ["yellow"], "color2": ["blue"], "lightness": [30]}}]}
{"id": "parallel_multiple_141", "ground_truth": [{"mutation_type.find": {"snp_id": ["rs123456"], "species": ["Homo sapiens", ""]}}, {"find_exhibition": {"location": ["New York, NY"], "art_form": ["sculpture"], "month": ["Febuary"], "user_ratings": ["high"]}}, {"cellbio.get_proteins": {"cell_compartment": ["nucleus"], "include_description": [true]}}]}
{"id": "parallel_multiple_142", "ground_truth": [{"get_collectables_in_season": {"game_name": ["Animal Crossing"], "season": ["Summer"], "item_type": ["bug"]}}, {"get_collectables_in_season": {"game_name": ["Animal Crossing"], "season": ["Winter"], "item_type": ["fish"]}}, {"mutation_type.find": {"snp_id": ["rs53576"], "species": ["Homo sapiens", ""]}}, {"mutation_type.find": {"snp_id": ["rs1800497"], "species": ["Mus musculus"]}}]}
{"id": "parallel_multiple_143", "ground_truth": [{"math.factorial": {"number": [7]}}, {"find_flute": {"brand": ["Yamaha", "Yamaha"], "specs": [["open hole", "silver headjoint"], ["open-hole", "silver-headjoint"]]}}, {"calculate_genotype_frequency": {"allele_frequency": [0.6], "genotype": ["AA"]}}]}
{"id": "parallel_multiple_144", "ground_truth": [{"forest_growth_forecast": {"location": ["Amazon rainforest", "Amazon"], "years": [10], "include_human_impact": [true]}}, {"forest_growth_forecast": {"location": ["Amazon rainforest", "Amazon"], "years": [10], "include_human_impact": [false, ""]}}, {"get_scientist_for_discovery": {"discovery": ["theory of relativity", "relativity"]}}, {"get_scientist_for_discovery": {"discovery": ["DNA double helix structure", "double helix"]}}]}
{"id": "parallel_multiple_145", "ground_truth": [{"calculate_fitness": {"trait_values": [[0.7, 0.8, 0.9]], "trait_contributions": [[0.3, 0.4, 0.3]]}}, {"lawyer.find_nearby": {"city": ["New York, NY", "NY"], "specialty": [["Civil", "Divorce"]], "fee": [300]}}, {"chess.rating": {"player_name": ["Magnus Carlsen"], "variant": ["classical", ""]}}, {"walmart.purchase": {"loc": ["Los Angeles, CA", "LA"], "product_list": [["Milk", "Bread", "Eggs"]], "pack_size": [[1, 2, 12]]}}]}
{"id": "parallel_multiple_146", "ground_truth": [{"modify_painting": {"size": ["30x40 inches", "30x40"], "medium": ["oil"], "dominant_color": ["red"]}}, {"prediction.evolution": {"species": ["African elephant"], "years": [100], "model": ["Darwin", ""]}}, {"calculate_probability": {"total_outcomes": [52], "favorable_outcomes": [4], "round_to": [3]}}]}
{"id": "parallel_multiple_147", "ground_truth": [{"find_restaurants": {"location": ["San Francisco", "SF", "San Francisco, California", "San Francisco, CA"], "food_type": ["Italian"], "number": [5], "dietary_requirements": [["vegan"]]}}, {"sports.match_schedule": {"team_name": ["Golden State Warriors"], "num_matches": [3], "league": ["NBA", ""]}}, {"get_stock_info": {"company_name": ["Apple Inc."], "detail_level": ["detailed"], "market": ["NASDAQ", ""]}}, {"find_instrument": {"budget": [500], "type": ["guitar"], "make": ["Fender"]}}]}
{"id": "parallel_multiple_148", "ground_truth": [{"celebrity_net_worth.get": {"name": ["Lionel Messi"], "currency": ["EUR", "Euros"]}}, {"celebrity_net_worth.get": {"name": ["LeBron James"], "currency": ["GBP", "British Pounds"]}}, {"calculate_bmi": {"weight": [85], "height": [180], "unit": ["metric", ""]}}, {"calculate_bmi": {"weight": [200], "height": [74], "unit": ["imperial"]}}]}
{"id": "parallel_multiple_149", "ground_truth": [{"hotel_booking": {"location": ["Paris"], "room_type": ["deluxe"], "duration": [5], "start_date": ["20th June", "2023-06-20", "06/20/2023", "Jun.20,2023"], "preferences": [["gym", "free_breakfast"]]}}, {"soccer.get_last_match": {"team_name": ["Manchester United"], "include_stats": [true]}}, {"calculate_BMI": {"weight_kg": [75], "height_m": [1.8]}}]}
{"id": "parallel_multiple_150", "ground_truth": [{"imdb.find_movies_by_actor": {"actor_name": ["Leonardo DiCaprio"], "year": [2010], "category": ["Drama"]}}, {"lawsuits_search": {"company_name": ["Apple Inc."], "location": ["California", "CA"], "year": [2015], "case_type": ["civil", ""]}}, {"flight.book": {"departure_location": ["New York", "NY"], "destination_location": ["London"], "date": ["2022-12-25", "12/25/2022", "Dec 25, 2022"], "time": ["10:00AM"], "direct_flight": ["", true]}}]}
{"id": "parallel_multiple_151", "ground_truth": [{"book_hotel": {"hotel_name": ["Hotel Le Bristol Paris"], "location": ["Paris, France", "Paris"], "room_type": ["suite", "Suite"], "start_date": ["12-01-2022", "2022-12-01", "Dec 1, 2022"], "stay_duration": [10], "view": ["city view", "city"]}}, {"latest_exchange_rate": {"source_currency": ["USD", "US Dollars", "US Dollar"], "target_currency": ["EUR", "Euro"], "amount": [1000]}}, {"safeway.order": {"location": ["Palo Alto, CA", "Palo Alto", "CA"], "items": [["water", "apples", "bread"]], "quantity": [[2, 3, 1]]}}, {"light_travel_time": {"distance_in_light_years": [4.24], "speed_of_light": [*********, ""]}}]}
{"id": "parallel_multiple_152", "ground_truth": [{"geometry.area_triangle": {"base": [12], "height": [15], "unit": ["square meters", "m^2", ""]}}, {"science_history.get_invention": {"invention_name": ["Telephone", "Telephone"], "want_year": [true]}}, {"map_service.get_directions": {"start": ["New York City", "NYC"], "end": ["Los Angeles", "LA"], "avoid": [["tolls", "highways"], ["highways", "tolls"]]}}]}
{"id": "parallel_multiple_153", "ground_truth": [{"run_linear_regression": {"predictors": [["age", "income", "education level"]], "target": ["job satisfaction"], "standardize": [true]}}, {"travel_itinerary_generator": {"destination": ["Paris", "Paris, France"], "days": [7], "daily_budget": [200], "exploration_type": ["urban", ""]}}, {"find_recipe": {"recipeName": ["Chicken Alfredo"], "maxCalories": [800]}}, {"cooking_conversion.convert": {"quantity": [2], "from_unit": ["cups", "cup", "c"], "to_unit": ["grams", "gram", "g"], "item": ["flour"]}}]}
{"id": "parallel_multiple_154", "ground_truth": [{"predict_house_price": {"area": [2000], "rooms": [4], "year": [1985], "location": ["San Francisco", "SF"]}}, {"lawsuit_search": {"entity": ["John Doe", "Mr. John Doe"], "county": ["San Francisco", "San Francisco County"], "state": ["California", ""]}}, {"calculate_probability": {"total_outcomes": [1000], "favorable_outcomes": [5], "round_to": [3]}}]}
{"id": "parallel_multiple_155", "ground_truth": [{"math.power": {"base": [7], "exponent": [3], "mod": [""]}}, {"probabilities.calculate_single": {"total_outcomes": [52], "event_outcomes": [26], "round": [3]}}, {"fetch_DNA_sequence": {"DNA_id": ["XYZ123"], "format": ["genbank", "gb"], "upstream": [5]}}, {"math.power": {"base": [2], "exponent": [5], "mod": [3]}}]}
{"id": "parallel_multiple_156", "ground_truth": [{"run_two_sample_ttest": {"group1": [[12, 15, 18, 22, 25]], "group2": [[20, 23, 26, 29, 32]], "equal_variance": [true, ""]}}, {"restaurant_search.find_closest": {"location": ["Boston, MA", "Boston,MA", "Boston", "MA"], "cuisine": ["Sushi"], "amenities": [["Patio", "Wi-Fi"], ["Patio"], ["Wi-Fi"]]}}, {"get_personality_traits": {"hobby": ["painting"], "trait_count": [5, ""]}}]}
{"id": "parallel_multiple_157", "ground_truth": [{"geometry.area_triangle": {"base": [15], "height": [20], "unit": ["square meters", "m^2", ""]}}, {"geometry.area_triangle": {"base": [10], "height": [30], "unit": ["square meters", "m^2", ""]}}, {"t_test": {"dataset_A": [[12, 15, 18, 20, 22, 25]], "dataset_B": [[14, 16, 19, 21, 23, 26]], "alpha": [0.05, ""]}}, {"event_finder.find_upcoming": {"location": ["Los Angeles, CA", "Los Angeles", "LA, CA"], "genre": ["rock"], "days_ahead": [14]}}]}
{"id": "parallel_multiple_158", "ground_truth": [{"finance.calculate_quarterly_dividend_per_share": {"total_payout": [1000000], "outstanding_shares": [500000]}}, {"get_song_lyrics": {"song_title": ["Hey Jude"], "artist_name": ["The Beatles", "Beatles"], "lang": ["", "English"]}}, {"movie_details.brief": {"title": ["The Godfather"], "extra_info": [true]}}, {"mix_paint_color": {"color1": ["red"], "color2": ["blue"], "lightness": [70]}}]}
{"id": "parallel_multiple_159", "ground_truth": [{"calculate_return_on_equity": {"net_income": [2000000], "shareholder_equity": [10000000], "dividends_paid": [500000]}}, {"get_song_lyrics": {"song_title": ["Bohemian Rhapsody"], "artist_name": ["Queen"], "lang": ["English", ""]}}, {"law_case_search.find_historical": {"subject": ["fraud"], "from_year": [1990], "to_year": [2000]}}, {"public_library.find_nearby": {"location": ["Boston, MA", "Boston,MA", "Boston"], "facilities": [["Reading Room", "Wi-Fi"], ["Wi-Fi", "Reading Room"]]}}]}
{"id": "parallel_multiple_160", "ground_truth": [{"compound_interest": {"principal": [5000], "annual_rate": [0.05], "compounding_freq": ["quarterly"], "time_in_years": [7]}}, {"lawsuits_search": {"company_name": ["Tech Corp"], "location": ["San Francisco", "SF"], "year": [2018], "case_type": [""]}}]}
{"id": "parallel_multiple_161", "ground_truth": [{"chess.rating": {"player_name": ["Magnus Carlsen"], "variant": ["classical", "Classical", "CLASSICAL", ""]}}, {"solve_quadratic": {"a": [2], "b": [-3], "c": [1]}}, {"calculate_cagr": {"initial_value": [5000], "final_value": [8000], "period_in_years": [5]}}]}
{"id": "parallel_multiple_162", "ground_truth": [{"finance.calculate_future_value": {"initial_investment": [5000], "rate_of_return": [0.07], "years": [10], "contribution": [200]}}, {"create_histogram": {"data": [[7, 8, 9, 6, 7, 8, 10, 9, 8, 7]], "bins": [5]}}, {"mix_paint_color": {"color1": ["blue"], "color2": ["yellow"], "lightness": [70]}}]}
{"id": "parallel_multiple_163", "ground_truth": [{"geometry.calculate_area_circle": {"radius": [5], "unit": ["", "meters", "m", "centimeters", "cm"]}}, {"calculate_mutual_fund_balance": {"investment_amount": [5000], "annual_yield": [0.07], "years": [10]}}]}
{"id": "parallel_multiple_164", "ground_truth": [{"calculate_triangle_area": {"base": [10], "height": [5], "unit": ["square meters", "m^2", "sq m", "sq. meters"]}}, {"get_case_info": {"docket": ["12345"], "court": ["Supreme Court"], "info_type": ["accused"]}}, {"get_case_info": {"docket": ["67890"], "court": ["High Court"], "info_type": ["verdict"]}}]}
{"id": "parallel_multiple_165", "ground_truth": [{"event_finder.find_upcoming": {"location": ["San Francisco, CA"], "genre": ["jazz"], "days_ahead": [5]}}, {"lawsuit_search": {"company": ["Apple Inc."], "start_date": ["2020-01-01", "01/01/2020", "Jan 1, 2020"], "location": ["California", "CA"], "status": ["", "ongoing"]}}, {"walmart.check_price": {"items": [["olive oil", "rice", "beans"], ["olive oil", "beans", "rice"], ["rice", "olive oil", "beans"], ["rice", "beans", "olive oil"], ["beans", "olive oil", "rice"], ["beans", "rice", "olive oil"]], "quantities": [[2, 3, 4]], "store_location": ["San Jose, CA"]}}]}
{"id": "parallel_multiple_166", "ground_truth": [{"park_information": {"park_name": ["Yellowstone National Park"], "information": [["Elevation", "Area"]]}}, {"calculate_stock_return": {"investment_amount": [5000], "annual_growth_rate": [0.07], "holding_period": [10], "dividends": [true]}}, {"legal_case.fetch": {"case_id": ["LC12345"], "details": [true]}}, {"park_information": {"park_name": ["Yosemite National Park"], "information": [["Location", "Established Year"]]}}]}
{"id": "parallel_multiple_167", "ground_truth": [{"get_collectables_in_season": {"game_name": ["Animal Crossing"], "season": ["Summer"], "item_type": ["fish"]}}, {"game_score.highest": {"game": ["Fortnite"], "platform": ["Playstation", "PS"], "region": ["Asia"]}}, {"lawsuit_details.find": {"company_name": ["Apple Inc."], "year": [2018], "case_type": [""]}}, {"calculate_binomial_probability": {"number_of_trials": [10], "number_of_successes": [3], "probability_of_success": [0.7]}}]}
{"id": "parallel_multiple_168", "ground_truth": [{"lawsuits_search": {"company_name": ["TechCorp"], "location": ["San Francisco", "SF"], "year": [2018], "case_type": ["civil"]}}, {"hilton_hotel.check_availability": {"location": ["New York City", "NYC"], "check_in_date": ["2022-10-15", "10/15/2022", "Oct. 15, 2022"], "check_out_date": ["2022-10-20", "10/20/2022", "Oct. 20, 2022"], "no_of_adults": [2], "hotel_chain": ["Hilton", ""]}}]}
{"id": "parallel_multiple_169", "ground_truth": [{"get_team_score": {"team_name": ["Los Angeles Lakers", "L.A. Lakers"], "league": ["NBA"], "include_player_stats": [true]}}, {"get_team_score": {"team_name": ["Manchester United", "Man United", "Man Utd"], "league": ["Premier League", "EPL", "English Premier League"], "include_player_stats": [true]}}, {"weather.humidity_forecast": {"location": ["New York", "New York, NY", "NYC"], "days": [5], "min_humidity": [60]}}, {"weather.humidity_forecast": {"location": ["London"], "days": [7], "min_humidity": [""]}}]}
{"id": "parallel_multiple_170", "ground_truth": [{"create_player_profile": {"player_name": ["DragonSlayer"], "class_type": ["Warrior"], "starting_level": [5]}}, {"concert.find_nearby": {"location": ["New York, NY", "NY", "New York"], "genre": ["Rock"]}}, {"poker_probability.full_house": {"deck_size": [52], "hand_size": [5]}}, {"calculate_slope_gradient": {"point1": [[40.7128, -74.006]], "point2": [[34.0522, -118.2437]], "unit": ["degree", ""]}}]}
{"id": "parallel_multiple_171", "ground_truth": [{"sports_ranking": {"team": ["New York Yankees", "NY Yankees"], "league": ["Major League Baseball", "MLB"], "season": [2019]}}, {"sports_ranking": {"team": ["Los Angeles Lakers", "LA Lakers"], "league": ["National Basketball Association", "NBA"], "season": [2020]}}, {"air_quality": {"location": ["Los Angeles", "Los Angeles, California", "LA"], "date": ["2020-12-25", "12/25/2020", "Dec 25, 2020", "December 25, 2020"]}}, {"air_quality": {"location": ["New York", "New York, NY", "NY"], "date": ["2021-01-01", "01/01/2021", "Jan 1, 2021", "January 1, 2021"]}}]}
{"id": "parallel_multiple_172", "ground_truth": [{"grocery_store.find_best": {"my_location": ["123 Main Street, New York", "123 Main St., NY"], "rating": [4.5], "products": [["milk", "bread", "eggs"]]}}, {"sculpture.get_details": {"artist": ["Auguste Rodin"], "title": ["The Thinker"], "detail": ["material", ""]}}, {"calculate_emissions": {"distance": [12000], "fuel_type": ["diesel"], "fuel_efficiency": [25], "efficiency_reduction": [2]}}]}
{"id": "parallel_multiple_173", "ground_truth": [{"restaurant.find_nearby": {"location": ["New York, NY", "NY", "New York"], "cuisine": ["Thai"], "max_distance": [10.0]}}, {"restaurant.find_nearby": {"location": ["New York, NY", "NY", "New York"], "cuisine": ["Italian"], "max_distance": [10.0]}}, {"ecology_data.precipitation_stats": {"location": ["Amazon rainforest"], "time_frame": ["year", "1 year", "12 months"]}}, {"ecology_data.precipitation_stats": {"location": ["Amazon rainforest"], "time_frame": ["five_years", "5 years"]}}]}
{"id": "parallel_multiple_174", "ground_truth": [{"convert_currency": {"base_currency": ["EUR", "Euros"], "target_currency": ["USD", "US dollars"], "amount": [5000]}}, {"ecology.get_turtle_population": {"location": ["Galapagos Islands"], "year": [2018], "species": [true]}}, {"map_service.get_directions": {"start": ["New York", "NY"], "end": ["Los Angeles", "LA"], "avoid": [["tolls", "ferries"], ["ferries", "tolls"]]}}, {"convert_currency": {"base_currency": ["GBP", "British Pounds"], "target_currency": ["JPY", "Japanese Yen"], "amount": [3000]}}]}
{"id": "parallel_multiple_175", "ground_truth": [{"get_current_time": {"location": ["Tokyo"], "country": ["Japan", "JP"], "timezone": ["Asia/Tokyo"]}}, {"get_current_time": {"location": ["New York", "NY"], "country": ["United States", "US", "USA"], "timezone": ["America/New_York"]}}, {"get_stock_info": {"company_name": ["Microsoft"], "detail_level": ["detailed"], "market": ["NASDAQ", ""]}}, {"get_stock_info": {"company_name": ["Apple"], "detail_level": ["summary"], "market": ["NASDAQ", ""]}}]}
{"id": "parallel_multiple_176", "ground_truth": [{"hotel_booking": {"hotel_name": ["Hilton"], "location": ["Los Angeles, CA", "LA, CA", "Los Angeles, California"], "start_date": ["2022-05-01", "05/01/2022", "May 1, 2022"], "end_date": ["2022-05-10", "05/10/2022", "May 10, 2022"], "rooms": [2]}}, {"get_time_difference": {"place1": ["New York, NY", "NY, NY", "New York, New York"], "place2": ["Los Angeles, CA", "LA, CA", "Los Angeles, California"]}}, {"calculate_bmi": {"weight": [75], "height": [180], "system": ["metric", ""]}}, {"sentiment_analysis": {"text": ["I had a wonderful day at the beach. The weather was perfect and I enjoyed a delicious ice cream."], "language": ["English"]}}]}
{"id": "parallel_multiple_177", "ground_truth": [{"history.get_key_events": {"country": ["France"], "start_year": [1800], "end_year": [1900], "event_type": [["War", "Economy"]]}}, {"get_sculpture_value": {"sculpture": ["The Thinker"], "artist": ["Auguste Rodin"]}}, {"get_sculpture_value": {"sculpture": ["The Kiss"], "artist": ["Auguste Rodin"]}}]}
{"id": "parallel_multiple_178", "ground_truth": [{"locate_tallest_mountains": {"location": ["Tokyo"], "radius": [200], "amount": [5]}}, {"calculate_entropy_change": {"initial_temp": [300], "final_temp": [350], "heat_capacity": [1.5], "isothermal": ["", true]}}, {"get_event_date": {"event": ["Battle of Waterloo"], "location": ["Belgium"]}}]}
{"id": "parallel_multiple_179", "ground_truth": [{"update_user_info": {"user_id": [12345], "update_info": [{"name": ["John Doe"], "email": ["<EMAIL>"]}], "database": ["CustomerInfo", ""]}}, {"soccer.get_last_match": {"team_name": ["Manchester United", "Man United", "Man U", "MUFC"], "include_stats": [true]}}, {"US_president.in_year": {"year": [1980], "full_name": [true]}}, {"find_card_in_deck": {"rank": ["Ace"], "suit": ["Spades"], "deck": [[], ""]}}]}
{"id": "parallel_multiple_180", "ground_truth": [{"get_discoverer": {"discovery": ["Higgs Boson", "higgs boson", "Higgs Boson particle"], "detail": [true]}}, {"diabetes_prediction": {"weight": [180], "height": [71], "activity_level": ["moderately active"]}}, {"museum_working_hours.get": {"museum": ["Louvre", "the Louvre museum"], "location": ["Paris", "Paris, France"], "day": ["Monday", "monday", ""]}}]}
{"id": "parallel_multiple_181", "ground_truth": [{"math.gcd": {"num1": [48], "num2": [36]}}, {"historical_contrib.get_contrib": {"scientist": ["Albert Einstein"], "date": ["1905-05-14", "05/14/1905", "May 14, 1905"], "category": ["Physics"]}}, {"music.calculate_note_duration": {"first_note_frequency": [440], "second_note_frequency": [880], "tempo": [100]}}]}
{"id": "parallel_multiple_182", "ground_truth": [{"prob_dist.binomial": {"trials": [20], "successes": [10], "p": [0.6]}}, {"calculate_paint_needed": {"coverage_rate": [350], "length": [12], "height": [8]}}, {"musical_scale": {"key": ["D"], "scale_type": ["minor"]}}]}
{"id": "parallel_multiple_183", "ground_truth": [{"card_game_probability.calculate": {"total_cards": [52], "desired_cards": [13], "cards_drawn": [1, ""]}}, {"card_game_probability.calculate": {"total_cards": [52], "desired_cards": [4], "cards_drawn": [1, ""]}}, {"get_sculpture_info": {"artist_name": ["Pablo Picasso"], "detail": [true]}}, {"find_exhibition": {"location": ["New York, NY", "NY", "New York"], "art_form": ["sculpture"], "month": ["December", "12", "12/2022", "Dec", "Dec."], "user_ratings": ["high"]}}]}
{"id": "parallel_multiple_184", "ground_truth": [{"analyze_structure": {"building_id": ["B1234"], "floors": [[1, 2, 3, 4]], "mode": ["dynamic"]}}, {"player_statistic": {"player_name": ["Michael Jordan"], "year": [1996], "team_name": [""]}}, {"analyze_structure": {"building_id": ["B5678"], "floors": [[5, 6, 7, 8]], "mode": ["static", ""]}}, {"player_statistic": {"player_name": ["LeBron James"], "year": [2018], "team_name": ["Los Angeles Lakers", "Lakers"]}}]}
{"id": "parallel_multiple_185", "ground_truth": [{"metropolitan_museum.get_top_artworks": {"number": [10], "sort_by": ["popularity", ""]}}, {"metropolitan_museum.get_top_artworks": {"number": [5], "sort_by": ["chronological"]}}, {"lawsuit_search": {"company": ["Google"], "start_date": ["2020-01-01", "01/01/2020", "Jan 1, 2020"], "location": ["California", "CA"], "status": ["ongoing", ""]}}, {"lawsuit_search": {"company": ["Microsoft"], "start_date": ["2018-01-01", "01/01/2018", "Jan 1, 2018"], "location": ["New York", "NY"], "status": ["settled"]}}]}
{"id": "parallel_multiple_186", "ground_truth": [{"identify_color_rgb": {"color_name": ["Cerulean"], "standard": ["pantone", "Pantone"]}}, {"guitar_price.find": {"model": ["Fender Stratocaster"], "condition": ["Good"], "location": ["Los Angeles", "LA", "Los Angeles, CA", "Los Angeles, California"]}}, {"board_game.chess.get_top_players": {"location": ["New York", "NY", "New York, NY", "New York, New York"], "minimum_rating": [2200], "number_of_players": [15]}}]}
{"id": "parallel_multiple_187", "ground_truth": [{"get_defense_ranking": {"season": [2018], "top": [5]}}, {"array_sort": {"list": [[23, 45, 12, 89, 34, 67, 29]], "order": ["descending"]}}, {"calculate_cagr": {"initial_value": [5000], "final_value": [15000], "period_in_years": [7]}}]}
{"id": "parallel_multiple_188", "ground_truth": [{"calculate_binomial_probability": {"number_of_trials": [20], "number_of_successes": [5], "probability_of_success": [0.25]}}, {"sports_ranking.get_top_player": {"sport": ["basketball"], "gender": ["female", "women"]}}, {"find_instrument": {"budget": [500], "type": ["guitar"], "make": ["Fender"]}}, {"electromagnetic_force": {"charge1": [2], "charge2": [3], "distance": [0.5], "medium_permittivity": [8.854e-12, ""]}}]}
{"id": "parallel_multiple_189", "ground_truth": [{"vegan_restaurant.find_nearby": {"location": ["San Francisco, CA", "San Francisco"], "operating_hours": [22]}}, {"hotel_booking": {"location": ["San Francisco, CA", "San Francisco"], "room_type": ["deluxe"], "duration": [3], "start_date": ["July 1st", "2023-07-01", "07/01/2023"], "preferences": [["pet_friendly", "gym"]]}}, {"sports_team.get_schedule": {"team_name": ["Golden State Warriors"], "num_of_games": [5], "league": ["NBA"], "location": [""]}}, {"find_card_in_deck": {"rank": ["Queen"], "suit": ["Hearts"], "deck": [""]}}]}
{"id": "parallel_multiple_190", "ground_truth": [{"maps.get_distance_duration": {"start_location": ["New York", "NY"], "end_location": ["Boston", "Boston, MA", "Boston,MA"], "traffic": [true]}}, {"board_game.chess.get_top_players": {"location": ["San Francisco", "San Francisco, CA"], "minimum_rating": [2500], "number_of_players": [5]}}, {"get_historical_GDP": {"country": ["Japan"], "start_year": [2000], "end_year": [2020]}}]}
{"id": "parallel_multiple_191", "ground_truth": [{"find_card_in_deck": {"rank": ["King"], "suit": ["Hearts", "hearts"], "deck": [""]}}, {"currency_exchange.convert": {"base_currency": ["Euros", "EUR"], "target_currency": ["US dollars", "USD"], "amount": [100]}}, {"recipe.unit_conversion": {"value": [2], "from_unit": ["cups", "cup"], "to_unit": ["tablespoons", "tablespoon"], "precision": [0, ""]}}, {"local_nursery.find": {"location": ["San Francisco", "San Francisco, California", "SF"], "plant_types": [["Annual", "Tree"]]}}]}
{"id": "parallel_multiple_192", "ground_truth": [{"find_recipe": {"dietary_restrictions": ["vegan"], "recipe_type": ["main course"], "time": [45]}}, {"poker_probability.full_house": {"deck_size": [52], "hand_size": [5]}}, {"hospital.locate": {"location": ["Denver, CO", "Denver", "CO"], "radius": [10], "department": ["Emergency"]}}]}
{"id": "parallel_multiple_193", "ground_truth": [{"get_scientist_for_discovery": {"discovery": ["Relativity Theory"]}}, {"flight.book": {"departure_location": ["Los Angeles", "LAX", "Los Angeles, CA"], "destination_location": ["New York", "NY", "New York, NY"], "date": ["2022-12-25", "12/25/2022", "Dec 25, 2022"], "time": ["10:00 AM"], "direct_flight": [true]}}, {"game_stats.fetch_player_statistics": {"game": ["Call of Duty"], "username": ["gamer123"], "platform": ["PlayStation", "PS"]}}, {"event_finder.find_upcoming": {"location": ["San Francisco, CA", "San Francisco"], "genre": ["rock"], "days_ahead": [14]}}]}
{"id": "parallel_multiple_194", "ground_truth": [{"plot_sine_wave": {"start_range": [0], "end_range": [10], "frequency": [5], "amplitude": [2], "phase_shift": [1]}}, {"random_forest.train": {"n_estimators": [200], "max_depth": [10], "data": ["dataset"]}}, {"soccer.get_last_match": {"team_name": ["Manchester United"], "include_stats": [true]}}, {"building.get_dimensions": {"building_name": ["Empire State Building"], "unit": ["feet", "ft"]}}]}
{"id": "parallel_multiple_195", "ground_truth": [{"multiplayer_game_finder": {"platform": ["Windows 10"], "rating": [4], "genre": ["Action"]}}, {"calculate_area_under_curve": {"function": ["x**2"], "interval": [[0, 5]], "method": ["trapezoidal", ""]}}, {"geo_distance.calculate": {"start_location": ["Los Angeles", "Los Angeles, CA", "LA"], "end_location": ["New York", "New York, NY", "NYC"], "units": ["kilometers", "km"]}}, {"send_email": {"to": ["<EMAIL>"], "subject": ["Meeting Reminder"], "body": ["Do not forget about our meeting tomorrow at 10 AM"], "cc": ["<EMAIL>"], "bcc": [""]}}]}
{"id": "parallel_multiple_196", "ground_truth": [{"recipe_info.get_calories": {"website": ["AllRecipes"], "recipe": ["Chicken Alfredo"], "optional_meal_time": ["Dinner", ""]}}, {"get_stock_price": {"company_names": [["Apple", "Microsoft", "Tesla"]]}}, {"get_team_ranking": {"team_name": ["Brazil"], "year": [2018], "gender": ["men", ""]}}]}
{"id": "parallel_multiple_197", "ground_truth": [{"recipe_search": {"dietary_restriction": ["Vegetarian"], "ingredients": [["potatoes", "carrots", "onions"]], "servings": [4]}}, {"detailed_weather_forecast": {"location": ["New York", "NY"], "duration": [12], "include_precipitation": [true]}}, {"get_time_difference": {"place1": ["New York", "NY"], "place2": ["Tokyo"]}}]}
{"id": "parallel_multiple_198", "ground_truth": [{"find_recipe": {"dietary_restrictions": ["vegan"], "recipe_type": ["main course"], "time": [30]}}, {"science_history.get_discovery_details": {"discovery": ["Gravity"], "method_used": ["default", ""]}}, {"science_history.get_discovery_details": {"discovery": ["Higgs Boson", "Higgs Boson particle"], "method_used": ["default", ""]}}, {"find_recipe": {"dietary_restrictions": ["gluten free"], "recipe_type": ["dessert"], "time": [45]}}]}
{"id": "parallel_multiple_199", "ground_truth": [{"timezone.convert": {"time": ["2pm"], "from_timezone": ["New York", "NY", "America/New_York"], "to_timezone": ["London", "Europe/London"]}}, {"timezone.convert": {"time": ["2pm"], "from_timezone": ["New York", "NY", "America/New_York"], "to_timezone": ["Tokyo", "Asia/Tokyo"]}}, {"calculate_emission_savings": {"energy_type": ["solar"], "usage_duration": [12], "region": ["California", "CA"]}}]}