[{"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Slides API - presentations.batchUpdate", "api_call": "service.presentations().batchUpdate(presentationId: str).execute()", "api_version": "v1", "api_arguments": {"presentationId": "[REQUIRED] string: The presentation to apply the updates to."}, "functionality": "Applies one or more updates to the presentation. Each request is validated before being applied. If any request is not valid, then the entire request will fail and nothing will be applied. Some requests have replies to give you some information about how they are applied. Other requests do not need to return information; these each return an empty reply. The order of replies matches that of the requests. For example, suppose you call batchUpdate with four updates, and only the third one returns information. The response would have two empty replies: the reply to the third request, and another empty reply, in that order. Because other users may be editing the presentation, the presentation might not exactly reflect your changes: your changes may be altered with respect to collaborator changes. If there are no collaborators, the presentation should reflect your changes. In any case, the updates in your request are guaranteed to be applied together atomically.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/slides/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Slides API - presentations.create", "api_call": "service.presentations().create().execute()", "api_version": "v1", "api_arguments": {}, "functionality": "Creates a blank presentation using the title given in the request. If a `presentationId` is provided, it is used as the ID of the new presentation. Otherwise, a new ID is generated. Other fields in the request, including any provided content, are ignored. Returns the created presentation.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/slides/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Slides API - presentations.get", "api_call": "service.presentations().get(presentationId: str).execute()", "api_version": "v1", "api_arguments": {"presentationId": "[REQUIRED] string: The ID of the presentation to retrieve."}, "functionality": "Gets the latest version of the specified presentation.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/slides/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Slides API - presentations.pages.get", "api_call": "service.presentations().pages().get(presentationId: str, pageObjectId: str).execute()", "api_version": "v1", "api_arguments": {"pageObjectId": "[REQUIRED] string: The object ID of the page to retrieve.", "presentationId": "[REQUIRED] string: The ID of the presentation to retrieve."}, "functionality": "Gets the latest version of the specified page in the presentation.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/slides/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Slides API - presentations.pages.getThumbnail", "api_call": "service.presentations().pages().getThumbnail(presentationId: str, pageObjectId: str).execute()", "api_version": "v1", "api_arguments": {"pageObjectId": "[REQUIRED] string: The object ID of the page whose thumbnail to retrieve.", "presentationId": "[REQUIRED] string: The ID of the presentation to retrieve.", "thumbnailProperties.mimeType": "string: The optional mime type of the thumbnail image. If you don't specify the mime type, the mime type defaults to PNG.", "thumbnailProperties.thumbnailSize": "string: The optional thumbnail image size. If you don't specify the size, the server chooses a default size of the image."}, "functionality": "Generates a thumbnail of the latest version of the specified page in the presentation and returns a URL to the thumbnail image. This request counts as an [expensive read request](/slides/limits) for quota purposes.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/slides/api/reference/rest"}}]