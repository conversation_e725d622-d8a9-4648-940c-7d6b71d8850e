{"cells": [{"cell_type": "markdown", "id": "121a0915-a78a-4921-87d0-0dc4fad7ce23", "metadata": {}, "source": ["# 从0到1训练大模型\n", "# Part 1 数据收集与数据预处理"]}, {"cell_type": "markdown", "id": "caf72327-062f-4184-a176-7977fe8bae9b", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/11.jpg)"]}, {"cell_type": "markdown", "id": "413b0a98-a2de-4140-9987-2ecf3a28def3", "metadata": {}, "source": ["## 0 开篇：MateConv训练之旅"]}, {"cell_type": "markdown", "id": "ed6242a3-083a-48da-90b8-1d9b9d0a6db6", "metadata": {}, "source": ["在人工智能领域，大模型的崛起正掀起一股新的浪潮。随着技术的不断突破，大模型已逐渐成为AI发展不可或缺的重要基石。无论是在自然语言处理、计算机视觉，还是其他领域，大模型展现了前所未有的潜力。而随着计算能力的提升与行业需求的细化，**越来越多的岗位需要针对特定应用场景开发专业的大模型，比如编程助手、医疗诊断工具、法律专业大模型，甚至是用于保密环境的专属模型**。\n", "\n", "然而，尽管大模型已经如此热门，互联网上关于如何从零开始训练一套大模型的详细资料却凤毛麟角、各岗位的面试问题围绕原理深度展开、训练经验可谓十分宝贵。这种空白促使我们踏上了探索之路、亲自开启训练，我们希望通过亲身实践，**为大模型课程学员们贡献第一手的经验与资料、同时为你们提供一个非常强有力的实践案例**。\n", "\n", "我们训练的大模型名为 **MateConv**，这一旅程从初始的“小试牛刀”到目标明确的1B规模模型，凝聚了许多的努力。正如老师们在答疑群中提到，考虑到训练投入的成本、这门课程不仅知识很“值钱”、物理上也很“值钱”。以下是我们的训练概况：\n", "\n", "| 模型名称     | 模型参数量级      | 数据量级                | 硬件资源               | 训练时间              | 训练成本                |\n", "| ------------ | ----------------- | ----------------------- | ---------------------- | --------------------- | ----------------------- |\n", "| **MateConv mini** | 0.02B<br>（两千万参数） | 约60G（分词器约1G、预训练约35G、微调约20G） | RTX 3090 x2           | 全流程约3-4天          | AutoDL租赁，约250元     |\n", "| **MateConv**      | 1B<br>（10亿参数）      | 约3~4T文本+1T代码（实际收集超过10T数据、如果你们有更大的运存可以使用更大的数据集） | A800 x 8，硬盘4T      | 全流程约1个多月（预训练约20天） | AutoDL租赁2.5\\~3万元；购置设备需80\\~100万元，电费约3000r |\n", "\n", "为了实现MateConv的训练，我们参考了国内外大量的语言模型训练代码和资料，结合团队的摸索与实验，逐步搭建起了这一套完整的训练流程。尽管由于资源限制、以及团队还在同步进行其他大模型产品开发、我们未能穷尽所有可能的训练手段和理论，但我们已投入百万级的训练与人力成本，将这份经验倾囊相授，只为更多有志于大模型训练的团队和个人能够有更好的学习基础与资料、同时为大家抛砖引玉。\n", "\n", "接下来的时间里，我们会将这次从0到1的训练历程、技术细节、优化经验与心得完整记录在课程中。欢迎你和我们一起训练、共同探索不同设备、不同数据下的更多可能性，我们期待与更多的大模型技术人一起，推动大模型技术的发展，为行业带来更多可能！\n", "\n", "MateConv项目的第一步已经迈出，一起来吧！"]}, {"cell_type": "code", "execution_count": null, "id": "12697c17-cdec-4fe1-b3d6-04ba5a160a09", "metadata": {}, "outputs": [], "source": ["如何让传统文本转变成大模型能够识别的数据？\n", "\n", "文字/段落  ==> chunkize ==> tokenizer（token）==> encoder[0,1,2,3,4]  ==> 词向量构成"]}, {"cell_type": "markdown", "id": "f258a56a-af89-45dd-8ccc-2dd763a9cc62", "metadata": {}, "source": ["- **大模型训练流程概览与讲解方式**"]}, {"cell_type": "markdown", "id": "75d93552-3e40-4bcb-9b64-7ec14eebac89", "metadata": {}, "source": ["从 **0到1** 训练大模型是一个复杂而系统的工程，需要涵盖从数据准备到模型部署的多个环节。以下是一个完整的流程框架："]}, {"cell_type": "markdown", "id": "157a0895-5521-45bf-9e5c-2a07132350ab", "metadata": {}, "source": ["| 流程            | 说明                                                                                           |\n", "|:---------------:|--|\n", "| **数据准备**        | 收集高质量、覆盖面广的训练数据，对其进行清洗、去噪和格式化处理。划分<br>训练集、验证集，并存储为高效读取的格式。这一步为模型提供了扎实的输入基础。 |\n", "| **硬件与环境配置**  | 为模型训练准备高性能硬件（如 A800、A100 GPU），搭建分布式训练环境，<br>并优化深度学习框架的配置。这一步确保训练效率和稳定性。 |\n", "| **分词器训练**      | 根据训练数据量和模型任务需求，选择适合的分词算法（如 BPE 或 <br>SentencePiece）。分词器决定了模型如何理解数据，是数据与模型的桥梁。 |\n", "| **设计模型架构**    | 选择适合的模型结构（如 GPT、BERT），并配置参数量、层数、激活函数<br>等细节。对于大规模任务，可以结合领域特点定制模型。 |\n", "| **预训练**        | 使用无监督任务从海量数据中提取通用知识，比如语言模型的自回归建模或<br>掩码建模。预训练的效果直接影响模型后续的微调能力。 |\n", "| **意图对齐微调**        | 通过监督微调（SFT）或强化学习对齐（RLHF），让模型学习人类偏好，<br>避免输出无意义或有害内容。对齐步骤是模型实用化的关键。 |\n", "| **特定优化微调**  | 在特定任务（如文本分类、问答）上微调模型，结合冻结与解冻层的策略进一步<br>优化性能，满足应用需求。                          |\n", "| **模型量化**       | 通过剪枝、量化和知识蒸馏等技术优化模型，提高推理效率，降低计算与存储<br>成本，使模型更适合部署环境。                            |\n", "| **部署与监控**      | 将模型部署到生产环境中，使用推理优化工具提升服务效率，同时通过实时<br>监控与用户反馈不断改进模型性能和可靠性。                     |"]}, {"attachments": {}, "cell_type": "markdown", "id": "012de218-ee13-4481-9e2e-72fb9c7f58f2", "metadata": {}, "source": ["**在课程中我们呈现的环节有——**\n", "\n", "| 流程            | MateConv Mini<br> (0.02B) | MateConv <br> (1B) | 单独的案例 |\n", "|:---------------:|:---------------------:|:-------------:|:-:|\n", "| 数据准备        | ✅ | ✅ | ✅ |\n", "| 硬件与环境配置  | ✅                     | ✅             | ✅|\n", "| 分词器训练      | ✅  | --              |--|\n", "| 设计模型架构    | ✅  | ✅     |✅|\n", "| 预训练          | ✅                     | ✅             |--|\n", "| 意图对齐微调        | ✅                     | ✅             |--|\n", "| 特定优化微调  | --                     | --             |✅|\n", "| 模型量化       | --                   | --             |✅|\n", "| 部署与监控      | ✅                     | --             |--|"]}, {"cell_type": "markdown", "id": "5c218f47-67f2-4fac-af64-6f207032efc6", "metadata": {}, "source": ["在进行课程讲解时，每个环节我们将采用【技术概览 - MateConv Mini代码实现 - MateConv代码实现 - 深度原理或补充】的4段式结构进行讲解、你可以挑选自己最感兴趣的部分进行学习。"]}, {"cell_type": "markdown", "id": "6340b18e-ccb8-48e3-a93c-6b47ccf71143", "metadata": {}, "source": ["**在开始正式课程之前、千万需要先阅览硬件配置指南以及Autodl环境配置流程**。确保你已经搭建好线上环境、并且在finalshell中执行了下面这些流程 ↓\n", "\n", "```shell\n", "\n", "#建立线上虚拟环境，命名为MateConv\n", "conda create --name MateConv python=3.10\n", "conda init\n", "source ~/.bashrc\n", "conda activate MateConv\n", "\n", "#创建<PERSON><PERSON><PERSON>\n", "conda install jupyterlab\n", "conda install ipykernel\n", "python -m ipykernel install --user --name MateConv --display-name \"Python (MateConv)\"\n", "\n", "#创建项目主目录\n", "cd ~/autodl-tmp\n", "mk<PERSON>\n", "\n", "#打开jupyter\n", "cd ~/autodl-tmp/MateConv\n", "jupyter lab --allow-root\n", "\n", "#根据requirements.txt配置环境\n", "touch requirements.txt\n", "cd ~/autodl-tmp/MateConv\n", "pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple\n", "```"]}, {"cell_type": "markdown", "id": "78a1006e-dba1-4ac3-b5e7-43d8688ea6fd", "metadata": {}, "source": ["## 1 MateConv Mini的数据收集与数据预处理"]}, {"cell_type": "markdown", "id": "d8088583-654a-43a9-bd81-52e8a067f3ff", "metadata": {}, "source": ["数据是大模型训练的核心，质量和数量直接影响模型效果。在我们进行数据准备时，我们要考虑到模型的具体用途、模型的规模以及模型的精度要求等等、这些都会影响我们对数据的收集和参考。在大模型进行训练时，我们的数据来源主要有两种——一种是公开的、已经收集整理好的数据集、主要来自于Huggingface上已开源的各种中英文/代码数据集，另一种则是存储于数据库或者干脆就是以文件形式存储的原始数据（raw data）、核心来源是我们收集的1000份PDF行业报告。"]}, {"cell_type": "markdown", "id": "00a0623e-0568-4d63-8151-b4947bc92fc8", "metadata": {}, "source": ["### 1.1 MateConv Mini所使用的数据集"]}, {"cell_type": "markdown", "id": "45152d5b-6c0c-4f5b-aa30-afca69e1339f", "metadata": {}, "source": ["MateConv Mini所使用的语料都是开源数据集、以中文为主、并且以轻量、高质量、少流程为主要追求、希望能够在最小的数据量级内获得最好的成果。分词器数据虽小，但能满足小模型实验需求；预训练数据覆盖广泛且经过清洗和去重，确保了多样性和高质量，对小规模模型的泛化能力有良好支持；意图对齐数据集经过精细设计，能帮助模型对齐人类意图，提升微调阶段的表现。然而，分词器数据词表覆盖范围有限，可能在特定领域的适应性上存在不足，同时预训练数据和微调数据在深度领域信息上略显欠缺。整体而言，这些数据集为 MateConv Mini 提供了扎实的训练基础，但在扩展到更大模型或特定领域时，还需补充规模更大、领域更专的高质量数据。"]}, {"cell_type": "markdown", "id": "40cfb058-edb8-4f6d-852f-669c60b48e57", "metadata": {}, "source": ["| 流程       | 数据集名称 | 数据量级 | 数据概况 |\n", "|:---------------:|:---------------------:|:-------------:|:-:|\n", "| Tokenizer训练流程 | Huggingface开源<br>Tokenizer_train.jsonl | 1G | **jsonl超短文本数据集**<br><br>开源的超小型词表数据集，总词表量只有6400，几乎是所有开源Tokenizer数据集中最小的|\n", "| 预训练流程   | huggingface开源<br>序列猴子通用文本数据集 | 31G | **jsonl短文本数据集**<br><br>序列猴子通用文本数据集由来自**网页、百科、博客、问答、开源代码、书籍、报刊、专利、教材、考题等多种公开可获取的数据进行汇总清洗之后而形成的大语言模型预训练语料**。它将不同来源的HTML、TEXT、PDF、EPUB等各类格式的数据统一整理为JSONL格式，并进行了仔细的筛选、去重、清洗和**价值对齐**，从而形成了一份覆盖全面、规模庞大、安全可信、质量上乘的预训练语料。|\n", "| 意图对齐微调   | ModelScope开源<br>匠数科技大模型SFT数据集 | 18G  | **jsonl对话（问答对数据）**<br><br>匠数大模型SFT数据集是从**网络上的公开数据源收集并整理得来**，经过细致的数据清洗、格式统一，最终获得了用于大模型SFT的包含10M条数据的中文数据集和包含2M条数据的英文数据集。|"]}, {"cell_type": "markdown", "id": "52d7b8c3-288a-4722-9117-dfb336019c14", "metadata": {}, "source": ["我们为你准备了这些数据集，你都可以在课程所对应的网盘中找到他们 ↓"]}, {"cell_type": "markdown", "id": "c89fdf15-2131-40ac-a864-197d859ccc7c", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/12.png)"]}, {"cell_type": "markdown", "id": "d65d54dd-bf90-4883-884b-c959484ae03a", "metadata": {}, "source": ["<font color = \"red\">**导入数据查看一下**，</font>这一步你可以在本地jupyter上操作。"]}, {"cell_type": "code", "execution_count": 6, "id": "d5f3f6b2-31e7-4afb-80cf-2040708b1942", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Reading from: D:\\pythonwork\\2025LLMtraining\\MateConvMini\\MateConv_Dataset\\sft_data_zh.jsonl\n", "Sample 1: {\n", "    \"id\": 1,\n", "    \"instruction\": \"\",\n", "    \"input\": \"好的。现在请你将这个文本中的所有的逗号都替换成空格。\",\n", "    \"output\": \"好的，请稍等一下，现在我会将文本中的所有逗号替换为空格。处理后文本为：\\\"这是一个句子 目的是看看是否可以正确地从这个句子中删除关键词。\\\"。处理结果如何？\",\n", "    \"history\": [\n", "        [\n", "            \"给定一段文本和关键词列表，删除文本中包含所有给定关键词的子字符串。\\n文本：\\\"这是一个测试句子，目的是看看模型是否可以正确地从这个句子中删除关键词。\\\"\\\\n关键词列表：[‘测试’，‘模型’]\",\n", "            \"删除包含所有给定关键词的子字符串后，文本变为：\\\"这是一个句子，目的是看看是否可以正确地从这个句子中删除关键词。\\\"\"\n", "        ]\n", "    ],\n", "    \"language\": \"chinese\",\n", "    \"data_source\": \"https://huggingface.co/datasets/BelleGroup/train_3.5M_CN\",\n", "    \"input_len\": 59,\n", "    \"output_len\": 66,\n", "    \"num_utter\": 2,\n", "    \"type\": 31,\n", "    \"type_keyword\": [\n", "        \"字符串\",\n", "        \"代码\",\n", "        \"函数\",\n", "        \"编写\",\n", "        \"实现\",\n", "        \"给定\",\n", "        \"使用\",\n", "        \"输入\",\n", "        \"文本\",\n", "        \"程序\"\n", "    ]\n", "}\n", "Sample 2: {\n", "    \"id\": 2,\n", "    \"instruction\": \"\",\n", "    \"input\": \"帮我回答一道历史题目。清朝时期的八旗共有多少旗人？\",\n", "    \"output\": \"清朝时期八旗旗人总数约为200万人左右，其中正黄旗、正蓝旗、正白旗、正红旗的人数较多，其他旗的人数较少。\",\n", "    \"history\": [\n", "        [\n", "            \"写一个关于秋天的诗吧。\",\n", "            \"红黄绿交织，田野金黄色。风吹枫叶落，人在秋天慢步行。\"\n", "        ],\n", "        [\n", "            \"给我推荐一些好听的摇滚歌曲。\",\n", "            \"摇滚歌曲推荐：AC/DC的Back in Black，Guns N' Roses的Sweet Child O' Mine，Rolling Stones的Satisfaction，Led Zeppelin的Stairway to Heaven，Nirvana的Smells Like Teen Spirit等。\"\n", "        ],\n", "        [\n", "            \"计算一下2的100次方。\",\n", "            \"2的100次方等于1.267650600228229401496703205376e+30，约等于1.27乘以10的30次方。\"\n", "        ]\n", "    ],\n", "    \"language\": \"chinese\",\n", "    \"data_source\": \"https://huggingface.co/datasets/BelleGroup/train_3.5M_CN\",\n", "    \"input_len\": 15,\n", "    \"output_len\": 73,\n", "    \"num_utter\": 4,\n", "    \"type\": 11,\n", "    \"type_keyword\": [\n", "        \"生成\",\n", "        \"诗歌\",\n", "        \"主题\",\n", "        \"爱情\",\n", "        \"创作\",\n", "        \"描述\",\n", "        \"歌词\",\n", "        \"表达\",\n", "        \"歌曲\",\n", "        \"有关\"\n", "    ]\n", "}\n", "\n", " ============================= \n", "\n", "Reading from: D:\\pythonwork\\2025LLMtraining\\MateConvMini\\MateConv_Dataset\\tokenizer_train.jsonl\n", "Sample 1: {\n", "    \"text\": \"好的。现在请你将这个文本中的所有的逗号都替换成空格。 好的，请稍等一下，现在我会将文本中的所有逗号替换为空格。处理后文本为：\\\"这是一个句子 目的是看看是否可以正确地从这个句子中删除关键词。\\\"。处理结果如何？\"\n", "}\n", "Sample 2: {\n", "    \"text\": \"帮我回答一道历史题目。清朝时期的八旗共有多少旗人？ 清朝时期八旗旗人总数约为200万人左右，其中正黄旗、正蓝旗、正白旗、正红旗的人数较多，其他旗的人数较少。\"\n", "}\n", "\n", " ============================= \n", "\n", "Reading from: D:\\pythonwork\\2025LLMtraining\\MateConvMini\\MateConv_Dataset\\mobvoi_seq_monkey_general_open_corpus.jsonl\n", "Sample 1: {\n", "    \"text\": \"在查处虚开增值税专用发票案件中，常常涉及进项留抵税额和税款损失的认定和处理。在计算税款损失时，要不要将进项留抵税额包括在内？\\n对此，实务中存在意见分歧。\\n有人主张归并，即计算税款损失时包括进项留抵税额；\\n有人主张剥离，即计算税款损失时剔除进项留抵税额。分析这个问题，需要确定进项留抵税额与税款损失之间是什么关系。\\n理清这二者之间的关系，首先需要了解增值税的概念和其抵扣机制。增值税是以商品（货物、服务等）在流转过程中产生的增值额作为计税依据而征收的一种流转税。为避免重复征税，在增值税中存在抵扣链条机制。\\n一般而言，交易上游企业缴纳的税额，交易下游企业可以对相应的税额进行抵扣。\\n对增值税一般纳税人来说，其购进货物、服务等取得增值税专用发票，发票上的税额是进项税额。\\n其出售货物、服务等，向购买方开具增值税专用发票，发票的税额是销项税额。\\n一般情况下，销项税额减去进项税额的金额是应纳税额，企业根据应纳税额按期申报纳税。\\n其次需要了解进项留抵税额的概念及产生原因。\\n在计算销项税额和进项税额的差额时，有时会出现负数，即当期进项税额大于当期销项税额。这个差额在当期未实现抵扣，为进项留抵税额，在以后纳税人有销项税额时再进行抵扣。\\n企业产生进项留抵税额的主要原因是其进项税额和销项税额时间上的不一致。\\n例如，企业前期集中采购货物和服务，投资大，销项税率低于进项税率等。\\n从税款抵扣的角度看，进项留抵税额只是购进的这部分进项税额参与到增值税应纳税额的计算过程中，但是其对应的进项税额抵扣还未真正实现，一般要等到其未来有相应的销项税额时，才能真正实现进项税额抵扣。\\n可见，进项留抵税额处于不确定状态，能否抵扣受到很多因素影响，例如企业经营中断，没有销项税额，这时进项留抵税额就无法实现抵扣。但如果企业按照税收政策规定申请进项留抵退税，进项税额抵扣就随之实现。\\n最后需要了解税款损失的概念。\\n税款损失，通常是指因虚开增值税专用发票，导致国家税款被骗或者流失的金额。关于税款损失，实务中有多种表述。\\n例如，北京大学法学院教授陈兴良曾谈到虚开行为本身不会造成国家税款损失，只有利用发票抵扣时才会造成国家税款损失。刘兵等编著的《虚开增值税专用发票案例司法观点和案例解析》一书中提到：“给国家税款造成损失的数额，实际上就是被骗取的国家税款在侦查终结以前无法追回的部分。”\\n赵清海与王家欣合著的《增值税专用发票虚开的判定与预防》一书中提到：“司法实践中，受票方用虚开的增值税专用发票予以抵扣的税款，从而导致受票方应纳税额的减少是法院所认定的国家税款流失的金额。”\\n从这些表述可见，税款损失应该是实际造成的损失，不应包括不确定的部分——进项留抵税额，进项留抵税额与税款损失之间不能直接画等号。\\n综上分析，进项留抵税额，只是使国家税款处于可能被抵扣的状态，还没有真正造成国家税款流失，一般情况下应将其从税款损失中剥离，特殊条件下将其归并入税款损失。\\n例如，当纳税人造假按照税收政策规定申请进项留抵税额退税后，有关税款损失将会从危险状态转化成危害结果，这时候要将有关进项留抵税额并入税款损失。\\n所以，在虚开增值税专用发票案件中，一般情况下，如果以纳税人的进项税额作为税款损失的计算基数，在对其进行行政处罚或刑事处罚时，应把进项留抵税额从税款损失中剔除，但纳税人申请进项留抵退税的除外。这样处理，把处罚与危害结果相对应，体现行政处罚法的过罚相当原则和刑法的罚当其罪原则。\"\n", "}\n", "Sample 2: {\n", "    \"text\": \"读者在使用本《年鉴》时发现与以前本局出版、公布、或内部提供的资料有出入的，概以本《年鉴》为准。\\n《年鉴》正文内容分为三大部分。第一部分为文字部分，收录了《2012年政府工作报告》以及《2011年河源市国民经济和社会发展统计公报》。第二部分为统计图，形象地反映建市以来河源市国民经济发展变化情况。第三部分为统计资料，具体分为行政区划和自然资源，综合、核算、人口，农村经济，工业，能源，交通、邮电，贸易业、物价指数，对外经济、旅游，财政、金融和保险，固定资产投资与建筑业，劳动工资，人民生活，文教、卫生和其他，河源市乡镇主要经济指标，广东省县域主要经济指标,广东省各市主要经济指标等16部分。此外，为便于读者正确理解和使用统计资料，特附主要统计指标解释、统计术语简介及统计法律法规等资料。\\n《年鉴》中，本市的数据是根据我局及有关部门的统计年报整理汇编而成，由于某些专业统计制度和统计口径的变化，有些数据空缺。使用本《年鉴》时，请注意指标名称的含义、统计口径、统计范围、计算单位、可比价与现行价(当年价)等。\\n《年鉴》第一部分中的有些数据为初步统计数，凡与本《年鉴》中第三部分的数据有出入的，则以第三部分的统计数据为准。\\n本《年鉴》部分统计数据使用了四舍五入的进位方法，因此，可能令统计表内个别项目相加与总数略有出入。\\n本《年鉴》统计表中符号使用说明：“＃”表示其中主要项；“空格”表示该项统计指标数据不详或无该项数据。\\n本《年鉴》的编辑出版，得到县、区及市直有关部门和单位的大力支持，在此表示感谢！本书疏漏之处敬请批评指正。\\n下载说明： �本站下载的文件一律为压缩文件，请使用 WinRAR 解压。\\n�PDF格式的资料请使用 Adobe Reader 浏览。\\n�本站提供的一些资料是供学习研究之用，如用于商业用途，请购买正版。\"\n", "}\n", "\n", " ============================= \n", "\n"]}], "source": ["import json\n", "\n", "# 文件路径\n", "file_paths = [\n", "    r\"D:\\pythonwork\\2025LLMtraining\\MateConvMini\\MateConv_Dataset\\sft_data_zh.jsonl\",\n", "    r\"D:\\pythonwork\\2025LLMtraining\\MateConvMini\\MateConv_Dataset\\tokenizer_train.jsonl\",\n", "    r\"D:\\pythonwork\\2025LLMtraining\\MateConvMini\\MateConv_Dataset\\mobvoi_seq_monkey_general_open_corpus.jsonl\",\n", "]\n", "\n", "# 读取并展示文件内容\n", "def read_and_display_samples(file_path, num_samples=2):\n", "    print(f\"Reading from: {file_path}\")\n", "    try:\n", "        with open(file_path, 'r', encoding='utf-8') as f:\n", "            for i, line in enumerate(f):\n", "                if i >= num_samples:\n", "                    break\n", "                data = json.loads(line.strip())\n", "                print(f\"Sample {i+1}: {json.dumps(data, indent=4, ensure_ascii=False)}\")\n", "    except Exception as e:\n", "        print(f\"Error reading {file_path}: {e}\")\n", "    print(\"\\n ============================= \\n\")\n", "\n", "# 遍历文件并读取样本\n", "for path in file_paths:\n", "    read_and_display_samples(path)"]}, {"cell_type": "markdown", "id": "9e370109-f4b3-4ce0-afe7-0807013bf765", "metadata": {}, "source": ["<font color =\"red\">**相信你已经注意到了，在不同的训练流程中、我们需要不同类型的数据——**"]}, {"cell_type": "markdown", "id": "323b6b65-3437-4436-bde1-84866aa31e04", "metadata": {}, "source": ["**1. 微调数据集：问答对**\n", "- **如何实现预训练**：\n", "  - **任务类型**：监督微调（SFT, Supervised Fine-Tuning）。\n", "  - **训练方法**：基于人类标注的问答对，通过优化模型在给定输入（问题）下生成期望输出（回答）的能力。\n", "    - 输入：问题文本（或带上下文的对话历史）。\n", "    - 输出：目标回答文本。\n", "  - **损失函数**：通常使用**交叉熵损失（Cross-Entropy Loss）**，评估模型生成回答与参考答案的相似程度。交叉熵损失会在每个时间步计算预测的分布与目标token的真实分布之间的差异，对于一段话、交叉熵损失通过将每个token的损失相加、得到整段话的损失。\n", "\n", "**2. 预训练数据集：短文本**\n", "- **如何实现预训练**：\n", "  - **任务类型**：无监督学习（例如自回归语言建模或掩码语言建模）。\n", "    - **自回归建模**（如 GPT）：模型按顺序预测每个词的下一个词。\n", "    - **掩码语言建模**（如 BERT）：模型根据上下文预测被掩盖的词。\n", "  - **训练方法**：利用短文本数据片段，构建语言建模任务。\n", "    - 自回归任务：输入前半部分文本，预测后续部分。此时使用的是**自回归损失CLM**。\n", "    - 掩码任务：随机掩盖部分单词，让模型填空。此时使用的是**掩码损失MLM**。\n", "    - 多模态任务：通常对图文进行匹配。此时使用的是**对比损失**。\n", "\n", "**3. 分词器训练数据集：超短文本**\n", "- **如何实现预训练**：\n", "  - **任务类型**：词汇构建与分词规则学习。\n", "  - **训练方法**：\n", "    1. 对超短文本进行统计分析，确定出现频率最高的字符或词。\n", "    2. 使用分词算法（如 BPE 或 SentencePiece）将常见的字符组合压缩为词表项。\n", "    3. 生成分词规则和词表，供后续模型训练使用。\n", "    4. 由于分词器的训练本质上是一个基于规则或统计算法的过程，因此不是神经网络的优化过程，因此分词器的训练过程**并不需要损失函数**。"]}, {"cell_type": "markdown", "id": "5c018d38-1054-40f6-8298-3f1f1a575b02", "metadata": {}, "source": ["<font color =\"red\">**除此之外，我们还需要认识一下Jsonl数据格式**"]}, {"cell_type": "markdown", "id": "4884e0a4-87a5-402a-8832-ca46ed0841d3", "metadata": {}, "source": ["JSONL（JSON Lines）：JSONL（JSON Lines）是一种特别适合处理大规模数据的格式，尤其在机器学习和大数据领域得到了广泛应用，它是一种逐行存储 JSON 对象的文件格式，每行是一个独立的 JSON 对象，行与行之间并没有特定的结构。每行的 JSON 对象独立存在，不属于同一个数组或对象。例如："]}, {"cell_type": "code", "execution_count": 10, "id": "8e6b9570-798a-4a42-bc6a-f07e971b4adb", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'name': '<PERSON>', 'age': 30}"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["{\"name\": \"<PERSON>\", \"age\": 25}\n", "{\"name\": \"<PERSON>\", \"age\": 30}"]}, {"cell_type": "markdown", "id": "fbebf9c2-869c-4328-a490-70c761fbad4b", "metadata": {}, "source": ["这种数据类型展示的信息其实就是——"]}, {"cell_type": "markdown", "id": "e4fc8d2a-ecf6-4c55-8f88-12b2232f0ca0", "metadata": {}, "source": ["|name|age|\n", "|:-:|:-:|\n", "|Alice|25|\n", "|<PERSON>|30|"]}, {"cell_type": "markdown", "id": "fb4a2b08-6bd8-47e3-a82f-c687b16f9c1c", "metadata": {}, "source": ["但由于JSONL天生的字典格式、它展示表单信息的效率远远高于Dataframe这些结构，因此许多大型数据都呈现jsonl格式，你可能看到超大数据集是这样的结构 ↓\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/13.png)\n", "\n", "但是，JSONL有它的劣势，其中最核心的一条就是文件体积稍大、它还不是最有效存储超大数据的格式——"]}, {"cell_type": "markdown", "id": "0757d31e-ba2c-43ff-9be0-439690c4c696", "metadata": {}, "source": ["| 格式          | 优势                                                                 | 劣势                                                          |\n", "|---------------|----------------------------------------------------------------------|---------------------------------------------------------------|\n", "| **JSONL**     | 支持流式处理、高容错性、易扩展、通用性强                              | 文件体积稍大（每条记录有元数据开销）。                         |\n", "| **CSV**       | 紧凑、高效，占用空间小                                               | 不适合嵌套或复杂结构数据，不支持多种数据类型。                 |\n", "| **Parquet**   | 高压缩比、支持列式存储、查询速度快                                    | 不易阅读，不适合直接调试或小规模任务。                         |\n", "| **Protobuf**  | 高效、压缩率高、适合大规模数据传输                                   | 二进制文件不可读，需要额外的工具和定义文件解析。               |\n", "| **二进制文件** | 存储高效，占用空间小，适合高密度数据（如矩阵或图像）                  | 不可读、不易调试，对文件结构高度依赖。                         |"]}, {"cell_type": "markdown", "id": "60623ce6-a85f-4c75-8572-140282ae4812", "metadata": {}, "source": ["针对这样的数据集，我们可以完成一个极为特殊的预处理手段，那就是将JSONL格式转化为二进制Bin文件，不过这个操作需要在我们完成Tokenzier训练后才可以实现。"]}, {"cell_type": "markdown", "id": "c7f4c398-c7f9-4c36-84b6-39b23c375889", "metadata": {}, "source": ["### 2.1 MateConv Mini的tokenizer训练"]}, {"cell_type": "markdown", "id": "641ba37e-e734-4386-a479-2c7f2ab18081", "metadata": {}, "source": ["**下面这段代码你需要在服务器的jupyter中运行，请在你的服务器中上传好tokenizer_jsonl数据集。**"]}, {"cell_type": "code", "execution_count": null, "id": "781efbd2-b0db-4831-ad62-67701f78fb14", "metadata": {}, "outputs": [], "source": ["huggingface  ==> library (transformers、diffuser、Dataset)"]}, {"cell_type": "markdown", "id": "013bdd97-4c9a-4a35-b900-9504eb75e27a", "metadata": {}, "source": ["- step 0. 服务器配置"]}, {"cell_type": "markdown", "id": "a7d0af61-a71b-43eb-b14a-bf8a9fb762b5", "metadata": {}, "source": ["```shell\n", "#激活已建好的虚拟环境\n", "conda activate MateConv\n", "\n", "#进入MateConv文件夹\n", "cd ~/autodl-tmp/MateConv/\n", "\n", "#在MateConv文件夹中建立dataset目录\n", "mkdir dataset\n", "\n", "#进入dataset目录\n", "cd ./dataset\n", "```"]}, {"cell_type": "markdown", "id": "8383fdda-7d79-486c-9c98-a614b6fa1dd9", "metadata": {}, "source": ["接着，找到网盘中的数据文件Tokenizer_jsonl，上传到dataset文件夹内 ↓"]}, {"cell_type": "markdown", "id": "17325587-0cff-4a79-ae5f-93f430a81109", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/12.png)\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/88.png)"]}, {"cell_type": "markdown", "id": "ed5f37f2-af7b-4cf1-9049-cd165f450209", "metadata": {}, "source": ["上传完毕后即可使用！整个分词器的训练流程（下面的训练代码）也已打包好一个单独的ipy文件（Tokenizer_Training.ipy），可在网盘中下载。你可以将这段代码同样上传至线上的`~/autodl-tmp/MateConv/`文件夹、然后在这个文件夹下启动jupyter，即可运行这段代码。"]}, {"cell_type": "markdown", "id": "48fec961-b936-44ff-ba2d-35b3428d5a0e", "metadata": {}, "source": ["```shell\n", "\n", "cd ~/autodl-tmp/MateConv/\n", "\n", "jupyter lab --allow-root\n", "```"]}, {"cell_type": "markdown", "id": "b0570589-62cb-4d60-8e90-08ba981be949", "metadata": {}, "source": ["启动jupyter之后，需要使用autodl的ssh隧道工具进行代理、才可以访问线上jupyter，相信你在看这节课之前已经看过环境配置与jupyter访问指南。打开代理页面之后就可以在左侧目录中直接看到tokenizer_training.ipy文件了。接下来就可以在线上运行这段文件、来实现Tokenizer的训练了。"]}, {"cell_type": "markdown", "id": "0c9382f8-0738-42e9-b83c-5f1cdda788a8", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/89.png)"]}, {"cell_type": "markdown", "id": "9ac80926-5dfe-4c3d-9225-7d58c4b2e07b", "metadata": {}, "source": ["- Step 1.导入必要的库"]}, {"cell_type": "code", "execution_count": 2, "id": "6d338742-8b8f-4ea3-9190-8f2b3b998d87", "metadata": {}, "outputs": [], "source": ["import random\n", "from tqdm import tqdm\n", "from transformers import AutoTokenizer\n", "import json\n", "from datasets import load_dataset\n", "from tokenizers import (\n", "    decoders,\n", "    models,\n", "    normalizers,\n", "    pre_tokenizers,\n", "    processors,\n", "    trainers,\n", "    <PERSON><PERSON><PERSON>,\n", ")\n", "import os"]}, {"cell_type": "markdown", "id": "82b358c1-6ef3-4688-be71-c7ae0299c6a7", "metadata": {}, "source": ["- Step 2.读取 tokenizer_train.jsonl 文件"]}, {"cell_type": "code", "execution_count": 3, "id": "bb1f1482-d37e-4f1e-a0f0-65709fbba4ce", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["好的。现在请你将这个文本中的所有的逗号都替换成空格。 好的，请稍等一下，现在我会将文本中的所有逗号替换为空格。处理后文本为：\"这是一个句子 目的是看看是否可以正确地从这个句子中删除关键词。\"。处理结果如何？\n", "帮我回答一道历史题目。清朝时期的八旗共有多少旗人？ 清朝时期八旗旗人总数约为200万人左右，其中正黄旗、正蓝旗、正白旗、正红旗的人数较多，其他旗的人数较少。\n", "嗯，谢谢你介绍的做法很详细，但我不喜欢吃鸡蛋，有没有其他菜做法能介绍一下？ 当然，你可以试试酸辣土豆丝这道菜。\n", "材料：\n", "土豆2个、红椒1个、青椒1个、大葱1根、醋、生抽、盐、鸡精、料酒\n", "做法：\n", "1.土豆去皮，切成丝；红椒和青椒切成细丝；大葱切段备用。\n", "2.热锅凉油，油热后放入土豆丝，煸炒至变软。\n", "3.倒入红椒、青椒和大葱段，继续煸炒至熟。\n", "4.加入适量的盐、鸡精、料酒和生抽，翻炒均匀。\n", "5.最后，加入适量的醋，翻炒均匀即可。\n", "小贴士：\n", "1. 土豆切丝时，可以放入淡盐水中泡一下，这样可以去除多余的淀粉。\n", "2. 煮土豆丝时，不要煮得太久，以免烂糊。\n", "3. 加入醋的时候，根据自己的口味多少来进行调节，一般来说，盐与醋的比例为1:1。\n", "4. 如果喜欢辣味可以加入一些干辣椒丝。\n", "希望你会喜欢这道酸辣土豆丝！\n", "请描述一下如何正确规划个人理财。 正确规划个人理财需要以下几个步骤：\n", "1.了解自己的财务状况。这包括收入、支出、资产和负债等信息。了解自己的财务状况可以帮助人们更好地制定财务计划。\n", "2.设定财务目标。需要考虑短期目标和长期目标，例如以年为单位设定的支出计划、购房、购车等的长期目标。\n", "3.制定预算计划。在了解自己的财务状况并设定财务目标后，需要制定一个预算计划。这可以帮助人们控制支出、节省开支并达到财务目标。\n", "4.理性投资和储蓄。人们可以投资于股票、基金、房产或其他投资渠道以实现财务目标。但在投资前需了解相关知识并进行风险评估。同时还应储蓄一定金额，以应对突发事件或为达成某些目标做准备。\n", "5.审时度势，合理调整。财务计划需要不断地审时度势，根据实际情况做出调整，以达到最终的财务目标。需要注意财务状况的变化、投资的收益和风险等因素。\n", "通过以上五个步骤，人们可以做到合理规划个人理财，掌握自己的财务命运，更好地实现自己的财务目标。\n", "描述一下天堂和地狱的生态系统和环境。 天堂和地狱被认为是灵性信仰中关于死后世界的两种不同概念。然而，它们的生态系统和环境都是具有类似特征的极端不同的地方。以下是我对天堂和地狱的生态系统和环境的描述。\n", "天堂的生态系统和环境:\n", "天堂被描绘为一个美丽、平静、和谐的地方，类似于一片无垢的花园。天堂的生态系统和环境的特征包括:\n", "1. 充满和平和爱的氛围。这是一个没有恐惧、痛苦、疾病和死亡的地方。\n", "2. 色彩缤纷，充满生机。这是一个绿树成荫、花团锦簇的地方，充满生机和活力。\n", "3. 各种生物和动物和谐共存。天使、圣人和各种动物和谐相处，生态系统中没有互相侵害或抢夺资源。\n", "4. 充满清新气息的空气。没有污染、烟雾或其他有害物质，空气中充满清新芬芳的气息。\n", "5. 物质丰富的环境。天堂中生活着满足需求和愿望的人们，他们拥有一切所需的物质资源，而且没有匮乏、浪费或不公平。\n", "地狱的生态系统和环境:\n", "地狱被描绘为阴暗、恐怖、嘈杂和可怕的地方。地狱的生态系统和环境的特征包括:\n", "1. 充满痛苦和折磨的氛围。这是一个充满恐惧、悔恨和痛苦的地方，全是罪恶的味道。\n", "2. 火焰和烈火环绕。地狱中有燃烧的火焰和烈火，许多受罚者被投入火坑中痛苦折磨。\n", "3. 恶魔和妖魔横行。地狱中有恶魔、妖怪等可怕的生物，它们在无休止的受苦中享受着自己的又一场比赛。\n", "4. 污染和恶臭的气味。地狱中到处都是恶臭和污染，没有清新的气息。\n", "5. 没有物质资源。地狱中生活着被惩罚的人们不可能拥有任何物质财富，地狱环境充满了无尽的贫困、饥饿和疾病。\n", "综上所述，天堂和地狱是两个完全不同的地方，它们的生态系统和环境反映了它们的性质，体现了人类对不同阶段的死后生命的不同想象和信仰。\n"]}], "source": ["def read_texts_from_jsonl(file_path):\n", "    with open(file_path, 'r', encoding='utf-8') as f:\n", "        for line in f:\n", "            data = json.loads(line)\n", "            yield data['text']\n", "\n", "# 测试读取数据，你可以自定义你的目录\n", "data_path = '/root/autodl-tmp/MateConv/dataset/tokenizer_train.jsonl'\n", "texts = read_texts_from_jsonl(data_path)\n", "\n", "# 打印前几行文本\n", "for i, text in enumerate(texts):\n", "    if i < 5:\n", "        print(text)\n", "    else:\n", "        break"]}, {"cell_type": "markdown", "id": "b229daab-fd17-4abb-80c8-caee600717c9", "metadata": {}, "source": ["- Step 3.初始化分词器"]}, {"cell_type": "markdown", "id": "adde3aec-6c26-4fdb-978d-8e96bd825092", "metadata": {}, "source": ["首先，通过 `models.BPE()` 创建了一个基于 Byte-Pair Encoding (BPE) 模型的分词器。BPE 是一种常用于文本分词的子词分解算法，特别在自然语言处理任务中被广泛使用，如机器翻译和语言模型训练。BPE 的主要思想是通过将频繁出现的字符或字符对合并成一个新的子词单元，逐步构建一个子词级别的词汇表，从而处理词汇表稀疏性和未登录词问题。"]}, {"cell_type": "code", "execution_count": 5, "id": "2029646f-5b23-4785-a74b-af06de199ebd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["分词器初始化成功，准备训练。\n"]}], "source": ["# 初始化tokenizer\n", "tokenizer = Tokenizer(models.BPE())\n", "tokenizer.pre_tokenizer = pre_tokenizers.ByteLevel(add_prefix_space=False)\n", "\n", "# 定义特殊token\n", "special_tokens = [\"<unk>\", \"<s>\", \"</s>\"]\n", "\n", "# 设置训练器并添加特殊token\n", "trainer = trainers.<PERSON><PERSON><PERSON><PERSON><PERSON>(\n", "    vocab_size=6400,\n", "    special_tokens=special_tokens,  # 确保这三个token被包含\n", "    show_progress=True,\n", "    initial_alphabet=pre_tokenizers.ByteLevel.alphabet()\n", ")\n", "\n", "print(\"分词器初始化成功，准备训练。\")"]}, {"cell_type": "markdown", "id": "cf9e9db3-b399-4329-8c73-6ddbc64045ea", "metadata": {}, "source": ["- Step 4.训练分词器"]}, {"cell_type": "markdown", "id": "9251461c-b51f-4536-8c20-4e966a7e8c12", "metadata": {}, "source": ["【TIME Warning：10mins】"]}, {"cell_type": "code", "execution_count": 6, "id": "4e94774f-8bed-407e-823d-b8e91e58c046", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "\n", "\n", "分词器训练完成！\n"]}], "source": ["# 读取文本数据\n", "texts = read_texts_from_jsonl(data_path)\n", "\n", "# 训练tokenizer\n", "tokenizer.train_from_iterator(texts, trainer=trainer)\n", "\n", "print(\"分词器训练完成！\")"]}, {"cell_type": "markdown", "id": "1ce1fec8-d58c-4fdf-a01a-9ae643bbbd28", "metadata": {}, "source": ["- Step 5.保存分词器"]}, {"cell_type": "markdown", "id": "773b281d-316c-4a23-853b-3b1dc9653515", "metadata": {}, "source": ["在训练完毕之后，还需要设置解码器 (`tokenizer.decoder = decoders.ByteLevel()`) ，这是为了在生成文本时正确地将分词器产生的 token 序列还原回原始文本。"]}, {"cell_type": "markdown", "id": "df3b34b4-4538-4943-bd22-f331eab4c03f", "metadata": {}, "source": ["同时，在保存tokenizer之前，你需要建立用于存放模型的目录 ↓"]}, {"cell_type": "markdown", "id": "f9b9f15e-103c-4f37-b6bd-a09ec6bd851c", "metadata": {}, "source": ["```shell\n", "#激活已建好的虚拟环境\n", "conda activate MateConv\n", "\n", "#进入MateConv文件夹\n", "cd ~/autodl-tmp/MateConv/\n", "\n", "#在MateConv文件夹中建立dataset目录\n", "mkdir -p ~/autodl-tmp/MateConv/model/mateconv_tokenizer\n", "\n", "#进入dataset目录\n", "cd ./model/mateconv_tokenizer\n", "```"]}, {"cell_type": "code", "execution_count": 7, "id": "5f65ad13-624c-4f26-ab30-f6cc3584d131", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tokenizer 保存成功！\n"]}], "source": ["# 设置解码器\n", "tokenizer.decoder = decoders.ByteLevel()\n", "\n", "# 保存tokenizer\n", "tokenizer_dir = \"/root/autodl-tmp/MateConv/model/mateconv_tokenizer\"\n", "os.makedirs(tokenizer_dir, exist_ok=True)\n", "tokenizer.save(os.path.join(tokenizer_dir, \"tokenizer.json\"))\n", "tokenizer.model.save(\"/root/autodl-tmp/MateConv/model/mateconv_tokenizer\")\n", "\n", "# 手动创建配置文件\n", "config = {\n", "    \"add_bos_token\": <PERSON>alse,\n", "    \"add_eos_token\": False,\n", "    \"add_prefix_space\": True,\n", "    \"added_tokens_decoder\": {\n", "        \"0\": {\n", "            \"content\": \"<unk>\",\n", "            \"lstrip\": <PERSON><PERSON><PERSON>,\n", "            \"normalized\": <PERSON><PERSON><PERSON>,\n", "            \"rstrip\": <PERSON><PERSON><PERSON>,\n", "            \"single_word\": <PERSON><PERSON><PERSON>,\n", "            \"special\": <PERSON>\n", "            },\n", "        \"1\": {\n", "            \"content\": \"<s>\",\n", "         -p ~/autodl-tmp/MateConv/model/mateconv_tokenizer   \"lstrip\": False,\n", "            \"normalized\": <PERSON><PERSON><PERSON>,\n", "            \"rstrip\": <PERSON><PERSON><PERSON>,\n", "            \"single_word\": <PERSON><PERSON><PERSON>,\n", "            \"special\": <PERSON>\n", "            },\n", "        \"2\": {\n", "            \"content\": \"</s>\",\n", "            \"lstrip\": <PERSON><PERSON><PERSON>,\n", "            \"normalized\": <PERSON><PERSON><PERSON>,\n", "            \"rstrip\": <PERSON><PERSON><PERSON>,\n", "            \"single_word\": <PERSON><PERSON><PERSON>,\n", "            \"special\": <PERSON>\n", "            }\n", "    },\n", "    \"bos_token\": \"<s>\",\n", "    \"clean_up_tokenization_spaces\": False,\n", "    \"eos_token\": \"</s>\",\n", "    \"legacy\": True,\n", "    \"model_max_length\": 1000000000000000019884624838656,\n", "    \"pad_token\": None,\n", "    \"sp_model_kwargs\": {},\n", "    \"spaces_between_special_tokens\": <PERSON>alse,\n", "    \"tokenizer_class\": \"PreTrainedTokenizerFast\",\n", "    \"unk_token\": \"<unk>\",\n", "    \"use_default_system_prompt\": False,\n", "    \"chat_template\": \"{% if messages[0]['role'] == 'system' %}{% set system_message = messages[0]['content'] %}{% endif %}{% if system_message is defined %}{{ system_message }}{% endif %}{% for message in messages %}{% set content = message['content'] %}{% if message['role'] == 'user' %}{{ '<s>user\\\\n' + content + '</s>\\\\n<s>assistant\\\\n' }}{% elif message['role'] == 'assistant' %}{{ content + '</s>' + '\\\\n' }}{% endif %}{% endfor %}\"\n", "}\n", "\n", "# 保存配置文件\n", "with open(os.path.join(tokenizer_dir, \"tokenizer_config.json\"), \"w\", encoding=\"utf-8\") as config_file:\n", "    json.dump(config, config_file, ensure_ascii=False, indent=4)\n", "\n", "print(\"Tokenizer 保存成功！\")"]}, {"cell_type": "markdown", "id": "e656368a-b9cb-4f4f-823b-6acb93dafc22", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/image-20241022194241795.png\" alt=\"image-20241022194241795\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "5f42e2a8-eea4-4ccc-839a-d44d8fad23de", "metadata": {}, "source": ["- Step 6.评估分词器"]}, {"cell_type": "code", "execution_count": 2, "id": "b91c7a0b-47e3-4692-af01-81faf467eb06", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[608, 1589, 4835, 269, 4833, 954, 4725, 270, 1170, 345, 4584, 5204, 1273, 648, 2207, 1, 320, 275, 201, 345, 1390, 258, 3852, 1081, 269, 2, 201, 1, 1078, 538, 501, 201, 22, 23, 24, 2, 201, 1, 320, 275, 201, 22, 23, 24, 2, 201, 1, 1078, 538, 501, 201, 25, 26, 27, 2, 201]\n"]}], "source": ["from transformers import AutoTokenizer\n", "\n", "# 加载预训练的tokenizer\n", "tokenizer = AutoTokenizer.from_pretrained(\"./model/mateconv_tokenizer\")\n", "\n", "# 测试一段对话\n", "messages = [\n", "    {\"role\": \"system\", \"content\": \"你是一个优秀的聊天机器人，总是给我正确的回应！\"},\n", "    {\"role\": \"user\", \"content\": '是椭圆形的'},\n", "    {\"role\": \"assistant\", \"content\": '456'},\n", "    {\"role\": \"user\", \"content\": '456'},\n", "    {\"role\": \"assistant\", \"content\": '789'}\n", "]\n", "\n", "# 使用模板进行文本处理\n", "new_prompt = tokenizer.apply_chat_template(messages, tokenize=True)\n", "print(new_prompt)"]}, {"cell_type": "markdown", "id": "8d1947da-abf1-47b0-ac7a-176526485570", "metadata": {}, "source": ["### 3.1 MateConv Mini的数据预处理流程"]}, {"cell_type": "markdown", "id": "86cdcc93-8ab8-42a9-89fd-eb7aeca585a9", "metadata": {}, "source": ["数据集需要手动下载**mobvoi_seq_monkey_general_open_corpus.zip**文件并上传至服务器上的文件夹内。**由于数据有13.5G，上传大约需要半小时时间**。"]}, {"cell_type": "markdown", "id": "a036d7a1-cebd-4c48-a28a-a19f6bea6e2a", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/12.png)"]}, {"cell_type": "markdown", "id": "44239b40-7662-4d6c-be26-6c3ae39ab323", "metadata": {}, "source": ["在这里你可以自己选择上传至服务器的目录，我选择的目录是 ↓ "]}, {"cell_type": "markdown", "id": "e421c2e6-0a8e-4b9e-ac65-9ae4902b44cd", "metadata": {}, "source": ["<center><img src=\"https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/90.png\" alt=\"image-20241022200836959\" style=\"zoom:100%;\" />"]}, {"cell_type": "markdown", "id": "5c14fc8a-f1c7-439b-a55c-e15f78ecadd0", "metadata": {}, "source": ["- 文件解压缩"]}, {"cell_type": "markdown", "id": "8c775e47-0469-4bf4-ba4d-d01ea370990e", "metadata": {}, "source": ["&emsp;&emsp;接下来则需要在服务器上运行如下命令："]}, {"cell_type": "markdown", "id": "0e08a69f-9c2e-4916-8ec4-4eb310a9fc69", "metadata": {}, "source": ["```bash\n", "conda activate MateConv\n", "\n", "sudo apt-get install unzip\n", "\n", "mkdir -p ~/autodl-tmp/MateConv/Data/seq-monkey #换成你自己的目录\n", "\n", "cd /root/autodl-tmp/MateConv/Data/seq-monkey #进入你自己设置的目录、开始解压缩\n", "\n", "unzip mobvoi_seq_monkey_general_open_corpus.zip #大约需要5~10mins时间，直到你看到jsonl的大小为32G为止\n", "```"]}, {"cell_type": "markdown", "id": "a1bd4f26-c22a-406a-bb24-d2395964ab14", "metadata": {}, "source": ["解压缩后就会得到文件`mobvoi_seq_monkey_general_open_corpus.jsonl`，文件的标准大小应该接近32G。刷新至文件大小不再增加、且命令行回复初始状态后，表示解压已结束。"]}, {"cell_type": "markdown", "id": "569c308c-2850-4713-bb89-42518749407d", "metadata": {}, "source": ["<center><img src=\"https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/91.png\" alt=\"image-20241022201037481\" style=\"zoom:100%;\" />"]}, {"cell_type": "markdown", "id": "17cd0999-1f63-467c-92c6-dc92b5118736", "metadata": {}, "source": ["接下来，你则需要对数据进行处理，并将数据转换成二进制文件。下面这段代码已经被提取到ipy文件中（**Prepare_Train_Data.ipy**），同样你可以将其上传到MateConv默认命令中，并在代理服务器中运行它。"]}, {"cell_type": "markdown", "id": "8ce7a11d-ebf2-4266-b2fc-c80edc4ae940", "metadata": {}, "source": ["- Step 1.导入库"]}, {"cell_type": "code", "execution_count": 1, "id": "a4bce22c-55a4-48a0-a1b9-4478b0dfc821", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import itertools\n", "import re\n", "import json\n", "import jsonlines\n", "import psutil\n", "import ujson\n", "import numpy as np\n", "import pandas as pd\n", "from transformers import AutoTokenizer\n", "from datasets import load_dataset\n", "import os\n", "from tqdm import tqdm"]}, {"cell_type": "markdown", "id": "64b2ba3c-cd62-409e-94dc-3d662dc2f658", "metadata": {}, "source": ["- Step 2.定义BOS和EOS标记，并加载分词器"]}, {"cell_type": "code", "execution_count": 2, "id": "b21d21a1-f7d4-4dbe-bc2a-4f0ab3c75ca1", "metadata": {}, "outputs": [], "source": ["# 定义BOS和EOS标记\n", "bos_token = \"<s>\"\n", "eos_token = \"</s>\""]}, {"cell_type": "code", "execution_count": 3, "id": "e78e7a3c-e709-4e96-aae0-1954f39ad37f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["加载的tokenizer词表大小: 6400\n"]}], "source": ["# 加载训练好的分词器路径\n", "tokenizer = AutoTokenizer.from_pretrained('/root/autodl-tmp/MateConv/model/mateconv_tokenizer', use_fast=False)\n", "print(f'加载的tokenizer词表大小: {len(tokenizer)}')"]}, {"cell_type": "markdown", "id": "bee836ae-c69f-439a-9631-365cdad105e4", "metadata": {}, "source": ["- Step 3.读取部分数据"]}, {"cell_type": "code", "execution_count": 6, "id": "6157b0d1-dfc2-4e2f-b4ea-e85e224b9f7e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第 1 行数据: {'text': '在查处虚开增值税专用发票案件中，常常涉及进项留抵税额和税款损失的认定和处理。在计算税款损失时，要不要将进项留抵税额包括在内？\\n对此，实务中存在意见分歧。\\n有人主张归并，即计算税款损失时包括进项留抵税额；\\n有人主张剥离，即计算税款损失时剔除进项留抵税额。分析这个问题，需要确定进项留抵税额与税款损失之间是什么关系。\\n理清这二者之间的关系，首先需要了解增值税的概念和其抵扣机制。增值税是以商品（货物、服务等）在流转过程中产生的增值额作为计税依据而征收的一种流转税。为避免重复征税，在增值税中存在抵扣链条机制。\\n一般而言，交易上游企业缴纳的税额，交易下游企业可以对相应的税额进行抵扣。\\n对增值税一般纳税人来说，其购进货物、服务等取得增值税专用发票，发票上的税额是进项税额。\\n其出售货物、服务等，向购买方开具增值税专用发票，发票的税额是销项税额。\\n一般情况下，销项税额减去进项税额的金额是应纳税额，企业根据应纳税额按期申报纳税。\\n其次需要了解进项留抵税额的概念及产生原因。\\n在计算销项税额和进项税额的差额时，有时会出现负数，即当期进项税额大于当期销项税额。这个差额在当期未实现抵扣，为进项留抵税额，在以后纳税人有销项税额时再进行抵扣。\\n企业产生进项留抵税额的主要原因是其进项税额和销项税额时间上的不一致。\\n例如，企业前期集中采购货物和服务，投资大，销项税率低于进项税率等。\\n从税款抵扣的角度看，进项留抵税额只是购进的这部分进项税额参与到增值税应纳税额的计算过程中，但是其对应的进项税额抵扣还未真正实现，一般要等到其未来有相应的销项税额时，才能真正实现进项税额抵扣。\\n可见，进项留抵税额处于不确定状态，能否抵扣受到很多因素影响，例如企业经营中断，没有销项税额，这时进项留抵税额就无法实现抵扣。但如果企业按照税收政策规定申请进项留抵退税，进项税额抵扣就随之实现。\\n最后需要了解税款损失的概念。\\n税款损失，通常是指因虚开增值税专用发票，导致国家税款被骗或者流失的金额。关于税款损失，实务中有多种表述。\\n例如，北京大学法学院教授陈兴良曾谈到虚开行为本身不会造成国家税款损失，只有利用发票抵扣时才会造成国家税款损失。刘兵等编著的《虚开增值税专用发票案例司法观点和案例解析》一书中提到：“给国家税款造成损失的数额，实际上就是被骗取的国家税款在侦查终结以前无法追回的部分。”\\n赵清海与王家欣合著的《增值税专用发票虚开的判定与预防》一书中提到：“司法实践中，受票方用虚开的增值税专用发票予以抵扣的税款，从而导致受票方应纳税额的减少是法院所认定的国家税款流失的金额。”\\n从这些表述可见，税款损失应该是实际造成的损失，不应包括不确定的部分——进项留抵税额，进项留抵税额与税款损失之间不能直接画等号。\\n综上分析，进项留抵税额，只是使国家税款处于可能被抵扣的状态，还没有真正造成国家税款流失，一般情况下应将其从税款损失中剥离，特殊条件下将其归并入税款损失。\\n例如，当纳税人造假按照税收政策规定申请进项留抵税额退税后，有关税款损失将会从危险状态转化成危害结果，这时候要将有关进项留抵税额并入税款损失。\\n所以，在虚开增值税专用发票案件中，一般情况下，如果以纳税人的进项税额作为税款损失的计算基数，在对其进行行政处罚或刑事处罚时，应把进项留抵税额从税款损失中剔除，但纳税人申请进项留抵退税的除外。这样处理，把处罚与危害结果相对应，体现行政处罚法的过罚相当原则和刑法的罚当其罪原则。'}\n", "第 2 行数据: {'text': '读者在使用本《年鉴》时发现与以前本局出版、公布、或内部提供的资料有出入的，概以本《年鉴》为准。\\n《年鉴》正文内容分为三大部分。第一部分为文字部分，收录了《2012年政府工作报告》以及《2011年河源市国民经济和社会发展统计公报》。第二部分为统计图，形象地反映建市以来河源市国民经济发展变化情况。第三部分为统计资料，具体分为行政区划和自然资源，综合、核算、人口，农村经济，工业，能源，交通、邮电，贸易业、物价指数，对外经济、旅游，财政、金融和保险，固定资产投资与建筑业，劳动工资，人民生活，文教、卫生和其他，河源市乡镇主要经济指标，广东省县域主要经济指标,广东省各市主要经济指标等16部分。此外，为便于读者正确理解和使用统计资料，特附主要统计指标解释、统计术语简介及统计法律法规等资料。\\n《年鉴》中，本市的数据是根据我局及有关部门的统计年报整理汇编而成，由于某些专业统计制度和统计口径的变化，有些数据空缺。使用本《年鉴》时，请注意指标名称的含义、统计口径、统计范围、计算单位、可比价与现行价(当年价)等。\\n《年鉴》第一部分中的有些数据为初步统计数，凡与本《年鉴》中第三部分的数据有出入的，则以第三部分的统计数据为准。\\n本《年鉴》部分统计数据使用了四舍五入的进位方法，因此，可能令统计表内个别项目相加与总数略有出入。\\n本《年鉴》统计表中符号使用说明：“＃”表示其中主要项；“空格”表示该项统计指标数据不详或无该项数据。\\n本《年鉴》的编辑出版，得到县、区及市直有关部门和单位的大力支持，在此表示感谢！本书疏漏之处敬请批评指正。\\n下载说明： �本站下载的文件一律为压缩文件，请使用 WinRAR 解压。\\n�PDF格式的资料请使用 Adobe Reader 浏览。\\n�本站提供的一些资料是供学习研究之用，如用于商业用途，请购买正版。'}\n", "第 3 行数据: {'text': '初中阶段是学生身心发育的一个突变期。尤其是初一学生，从小学到中学，随着环境改变，课程增多，难度加大，他们内心发生了急剧变化，产生了许多烦恼、困惑，造成较大的心理偏差，这就需要教师和家长及时给予心理指导和帮助。\\n一、心理偏差的种种表现\\n1、骄傲自负心理。这种心理偏差主要表现在思维敏捷、小学成绩拔尖的学生身上，特别是一些长期担任班干部、竞赛获奖、父母有权力的学生表现尤为明显。\\n2．单纯求趣心理。求趣激趣，这是教学的原则之一，但是，有些初一学生过分地追求接受知识要符合自己的兴趣，还想回到幼儿园、小学时“游戏教育”和“愉快教育”中去，不能努力适应初中阶段的学习生活。\\n3．自卑孤僻心理。多数来自普通工薪家庭及农村贫困地区或遭遇父母婚变的学生，往往在干部、富家子弟、有特长的同学面前感到自卑，心理压抑，行为孤僻，甚至变态的自尊，影响学习。\\n4．胆怯畏惧心理。部分性格内向、胆小的学生，主要是女生，羞于用语言表达思想，沉溺于内心活动和笔头表达。内心活动不能外显，妨碍了思维素质的深入发展。\\n5．浮躁马虎心理。部分活泼好动的学生，智力水平不低，但就是不能静下心学习，总是浅尝辄止，马虎应付，不愿作深入的思考，常常“半罐水响叮当”。\\n6．贪图享受心理。一些家境较好的学生，行为懒散，好逸恶劳，学习上畏难怕苦，生活上讲吃讲穿。\\n二、上述心理偏差的形成原因\\n1．生理上的原因。初中学生处于发育高峰期，身高体重剧增，性发育开始。生理上的急剧变化使儿童意识到自己不再是孩子，“成人感”增强。但是，青年身体成熟速度存在着很大的个体差异：不同性别之间相差两年左右，同性别之间相差四年左右。因此，同是初中学生，一部分学生生理已跨入青年期，而另一部分学生可能还停留在童年期。\\n2．心理上的原因。随着生理的变化，“成人感”的出现，初中学生心理产生“独立”，力求摆脱对成人的依赖，老师、家长在他们心目中的权威降低，同学之间相互影响增强。思维上发展了批判性，但由于经验的缺乏含有片面性和主观性；行为上出现“独特性”和“受暗示性”乃至“抗拒性”，即逆反心理；情绪上带有冲动性，不善于克制自己；兴趣和愿望上带有随意性、多变性、狂热性，常为了所谓讲“义气”而庇护同伴，或为同伴打抱不平；感情上具有“闭锁性”，而对于艰苦的学习活动特别重要的意志品质，则还处在比较软弱的状态。\\n3．环境的原因。心理学认为，个体的生物遗传因素规定了发展的潜在可能范围，而个体环境教育则确定他在此可能范围内的现实水平。环境条件有利与否对个体发展的现实水平起了决定性作用。\\n①家庭。社会的信仰、观念等社会化目标都是首先通过父母的过渡，以高度个性化了的、有选择的形式传递给儿童的。父母本身的个性特征、社会地位、教育水平、宗教信仰、价值标准等等都强烈地影响他们的后代。父母的教养方式、家庭结构、物质条件、人际环境、文化和情绪氛围，都在很大程度上影响着学生。\\n②学校。学校不仅是对学生传授文化科学知识，进行政治思想教育的社会基本教育单元，还是促进学生良好品格形成和发展的重要场所。学生在学校里形成良好的品格，才能顺利走向社会，适应社会生活。反之，则会发生各种问题。而现在的应试教育制度，像紧箍咒一样，时时冲击着素质教育，教师以升学率论质量给待遇，使一些教师对成绩好的学生倍加宠爱，对成绩差的学生则百般呵斥。更有少数教师将腐朽庸俗的人际关系引入师生和家长的关系，身教言传，污染了学生心灵；让孩子过早成人化、世故化。\\n③社会。社会上各种腐朽思想沉渣泛起，对学生负面影响很大。影视传媒、流失少年、勒索等等，浸染着学生稚嫩的心灵；电子游戏机、卡拉OK厅等，又使我们的孩子面临着极强的诱惑，意志薄弱者稍不留意，便坠入其间。\\n三、纠正初中学生的心理偏差的对策\\n为了纠正初中学生的心理偏差，我们必须对教育环境影响予以高度重视。在现有环境中，我们应做到：\\n1．坚持以德、智、体、美、劳全面的教育方针为指导思想进行教育管理，坚持“要成才先成人”的教育思想。\\n2．“学高为师，身正为范。”作为教师，必须加强道德修养，提高职业素质，全面关心和爱护每一位学生的身心健康发展。\\n3．以激励为心育的主要手段。我们要将思想教育和学生喜闻乐见的实践活动结合起来，不断提高学生对美的感受和鉴赏力，使其求真向善，茁壮成长。\\n4．形成教育合力。在抓好班集体建设的同时，我们必须密切联系家长，与家长一起研究分析学生，共同教育学生。\\n5．帮助学生正确认识、分析、评价自己的心理过程。让他们将社会化标准－－《中学生日常行为规范》逐渐内化，用以规范自己的言行，自觉抵制不良诱惑，不断提高自我意识水平和自我教育能力。\\n6．对各类心理偏差学生施以不同的教育。对有骄傲自负心理的学生施以“挫折教育”；对有自卑、胆怯畏惧心理的学生施以“磨难教育”；对有虚荣忌妒、趋同报复、庸俗心理的学生施以分辨真美善、假丑恶的“是非教育”等。\\n与此同时，还应努力提高、优化当代中学生的心理特点。\\n首先，作为家长必须转变观念。对自己的孩子，在作业和职业方面的“期望值”不能脱离子女的实际而好高骛远，每个孩子因智力因素、情趣爱好，性格意志和心理承力各不相同，如果孩子确实尽了自己的努力，而未达到你所期望的目标，不应过多责怪，更不能冷嘲热讽，惩罚打骂。诚然，家长望子成龙“天经地义”，无可厚非。但“龙”的内涵并不专指读大学、考研究生。“三百六十行，行行出状元”，如果每一位家长都能建立这样的“职业观”，让孩子在宽松的环境里读书，\\n其次，作为教育者----教师来说，则更要不断学习，及时吸收新鲜气息，不断提高自己的思想、政治教育水平，提高自己的专业知识和业务水平，做到不仅能教书育人，更能进行教育评价，尊重学生人格，依法执教，用先进的具有创造性的教育思想、理论、方法促进教育水平的提高，注重培养学生的全面发展，加强能力培养和思维训练，提高学生的综合素质。具体方法如下：\\n第一，让学生充分了解自己的心理特点，通过与周围的同学以及其他同龄人相比，通过同电影、小说电视里特定情景中的人物相比，如宣传奥斯特洛夫斯基、托尔斯泰、张海迪、贝多芬等等，通过对比，找出自己在哪些方面存在弱点，或者也可以通过父母、老师、同学对自己经常的评价了解自己在哪些方面存在不良心理特点，从而扬长避短。\\n第二，选择恰当的方法进行锻炼。例如：\\n1、教他们多读好书，如《周恩来》、《钢铁是怎样炼成的》等优化心理品质。人类的几千年文明，其智慧、经验、真知灼见，都浓缩于书中，如果多读好书，能经常与这样一些“高尚朋友”对话，听听他们的“指点”以此开阔视野，启迪智慧，这对优化学生的心理品质是大有裨益的，作为中学生，不仅要读好的故事书，还应该读一些伟人的传记，读一些思想、修养方面的书籍，并且养成做读书笔记的习惯。\\n2、鼓励学生参加社会活动，锻炼心理品质，如送“温暖小组”、“助残小分队”等活动的开展，都是锻炼心理品质行之有效的方法。\\n3、也要注重培养学生琴、棋、书、画、音、体、美等美育活动，有助于疏导、排解不良情绪，给人以美的熏陶和享受，从而对心理产生良性刺激。让美来充实孩子的精神生活，让美来帮助塑造孩子健康的心理。\\n4、在条件可能的情况下，可组织学生春游、郊游、野炊等活动，学生也可以利用寒、曙假、节假日到一些名胜古迹去游览、旅游、参观、陶冶自己的情操，走进大自然，亲近大自然，细心体会大自然，不仅能使人心胸开阔、情绪放松，精神振奋，还常能使人领悟到人生的真谛。\\n只有这样，优化了学生的心理特点，才能促使学生健康成长，从而成为新世纪的合格人才。'}\n", "第 4 行数据: {'text': '我们生产的食品消泡剂，具有可以快速消除泡沫的特点。\\n丹东食品消泡剂相关内容：一般而言，纯水和纯表面活性剂不起泡，这是因为它们的表面和内部是均匀的，很难形成弹性薄膜，即使形成亦不稳定，会瞬间消失。\\n丹东食品消泡剂选择：\\n1. 相容性：相容性是指两种或者两种以上物质混合时，不产生相斥分离现象的能力，相容性好，消泡剂就能够长期、稳定、均匀地存在于体系中，进而发挥消抑泡的作用；反之，就会出现分层等现象，使消泡剂的消泡工作无法正常进行。\\n2. 消泡能力：消泡能力是消泡剂的最主要性能，鉴别此项性能的标准是在同等条件下，分别加入等量不同的消泡剂，观察消泡剂的消泡速度。'}\n", "第 5 行数据: {'text': '程总在座谈中首先向学校的客人介绍了三一集团和北京三一重机的情况，以及在公司快速发展过程中对人才的渴求，指出通过校企联合，学校可以依靠企业的参与制定人才培养方案，使培养的人才更贴近市场，贴近企业，又可以借助企业的资源充实学校的办学实力。同时校企联合有利于企业的可持续发展。校企联合是企业实现人才战略的途径。企业在与高等职业教育合作过程中可以贯彻自己的培养意向，满足对生产第一线实用型人才的需求。\\n武汉交通职业学院盛建龙院长和河北工业职业技术学院李军锁副院长分别介绍了各自学校人才培养情况，并对三一集团的高速发展表示钦佩和赞赏，表示将和公司开展深入、全面的合作，优势互补，使学校和企业实现充分的资源共享，建立全方位长效合作机制。\\n本次联合办学签约仪式，是北京桩机高起点校企合作的开始。按照北京桩机人力资源提升计划，明年北京桩机将和所高职高专院校进行联合办学成立“三一班”，均为统招大专高技学历层次，涉及焊接、装配、机加工、售后服务等紧缺工种，“三一班”学员将达到近300人，为北京桩机的下一个五年跨越式发展打下良好的人才基础。'}\n"]}], "source": ["def preview_dataset(file_path, num_lines=5):\n", "    \"\"\"\n", "    读取并展示数据集的前 num_lines 行\n", "    \"\"\"\n", "    # 检查文件是否存在\n", "    if not os.path.exists(file_path):\n", "        raise FileNotFoundError(f\"{file_path} 文件不存在，请检查路径！\")\n", "\n", "    # 逐行读取并展示前 num_lines 行\n", "    with jsonlines.open(file_path) as reader:\n", "        for idx, obj in enumerate(reader):\n", "            print(f\"第 {idx + 1} 行数据: {obj}\")\n", "            if idx + 1 >= num_lines:\n", "                break\n", "\n", "# 指定文件路径和需要展示的行数\n", "file_path = './dataset/mobvoi_seq_monkey_general_open_corpus.jsonl'\n", "preview_dataset(file_path, num_lines=5)"]}, {"cell_type": "markdown", "id": "d9e4b2a7-3a50-438b-8a99-45788a430bb5", "metadata": {}, "source": ["> - **什么样的 JSONL 中会携带无效的 JSON 格式**？\n", "\n", "在 JSONL 文件中，每一行都应当是有效的 JSON 对象，但有以下几种常见情况会导致无效的 JSON 行：\n", "\n", "1. **缺少必要的符号**：JSON 对象必须用 `{}` 括起来，且键值对之间要用逗号分隔。例如，缺少结束花括号：\n", "   ```\n", "   {\"name\": \"<PERSON>\", \"age\": 25\n", "   ```\n", "\n", "2. **引号问题**：JSON 的键和值（非数字类型）必须使用双引号。如果使用了单引号或忘记引号，都会导致无效格式：\n", "   ```\n", "   {'name': '<PERSON>', 'age': 25}  # 错误的引号\n", "   ```\n", "\n", "3. **数据未完整写入**：例如由于文件写入中断，某一行可能是不完整的 JSON 对象：\n", "   ```\n", "   {\"name\": \"<PERSON>\", \"age\":\n", "   ```\n", "\n", "4. **额外的标点符号或换行符**：一些 JSONL 文件可能错误地加入了多余的标点符号或不正确的换行符，导致解析错误：\n", "   ```\n", "   {\"name\": \"<PERSON>\", \"age\": 25},\n", "   {\"name\": \"<PERSON>\", \"age\": 30}\n", "   ```\n", "\n", "   这里的逗号在 JSONL 中是不需要的，因为每一行都是独立的。\n", "\n", "5. **字符编码问题**：文件可能存在编码不一致的情况，特别是非 UTF-8 编码时，可能会引发解码错误，例如你的错误提示中看到的 \"codec can't decode byte\"。"]}, {"cell_type": "markdown", "id": "c7fb4928-1abb-4597-986f-0b3cc1a98634", "metadata": {}, "source": ["> - **无效的 JSON 格式示例**\n", "\n", "例如，以下 JSON 数据格式就是无效的：\n", "\n", "- 缺少关闭括号：\n", "   ```json\n", "   {\"name\": \"<PERSON>\", \"age\": 25\n", "   ```"]}, {"cell_type": "markdown", "id": "ff59d745-223a-478a-9392-79c2e8053ef6", "metadata": {}, "source": ["- 键名未加双引号：\n", "   ```json\n", "   {name: \"<PERSON>\", \"age\": 25}\n", "   ```"]}, {"cell_type": "markdown", "id": "8081fce3-2691-441b-a5ea-601eeb6d14f7", "metadata": {}, "source": ["- 值部分使用了错误的引号：\n", "   ```json\n", "   {\"name\": '<PERSON>', \"age\": 25}\n", "   ```"]}, {"cell_type": "markdown", "id": "8737e286-eccc-41e0-9c6e-175ff6d4b780", "metadata": {}, "source": ["- Step 4.统计与清理数据"]}, {"cell_type": "code", "execution_count": 8, "id": "f5f9491d-057c-4aff-acf5-6684381d5935", "metadata": {}, "outputs": [], "source": ["def get_total_lines(file_path):\n", "    \"\"\"\n", "    获取 JSONL 文件的总行数，不忽略错误，保证能够全面统计。\n", "    \"\"\"\n", "    with open(file_path, 'rb') as f:  # 使用二进制模式避免编码问题\n", "        return sum(1 for _ in f)"]}, {"cell_type": "code", "execution_count": 12, "id": "d90978ff-43d5-449e-bda3-c838c2ba6006", "metadata": {}, "outputs": [], "source": ["def check_jsonl_format(file_path):\n", "    \"\"\"\n", "    检查 JSONL 文件中的每一行是否是有效的 JSON 格式，带进度显示，并统计所有有问题的行。\n", "    \"\"\"\n", "    total_lines = get_total_lines(file_path)  # 获取文件总行数\n", "    valid_lines = 0\n", "    invalid_lines = 0\n", "\n", "    # 使用逐行读取，捕获 JSON 和编码错误\n", "    with open(file_path, 'rb') as f:  # 使用二进制读取避免编码问题\n", "        # 使用 tqdm 进度条显示检查进度\n", "        for idx, line in tqdm(enumerate(f), total=total_lines, desc=\"Checking JSONL format\"):\n", "            try:\n", "                # 先尝试将每行数据解码为 UTF-8\n", "                decoded_line = line.decode('utf-8')\n", "                # 然后检查是否是有效的 JSON 格式\n", "                obj = jsonlines.Reader([decoded_line]).read()\n", "                valid_lines += 1\n", "            except UnicodeDecodeError as e:\n", "                print(f\"Encoding error at line {idx + 1}: {e}\")\n", "                invalid_lines += 1\n", "            except jsonlines.InvalidLineError as e:\n", "                print(f\"Invalid JSON at line {idx + 1}: {e}\")\n", "                invalid_lines += 1\n", "\n", "    print(f\"检查完成，文件中共有 {valid_lines} 行有效的 JSON 数据，{invalid_lines} 行无效的 JSON 数据。\")\n", "    return valid_lines, invalid_lines"]}, {"cell_type": "code", "execution_count": 13, "id": "65b44f7f-5855-406d-a893-e600f0954a1a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Checking JSONL format: 100%|██████████| 9598787/9598787 [02:01<00:00, 79274.88it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["Encoding error at line 9598787: 'utf-8' codec can't decode byte 0xe5 in position 503: unexpected end of data\n", "检查完成，文件中共有 9598786 行有效的 JSON 数据，1 行无效的 JSON 数据。\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["valid_lines, invalid_lines = check_jsonl_format(file_path)"]}, {"cell_type": "code", "execution_count": 14, "id": "7f866df5-3be9-466e-a592-5435901d75e7", "metadata": {}, "outputs": [], "source": ["def remove_invalid_line(file_path, output_path, invalid_line_num):\n", "    \"\"\"\n", "    读取文件，跳过指定的无效行，并将结果写入新文件\n", "    \"\"\"\n", "    with open(file_path, 'rb') as infile, open(output_path, 'wb') as outfile:\n", "        for idx, line in enumerate(infile):\n", "            if idx + 1 != invalid_line_num:  # 跳过无效行\n", "                outfile.write(line)\n", "\n", "# 使用该函数删除第 9598787 行并保存为新文件\n", "remove_invalid_line('./dataset/mobvoi_seq_monkey_general_open_corpus.jsonl',\n", "                    './dataset/mobvoi_seq_monkey_general_open_corpus_cleaned.jsonl', \n", "                    invalid_line_num=9598787)"]}, {"cell_type": "markdown", "id": "42c5b21f-6910-48e4-ab64-46fa9ff44680", "metadata": {}, "source": ["最终会得到如下的文件 ↓"]}, {"cell_type": "markdown", "id": "76e124d8-aa3b-4f4b-9ff0-d54d63eb31ff", "metadata": {}, "source": ["<center><img src=\"https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/92.png\" alt=\"image-20241022201401913\" style=\"zoom:80%;\" />"]}, {"cell_type": "markdown", "id": "7b7ea305-db8f-4040-8edf-95e04da54642", "metadata": {}, "source": ["- Step 5.定义处理函数（逐块处理数据）"]}, {"cell_type": "markdown", "id": "4aded9f1-3ed1-40d0-b30b-93cc11630508", "metadata": {}, "source": ["**注意修改函数中的目录为自己的目录！**"]}, {"cell_type": "code", "execution_count": 19, "id": "ff840174-a9a5-4578-8a2f-461695248bae", "metadata": {}, "outputs": [], "source": ["def process_seq_monkey(chunk_size=50000):\n", "    \"\"\"\n", "    逐块读取 mobvoi_seq_monkey_general_open_corpus.jsonl 文件，\n", "    对文本进行分词，并将分词结果保存为二进制文件，支持跳过无效行，并显示处理进度。\n", "    \"\"\"\n", "    doc_ids = []\n", "    chunk_idx = 0\n", "    total_lines = 0\n", "\n", "    # 先计算总行数以便显示进度\n", "    # 注意调整成你自己的目录\n", "    with open('./Data/seq-monkey/mobvoi_seq_monkey_general_open_corpus_cleaned.jsonl', 'r', encoding='utf-8') as f:\n", "        total_lines = sum(1 for _ in f)\n", "\n", "    # 打开jsonlines文件逐行读取\n", "    with jsonlines.open('./Data/seq-monkey/mobvoi_seq_monkey_general_open_corpus_cleaned.jsonl') as reader:\n", "        # 使用 tqdm 进度条显示进度\n", "        with tqdm(total=total_lines, desc=\"Processing lines\") as pbar:\n", "            while True:\n", "                try:\n", "                    # 使用 itertools.islice 按块读取文件，每次读取 chunk_size 行数据\n", "                    chunk = list(itertools.islice(reader, chunk_size))\n", "                except jsonlines.InvalidLineError as e:\n", "                    print(f\"Skipping invalid chunk at chunk {chunk_idx}: {e}\")\n", "                    continue\n", "\n", "                if not chunk:  # 如果读取到文件末尾，则停止\n", "                    break\n", "\n", "                # 遍历块中的每一行数据\n", "                # 逐行对数据进行编码（按token进行编码）\n", "                for idx, obj in enumerate(chunk):\n", "                    try:\n", "                        # 从每一行数据中提取'text'字段（即文本内容）\n", "                        content = obj.get('text', '')\n", "                        \n", "                        # 跳过长度超过512的文本\n", "                        if len(content) > 512:\n", "                            continue\n", "\n", "                        # 对文本进行分词，将其转为 token ids 序列，并加上BOS和EOS标记\n", "                        text_id = tokenizer(f'{bos_token}{content}{eos_token}').data['input_ids']\n", "                        \n", "                        # 将分词结果添加到 doc_ids 列表中\n", "                        doc_ids += text_id\n", "\n", "                    except UnicodeDecodeError as e:\n", "                        # 如果遇到编码错误，跳过该行，并打印错误信息\n", "                        print(f\"Skipping invalid line {chunk_idx * chunk_size + idx + 1}: {e}\")\n", "                        continue\n", "\n", "                # 每处理完一块数据，更新 chunk_idx 并打印进度信息\n", "                chunk_idx += 1\n", "                pbar.update(len(chunk))  # 更新进度条\n", "\n", "                # 如果累积的 token ids 超过 1,000,000 个，保存到文件中\n", "                if len(doc_ids) > 1000000:\n", "                    arr = np.array(doc_ids, dtype=np.uint16)\n", "                    with open(f'./Data/seq-monkey/clean_seq_monkey.bin', 'ab') as f:\n", "                        f.write(arr.tobytes())\n", "                    doc_ids = []\n", "\n", "    # 如果处理完所有数据后 doc_ids 中还有未保存的内容，最后再保存一次\n", "    if doc_ids:\n", "        arr = np.array(doc_ids, dtype=np.uint16)\n", "        with open(f'./Data/seq-monkey/clean_seq_monkey.bin', 'ab') as f:\n", "            f.write(arr.tobytes())"]}, {"cell_type": "code", "execution_count": 20, "id": "e38f16ae-0250-4b76-9eed-349e806bf0e3", "metadata": {}, "outputs": [], "source": ["def pretrain_process():\n", "    \"\"\"\n", "    函数的作用是调用 process_seq_monkey() 函数生成数据，\n", "    然后整合所有生成的二进制文件，并将其合并保存为一个总的预训练数据文件。\n", "    \"\"\"\n", "    # 调用 process_seq_monkey 函数处理数据\n", "    process_seq_monkey()\n", "\n", "    # 数据文件路径列表，目前只处理 clean_seq_monkey.bin 文件\n", "    data_path_list = [\n", "        './Data/seq-monkey/clean_seq_monkey.bin'\n", "    ]\n", "    \n", "    data_lst = []\n", "    \n", "    # 读取生成的二进制文件\n", "    for data_path in data_path_list:\n", "        with open(data_path, 'rb') as f:\n", "            # 将二进制文件中的内容加载到 numpy 数组中\n", "            data = np.fromfile(f, dtype=np.uint16)\n", "            data_lst.append(data)\n", "\n", "    # 将所有读取到的数据合并为一个大数组\n", "    arr = np.concatenate(data_lst)\n", "    print(f\"合并后的数据大小: {arr.shape}\")\n", "\n", "    # 将合并后的数据保存为最终的预训练数据文件\n", "    with open('./Data/seq-monkey/pretrain_data.bin', 'wb') as f:\n", "        f.write(arr.tobytes())"]}, {"cell_type": "markdown", "id": "efa1e9a4-138b-4745-845a-bb736595488a", "metadata": {}, "source": ["- **为什么要将数据转换为二进制文件**：\n", "\n", "1. **高效存储和读取**：二进制文件相比文本文件具有更高的存储和读取效率，尤其是对于大规模的数据集。由于二进制文件是以原始的机器可读格式存储的，不需要进行字符编码转换，因此读取速度更快。对于预训练阶段通常需要处理大量数据，二进制文件可以显著减少读取时间。\n", "\n", "2. **减少文件大小**：二进制格式的数据比常规的文本格式更紧凑，占用的磁盘空间更少。这对存储大数据集尤其重要，能够显著节省存储资源。\n", "\n", "3. **与深度学习框架兼容**：深度学习框架（如 PyTorch、TensorFlow 等）在训练时往往需要数据以某种高效的格式加载到内存中。将数据保存为二进制格式有助于快速载入到 NumPy 数组或直接作为模型输入，避免了每次都需要重新转换。\n", "\n", "4. **跨平台一致性**：二进制文件可以跨平台使用而不丢失精度和数据信息，适合在不同的硬件和操作系统环境中使用。\n", "\n", "- 这个函数的具体作用：\n", "> - `process_seq_monkey()` 函数负责处理原始数据并生成单个或多个二进制文件。\n", "> - 然后这些生成的二进制文件通过 `np.fromfile` 读取并存入 NumPy 数组，所有的二进制数据都会被拼接到一起。\n", "> - 最后，通过 `np.concatenate` 合并所有的 NumPy 数组，生成一个总的数据数组，并将其存储为一个大的二进制文件 (`pretrain_data.bin`)，供后续的模型预训练使用。\n", "\n", "总结来说，这种处理方式主要是为了提高效率，方便在大规模预训练任务中快速加载数据并减少磁盘和内存的占用。"]}, {"cell_type": "markdown", "id": "348d219c-106f-499d-b836-ce798aaa1cc4", "metadata": {}, "source": ["- 运行数据处理"]}, {"cell_type": "code", "execution_count": 22, "id": "c27971fa-5750-4cb1-bf7e-763344c8f3ab", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processing lines: 100%|██████████| 9598786/9598786 [36:17<00:00, 4408.66it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["合并后的数据大小: (1115541384,)\n"]}], "source": ["pretrain_process()"]}, {"cell_type": "markdown", "id": "0f9e87ce-1cc2-4b70-9980-43de54875758", "metadata": {}, "source": ["运行结束后会创建一个名为pretrain_data.bin的二进制数据文件，该文件也就是接下来进行模型预训练的文件："]}, {"cell_type": "markdown", "id": "3f044684-05b9-4805-a2c2-345d0eb29875", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/image-20241023183247295.png\" alt=\"image-20241023183247295\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "6115b9ee-9b77-450c-a9ee-83c6fe296770", "metadata": {}, "source": ["## 2. MateConv的数据收集与数据预处理"]}, {"cell_type": "markdown", "id": "32428ff3-a538-42cf-8ff0-7443153b1732", "metadata": {}, "source": ["### 2.1 MateConv的预训练数据组成"]}, {"cell_type": "markdown", "id": "db313f21-1bf5-4481-99b8-54086c<PERSON>fcee", "metadata": {}, "source": ["MateConv所使用的语料是由开源数据集和我们自制的数据分析报告/商业分析报告数据集结合、但数据体量要大得多、并且语料也没有那么干净。在构建大型模型的训练预料时，我们有如下三个基本准则——\n", "\n", "1. **规模**：构建大型模型的训练语料需要以T级别数据为目标，这意味着数据集的规模必须能够覆盖大模型所需的丰富知识和复杂模式。幸运的是，无论是在中文社区、还是英文社区中，现在都存在许多规模巨大的语料包供我们选择。在训练10B以下模型时，公开语料本身足以覆盖我们的需求。当然，我们要追求的是规模和质量的平衡、单纯增大规模并无意义。\n", "\n", "2. **多样性**：不仅包括语料来源的多样性，还应涵盖覆盖领域、语言的多样性以及文本类型的多样性。语料来源应囊括学术文章、新闻报道、社交媒体内容、技术文档等多种形式，以保证模型适应不同的语境。覆盖领域需要广泛，包括科学、技术、艺术、教育、医学和法律等，以确保模型在各个行业都有所表现。同时，语言的多样性要求语料覆盖常见语言和低资源语言，满足多语言模型的训练需求。文本类型的多样性同样重要，应包含叙述性文本、对话文本、代码样本等，从而增强模型在不同应用场景中的表现。\n", "\n", "3. **质量**：高质量的数据是训练优秀模型的前提。语料需要经过严格的清洗与筛选，确保其语法正确、内容无噪声且无重复。同时，数据应具备良好的可读性，避免低质量或不完整的文本进入数据集。此外，语料的合法性和道德合规性至关重要，需要确保数据来源符合法律要求，不包含敏感信息或侵犯隐私的内容。高质量的标注和丰富的元信息也能帮助模型更好地理解数据，从而提升训练效果。"]}, {"cell_type": "markdown", "id": "2b22f1ec-a6a2-4470-9386-afcc83b0c65d", "metadata": {}, "source": ["在MateConv构建过程中，我们使用了55%中文、37%英文及其他语言、8%代码的方式进行构建，总数据集大小为4.33TB（当然由于每个数据集的存储格式不同、因此数据集所占用的内存大小本身并不完全代表数据量的大小，但依照存储的格式来判断，通常来说数据量是 csv < Parquet < JSON < JSONL）。为了降低训练成本，我们选择了在数据集打包时就已经经过一定清洗的数据、而没有选择完全没清洗过的数据。当然、这些数据还要再经过清洗流程才能够使用、但耗费的时间与人力会远远低于直接在raw data上进行清洗。\n", "\n", "| 数据集编号 | 数据集名称                 | 数据属性（中文/英文/代码）                 | 数据量级           | 存储格式            | 是否经过数据清洗 |\n", "|---|----------------------------|-------------------------------------------|--------------------|---------------------|------------------|\n", "| 1| Skywork-SkyPile 150B       | 中文文本                                      | 620GB             | JSONL 短文本        | 是               |\n", "| 2| wanjuan1.0-nlp-CN             | 中文文本                                 | 580GB | JSONL 短文本压缩  | 是               |\n", "| 3| WuDaoCorporaText | 中文文本                                      | 200GB  | JSONL 短文本        | 是               |\n", "| 4| chinese-fineweb-edu-v2 | 75%中文文本，25%英文文本                                      | 670GB  | Parquet        |   是            |\n", "| 5| Wikipedia-CN | 中文文本                                      | 1.1GB  | JSON        |   是            |\n", "| 6| BaiduBaike-5.63M | 中文文本                                      | 17GB  | JSON        |   是            |\n", "| 7| wangrui6/Zhihu-KOL | 中文问答对                                      | 1.5 GB  | parquet        |   是            |\n", "| 8| wanjuan1.0-nlp-EN             | 英文文本                                 | 440GB | JSONL 短文本压缩  | 是               |\n", "| 9| SlimPajama-627B            | 英文为主的混合语言文本                                      | 1TB               | JSONL 短文本压缩    | 是               |\n", "| 10| Starcoder                  | 代码             | 100+GB/768G             | Parquet            | 是               |\n", "| 11| TheStackDedup                 | 代码             | 700+GB/3TB             | Parquet            | 是               |"]}, {"cell_type": "markdown", "id": "b036dea0-f801-4902-9547-d235471f1eba", "metadata": {}, "source": ["在这些数据中、大部分为纯粹的文本数据、有很少的一部分为问答对数据。通常来说，我们不会在预训练阶段加入问答对，但业内有研究声称在预训练阶段加入问答对可以提升模型表现。出于教学目的（教导大家如何处理问答对）以及验证目的（观察是否在预训练阶段加入问答对可以提升模型表现），我们保留了少许问答对数据。同时，除了大型的数据之外，我们还使用了10G上下、1G上下的不同大小的文字数据，如果你没有足够的硬件设备可以拉取这些数据，那你可以拉取小型的数据来体验一下全流程。"]}, {"cell_type": "markdown", "id": "d38782db-1555-413b-93f6-59cb<PERSON>bbae95", "metadata": {}, "source": ["- 预训练数据的具体详情"]}, {"attachments": {}, "cell_type": "markdown", "id": "36de12b5-a7ea-48b9-bdf3-6bc4a98767d2", "metadata": {}, "source": ["1. **Skywork-SkyPile 150B**\n", "\n", "Huggingface 开源，包含 150B 中文 tokens，数据集总大小约 620GB，**采用 JSONL 短文本格式储存**。该数据集从公开可访问的中文互联网网页中获取，经过严格的过滤、去重和敏感数据筛除，以确保数据质量。Skywork-SkyPile数据集被开发者描述为目前中文语料社区中最大的数据集、同时也是我们在众多中文预训练任务中都会看到的一个重要数据集，**在本次训练中我们拉取了Skywork-SkyPile的所有数据来使用**。<br><br>\n", "开源网址：[https://huggingface.co/datasets/Skywork/SkyPile-150B/tree/main/data](https://huggingface.co/datasets/Skywork/SkyPile-150B/tree/main/data)\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/14.png)\n", "\n", "2. & 8. **wanjuan1.0-nlp**\n", "\n", "OpenDataLab开源（**需注册OpenDataLab官网、登录并添加密钥后可访问**）、包含文本数据集、图文数据集、视频数据集三部分，数据总量超过2TB，且已经经过了细粒度的清晰、去重、价值对齐、数据质量较高。其中NLP文本数据集部分包含580G中文数据与440G英文数据，**存储格式为 JSONL 短文本压缩文件格式。在本次训练中我们使用了NLP文本数据集下的所有内容**。\n", "\n", "访问网址：[https://opendatalab.org.cn/OpenDataLab/WanJuan1_dot_0](https://opendatalab.org.cn/OpenDataLab/WanJuan1_dot_0) \n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/16.png)\n", "\n", "3. **悟道文本数据集（WuDaoCorporaText）**\n", "\n", "BAAI（智源社区）开源（**注册智源社区后即可访问**）、包含100TB原始网页数据中清晰得到的最终数据集、开源200G大小、**存储格式为 Jsonl 短文本**。悟道文本数据集是由北京智源人工智能研究院（BAAI）构建的大规模、高质量中文语料库，旨在支持大型语言模型的训练研究。该数据集从公开可获取的网络资源中收集，经过严格的筛选、去重和清洗，确保数据的多样性和高质量。**在本次训练中，我们使用了WuDao的全部数据，但需要注意的是，这个数据无法通过bash脚本直接从网站拉取、只能通过桌面端下载后再上传到服务器。**\n", "\n", "访问网址：[https://data.baai.ac.cn/details/WuDaoCorporaText](https://data.baai.ac.cn/details/WuDaoCorporaText) \n", "\n", "\n", "4. **Chinese FineWeb Edu V2**\n", "\n", "Huggingface开源、包含约 1.88 亿条记录，总计约 4200 亿个tokens，共计679G左右、以 Parquet 格式存储。Chinese FineWeb Edu V2 是一个面向教育领域自然语言处理（NLP）任务的开源中文预训练数据集，数据集经过显著的优化和扩展和清洗。数据虽然以是否具有足够教育意义评估、但实际上数据来源涵盖多个领域（如 IndustryCorpus2、CCI3、TeleChat 等）。**在本次训练中，我们使用了FineWeb Edu V2的全部数据。**\n", "\n", "访问链接：[https://huggingface.co/datasets/opencsg/chinese-fineweb-edu-v2](https://huggingface.co/datasets/opencsg/chinese-fineweb-edu-v2)\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/23.png)\n", "\n", "5. **Wikipedia-cn-20230720-filtered**\n", "\n", "huggingface开源、包含约 255,000 条记录，总大小约为 1.1 GB，约 85,000 个标记（tokens）、数据结构为JSON，已经过过滤和清洗。Wikipedia-cn-20230720-filtered 是由 Pleisto 发布的开源中文维基百科数据集。\n", "\n", "访问网址：[https://huggingface.co/datasets/pleisto/wikipedia-cn-20230720-filtered](https://huggingface.co/datasets/pleisto/wikipedia-cn-20230720-filtered) \n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/20.png)\n", "\n", "6. **BaiduBaike-5.63M**\n", "\n", "Huggingface开源、包含约 563 万条百度百科的条目，文件大小约为 16.8 GB、**存储为 JSON 格式**。BaiduBaike-5.63M 是由 Hugging Face 用户 xuqinyang 发布的开源中文百科数据集，由于数据集的条目数量和内容丰富，具体的标记（token）数量可能达到数亿级别。**在本次训练中我们使用了该数据集的全部数据**。\n", "\n", "访问网址：[https://huggingface.co/datasets/xuqinyang/BaiduBaike-5.63M](https://huggingface.co/datasets/xuqinyang/BaiduBaike-5.63M) \n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/21.png)\n", "\n", "7. **Zhihu-KOL**\n", "\n", "Huggingface开源，该数据集包含约 101 万条知乎问答对，数据总大小约为 1.5 GB，**存储为 Parquet 格式**。Zhihu-KOL 是由 Hugging Face 用户 wangrui6 发布的开源中文问答数据集。\n", "\n", "访问网址：[https://huggingface.co/datasets/wangrui6/Zhihu-KOL](https://huggingface.co/datasets/wangrui6/Zhihu-KOL) \n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/22.png)\n", "\n", "\n", "9. **Slim<PERSON><PERSON>ma-627B**\n", "\n", "Huggingface开源（**需提交邮箱地址、以申请访问权限**）、包含6270亿Token，1T左右总数据大小，**存储格式为Jsonl 短文本压缩文件**。SlimPajama-627B 是由 Cerebras 开发的开源数据集，也是TinyLlaMA项目的训练数据集。该数据集通过对 RedPajama 的 1.2 万亿标记（tokens）进行清洗和去重，精简至 6270 亿标记，删除了约 49.6% 的低质量和重复数据。SlimPajama-627B 包含来自多种语料来源的文本，主要以英语为主，经过严格的去重和清洗，以确保数据的高质量和多样性。**在本次训练中，我们使用了SlimPajama全部的数据。**\n", "\n", "访问网址：[https://huggingface.co/datasets/cerebras/SlimPajama-627B](https://huggingface.co/datasets/cerebras/SlimPajama-627B) \n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/18.png)\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/17.png)\n", "\n", "10. **Starcoder**\n", "\n", "Huggingface开源（**需提交邮箱地址、以申请访问权限**）、包含超过 783GB 的代码，总计约 2,500 亿个tokens，涵盖 86 种编程语言，**采用parquet格式存储**。该数据集是由 BigCode 项目发布的开源代码数据集，旨在用于训练大型代码语言模型，信息来源包括 GitHub 提交记录、问题讨论、和 Jupyter 笔记本等多种数据源。数据经过严格的去重和清洗，以确保高质量和多样性。**在本次训练中，我们拉取了Python、SQL、R、Matlab、JavaScript、Java、Json、C、Rust、Go、TypeScript、Kotlin、Swift、Julia、markdown、html等16种语言进行训练**。<br><br>\n", "\n", "开源网址：[https://huggingface.co/datasets/bigcode/starcoderdata](https://huggingface.co/datasets/bigcode/starcoderdata)\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/15.png)\n", "\n", "11. **The Stack Dedup**\n", "\n", "Huggingface开源（**需提交邮箱地址、以申请访问权限**），3TB大小、覆盖358种编程语言、数据存储方式为Parquet。The Stack Dedup 是由 BigCode 项目发布的开源代码数据集，旨在为代码大型语言模型（Code LLMs）的预训练提供高质量语料、数据来源于具有宽松许可证的开源代码库，经过严格的去重和清洗，以确保数据的高质量和多样性。\n", "\n", "访问网址：[https://huggingface.co/datasets/bigcode/the-stack-dedup](https://huggingface.co/datasets/bigcode/the-stack-dedup) \n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/19_.png)"]}, {"cell_type": "markdown", "id": "c4169521-b876-44ae-bcde-9f1d8eee04b0", "metadata": {}, "source": ["- 未清洗数据"]}, {"cell_type": "markdown", "id": "03aeb8b5-e103-4e19-b591-df53c6513449", "metadata": {}, "source": ["同时，我们曾考虑过使用现在国内已经非常火热的**MNBVC(Massive Never-ending BT Vast Chinese corpus)超大规模中文语料集**，这个数据集是国内爱好者自发收集的、决定制作成最大的中文开源数据集，目前已收集了40T数据，但数据还没有经过校验、质量参差不齐。走这里可以看到当前这个中文数据集的现状：https://github.com/esbatmop/MNBVC\n", "\n", "考虑了很久、我们还是没有使用这个不成熟的数据集。在刚收集好、未清洗的巨量文字数据集上进行清洗是一项耗时耗人工的巨大的工作、需要高度自动化的工具和清晰的策略，同时必须结合人工干预以确保高质量结果、这个数据集完成之时、中文预训练模型的质量也将更上一层楼。现在这个数据集的清洗工作将在社区帮助下逐步完成，如果你感兴趣你可以参与。"]}, {"cell_type": "markdown", "id": "38e9989e-e782-47d5-9c82-9d8d008c58e1", "metadata": {}, "source": ["- 领域数据集"]}, {"cell_type": "markdown", "id": "afe6ecbd-d042-4064-b220-0f9bd501fd22", "metadata": {}, "source": ["在数据组成中还有许多不成熟的地方，例如我们其实并没有来得及考虑加入一些更专业的领域的数据（金融、医疗、法律、教育、科技等），如果你的目标模型是一个专业领域的模型，那在预训练阶段加入你的专业文本其实对于后续微调也会很有好处！我们尝试在Huggingface以及各类开源社区中收集更专业的数据集、但是得到的大部分领域数据集尺寸都很小，适合微调但并不适合预训练。后续在微调阶段我们会进一步进行介绍。"]}, {"cell_type": "markdown", "id": "26a1f7d9-3e35-45fd-8274-708e6f1e41e4", "metadata": {}, "source": ["- 合成数据集"]}, {"cell_type": "markdown", "id": "cd60ee78-83b7-4047-8f9d-cd23e594258d", "metadata": {}, "source": ["今年以来、合成数据在大模型训练中的应用逐渐普及，尤其是在预训练和微调阶段，在数据集缺乏的领域、或者在对数据集要求特别高的场合、合成数据集展现了其强大的价值、合成数据集训练也成为目前备受关注的领域之一。\n", "\n", "**合成数据具有显著的成本优势**，通过使用大模型生成大规模数据，可以显著降低人工标注和数据收集的成本。在稀有场景、多语言任务或特定领域（如医学和法律）中，合成数据能够快速弥补数据空白，增强语料的多样性。此外，合成数据因其高度可控性，可以根据任务需求设计生成策略，用于提高模型在特定场景中的表现。同时，合成数据还能在保护隐私和遵守法规（如 GDPR）方面发挥关键作用，为敏感数据的训练提供替代方案。\n", "\n", "然而，依赖合成数据也面临挑战。**合成数据的质量高度依赖生成模型，如果生成模型本身存在偏差或错误，可能导致数据中出现噪声或不准确的内容，从而影响模型的训练效果**。过度依赖合成数据会引发“模式崩塌”问题，即模型的输出缺乏多样性，重复生成相似内容。此外，合成数据可能无法完全反映真实数据分布，容易导致模型在真实应用场景中表现不佳。在伦理层面，合成数据如果模仿真实内容（如代码或文档），可能引发版权和归属争议，尤其是在大规模使用时。\n", "\n", "为了更高效地利用合成数据，必须将其与真实数据结合使用，以平衡数据分布，避免模型过拟合到生成模式。对合成数据的质量控制至关重要，可以通过去噪、筛选和验证等手段确保数据的可靠性。此外，在缺乏真实数据的领域，如低资源语言或稀有任务场景，合成数据的作用尤为显著，可以显著提高模型的性能。未来，随着生成技术的发展和质量评估方法的完善，合成数据将更广泛地应用于大模型的训练，但需要开发者在使用中保持审慎，确保其作为真实数据的有益补充而非单一依赖来源。\n", "\n", "未来我们会有更多关于合成数据集的内容被讲解、如果你了解更多信息的话，也欢迎让我们知晓！"]}, {"cell_type": "markdown", "id": "4d1b2be0-6229-4518-b688-5a0be22cfcca", "metadata": {}, "source": ["### 2.2 巨量数据的拉取与存储"]}, {"cell_type": "markdown", "id": "9a0f22a9-9074-4e63-8b7a-68cdb6f3fa0d", "metadata": {}, "source": ["在我们使用几个G甚至几十G大小的数据集时，我们可以通过直接下载到本地、或者直接拉取到服务器的方式来调取，但是当数据集变得巨大时、拉取的过程会变得极其漫长且极其不稳定、且每个数据集可能都被原作者或者开源方定义了独特的拉取方式，因此大型数据的拉取是一个相对复杂的问题。在进行预训练之前，我们需要保证我们的所有数据都能够到位。\n", "\n", "我们将以巨型中文数据、英文数据、以及代码数据为例、为你讲解多种不同的数据拉取方式、并且在课件中呈现所有数据的拉取流程，你可以选择任意的数据集进行拉取。"]}, {"cell_type": "markdown", "id": "197ca379-6892-46b9-b3fb-e0a8ecb9d2d4", "metadata": {}, "source": ["| 数据集编号 | 数据集名称                 | 数据属性（中文/英文/代码）                 | 数据量级           | 存储格式            | 是否经过数据清洗 |\n", "|---|----------------------------|-------------------------------------------|--------------------|---------------------|------------------|\n", "| 1| Skywork-SkyPile 150B       | 中文文本                                      | 620GB             | JSONL 短文本        | 是               |\n", "| 2| wanjuan1.0-nlp-CN             | 中文文本                                 | 580GB | JSONL 短文本压缩  | 是               |\n", "| 3| WuDaoCorporaText | 中文文本                                      | 200GB  | JSONL 短文本        | 是               |\n", "| 4| chinese-fineweb-edu-v2 | 75%中文文本，25%英文文本                                      | 670GB  | Parquet        |   是            |\n", "| 5| Wikipedia-CN | 中文文本                                      | 1.1GB  | JSON        |   是            |\n", "| 6| BaiduBaike-5.63M | 中文文本                                      | 17GB  | JSON        |   是            |\n", "| 7| wangrui6/Zhihu-KOL | 中文问答对                                      | 1.5 GB  | parquet        |   是            |\n", "| 8| wanjuan1.0-nlp-EN             | 英文文本                                 | 440GB | JSONL 短文本压缩  | 是               |\n", "| 9| SlimPajama-627B            | 英文为主的混合语言文本                                      | 1TB               | JSONL 短文本压缩    | 是               |\n", "| 10| Starcoder                  | 代码             | 100+GB/768G             | Parquet            | 是               |\n", "| 11| TheStackDedup                 | 代码             | 700+GB/3TB             | Parquet            | 是               |"]}, {"cell_type": "markdown", "id": "6d84bcc9-bb03-4206-97ba-c693286bfd17", "metadata": {}, "source": ["- **建立拉取数据专用目录**"]}, {"cell_type": "markdown", "id": "784f383d-8a33-409b-8515-a5fb48260216", "metadata": {}, "source": ["下面是**命令行代码**，确保你在运行之前已经按照九天老师的视频建立了MateConv虚拟环境，进入了autodl-tmp这个硬盘存储空间，并且建立了MateConv文件夹。当然，你也可以把目录替换成你自己的目录 ↓"]}, {"cell_type": "markdown", "id": "2c17fb51-741f-4f34-9df4-d588d0ef1077", "metadata": {}, "source": ["```bash\n", "mkdir -p ~/autodl-tmp/MateConv/Data\n", "```"]}, {"cell_type": "markdown", "id": "67ef6d05-01a0-4a35-935a-9cbac07cfb7d", "metadata": {}, "source": ["你也可以为某个数据集单独设置目录——"]}, {"cell_type": "markdown", "id": "f10725ea-e5c7-4125-8ced-aea66af0e472", "metadata": {}, "source": ["```bash\n", "mkdir -p ~/autodl-tmp/MateConv/Data/SkyPile\n", "```"]}, {"cell_type": "markdown", "id": "ae3d390d-3364-4dfb-8f72-0f349c35f50e", "metadata": {}, "source": ["你可以查看自己的硬盘空间大小、确保自己有给数据准备好足够的空间（在h之后还有一个空格一个点哦）——"]}, {"cell_type": "markdown", "id": "1fa48467-b733-4034-bda0-43f1c6083e16", "metadata": {}, "source": ["```bash\n", "pwd          # 查看当前路径\n", "df -h .      # 查看当前路径所属磁盘的使用情况\n", "```"]}, {"cell_type": "markdown", "id": "2ea29f32-ccd6-42ca-9c1a-b4986dd115ca", "metadata": {}, "source": ["返回的结果如下 ↓"]}, {"cell_type": "markdown", "id": "e9d77d86-5250-47a8-9731-a04055d60c42", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/25.png)"]}, {"cell_type": "markdown", "id": "8ba7ec77-69b7-4620-ad7d-0d4c27b87f1c", "metadata": {}, "source": ["这说明还有1.1T的空间供我们使用，你在你自己的磁盘上将会看到相应的空间。你可以在上面的表格中查看具体数据的大小，如果是我们没有标注的数据集，你可以通过Huggingface主页面查看具体的大小，许多数据集都会提到总数据大小，如果没有提到，你也可以通过files页面或者其他页面信息来估算数据总量的大小。"]}, {"cell_type": "markdown", "id": "f5f4d055-7405-4f3b-83ac-a35415de98ce", "metadata": {}, "source": ["- **设置镜像站**"]}, {"cell_type": "markdown", "id": "11113a37-4465-4adb-8409-2bb6a584d177", "metadata": {}, "source": ["不同于模型、几乎所有的数据都可以从Huggingface镜像站（[https://hf-mirror.com](https://hf-mirror.com)）进行拉取下载，这样可以避开网络问题、而不用将本地信息代理到线上。因此，在下载数据之前、我们首先要通过设置HF_ENDPOINT环境变量来将我们的下载地址切换到镜像站。"]}, {"cell_type": "markdown", "id": "7613406b-fe5d-49da-b61f-a7c08c800fd6", "metadata": {}, "source": ["```shell\n", "export HF_ENDPOINT=https://hf-mirror.com     #命令行、设置镜像站环境变量\n", "\n", "```"]}, {"cell_type": "markdown", "id": "7b83df86-e78f-4c36-b241-e8e99ccdd0c2", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/24.png)"]}, {"cell_type": "markdown", "id": "ba904f0a-eaa8-46ea-a345-04381e17ee4b", "metadata": {}, "source": ["然后我们有两种方式来拉取数据，一种是使用Huggingface提供的命令行工具`huggingface-cli`来拉取巨型数据、另一种则是可以自定义拉取脚本、结合`aria2c`和`git-lfs`这些工具来更智能、更安全地拉取数据。两种方法分别适用于不同的场景——\n", "\n", "| 特性                   | 方法 1：huggingface-cli                           | 方法 2：自定义脚本 + aria2c + git-lfs                       |\n", "|------------------------|--------------------------------------------------|-----------------------------------------------|\n", "| **适用场景**           | 中小型数据集、较小的数据集或一次性下载    | 大型数据集，特别是超大规模数据集、下载耗时长且需要高并发的场景|\n", "| **安装要求**           | 需使用 `huggingface-cli`，但是这一般是huggingface自带的工具           | 需要额外安装 `aria2c` 和 `git-lfs`           |\n", "| **下载速度**           | 受限于单线程速度，可能较慢                        | 支持多线程（如 `-x 16`），下载速度显著提升    |\n", "| **断点续传**           | 理论支持，但大部分实践中无法实现、容易导致重复下载        | 稳定支持，断点续传功能可靠                   |\n", "| **并发支持**           | 不支持                                           | 支持多线程并发下载，提升下载效率             |\n", "| **使用复杂度**         | 简单，官方 CLI 工具，命令较直观                  | 较复杂，**需自定义shell脚本并配置参数**               |\n", "| **镜像支持**           | 支持 `HF_ENDPOINT` 环境变量切换镜像               | 同样支持，可与镜像站搭配使用                 |\n", "| **稳定与适配**             | 对 Hugging Face 官方数据集的兼容性最好<br>但是网络中断或大文件下载时，容易失败                  | 对数据集兼容性较好<br>且在网络不稳定或大文件情况下更稳定             |"]}, {"cell_type": "markdown", "id": "2840a498-afba-48d9-a4ea-4d4d19f9d10e", "metadata": {}, "source": ["#### 2.2.1 huggingface-cli拉取SkyPile数据集"]}, {"cell_type": "markdown", "id": "b82ce3b0-bb9a-4491-b14d-bc3ef9fd9e9a", "metadata": {}, "source": ["如果你没有安装过huggingface-cli，则需要运行下面的命令行代码👇"]}, {"cell_type": "markdown", "id": "70e53599-a4d8-4e29-ad4e-6cbc3b522bd5", "metadata": {}, "source": ["```bash\n", "pkgx install huggingface-cli                  #安装huggingface-cli\n", "```"]}, {"cell_type": "markdown", "id": "41ac9346-5ae9-463e-99fc-0990d84b9878", "metadata": {}, "source": ["然后你就可以开始拉取数据了！下面帮助你一次性下载完整的数据的命令行代码、请在你的下载目录中准备好至少7-800G的存储空间。<font color=\"red\">**注意！在4MB/s的拉取速度下、下载全部SkyPile数据需要45个小时+7~800G内存，请谨慎运行下面的命令行。**"]}, {"cell_type": "markdown", "id": "25fb7cb5-611d-4d97-87fc-1f26e162eae5", "metadata": {}, "source": ["```bash\n", "huggingface-cli download Skywork/SkyPile-150B --repo-type dataset --resume-download --local-dir ~/autodl-tmp/MateConv/Data/SkyPile  --local-dir-use-symlinks False\n", "```"]}, {"cell_type": "markdown", "id": "ea4e4853-ba32-4bba-8c0b-27806358cf08", "metadata": {}, "source": ["在这段代码中——\n", "- **`huggingface-cli download`**  调用 Hugging Face 提供的命令行工具，执行下载操作。\n", "  \n", "- **`Skywork/SkyPile-150B`**  指定 Hugging Face 平台上的数据集名称，一般Huggingface上的数据集名称由两部分组成，一个是用户或组织的名称（例如Skywork），另一个是具体的数据集名称（SkyPile-150B）。你需要从Huggingface页面复制正确的名字。\n", "\n", "- **`--repo-type dataset`**  明确指定要下载的资源类型是 **数据集**（dataset），如果没有此参数，默认会尝试下载模型（model）。\n", "\n", "- **`--resume-download`**  启用断点续传功能。如果之前的下载因网络或其他问题中断，可以从中断处继续下载。但是该功能大部分时候不稳定。\n", "\n", "- **`--local-dir ~/autodl-tmp/MateConv/Data/SkyPile`**  指定数据下载后在本地存储的目录路径。在当前代码中，我们是数据将下载到先前建立好的 `~/autodl-tmp/MateConv/Data/SkyPile` 目录。\n", "\n", "- **`--local-dir-use-symlinks False`**  关闭符号链接（symlinks）。默认情况下，Hugging Face 会尝试使用符号链接以节省磁盘空间。此参数明确要求不使用符号链接，而是直接将数据下载到指定目录。并且，这一代码现在即将被弃用、因此你可以不写`--local-dir-use-symlinks False`这部分内容。如果你磁盘空间不足、则可以继续尝试开启，并无视掉所有会报的警告。"]}, {"cell_type": "markdown", "id": "afae5c77-755e-4b2e-8d5a-e900156ad851", "metadata": {}, "source": ["**如果你想测试一下拉取数据是否成功、则不必一次性拉取全部的数据、而是可以只拉取一部分数据**。我们只需要在原本的代码中间加上特定文件的名字即可👇<font color=\"red\">**对于SkyPile数据拉取一条数据大约需要10min，网速快的情况下可以在5mins内拉取完**——"]}, {"cell_type": "markdown", "id": "604f0b06-3ba8-431e-97b0-949532ecfcfd", "metadata": {}, "source": ["```bash\n", "huggingface-cli download Skywork/SkyPile-150B data/2020-40_zh_head_0000.jsonl --repo-type dataset --resume-download --local-dir ~/autodl-tmp/MateConv/Data/SkyPile  --local-dir-use-symlinks False\n", "```"]}, {"cell_type": "markdown", "id": "ae3a8bba-4a9b-4b74-9f38-87f4a289c0db", "metadata": {}, "source": ["在这段命令中，我们只需要在原本的数据集名称后面**增加一个空格、并街上我们要下载的单一文件的目录**，就可以下载单一文件了。同样的，如果我们要拉取多个文件，则可以按照**空格 + 要下载的文件目录**的方式排列相应的代码。"]}, {"cell_type": "markdown", "id": "72c7ad33-4666-470a-a43b-ba47ae807705", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/27.png)"]}, {"cell_type": "markdown", "id": "cbe65588-0b9b-4905-91f2-88051d5b3c8e", "metadata": {}, "source": ["数据的目录具体是什么？你可以在Huggingface页面的files页面下找到👇"]}, {"cell_type": "markdown", "id": "ee046492-6936-42dd-bb3f-bb59073a855a", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/28.png)\n", "\n", "找到文件目录后、点击相应的具体要下载的文件👇\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/29.png)"]}, {"cell_type": "markdown", "id": "bbb9dacd-5b7e-4873-a6ec-8d2461d6a2a6", "metadata": {}, "source": ["进行拉取时，你将看到这样的进度条👇"]}, {"cell_type": "markdown", "id": "7e22c504-5ec1-4468-a6d6-2a4b03390ecf", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/30.png)"]}, {"cell_type": "markdown", "id": "a04f5832-b81d-475f-91d7-b6b888039e43", "metadata": {}, "source": ["拉取完成之后，你将可以在你设置的线上目录下找到相应的数据👇"]}, {"cell_type": "markdown", "id": "fc75c60c-e049-48ef-a755-aa92fd32e047", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/31.png)"]}, {"cell_type": "markdown", "id": "46b73d8e-700e-4aad-ae16-c7aba01e9dd3", "metadata": {}, "source": ["#### 2.2.2 多线程并行拉取SkyPile数据集"]}, {"cell_type": "markdown", "id": "b576101d-e970-4c38-b12c-b86501e9de9c", "metadata": {}, "source": ["使用`huggingface-cli`拉取数据固然简单，但是最大的困境就在于拉取时间太长、极其容易断线、而且网速极其不稳定。因此针对大型数据、我们要使用Huggingface镜像站官方提供的hfd脚本、并结合`aria2c`和`git-lfs`来进行**多线程并行拉取**。"]}, {"cell_type": "markdown", "id": "9f41d958-d9ef-4edd-9608-ed09e9343d43", "metadata": {}, "source": ["`hfd.sh` 是由 Hugging Face 镜像站开发的一款专用下载工具，专为 Hugging Face 平台的模型和数据集提供稳定、高速的下载支持。它基于成熟的多线程下载工具 Aria2 构建，结合 Hugging Face 的资源特点，实现了多源分块下载和断点续传功能，能够有效解决大文件下载中的中断问题。通过 `hfd.sh`，用户可以灵活选择下载模型或数据集，并支持包括文件筛选（`--include` 和 `--exclude`）、多线程设置（`-x`）和并行任务配置（`-j`）在内的高度自定义参数，适用于高效管理 Hugging Face 平台上的资源下载需求。\n", "\n", "在使用hfd时、还需要单独安装支持库aria2c和git-lfs。其中 Aria2 是一个轻量级、多协议的、**支持多线程分块下载、文件分块下载、多元下载、多服务器下载的数据并行下载工具**，Aria2 可以通过命令行运行，适合集成到脚本中进行自动化下载，且其丰富的参数配置允许用户高度自定义，包括限速、连接数、分块大小等。此外，Aria2 的资源占用极低，即使在性能较低的设备上也能顺畅运行，因而成为开发者和自动化任务中常用的下载工具之一。\n", "\n", "而 Git LFS（Git Large File Storage）是 Git 的一种扩展工具，用于优化对大文件的版本控制和存储管理。**它通过将大文件（如音视频文件、图像、模型权重等）存储在外部专用的文件存储中，而不是直接保存在 Git 仓库中，从而避免仓库膨胀问题**。Git LFS 用指针文件替代大文件在版本控制系统中的实际存储，当需要使用这些大文件时，Git LFS 会在后台自动下载和管理它们，确保开发者的操作与普通 Git 工作流无缝兼容。其优点在于有效减少 Git 仓库的体积，提升拉取、克隆和推送等操作的效率，是处理数据密集型项目（如机器学习、音视频编辑等）时的重要工具。\n", "\n", "这三个工具结合、可以大幅度提升数据和模型的下载速度和稳定性、可以为开发者提供便捷且高效的下载体验。\n", "\n", "接下来我们来看一下首次使用hfd脚本的流程——"]}, {"cell_type": "markdown", "id": "4c81cfa5-ed30-4043-b8a5-baedec0605ea", "metadata": {}, "source": ["**下载hfd.sh文件**、并赋予直接修改命令的权限——"]}, {"cell_type": "markdown", "id": "f1a374dd-2efe-45ee-8286-72c68c969692", "metadata": {}, "source": ["```bash\n", "wget https://hf-mirror.com/hfd/hfd.sh    #下载hfd.sh文件到当前的目录\n", "chmod a+x hfd.sh                         #文件hfd.sh被赋予执行权限\n", "```"]}, {"cell_type": "markdown", "id": "7e34a4e7-cec1-40bc-86b5-5f5e3878ffa1", "metadata": {}, "source": ["**安装aria2c**——"]}, {"cell_type": "markdown", "id": "1bd2a5fb-fa11-4ab3-9869-74e9fd1e418e", "metadata": {}, "source": ["```bash\n", "sudo apt update                            #更新软件包索引\n", "sudo apt install aria2                     #安装aria2c\n", "```"]}, {"cell_type": "markdown", "id": "08ab6be2-2871-437c-9ebf-37bbb4df1252", "metadata": {}, "source": ["**安装完毕后、需要打印版本号以验证成功**——"]}, {"cell_type": "markdown", "id": "7cc730f4-74eb-4ad6-9714-20b1942b1847", "metadata": {}, "source": ["```bash\n", "aria2c --version                           #打印版本号\n", "```"]}, {"cell_type": "markdown", "id": "7cf5a2f8-cdc9-4e2b-9ccf-07fc7cb34110", "metadata": {}, "source": ["出版本号则说明安装成功👇"]}, {"cell_type": "markdown", "id": "8f290b4b-a23d-4000-b060-b1641cfb9cf0", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/34.png)"]}, {"cell_type": "markdown", "id": "7224a5f2-274e-4f6c-abac-2a27a82f4043", "metadata": {}, "source": ["**安装git-lfs**——"]}, {"cell_type": "markdown", "id": "c0305339-6bc5-486d-938d-40f87d1c8d95", "metadata": {}, "source": ["```bash\n", "sudo apt install git-lfs                   #安装git-lfs\n", "```"]}, {"cell_type": "markdown", "id": "364a78b0-8864-4eeb-b40b-83cc3235ecb2", "metadata": {}, "source": ["**安装完毕后、打印版本号以验证成功**——"]}, {"cell_type": "markdown", "id": "36a2dc47-d85c-44a6-9888-acbdc2318ef4", "metadata": {}, "source": ["```bash\n", "git lfs --version                         #打印版本号\n", "```"]}, {"cell_type": "markdown", "id": "85cbe858-8b0e-4e42-88fe-b9e1e8f5f1e3", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/33.png)"]}, {"cell_type": "markdown", "id": "080e65ba-402d-4a42-bf69-d48f6f11fcaf", "metadata": {}, "source": ["现在可以开始使用`hfd脚本` + `aria2c` + `git-lfs`来拉取数据了。<font color=\"red\">**注意！在4MB/s的拉取速度下、下载下面全部SkyPile数据需要6\\~7个小时+7~800G内存，请谨慎运行下面的命令行。**"]}, {"cell_type": "markdown", "id": "4a847bee-f372-4229-8680-f200de2c6f25", "metadata": {}, "source": ["```bash\n", "./hfd.sh Skywork/SkyPile-150B --dataset --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/SkyPile\n", "```"]}, {"cell_type": "markdown", "id": "c91bd4e9-e758-419a-bd4b-374b7aeb4921", "metadata": {}, "source": ["在这段代码中——\n", "\n", "- **`./hfd.sh`**：调用本地的 Bash 脚本 `hfd.sh`，用来从 Hugging Face 平台下载指定的资源。`hfd.sh` 是一个辅助工具，封装了 Hugging Face 的下载逻辑，并支持更多高级功能（如断点续传、多线程、多任务下载）。\n", "\n", "- **`Skywork/SkyPile-150B`**：指定 Hugging Face 数据集的 **完整名称**，由组织或用户名称（Skywork）和具体数据集名称（SkyPile-150B）组成。\n", "\n", "- **`--dataset`**：指明要下载的是一个 **数据集**（dataset），而不是模型（model）。\n", "\n", "- **`--tool aria2c`**：指定使用的下载工具，这里是 `aria2c`，相比于 `wget` 或其他工具，`aria2c` 支持分块下载和多线程，能显著提升下载速度。\n", "\n", "- **`-x 10`**：表示设置 **线程数** 为 10，线程数决定了单个文件的下载并发数，`aria2c` 会分块并同时下载多个部分。`-x` 的最大值通常受hfd脚本或工具限制，如果超过限制会报错👇例如设置了-x为16、\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/35.png)\n", "\n", "**`--local-dir ~/autodl-tmp/MateConv/Data/SkyPile`**：指定下载文件存储的本地路径。在这里，文件将存储在 `~/autodl-tmp/MateConv/Data/SkyPile` 目录中。如果目录不存在，脚本会尝试自动创建。"]}, {"cell_type": "markdown", "id": "d2cd0f52-04c0-4deb-8c44-69fd4100ea39", "metadata": {}, "source": ["同样的、**如果你想测试一下拉取数据是否成功、则不必一次性拉取全部的数据、而是可以只拉取一部分数据**。我们只需要在原本的代码中间加上`--include` + 特定文件的名字即可👇<font color=\"red\">**在4MB/s网络下、对于SkyPile数据拉取一条数据大约需要1mins，网速快的情况下可以在30s内拉取完**——"]}, {"cell_type": "markdown", "id": "a9d513de-0c4c-4b1d-8c68-7c40d63c63e7", "metadata": {}, "source": ["```bash\n", "./hfd.sh Skywork/SkyPile-150B --dataset --include data/2020-40_zh_head_0001.jsonl --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/SkyPile\n", "```"]}, {"cell_type": "markdown", "id": "b2092ec4-6ef2-46ee-a8c9-1f1852c3fdfd", "metadata": {}, "source": ["- **`--include data/2020-40_zh_head_0001.jsonl`**：表示只下载数据集中的指定文件路径。在 `Skywork/SkyPile-150B` 数据集中，这个路径为 `data/2020-40_zh_head_0000.jsonl`。该参数允许使用通配符（如 `*.jsonl`）来匹配多个文件。"]}, {"cell_type": "markdown", "id": "3037e16e-b4ff-4706-8a72-422729382bec", "metadata": {}, "source": ["> **`hfd脚本`**"]}, {"cell_type": "markdown", "id": "f9fb2a45-a46a-412c-9f6b-c546de04aa77", "metadata": {}, "source": ["作为Hugging Face 镜像站开发的专用下载工具、hfd脚本提供了巨大的可以修改的空间、几乎每个板块都有可以修改的流程，我们重点要关注的有——"]}, {"cell_type": "markdown", "id": "a8510e9e-46e2-49bd-be99-facc9cd55ae0", "metadata": {}, "source": ["- <font color=\"red\">**参数初始化Part**\n", "```bash\n", "REPO_ID=$1\n", "shift\n", "\n", "# Default values\n", "TOOL=\"aria2c\"\n", "THREADS=4\n", "CONCURRENT=5\n", "HF_ENDPOINT=${HF_ENDPOINT:-\"https://huggingface.co\"}\n", "INCLUDE_PATTERNS=()\n", "EXCLUDE_PATTERNS=()\n", "REVISION=\"main\"\n", "```\n", "\n", "- **解释**：**这段代码位于整个hfd脚本的开头**。\n", "  - `REPO_ID=$1`：读取第一个参数，作为 Hugging Face 的数据集或模型 ID。\n", "  - `shift`：移除第一个参数，便于解析后续参数。\n", "  - **默认参数**：\n", "    - `TOOL=\"aria2c\"`：默认使用 `aria2c` 作为下载工具。\n", "    - `THREADS=4`：每个文件下载的默认线程数为 4。\n", "    - `CONCURRENT=5`：同时并行下载的文件数为 5。\n", "    - `HF_ENDPOINT`：默认使用 Hugging Face 的主域名 `https://huggingface.co`，<font color=\"green\">**但我们之前已经通过修改环境变量将HF_ENDPOINT这个变量改变为Huggingface镜像站了。**</font>\n", "    - `REVISION=\"main\"`：默认下载主分支内容。\n", "\n", "---"]}, {"cell_type": "markdown", "id": "a81ae76c-900c-4118-bef8-ff87fbba3473", "metadata": {}, "source": ["- <font color=\"red\">**参数验证和解析Part**\n", "```bash\n", "validate_number() {\n", "    [[ \"$2\" =~ ^[1-9][0-9]*$ && \"$2\" -le \"$3\" ]] || { printf \"${RED}[Error] $1 must be 1-$3${NC}\\n\"; exit 1; }\n", "}\n", "\n", "while [[ $# -gt 0 ]]; do\n", "    case $1 in\n", "        --include) ... ;;\n", "        --exclude) ... ;;\n", "        --tool) ... ;;\n", "        -x) validate_number \"threads (-x)\" \"$2\" 10; THREADS=\"$2\"; shift 2 ;; \n", "        -j) validate_number \"concurrent downloads (-j)\" \"$2\" 10; CONCURRENT=\"$2\"; shift 2 ;; \n", "        --dataset) DATASET=1; shift ;;\n", "        --local-dir) LOCAL_DIR=\"$2\"; shift 2 ;;\n", "        --revision) REVISION=\"$2\"; shift 2 ;;\n", "        *) display_help ;;\n", "    esac\n", "done\n", "```\n", "\n", "- **解释**：这段代码位于整个脚本的前半段、位于参数初始化part的后面。<br><br>\n", "  - `validate_number`：验证参数是否为正整数，且不超过指定最大值。例如：<br><br>\n", "    - `-x`：线程数（最大 10）。将数据分块、分到不同线程上进行下载。<font color=\"green\">**如果你想要修改最大线程数、可以将`-x) validate_number \"threads (-x)\" \"$2\" 10; THREADS=\"$2\"; shift 2 ;;`这行中的10进行修改**。</font>\n", "    > - 一般来说、最大线程数会受到Huggingface等公共资源、以及CPU性能的限制，通常来说我们会将线程数设置在4-8之间（设备比较陈旧、或者设备有限就设置为4）、但最大可以设置到16。<br>\n", "    - `-j`：并行下载数（最大 10）。同步下载多个文件。<font color=\"green\">**如果想要修改并行下载数，可以将`-j) validate_number \"concurrent downloads (-j)\" \"$2\" 10; CONCURRENT=\"$2\"; shift 2 ;;`这行中的10进行修改**。</font>\n", "    > - 如果单个文件较大、增加线程数会比较明智、如果是下载多个小文件、增加并行下载数才可以更快完成。<br>\n", "  - `case $1`：解析传入参数，支持以下选项：\n", "    - `--include`：指定下载文件的匹配规则。\n", "    - `--exclude`：排除匹配的文件。\n", "    - `--tool`：选择下载工具（`aria2c` 或 `wget`）。\n", "    - `--local-dir`：指定文件存储路径。\n", "    - `--revision`：选择下载的分支版本。\n", "<br><br>\n", "\n", "**该如何选择具体的线程？**\n", "\n", "| 场景                          | 推荐 `-x`（线程数） | 推荐 `-j`（并行数） |\n", "|-------------------------------|---------------------|---------------------|\n", "| 普通个人电脑（带宽 ≤ 50Mbps）  | 4-6                 | 2-4                 |\n", "| 高性能电脑（带宽 ≥ 100Mbps）   | 8-16                | 5-8                 |\n", "| 服务器或高速网络（带宽 ≥ 1Gbps）| 16-32               | 10-16               |\n", "| 小文件为主                   | 2-4                 | 8-10                |\n", "| 大文件为主                   | 8-16                | 2-5                 |\n", "\n", "需要注意的是、`-x`（线程数）和 `-j`（并行下载数）会同时消耗资源，不建议都设置为过高值。如果 `-x=16`，建议 `-j` 限制在 4-5 左右，反之亦然。大多数情况下、`-x 8`，`-j 4` 是合理起点。高性能设备则可以适当提高到 `-x 16`，`-j 10`。\n", "<br><br>\n", "**你可以使用aria2c测试线程的情况**、测试后根据设备和网络的情况进一步优化，找到适合你的配置。\n", "\n", "> 单文件测试线程数（`-x`）、下载大文件时测试：\n", "  ```bash\n", "  aria2c -x 4 https://example.com/large-file.zip\n", "  ```\n", "  - 尝试增加 `-x` 的值，如 8、16，观察是否有明显提速。\n", "\n", "> 多文件测试并行数（`-j`）、下载多个小文件时测试：\n", "  ```bash\n", "  aria2c -x 4 -j 8 -i file-list.txt\n", "  ```\n", "  - 增加 `-j` 的值，如 10、16，观察下载时间是否减少。\n", "<br><br>\n", "\n", "**除此之外、你还可以通过代码来查看你CPU支持的最大线程数——**\n", "\n", "```bash\n", "grep -c processor /proc/cpuinfo          #显示现在CPU上的总线程数\n", "```\n", "\n", "输出——\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/36.png)\n", "\n", "虽然CPU支持的最大线程是128、但是实际上Huggingface mirror镜像站最大只能支持16。\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/37.png)\n", "\n", "---"]}, {"cell_type": "markdown", "id": "3458c664-9a9b-488b-a995-171bea118db3", "metadata": {}, "source": ["- **文件过滤Part**\n", "```bash\n", "INCLUDE_REGEX=$(printf '%s\\n' \"${INCLUDE_PATTERNS[@]}\" | sed 's/\\./\\\\./g; s/\\*/.*/g' | paste -sd '|' -)\n", "EXCLUDE_REGEX=$(printf '%s\\n' \"${EXCLUDE_PATTERNS[@]}\" | sed 's/\\./\\\\./g; s/\\*/.*/g' | paste -sd '|' -)\n", "```\n", "\n", "- **解释**：\n", "  - 将 `--include` 和 `--exclude` 的文件模式转换为正则表达式，用于筛选需要下载的文件。这样可以实现灵活的文件选择，避免下载整个数据集或模型，提高效率。\n", "\n", "---\n", "\n", "- **aria2c文件下载Part**\n", "```bash\n", "aria2c --console-log-level=error --file-allocation=none -x \"$THREADS\" -j \"$CONCURRENT\" -s \"$THREADS\" -k 1M -c -i \"$fileslist_file\"\n", "```\n", "\n", "- **解释**：\n", "  - 使用 `aria2c` 下载文件：\n", "    - `-x \"$THREADS\"`：每个文件的下载线程数、和之前代码中设置的一样。\n", "    - `-j \"$CONCURRENT\"`：并行下载的文件数、和之前代码中设置的一样。\n", "    - `-s \"$THREADS\"`：分块下载数、和之前代码中设置的一样。\n", "    - `-k 1M`：设置分块大小为 1MB，如果你的带宽较大、使用更大的分块（比如4M或者8M）能够更充分地发挥网络性能，如果你的网络不稳定你可以设置到1MB或者512kb。\n", "    - `-i \"$fileslist_file\"`：指定文件列表作为输入。\n", "\n", "---\n", "\n", "这段脚本支持文件筛选、授权访问、多线程并行下载等功能，适合高效下载 Hugging Face 数据集或模型。你可以通过调整 `-x`、`-j` 等参数优化下载速度，也可以通过 `--include` 精确控制下载的文件。"]}, {"cell_type": "markdown", "id": "61a73949-5839-48ad-9766-41d1dbbed6e9", "metadata": {}, "source": ["#### 2.2.3 其他Huggingface数据集的拉取"]}, {"cell_type": "markdown", "id": "2c2c1e47-f230-4725-a06b-c4982d2a522d", "metadata": {}, "source": ["| 数据集编号 | 数据集名称                 | 数据属性（中文/英文/代码）                 | 数据量级           | 存储格式            | 是否经过数据清洗 |\n", "|---|----------------------------|-------------------------------------------|--------------------|---------------------|------------------|\n", "| 1| Skywork-SkyPile 150B       | 中文文本                                      | 620GB             | JSONL 短文本        | 是               |\n", "| 2| wanjuan1.0-nlp-CN             | 中文文本                                 | 580GB | JSONL 短文本压缩  | 是               |\n", "| 3| WuDaoCorporaText | 中文文本                                      | 200GB  | JSONL 短文本        | 是               |\n", "| 4| chinese-fineweb-edu-v2 | 75%中文文本，25%英文文本                                      | 670GB  | Parquet        |   是            |\n", "| 5| Wikipedia-CN | 中文文本                                      | 1.1GB  | JSON        |   是            |\n", "| 6| BaiduBaike-5.63M | 中文文本                                      | 17GB  | JSON        |   是            |\n", "| 7| wangrui6/Zhihu-KOL | 中文问答对                                      | 1.5 GB  | parquet        |   是            |\n", "| 8| wanjuan1.0-nlp-EN             | 英文文本                                 | 440GB | JSONL 短文本压缩  | 是               |\n", "| 9| SlimPajama-627B            | 英文为主的混合语言文本                                      | 1TB               | JSONL 短文本压缩    | 是               |\n", "| 10| Starcoder                  | 代码             | 100+GB/768G             | Parquet            | 是               |\n", "| 11| TheStackDedup                 | 代码             | 700+GB/3TB             | Parquet            | 是               |"]}, {"cell_type": "markdown", "id": "79cafe8a-87e1-4607-acc9-f5fb94e53f9b", "metadata": {}, "source": ["剩下Huggingface数据集、包括opencsg/chinese-fineweb-edu-v2、cerebras/SlimPajama-627B、wangrui6/Zhihu-KOL、xuqinyang/BaiduBaike-5.63M以及pleisto/wikipedia-cn-20230720-filtered数据集，都可以通过和SkyPile类似的方式拉取。具体流程如下👇"]}, {"cell_type": "markdown", "id": "6f93da28-53d1-4c3d-8a0e-3b1f02d51c5a", "metadata": {}, "source": ["1. **设置Huggingface镜像站变量、并确保下载好hfd、设置好目录文件**"]}, {"cell_type": "markdown", "id": "2fa36968-4f6e-40a5-85ac-20cea6d650dd", "metadata": {}, "source": ["```shell\n", "export HF_ENDPOINT=https://hf-mirror.com            #命令行、设置镜像站环境变量\n", "\n", "wget https://hf-mirror.com/hfd/hfd.sh               #下载hfd.sh文件到当前的目录\n", "chmod a+x hfd.sh                                    #文件hfd.sh被赋予执行权限\n", "\n", "sudo apt update                                     #更新软件包索引\n", "sudo apt install aria2                              #安装aria2c\n", "\n", "sudo apt install git-lfs                            #安装git-lfs\n", "\n", "#设置目录文件\n", "mkdir -p ~/autodl-tmp/MateConv/Data/Fineweb         \n", "mkdir -p ~/autodl-tmp/MateConv/Data/wikicn       \n", "mkdir -p ~/autodl-tmp/MateConv/Data/baidubaike       \n", "mkdir -p ~/autodl-tmp/MateConv/Data/zhihu   \n", "mkdir -p ~/autodl-tmp/MateConv/Data/slimpajama       \n", "```"]}, {"cell_type": "markdown", "id": "680b8580-fef2-40c6-b009-027566fcb704", "metadata": {}, "source": ["2. **`hfd脚本` + `aria2c` + `git-lfs`多线程并行拉取所有数据**"]}, {"cell_type": "markdown", "id": "3de9a7da-41bf-4bf0-b383-1b7986d13221", "metadata": {}, "source": ["- Fineweb"]}, {"cell_type": "markdown", "id": "885b4377-b704-4d20-a31a-b1bf0f87cf75", "metadata": {}, "source": ["一次性拉取所有数据的命令👇<font color=\"red\">**注意！在4MB/s、10个线程并行拉取情况下、下载全部fineweb数据需要6\\~7小时时间+7\\~800G内存，请谨慎运行下面的命令行。**"]}, {"cell_type": "markdown", "id": "5a504b32-6faf-4375-b326-458d13a743c3", "metadata": {}, "source": ["```bash\n", "./hfd.sh opencsg/chinese-fineweb-edu-v2 --dataset --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/Fineweb\n", "```"]}, {"cell_type": "markdown", "id": "6ab1d260-dc3e-43d6-89e5-28744ada3459", "metadata": {}, "source": ["单独拉取一个文件的命令👇在10个线程并行拉取情况下、要大约1~2min左右的时间——"]}, {"cell_type": "markdown", "id": "a2e7cf13-f7de-431d-8a6c-47f29a8414e6", "metadata": {}, "source": ["```bash\n", "./hfd.sh opencsg/chinese-fineweb-edu-v2 --dataset --include data/00000.parquet --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/Fineweb\n", "```"]}, {"cell_type": "markdown", "id": "3cc7d497-e7ae-4ca7-949f-12bdf1301b4f", "metadata": {}, "source": ["- <PERSON><PERSON><PERSON><PERSON>"]}, {"cell_type": "markdown", "id": "1b961e32-468d-4b71-8fbc-030408e8502a", "metadata": {}, "source": ["一次性拉取所有数据的命令👇<font color=\"red\">**注意！在4MB/s、10个线程并行拉取情况下、下载全部SlimPajama数据需要个8\\~9小时+900G以上内存，请谨慎运行下面的命令行。**"]}, {"cell_type": "markdown", "id": "e37a5246-234d-4e25-9bfe-d9f8d94768c9", "metadata": {}, "source": ["```bash\n", "./hfd.sh cerebras/SlimPajama-627B --dataset --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/slimpajama\n", "```"]}, {"cell_type": "markdown", "id": "095944cc-1cc9-4455-8f11-0e3a91b6ffc4", "metadata": {}, "source": ["单独拉取一个文件的命令👇zst文件非常小，在10个线程并行拉取情况下、要大约3s左右的时间——"]}, {"cell_type": "markdown", "id": "b385a33d-9514-47ad-a1ce-eecb97656bdf", "metadata": {}, "source": ["```bash\n", "./hfd.sh cerebras/SlimPajama-627B --dataset --include train/chunk1/example_train_0.jsonl.zst --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/slimpajama\n", "```"]}, {"cell_type": "markdown", "id": "6e8df058-8fd8-48fb-8f91-633e5b36113f", "metadata": {}, "source": ["- 小型数据群"]}, {"cell_type": "markdown", "id": "0ed8a3ce-894f-4782-8b1b-6ddd772635ac", "metadata": {}, "source": ["其中，zhihu数据大约1.5G、wiki数据大约1G、下载时间都很短、百度百科数据大约17G，下载时间会略长一些，但在多线程并行下速度都不是问题。"]}, {"cell_type": "markdown", "id": "c2ef776d-a635-492a-9a4e-af5b2f2f2c6f", "metadata": {}, "source": ["```bash\n", "#zhihu\n", "./hfd.sh wangrui6/Zhihu-KOL --dataset --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/zhihu\n", "\n", "#Baidubaike\n", "./hfd.sh xuqinyang/BaiduBaike-5.63M --dataset --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/baidubaike\n", "\n", "#Wiki\n", "./hfd.sh pleisto/wikipedia-cn-20230720-filtered --dataset --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/wikicn\n", "\n", "```"]}, {"cell_type": "markdown", "id": "40b6f047-eaa4-4407-b9d1-6040e1ccf2ce", "metadata": {}, "source": ["下载好之后，可以在相应的目录下找到你的数据👇"]}, {"cell_type": "markdown", "id": "b601154f-c589-42b2-83d0-3785007f07cc", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/50.png)"]}, {"cell_type": "markdown", "id": "8962fdef-c701-438b-9a8d-3b17082bd7d7", "metadata": {}, "source": ["#### 2.2.4 万卷中英文数据集的拉取"]}, {"cell_type": "markdown", "id": "95c6685d-96f9-45d4-abc8-27c90d00d8ef", "metadata": {}, "source": ["万卷数据集来自Openxlab实验室，因此需要走Openxlab实验室的流程进行下载。\n", "\n", "**1. 在Opendatalab首页进行注册、获取自己的Access Key/Secret Access Key**\n", "\n", "在这里进行手机号注册 → https://opendatalab.org.cn/OpenDataLab/WanJuan1_dot_0\n", "\n", "注册完毕后到这里领取自己的AK/SK → https://sso.openxlab.org.cn/usercenter"]}, {"cell_type": "markdown", "id": "04e40330-f42a-4c61-bc28-e0c3dcee89ae", "metadata": {}, "source": ["> step1——\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/38.png)"]}, {"cell_type": "markdown", "id": "0f14cfc5-11d2-43c3-ba43-d8f5c1b9fa72", "metadata": {}, "source": ["> step 2——"]}, {"cell_type": "markdown", "id": "5575a860-ed94-4324-ae71-9765682bdcd7", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/39.png)"]}, {"cell_type": "markdown", "id": "117a7e45-f09b-46f6-a92a-e0e4e395e78a", "metadata": {}, "source": ["2. **安装openxlab、进行openxlab登录**"]}, {"cell_type": "markdown", "id": "baf76bd3-bede-4db3-b280-b70fa5b49337", "metadata": {}, "source": ["> step 1——"]}, {"cell_type": "markdown", "id": "b8bdee15-85b2-43d2-9bca-01bb8a0b0ead", "metadata": {}, "source": ["```bash\n", "  pip install openxlab #安装\n", "  pip install -U openxlab #版本升级\n", "```"]}, {"cell_type": "markdown", "id": "3ac31e90-8a40-497d-955f-029a0d64ba3b", "metadata": {}, "source": ["> step 2——"]}, {"cell_type": "markdown", "id": "83c07761-34be-48f2-bbd6-23f90da1d9f1", "metadata": {}, "source": ["```bash\n", "  openxlab login\n", "```"]}, {"cell_type": "markdown", "id": "40999ac4-027d-402b-b522-256a876a2b87", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/40.png)"]}, {"cell_type": "markdown", "id": "33c75087-3134-49c4-ae3b-a56985743907", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/42.png)"]}, {"cell_type": "markdown", "id": "06de2afd-6d83-46e4-95a1-c50a02bd5f18", "metadata": {}, "source": ["3. **设置数据下载目录、查看数据集线上详情**"]}, {"cell_type": "markdown", "id": "82edfecf-ccd9-4fa7-bd53-a3c63aad69e4", "metadata": {}, "source": ["```bash\n", "mkdir -p ~/autodl-tmp/MateConv/Data/wanjuan\n", "\n", "openxlab dataset info --dataset-repo OpenDataLab/WanJuan1_dot_0 #数据集信息及文件列表查看\n", "```"]}, {"cell_type": "markdown", "id": "f40d1955-3b4f-4e2d-b055-6cb377c28242", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/43.png)"]}, {"cell_type": "markdown", "id": "8bdea28e-e6e1-42e0-b800-37b8e3706049", "metadata": {}, "source": ["4. **数据下载**"]}, {"cell_type": "markdown", "id": "9c8135e5-10af-454a-8bc0-c86371afadb1", "metadata": {}, "source": ["<font color=\"red\">**注意！wanjuan数据下载是自动多线程并行的、然而在平均网速32MB/s情况下、下载中文+英文nlp方向的wanjuan数据需要12小时+1.5T以上内存，请谨慎运行下面的命令行。**"]}, {"cell_type": "markdown", "id": "64304c99-3c5e-4b6e-80a7-3ae37068b2c4", "metadata": {}, "source": ["```bash\n", "#get命令下载整个数据集\n", "openxlab dataset get --dataset-repo OpenDataLab/WanJuan1_dot_0 --source-path /raw/nlp --target-path ~/autodl-tmp/MateConv/Data/wanjuan\n", "```"]}, {"cell_type": "markdown", "id": "fc062a65-7152-43dc-ad02-fcbb60f7ceb6", "metadata": {}, "source": ["<font color=\"red\">**注意！在平均网速32MB/s情况下、下载1个4G左右的压缩文件大约需要2分钟时间，你依然可以用下面的代码来测试、流程通了之后再执行完整命令。**"]}, {"cell_type": "markdown", "id": "d4f5f6d4-18ba-4d44-a1a3-0b5824995faf", "metadata": {}, "source": ["```bash\n", "#download命令特定数据集文件下载\n", "openxlab dataset download --dataset-repo OpenDataLab/WanJuan1_dot_0 --source-path /raw/nlp/CN/ChinaNews-cn/part-006853-a894b46e.jsonl.tar.gz --target-path ~/autodl-tmp/MateConv/Data/wanjuan \n", "```"]}, {"cell_type": "markdown", "id": "de88d31e-485f-47db-a464-f67bdd7177c4", "metadata": {}, "source": ["- **`openxlab dataset download`**：调用 OpenXLab 提供的命令行工具，用于管理和操作 OpenXLab 平台上的数据。**`dataset download`**：表示操作类型为下载数据集文件。\n", "\n", "- **`--dataset-repo OpenDataLab/WanJuan1_dot_0`**：**`--dataset-repo`**：指定目标数据集的仓库名称，和huggingface一样，会包括数据集所属的组织或用户名、同时还会包括数据集本身的名称。你可以在这里找到数据集的仓库全名 ↓\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/45.png)\n", "\n", "- **`--source-path /raw/nlp/CN/ChinaNews-cn/part-006853-a894b46e.jsonl.tar.gz`**：**`--source-path`**：指定要下载的数据集中的具体文件路径，你可以在这里找到相应的路径。你可以指向一个文件夹、也可以指向具体的数据集文件 ↓\n", "       \n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/46.png)\n", "\n", "- **`--target-path ~/autodl-tmp/MateConv/Data/wanjuan`**：指定下载后文件的存储位置。"]}, {"cell_type": "markdown", "id": "901f2466-3378-4861-8f61-8195f0c8d5bc", "metadata": {}, "source": ["开始下载之后，会有自动打印和监控流程——"]}, {"cell_type": "markdown", "id": "982cbd16-c46f-4bfb-b225-41d737556c96", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/44.png)"]}, {"cell_type": "markdown", "id": "4a5a7048-b302-4e8a-86fa-ff81b64bedc7", "metadata": {}, "source": ["下载完毕后，你将可以在finalshell的目录中查看到文件👇"]}, {"cell_type": "markdown", "id": "c3908d39-1367-400e-97d8-5bcc32f98156", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/47.png)"]}, {"cell_type": "markdown", "id": "345318eb-23c5-4726-a4f8-83488cb51b94", "metadata": {}, "source": ["下载完成后、需要进行解压、先单独设置解压后的文件目录——"]}, {"cell_type": "markdown", "id": "5e09df9a-df17-40e4-b442-110aa150f9f7", "metadata": {}, "source": ["```bash\n", "mkdir -p ~/autodl-tmp/MateConv/Data/wanjuan/clean            #单独设置解压后的文件目录\n", "```"]}, {"cell_type": "markdown", "id": "a74a938f-75bd-4bfc-9bef-56cf67a9590d", "metadata": {}, "source": ["wanjuan官方提供了解压用的py脚本，可以直接上传到服务器进行使用👇也可直接在线上jupyter进行使用。"]}, {"cell_type": "markdown", "id": "a426438d-7d02-404f-bb70-9c6583717b4a", "metadata": {}, "source": ["```python\n", "import glob \n", "import os\n", "import time\n", "if __name__ == \"__main__\":\n", "    dir_list = glob.glob(\"/data1/step0_rawdata/wanjuan/**/*.jsonl.tar.gz\", recursive=True)\n", "    target_root_dir = \"~/autodl-tmp/MateConv/Data/wanjuan/clean\"\n", "    print(dir_list)\n", "    print(len(dir_list))\n", "    for coutner, file_dir in enumerate(dir_list):\n", "        print(file_dir)\n", "        t0 = time.time()\n", "        save_need_name = file_dir.split(\"/\")[-3:]\n", "        tmp_name = save_need_name[-1]\n", "        # 解压后的文件名称\n", "        tmp_name = tmp_name.replace(\".jsonl.tar.gz\", \".jsonl\")\n", "        tmp_name = os.path.join(target_root_dir, tmp_name)\n", "        # 目标名称带着文件夹名\n", "        save_need_name[-1] = save_need_name[-1].split(\".\")[0]\n", "        target_file_name = \"_\".join(save_need_name)\n", "        target_file_name += \".jsonl\"\n", "        target_file_name = os.path.join(target_root_dir, target_file_name)\n", "        print(\"old_name:\", tmp_name)\n", "        print(\"new name:\",target_file_name)\n", "        if os.path.exists(target_file_name):\n", "            print(f\"have: {target_file_name}\")\n", "        cmd = f\"tar -zxvf {file_dir} -C {target_root_dir}\"\n", "        os.system(cmd)\n", "        os.system(f\"mv {tmp_name} {target_file_name}\")\n", "        print(coutner, time.time()-t0)\n", "    \n", "    en_dir = os.path.join(target_root_dir, \"wanjuan_en\")\n", "    cn_dir = os.path.join(target_root_dir, \"wanjuan_zh\")\n", "    os.mkdir(en_dir)\n", "    os.mkdir(cn_dir)\n", "    mv_cmd1 = f\"mv {target_root_dir}/EN*.jsonl {en_dir}\"\n", "    os.system(mv_cmd1)\n", "    mv_cmd2 = f\"mv {target_root_dir}/CN*.jsonl {cn_dir}\"\n", "    os.system(mv_cmd2)\n", "\n", "```"]}, {"cell_type": "markdown", "id": "781345b3-4c2c-41f4-9bb0-96579489a275", "metadata": {}, "source": ["同时需要注意的是，<font color=\"green\">**由于wanjuan数据集在下载的时候需要登录到Openxlab，因此在开始下载其他数据集之前、可以尝试重开Bash、并需要重新设置其他数据集所需要的下载环境变量、镜像站等信息**。"]}, {"cell_type": "markdown", "id": "58efd44f-c96c-4b08-92d7-2b4d6a462d24", "metadata": {}, "source": ["#### 2.2.5 Wu<PERSON><PERSON>数据集的拉取"]}, {"cell_type": "markdown", "id": "8a8d511b-d8b2-4b42-be51-3392bdd58344", "metadata": {}, "source": ["**WuDao数据集不支持命令行方式下载、因此只能从网页下载并上传到线上服务器**。幸运的是WuDao数据集本身不是很大、只需要关注中间断线情况、耐心下载即可。"]}, {"cell_type": "markdown", "id": "f1455783-09dc-470a-99c5-2888266e7e8a", "metadata": {}, "source": ["1. **进入页面、微信登录注册**：https://data.baai.ac.cn/details/WuDaoCorporaText"]}, {"cell_type": "markdown", "id": "dbe151e5-d934-46ad-8aad-e524b43e8f04", "metadata": {}, "source": ["2. **网页进行下载、也可以使用迅雷等工具👇**"]}, {"cell_type": "markdown", "id": "2c9945b5-62f3-4810-905f-05a237a8b335", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/48.png)"]}, {"cell_type": "markdown", "id": "39491807-bd76-48b4-b09d-0e3a96008101", "metadata": {}, "source": ["3. **上传至服务器👇由于autodl上传有限速，因此推荐和阿里云盘结合使用、可大幅提升上传速度**"]}, {"cell_type": "markdown", "id": "dc02ca86-15df-494c-872b-43fd01194cdf", "metadata": {}, "source": ["具体autodl链接阿里云盘文件参考👉https://www.autodl.com/docs/netdisk/"]}, {"cell_type": "markdown", "id": "79b855f7-9267-4261-83b6-15720687a9f5", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/49.png)"]}, {"cell_type": "markdown", "id": "3fe96035-b7f6-47e9-940a-60b8d6db179b", "metadata": {}, "source": ["#### 2.2.6 代码类数据集的拉取"]}, {"cell_type": "markdown", "id": "af81c8df-1f12-4f0e-b973-998600ce2051", "metadata": {}, "source": ["| 数据集编号 | 数据集名称                 | 数据属性（中文/英文/代码）                 | 数据量级           | 存储格式            | 是否经过数据清洗 |\n", "|---|----------------------------|-------------------------------------------|--------------------|---------------------|------------------|\n", "| 1| Skywork-SkyPile 150B       | 中文文本                                      | 620GB             | JSONL 短文本        | 是               |\n", "| 2| wanjuan1.0-nlp-CN             | 中文文本                                 | 580GB | JSONL 短文本压缩  | 是               |\n", "| 3| WuDaoCorporaText | 中文文本                                      | 200GB  | JSONL 短文本        | 是               |\n", "| 4| chinese-fineweb-edu-v2 | 75%中文文本，25%英文文本                                      | 670GB  | Parquet        |   是            |\n", "| 5| Wikipedia-CN | 中文文本                                      | 1.1GB  | JSON        |   是            |\n", "| 6| BaiduBaike-5.63M | 中文文本                                      | 17GB  | JSON        |   是            |\n", "| 7| wangrui6/Zhihu-KOL | 中文问答对                                      | 1.5 GB  | parquet        |   是            |\n", "| 8| wanjuan1.0-nlp-EN             | 英文文本                                 | 440GB | JSONL 短文本压缩  | 是               |\n", "| 9| SlimPajama-627B            | 英文为主的混合语言文本                                      | 1TB               | JSONL 短文本压缩    | 是               |\n", "| 10| Starcoder                  | 代码             | 100+GB/768G             | Parquet            | 是               |\n", "| 11| TheStackDedup                 | 代码             | 700+GB/3TB             | Parquet            | 是               |"]}, {"cell_type": "markdown", "id": "8f339b0a-f236-44ff-b9d8-300043dcf081", "metadata": {}, "source": ["- **权限申请**"]}, {"cell_type": "markdown", "id": "45e23532-8302-4449-bc09-b555ce169b57", "metadata": {}, "source": ["代码数据一般都是量非常大的数据、其特点不仅在于数据量大、还需要我们规定特定的编程语言进行下载，因此CLI代码会与传统文字数据集略有差别。首先，代码数据集的下载会需要权限、因此我们要先申请相应的access token——"]}, {"cell_type": "markdown", "id": "ad1d74fa-3a7e-45a1-a435-571bf052359c", "metadata": {}, "source": ["1. **挂上梯子、注册Huggingface账号**👉https://huggingface.co/\n", "\n", "2. **登录后、在个人profile页面找到自己的username**\n", "\n", "> - 点击右上角——\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/51.png)\n", "\n", "> - 复制username——\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/52.png)\n", "\n", "3. **登录后、在这个页面下建立属于自己的AccessToken**👉https://huggingface.co/settings/tokens"]}, {"cell_type": "markdown", "id": "c0324378-5a03-4773-9d7c-22d1a765f32f", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/53.png)"]}, {"cell_type": "markdown", "id": "91fbf5af-4f1c-4544-8080-77fa3b043c39", "metadata": {}, "source": ["4. **在下面的页面中、建立token名字、并且为所需要的数据集申请权限**"]}, {"cell_type": "markdown", "id": "d48ef298-37ef-4cf0-b24d-d2fc247a730a", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/54_.png)"]}, {"cell_type": "markdown", "id": "53d89b45-7423-4c2e-bb37-ab4e6b537c2d", "metadata": {}, "source": ["点击create token、获得token后直接复制，**你将不会有第二次复制token的机会、因此务必要在这个时候复制token**。"]}, {"cell_type": "markdown", "id": "19dcb5ee-bf3b-4874-be66-4855a3bae64f", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/56.png)"]}, {"cell_type": "markdown", "id": "7ffd5bcc-4e02-4580-86ee-05ee8ec0325e", "metadata": {}, "source": ["token建好后，可以在token页面中看到、但此时你只能看到你的token名称、将不能再复制具体的access token码了。"]}, {"cell_type": "markdown", "id": "b02c8ee6-b3fd-4a42-ac95-ab6b46f42244", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/57.png)"]}, {"cell_type": "markdown", "id": "7be9e2e0-8512-4556-ac77-7e33c0a6541f", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "44f170d6-8e27-4cfb-94d2-e60b4ad1c1e1", "metadata": {}, "source": ["- **jq工具配置**"]}, {"cell_type": "markdown", "id": "78c5d4cb-038a-458d-83e6-e027df78420a", "metadata": {}, "source": ["与文字数据不同的是、代码数据本身的结构比较复杂、元数据的JSON文件可能嵌套了更多层次结构（例如文件分组、标签、语言分类等），因此解析起来会更为缓慢。为此，我们要下载`jq`工具来帮助我们在下载过程中更高效地解析代码数据。\n", "\n", "`jq` 是一个专为处理 JSON 数据设计的轻量级命令行工具，能够高效解析、查询、修改和格式化 JSON 文件或数据流。它的语法灵活，支持强大的过滤和数据操作功能，类似于 JSON 格式的 `sed` 或 `awk`。`jq` 的优势在于其速度快、占用资源少，并能以简洁的方式处理复杂的 JSON 结构，而无需编写冗长的代码。安装 `jq` 的主要原因是许多现代工具（如 `hfd.sh`）在处理大型 JSON 文件时可以借助它快速解析元数据，显著提高文件筛选和匹配的效率，从而避免使用较慢的替代方案（如 `grep` 或 `awk`）。对于需要处理 JSON 数据的开发者和运维人员来说，`jq` 是不可或缺的工具之一。\n", "\n", "下面是配置`jq`库的命令行代码👇"]}, {"cell_type": "markdown", "id": "020faa86-2959-4de2-b41a-e323303aeac0", "metadata": {}, "source": ["```bash\n", "sudo apt update                                  #更新软件包索引\n", "sudo apt install jq -y                           #安装jq\n", "\n", "jq --version                                     #检查版本号\n", "```"]}, {"cell_type": "markdown", "id": "921c96b5-d995-4d09-ba3f-c5b3686af598", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/58.png)"]}, {"cell_type": "markdown", "id": "c259f861-06e0-46c1-b980-d73c03637d44", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "629b0af8-1725-4ea2-89f5-fd632f5d4013", "metadata": {}, "source": ["- **数据拉取**"]}, {"cell_type": "markdown", "id": "1a069b93-e0f9-4c90-ad72-202679e6e982", "metadata": {}, "source": ["配置好jq工具后，可以开始进行正式的数据下载了！首先还是要设置好相应的目录——"]}, {"cell_type": "markdown", "id": "7aed778c-13a8-4afb-8cd3-a5b416398847", "metadata": {}, "source": ["1. **设置Huggingface镜像站变量、并确保下载好hfd、设置好目录文件**"]}, {"cell_type": "markdown", "id": "13e54bbb-61c8-4c12-adea-b07bb999877e", "metadata": {}, "source": ["```shell\n", "export HF_ENDPOINT=https://hf-mirror.com            #命令行、设置镜像站环境变量\n", "\n", "wget https://hf-mirror.com/hfd/hfd.sh               #下载hfd.sh文件到当前的目录\n", "chmod a+x hfd.sh                                    #文件hfd.sh被赋予执行权限\n", "\n", "sudo apt update                                     #更新软件包索引\n", "sudo apt install aria2                              #安装aria2c\n", "\n", "sudo apt install git-lfs                            #安装git-lfs\n", "\n", "#设置目录文件\n", "mkdir -p ~/autodl-tmp/MateConv/Data/Starcoder   \n", "mkdir -p ~/autodl-tmp/MateConv/Data/TheStackDedup           \n", "```"]}, {"cell_type": "markdown", "id": "767cac80-1d37-4006-b7b3-e6fac55c0be3", "metadata": {}, "source": ["2. **`hfd脚本` + `aria2c` + `git-lfs`多线程并行拉取数据**"]}, {"cell_type": "markdown", "id": "81ac1a6c-906a-4951-aa3c-f0da9c536cbe", "metadata": {}, "source": ["- **StarCoder**"]}, {"cell_type": "markdown", "id": "0889f329-e2f0-4fcf-a365-5abe997567c3", "metadata": {}, "source": ["一次性拉取所有数据的命令👇<font color=\"green\">**注意！你需要将下面的hf_token修改为你刚才申请的有权限的token、同时将hf_username修改为你自己的huggingface账户的名字**。\n", "\n", "<font color=\"red\">**同时，在均32MB/s速度下、下载全部starcoder数据需要6\\~7小时时间+800G以上内存，请谨慎运行下面的命令行。**"]}, {"cell_type": "markdown", "id": "ee9a5066-6ac7-46bc-9084-004c1527a220", "metadata": {}, "source": ["```bash\n", "./hfd.sh bigcode/starcoderdata --dataset --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/Starcoder --hf_token xxx --hf_username TsaiTsai0929\n", "```"]}, {"cell_type": "markdown", "id": "f7f73d9a-8637-47c7-82cd-9ddd12624a67", "metadata": {}, "source": ["单独拉取一种语言的命令👇<font color=\"red\">**例如Python、大约22G的数据，在34MB/s速度下、需要大约11~12min左右的时间**——"]}, {"cell_type": "markdown", "id": "a363d89b-16be-498a-aa99-7576f8298937", "metadata": {}, "source": ["```bash\n", "./hfd.sh bigcode/starcoderdata --dataset --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/Starcoder --include *python --hf_token xxx --hf_username TsaiTsai0929\n", "```"]}, {"cell_type": "markdown", "id": "2631c57e-86ca-4fdd-a735-ee3227a0a9ff", "metadata": {}, "source": ["当前的hfd脚本一次只能支持一个目录、但是对代码数据集我们必然会需要不止一种代码。**在本次训练中，我们拉取了Python、SQL、R、Matlab、JavaScript、Java、Json、C、Rust、Go、TypeScript、Kotlin、Swift、Julia、markdown、html等16种语言进行训练**。因此你可以设置这样的CLI代码、一次性运行👇"]}, {"cell_type": "markdown", "id": "538b900f-4d6c-42bb-a7f0-58c3312db1ad", "metadata": {}, "source": ["```bash\n", "./hfd.sh bigcode/starcoderdata --dataset --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/Starcoder --include *sql* --hf_token xxx --hf_username TsaiTsai0929\n", "\n", "./hfd.sh bigcode/starcoderdata --dataset --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/Starcoder --include *r* --hf_token xxx --hf_username TsaiTsai0929\n", "\n", "./hfd.sh bigcode/starcoderdata --dataset --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/Starcoder --include *matlab* --hf_token xxx --hf_username TsaiTsai0929\n", "\n", "```"]}, {"cell_type": "markdown", "id": "95d2608d-35ee-4e5e-9cf3-694e1ed70916", "metadata": {}, "source": ["- **TheStackDedup**"]}, {"cell_type": "markdown", "id": "91be4237-4a87-4606-a98c-7a44ddab627c", "metadata": {}, "source": ["一次性拉取所有数据的命令👇<font color=\"green\">**注意！你需要将下面的hf_token修改为你刚才申请的有权限的token、同时将hf_username修改为你自己的huggingface账户的名字**。\n", "\n", "<font color=\"red\">**同时，在均32MB/s速度下、下载全部starcoder数据需要24小时时间+3T以上内存，请谨慎运行下面的命令行。**"]}, {"cell_type": "markdown", "id": "7283af68-eefd-42a2-a00c-17a6e9d08d3e", "metadata": {}, "source": ["```bash\n", "./hfd.sh bigcode/the-stack-dedup --dataset --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/TheStackDedup --hf_token xxx --hf_username TsaiTsai0929\n", "```"]}, {"cell_type": "markdown", "id": "c39a2255-95fb-4598-aad4-a9dc6c856bc4", "metadata": {}, "source": ["单独拉取一种语言的命令👇<font color=\"red\">**例如sql、大约4G的数据，在34MB/s速度下、需要大约3~4min左右的时间**——"]}, {"cell_type": "markdown", "id": "97dc09aa-4765-4d41-be08-b189d5ff4d61", "metadata": {}, "source": ["```bash\n", "./hfd.sh bigcode/the-stack-dedup --dataset --tool aria2c -x 10 --local-dir ~/autodl-tmp/MateConv/Data/TheStackDedup --include *sql --hf_token xxx --hf_username TsaiTsai0929\n", "```"]}, {"cell_type": "markdown", "id": "df884eef-18bb-42e7-a704-47df5a69982c", "metadata": {}, "source": ["至此、所有数据都拉取完毕了！下载好所有的数据后、你可能发现了、每个数据的格式、状态、结构都不尽相同，因此**我们还需要针对每一个数据进行特定的预处理**。与下载一样、对这些数据进行预处理的流程大多需要分布式工具来辅助我们，接下来让我们一起看看巨量数据的预处理流程。\n", "\n", "| 数据集编号 | 数据集名称                 | 数据属性（中文/英文/代码）                 | 数据量级           | 存储格式            | 是否经过数据清洗 |\n", "|---|----------------------------|-------------------------------------------|--------------------|---------------------|------------------|\n", "| 1| Skywork-SkyPile 150B       | 中文文本                                      | 620GB             | JSONL 短文本        | 是               |\n", "| 2| wanjuan1.0-nlp-CN             | 中文文本                                 | 580GB | JSONL 短文本压缩  | 是               |\n", "| 3| WuDaoCorporaText | 中文文本                                      | 200GB  | JSONL 短文本        | 是               |\n", "| 4| chinese-fineweb-edu-v2 | 75%中文文本，25%英文文本                                      | 670GB  | Parquet        |   是            |\n", "| 5| Wikipedia-CN | 中文文本                                      | 1.1GB  | JSON        |   是            |\n", "| 6| BaiduBaike-5.63M | 中文文本                                      | 17GB  | JSON        |   是            |\n", "| 7| wangrui6/Zhihu-KOL | 中文问答对                                      | 1.5 GB  | parquet        |   是            |\n", "| 8| wanjuan1.0-nlp-EN             | 英文文本                                 | 440GB | JSONL 短文本压缩  | 是               |\n", "| 9| SlimPajama-627B            | 英文为主的混合语言文本                                      | 1TB               | JSONL 短文本压缩    | 是               |\n", "| 10| Starcoder                  | 代码             | 100+GB/768G             | Parquet            | 是               |\n", "| 11| TheStackDedup                 | 代码             | 700+GB/3TB             | Parquet            | 是               |"]}, {"cell_type": "markdown", "id": "2a549b0e-74ba-42c3-8cb5-7f568d420f02", "metadata": {}, "source": ["### 2.3 巨量数据清洗与数据预处理"]}, {"cell_type": "markdown", "id": "3b481895-3be3-45f1-b18a-d321136cbe52", "metadata": {}, "source": []}, {"cell_type": "code", "execution_count": null, "id": "6092dd77-d068-4ddc-9c2f-1b598ec35bda", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "ad07f2b6-facd-4420-b09b-962d689875e2", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "cfaefe08-4968-426f-bb8a-2cfa24ea9163", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "3ae2e930-66d4-44df-869e-9bd6cf72754d", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "8267f003-0a4f-4c32-9d58-75ba85699ec9", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "5ca3cd31-cce8-4be5-813e-87684568603e", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}