[
  {
    "user_name": "sa<PERSON><PERSON><PERSON>",
    "api_name": "Kubernetes Read Pod",
    "api_call": "GET /api/v1/namespaces/{namespace}/pods/{podName}",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod is located)",
      "podName": "string (The name of the Pod to read)"
    },
    "functionality": "Reads information about a specified Kubernetes Pod in REST format",
    "env_requirements": ["kubectl"],
    "example_code": "import requests\n\n# Read a Pod in a namespace\napi_url = 'https://your-kubernetes-api-server/api/v1/namespaces/your-namespace/pods/your-pod-name'\nheaders = {'Authorization': 'Bearer your-auth-token'}\nresponse = requests.get(api_url, headers=headers)\nprint(response.json())",
    "meta_data": {
      "description": "This API method allows users to read information about a specified Kubernetes Pod using RESTful API calls.",
      "documentation": "https://kubernetes.io/docs/reference/generated/kubectl/kubectl-commands#get"
    },
    "questions": [
      "How can I retrieve information about a specific Pod?",
      "What method should I use to programmatically access Pod details?"
    ]
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Read Ephemeral Containers",
    "api_call": "GET /api/v1/namespaces/{namespace}/pods/{podName}/ephemeralcontainers",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod is located)",
      "podName": "string (The name of the Pod to read)"
    },
    "functionality": "Reads the ephemeral containers of a specified Kubernetes Pod in REST format",
    "env_requirements": ["kubectl"],
    "example_code": "import requests\n\n# Read ephemeral containers of a Pod in a namespace\napi_url = 'https://your-kubernetes-api-server/api/v1/namespaces/your-namespace/pods/your-pod-name'\nheaders = {'Authorization': 'Bearer your-auth-token'}\nresponse = requests.get(api_url, headers=headers)\nephemeral_containers = response.json()['spec']['ephemeralContainers']\nprint(ephemeral_containers)",
    "meta_data": {
      "description": "This API method allows users to read the ephemeral containers of a specified Kubernetes Pod using RESTful API calls.",
      "documentation": "https://kubernetes.io/docs/reference/generated/kubectl/kubectl-commands#get"
    },
    "questions": [
      "How can I retrieve information about ephemeral containers in a specific Pod?",
      "What method should I use to programmatically access ephemeral container details?"
    ]
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Read Pod Logs",
    "api_call": "GET /api/v1/namespaces/{namespace}/pods/{podName}/log",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod is located)",
      "podName": "string (The name of the Pod to read logs from)",
      "container": "string (Optional, the name of the container within the Pod)",
      "previous": "boolean (Optional, whether to retrieve previous container logs)",
      "tailLines": "integer (Optional, the number of lines from the end of the logs to show)",
      "timestamps": "boolean (Optional, whether to include timestamps in the log output)",
      "follow": "boolean (Optional, whether to stream the logs in real-time)",
      "pretty": "boolean (Optional, whether to format the response JSON)"
    },
    "functionality": "Reads the logs of a specified Kubernetes Pod in REST format",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Read Ephemeral Containers",
    "api_call": "GET /api/v1/namespaces/{namespace}/pods/{podName}/ephemeralcontainers",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod is located)",
      "podName": "string (The name of the Pod to read)",
      "pretty": "boolean (Optional, whether to format the response JSON)"
    },
    "functionality": "Reads the status of a specified Kubernetes Pod in REST format",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes List/Watch Pods",
    "api_call": "GET /api/v1/namespaces/{namespace}/pods",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pods are located)",
      "allowWatchBookmarks": "boolean (Optional, allow watch bookmarks)",
      "continue": "string (Optional, continue from a previous query)",
      "fieldSelector": "string (Optional, field selector to filter Pods)",
      "labelSelector": "string (Optional, label selector to filter Pods)",
      "limit": "integer (Optional, limit the number of results)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "resourceVersion": "string (Optional, resource version to watch for changes)",
      "resourceVersionMatch": "string (Optional, resource version match)",
      "sendInitialEvents": "boolean (Optional, send initial events)",
      "watch": "boolean (Optional, set to 'true' to watch for changes)"
    },
    "functionality": "Lists or watches objects of kind 'Pod' in Kubernetes in REST format",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes List/Watch Pods",
    "api_call": "GET /api/v1/pods",
    "api_version": "1.0",
    "api_arguments": {
      "allowWatchBookmarks": "boolean (Optional, allow watch bookmarks)",
      "continue": "string (Optional, continue from a previous query)",
      "fieldSelector": "string (Optional, field selector to filter Pods)",
      "labelSelector": "string (Optional, label selector to filter Pods)",
      "limit": "integer (Optional, limit the number of results)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "resourceVersion": "string (Optional, resource version to watch for changes)",
      "resourceVersionMatch": "string (Optional, resource version match)",
      "sendInitialEvents": "boolean (Optional, send initial events)",
      "watch": "boolean (Optional, set to 'true' to watch for changes)"
    },
    "functionality": "Lists or watches objects of kind 'Pod' in Kubernetes in REST format",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Create Pod",
    "api_call": "POST /api/v1/namespaces/{namespace}/pods",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod will be created)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "dryRun": "string (Optional, specify 'All', 'None', or 'Server' for dry run)",
      "fieldManager": "string (Optional, identifier for managing the resource fields)",
      "resourceVersion": "string (Optional, resource version)",
      "body": {
        "kind": "Pod",
        "apiVersion": "v1",
        "metadata": {
          "name": "your-pod-name"
        },
        "spec": {
          "containers": [
            {
              "name": "container-name",
              "image": "container-image"
            }
          ]
        }
      }
    },
    "functionality": "Creates a Kubernetes Pod in the specified namespace using a RESTful API call",
  },
  {
    "user_name": "saikolasani",  
    "api_name": "Kubernetes Update Pod",
    "api_call": "PUT /api/v1/namespaces/{namespace}/pods/{pod_name}",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod is located)",
      "pod_name": "string (The name of the Pod to update)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "dryRun": "string (Optional, specify 'All', 'None', or 'Server' for dry run)",
      "fieldManager": "string (Optional, identifier for managing the resource fields)",
      "resourceVersion": "string (Optional, resource version)",
      "request_body": {
        "kind": "Pod",
        "apiVersion": "v1",
        "metadata": {
          "name": "your-pod-name"
        },
        "spec": {
          "containers": [
            {
              "name": "new-container-name",
              "image": "new-container-image"
            }
          ]
          }
        }
      },
    "functionality": "Updates a specified Kubernetes Pod in the specified namespace using a RESTful API call",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Update Ephemeral Containers",
    "api_call": "PUT /api/v1/namespaces/{namespace}/pods/{pod_name}/ephemeralcontainers",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod is located)",
      "pod_name": "string (The name of the Pod with ephemeral containers to update)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "dryRun": "string (Optional, specify 'All', 'None', or 'Server' for dry run)",
      "fieldManager": "string (Optional, identifier for managing the resource fields)",
      "resourceVersion": "string (Optional, resource version)",
      "request_body": {
        "kind": "PodEphemeralContainers",
        "apiVersion": "v1",
        "metadata": {
          "name": "your-pod-name"
        },
        "ephemeralContainers": [
          {
            "name": "new-ephemeral-container-name",
            "image": "new-ephemeral-container-image"
          }
        ]
      }
    },
    "functionality": "Updates and replaces ephemeral containers of a specified Kubernetes Pod in the specified namespace using a RESTful API call",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Update and Replace Pod",
    "api_call": "UPDATE /api/v1/namespaces/{namespace}/pods/{pod_name}/status",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod is located)",
      "pod_name": "string (The name of the Pod to update and replace status)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "dryRun": "string (Optional, specify 'All', 'None', or 'Server' for dry run)",
      "fieldManager": "string (Optional, identifier for managing the resource fields)",
      "resourceVersion": "string (Optional, resource version)",
      "request_body": {
        "kind": "Pod",
        "apiVersion": "v1",
        "metadata": {
          "name": "your-pod-name"
        },
        "status": {
          "phase": "new-status-phase",
          "conditions": [
            {
              "type": "Ready",
              "status": "True",
              "reason": "Updated"
            }
          ]
        }
      }
    },
    "functionality": "Updates and replaces a the status of a Kubernetes Pod in the specified namespace using a RESTful API call",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Partially Update Pod",
    "api_call": "PATCH /api/v1/namespaces/{namespace}/pods/{pod_name}",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod is located)",
      "pod_name": "string (The name of the Pod to partially update)",
      "dryRun": "string (Optional, specify 'All', 'None', or 'Server' for dry run)",
      "fieldManager": "string (Optional, identifier for managing the resource fields)",
      "fieldValidation": "string (Optional, specify 'true' to enable field validation)",
      "force": "boolean (Optional, specify 'true' to force the update)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "request_body": "Patch (The JSON patch for the partial update)"
    },
    "functionality": "Partially updates a specified Kubernetes Pod in the specified namespace using a RESTful API call",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Partially Update Ephemeralcontainers of a Pod",
    "api_call": "PATCH /api/v1/namespaces/{namespace}/pods/{pod_name}/ephemeralcontainers",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod is located)",
      "pod_name": "string (The name of the Pod to partially update)",
      "dryRun": "string (Optional, specify 'All', 'None', or 'Server' for dry run)",
      "fieldManager": "string (Optional, identifier for managing the resource fields)",
      "fieldValidation": "string (Optional, specify 'true' to enable field validation)",
      "force": "boolean (Optional, specify 'true' to force the update)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "request_body": "Patch (The JSON patch for the partial update)"
    },
    "functionality": "Partially updates a specified Kubernetes Pod's ephemeralcontainers in the specified namespace using a RESTful API call",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Partially Update status of a Pod",
    "api_call": "PATCH /api/v1/namespaces/{namespace}/pods/{pod_name}/status",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod is located)",
      "pod_name": "string (The name of the Pod to partially update)",
      "dryRun": "string (Optional, specify 'All', 'None', or 'Server' for dry run)",
      "fieldManager": "string (Optional, identifier for managing the resource fields)",
      "fieldValidation": "string (Optional, specify 'true' to enable field validation)",
      "force": "boolean (Optional, specify 'true' to force the update)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "request_body": "Patch (The JSON patch for the partial update)"
    },
    "functionality": "Partially updates a specified Kubernetes Pod's status in the specified namespace using a RESTful API call",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Delete Pod",
    "api_call": "DELETE /api/v1/namespaces/{namespace}/pods/{pod_name}",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod is located)",
      "pod_name": "string (The name of the Pod to delete)",
      "dryRun": "string (Optional, specify 'All', 'None', or 'Server' for dry run)",
      "gracePeriodSeconds": "integer (Optional, specify the grace period in seconds before deletion)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "propagationPolicy": "string (Optional, specify 'Orphan', 'Background', or 'Foreground' for propagation policy)",
      "body": {
        "apiVersion": "string (API version)",
        "kind": "string (Resource kind, e.g., 'DeleteOptions')",
        "gracePeriodSeconds": "integer (Grace period in seconds)",
        "preconditions": {
          "uid": "string (Unique identifier)",
          "resourceVersion": "string (Resource version)"
        },
      },
    },
    "functionality": "Deletes a specified Kubernetes Pod in the specified namespace using a RESTful API call",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Delete Pod Collection",
    "api_call": "DELETE /api/v1/namespaces/{namespace}/pods",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod is located)",
      "body": {
        "apiVersion": "string (API version)",
        "kind": "string (Resource kind, e.g., 'DeleteOptions')",
        "gracePeriodSeconds": "integer (Grace period in seconds)",
        "preconditions": {
          "uid": "string (Unique identifier)",
          "resourceVersion": "string (Resource version)"
        },
      },
      "continue": "string (Continue token for pagination)",
      "fieldSelector": "string (Field selector for filtering Pods)",
      "labelSelector": "string (Label selector for filtering Pods)",
      "limit": "integer (Maximum number of Pods to return)",
      "pretty": "string (Specify 'true' to format the response JSON)",
      "resourceVersion": "string (Resource version to watch for changes)",
      "resourceVersionMatch": "string (Resource version match for watch)",
      "sendInitialEvents": "boolean (Specify 'true' to send initial events)",
      "timeoutSeconds": "integer (Timeout in seconds for the request)"
    },
    "functionality": "Deletes a collection of Kubernetes Pods in the specified namespace using a RESTful API call with query parameters",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Read PodTemplate",
    "api_call": "GET /api/v1/namespaces/{namespace}/podtemplates/{podName}",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod is located)",
      "podName": "string (The name of the PodTemplate to read)"
    },
    "functionality": "Reads information about a specified Kubernetes PodTemplate in REST format",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes List/Watch PodTemplates",
    "api_call": "GET /api/v1/namespaces/{namespace}/podtemplates",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pods are located)",
      "allowWatchBookmarks": "boolean (Optional, allow watch bookmarks)",
      "continue": "string (Optional, continue from a previous query)",
      "fieldSelector": "string (Optional, field selector to filter PodTemplates)",
      "labelSelector": "string (Optional, label selector to filter PodTemplates)",
      "limit": "integer (Optional, limit the number of results)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "resourceVersion": "string (Optional, resource version to watch for changes)",
      "resourceVersionMatch": "string (Optional, resource version match)",
      "sendInitialEvents": "boolean (Optional, send initial events)",
      "watch": "boolean (Optional, set to 'true' to watch for changes)"
    },
    "functionality": "Lists or watches objects of kind 'PodTemplate' in Kubernetes in REST format",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes List/Watch PodTemplates",
    "api_call": "GET /api/v1/podtemplates",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pods are located)",
      "allowWatchBookmarks": "boolean (Optional, allow watch bookmarks)",
      "continue": "string (Optional, continue from a previous query)",
      "fieldSelector": "string (Optional, field selector to filter PodTemplates)",
      "labelSelector": "string (Optional, label selector to filter PodTemplates)",
      "limit": "integer (Optional, limit the number of results)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "resourceVersion": "string (Optional, resource version to watch for changes)",
      "resourceVersionMatch": "string (Optional, resource version match)",
      "sendInitialEvents": "boolean (Optional, send initial events)",
      "watch": "boolean (Optional, set to 'true' to watch for changes)"
    },
    "functionality": "Lists or watches objects of kind 'PodTemplate' in Kubernetes in REST format",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Create PodTemplate",
    "api_call": "POST /api/v1/namespaces/{namespace}/podtemplates",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the Pod will be created)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "dryRun": "string (Optional, specify 'All', 'None', or 'Server' for dry run)",
      "fieldManager": "string (Optional, identifier for managing the resource fields)",
      "resourceVersion": "string (Optional, resource version)",
      "body": {
        "apiVersion": "string (API version)",
        "kind": "string (Resource kind, e.g., 'PodTemplate')",
        "metadata": {
          "name": "string (Name of the PodTemplate)",
          "namespace": "string (Namespace for the PodTemplate)"
        },
        "spec": {
          
        }
      },
    },
    "functionality": "Creates a Kubernetes PodTemplate in the specified namespace using a RESTful API call",
  },
  {
    "user_name": "saikolasani",  
    "api_name": "Kubernetes Replace PodTemplate",
    "api_call": "PUT /api/v1/namespaces/{namespace}/podtemplates/{pod_name}",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the PodTemplate is located)",
      "pod_name": "string (The name of the PodTemplate to update)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "dryRun": "string (Optional, specify 'All', 'None', or 'Server' for dry run)",
      "fieldManager": "string (Optional, identifier for managing the resource fields)",
      "resourceVersion": "string (Optional, resource version)",
      "body": {
        "apiVersion": "string (API version)",
        "kind": "string (Resource kind, e.g., 'PodTemplate')",
        "metadata": {
          "name": "string (Name of the PodTemplate)",
          "namespace": "string (Namespace for the PodTemplate)"
        },
        "spec": {
          
        }
      },
    },  
    "functionality": "Updates a specified Kubernetes PodTemplate in the specified namespace using a RESTful API call",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Partially Update PodTemplate",
    "api_call": "PATCH /api/v1/namespaces/{namespace}/podtemplates/{pod_name}",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the PodTemplate is located)",
      "pod_name": "string (The name of the PodTemplate to partially update)",
      "dryRun": "string (Optional, specify 'All', 'None', or 'Server' for dry run)",
      "fieldManager": "string (Optional, identifier for managing the resource fields)",
      "fieldValidation": "string (Optional, specify 'true' to enable field validation)",
      "force": "boolean (Optional, specify 'true' to force the update)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "request_body": "Patch (The JSON patch for the partial update)"
    },
    "functionality": "Partially updates a specified Kubernetes PodTemplate in the specified namespace using a RESTful API call",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Delete PodTemplate",
    "api_call": "DELETE /api/v1/namespaces/{namespace}/podtemplates/{pod_name}",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the PodTemplate is located)",
      "pod_name": "string (The name of the PodTemplate to delete)",
      "dryRun": "string (Optional, specify 'All', 'None', or 'Server' for dry run)",
      "gracePeriodSeconds": "integer (Optional, specify the grace period in seconds before deletion)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "propagationPolicy": "string (Optional, specify 'Orphan', 'Background', or 'Foreground' for propagation policy)",
      "body": {
        "apiVersion": "string (API version)",
        "kind": "string (Resource kind, e.g., 'DeleteOptions')",
        "gracePeriodSeconds": "integer (Grace period in seconds)",
        "preconditions": {
          "uid": "string (Unique identifier)",
          "resourceVersion": "string (Resource version)"
        },
      },
    },
    "functionality": "Deletes a specified Kubernetes PodTemplate in the specified namespace using a RESTful API call",
  },
  {
    "user_name": "saikolasani",
    "api_name": "Kubernetes Delete PodTemplate Collection",
    "api_call": "DELETE /api/v1/namespaces/{namespace}/podtemplates",
    "api_version": "1.0",
    "api_arguments": {
      "namespace": "string (The namespace where the PodTemplate is located)",
      "body": {
        "apiVersion": "string (API version)",
        "kind": "string (Resource kind, e.g., 'DeleteOptions')",
        "gracePeriodSeconds": "integer (Grace period in seconds)",
        "preconditions": {
          "uid": "string (Unique identifier)",
          "resourceVersion": "string (Resource version)"
        },
      },
      "continue": "string (Continue token for pagination)",
      "fieldSelector": "string (Field selector for filtering Pods)",
      "labelSelector": "string (Label selector for filtering Pods)",
      "limit": "integer (Maximum number of Pods to return)",
      "pretty": "string (Specify 'true' to format the response JSON)",
      "resourceVersion": "string (Resource version to watch for changes)",
      "resourceVersionMatch": "string (Resource version match for watch)",
      "sendInitialEvents": "boolean (Specify 'true' to send initial events)",
      "dryRun": "string (Optional, specify 'All', 'None', or 'Server' for dry run)",
      "gracePeriodSeconds": "integer (Optional, specify the grace period in seconds before deletion)",
      "pretty": "string (Optional, specify 'true' to format the response JSON)",
      "propagationPolicy": "string (Optional, specify 'Orphan', 'Background', or 'Foreground' for propagation policy)"
    },
    "functionality": "Deletes a collection of Kubernetes PodTemplates in the specified namespace using a RESTful API call",
  }

]
