2025-04-09 12:53:25,224 INFO    MainThread:441755 [wandb_setup.py:_flush():67] Current SDK version is 0.19.9
2025-04-09 12:53:25,224 INFO    MainThread:441755 [wandb_setup.py:_flush():67] Configure stats pid to 441755
2025-04-09 12:53:25,224 INFO    MainThread:441755 [wandb_setup.py:_flush():67] Loading settings from /root/.config/wandb/settings
2025-04-09 12:53:25,224 INFO    MainThread:441755 [wandb_setup.py:_flush():67] Loading settings from /root/autodl-tmp/MateConv/wandb/settings
2025-04-09 12:53:25,224 INFO    MainThread:441755 [wandb_setup.py:_flush():67] Loading settings from environment variables
2025-04-09 12:53:25,224 INFO    MainThread:441755 [wandb_init.py:setup_run_log_directory():662] Logging user logs to /root/autodl-tmp/MateConv/wandb/run-20250409_125325-m0cz3jki/logs/debug.log
2025-04-09 12:53:25,224 INFO    MainThread:441755 [wandb_init.py:setup_run_log_directory():663] Logging internal logs to /root/autodl-tmp/MateConv/wandb/run-20250409_125325-m0cz3jki/logs/debug-internal.log
2025-04-09 12:53:25,224 INFO    MainThread:441755 [wandb_init.py:init():781] calling init triggers
2025-04-09 12:53:25,224 INFO    MainThread:441755 [wandb_init.py:init():786] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-04-09 12:53:25,224 INFO    MainThread:441755 [wandb_init.py:init():809] starting backend
2025-04-09 12:53:25,224 INFO    MainThread:441755 [wandb_init.py:init():813] sending inform_init request
2025-04-09 12:53:25,230 INFO    MainThread:441755 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-04-09 12:53:25,230 INFO    MainThread:441755 [wandb_init.py:init():823] backend started and connected
2025-04-09 12:53:25,231 INFO    MainThread:441755 [wandb_init.py:init():915] updated telemetry
2025-04-09 12:53:25,232 INFO    MainThread:441755 [wandb_init.py:init():939] communicating run to backend with 90.0 second timeout
2025-04-09 12:53:26,669 INFO    MainThread:441755 [wandb_init.py:init():1014] starting run threads in backend
2025-04-09 12:53:26,982 INFO    MainThread:441755 [wandb_run.py:_console_start():2454] atexit reg
2025-04-09 12:53:26,983 INFO    MainThread:441755 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-04-09 12:53:26,983 INFO    MainThread:441755 [wandb_run.py:_redirect():2371] Wrapping output streams.
2025-04-09 12:53:26,983 INFO    MainThread:441755 [wandb_run.py:_redirect():2394] Redirects installed.
2025-04-09 12:53:26,985 INFO    MainThread:441755 [wandb_init.py:init():1056] run started, returning control to user process
