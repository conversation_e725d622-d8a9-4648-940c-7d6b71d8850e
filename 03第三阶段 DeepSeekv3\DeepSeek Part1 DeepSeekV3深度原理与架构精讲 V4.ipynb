{"cells": [{"cell_type": "markdown", "id": "7b72f92b-5a2e-4736-9ecc-5fed1c875f6d", "metadata": {}, "source": ["## 0 DeepSeek必备基础与学习路径梳理"]}, {"cell_type": "markdown", "id": "1a5b3db4-cabb-4e27-9851-339463033ddf", "metadata": {}, "source": ["2025是属于DeepSeek的一年。DeepSeek是深度求索公司推出的一系列创新型架构的总称，这一系列模型包括**通用大语言模型DeepSeekv3, 推理大模型DeepSeekR1，以及系列基于R1模型完成蒸馏的小型模型群**。DeepSeek系列模型以其超高性能、超低成本立足于市场，不仅很快激发了中国大模型落地狂潮，也在全世界都掀起了基于强化学习的新一轮大模型创新。\n", "\n", "---\n", "\n", "- **DeepSeekv3，R1，蒸馏，彼此之间什么关系？**\n", "\n", "  - <font color=\"red\">**DeepSeek v3模型**</font>：在架构、训练方式、量化方法、GPU优化、训练框架等等方面大胆创新、包括潜在注意力机制MLA、无负载均衡的DeepSeekMOE、FP8混合精度训练、以及MTP多token预测损失、还有DualPipe改进后的管道并行机制等等、综合实现了降本90%。<br><br>\n", "  - <font color=\"red\">**DeepSeek R1模型**</font>：基于DeepSeek V3 Base模型、创新了“多阶段强化学习”的全新训练流程、基于GRPO这一全新强化学习算法取得很好的效果、摆脱了传统的“预训练+微调”思路、创造了训练的全新范式、为训练底层模型开辟了新的道路<br><br>\n", "  - <font color=\"red\">**蒸馏模型群**</font>：可以利用知识蒸馏将r1模型超强推理能力迁移到各类小型模型上、令推理模型诞生的成本大幅降低<br><br>\n", "  - 在降本增效的路上一去不复返：李飞飞老师做的50美金创建推理小模型、清华大学做的1张4090部署deepseek等项目、都象征deepseek为“超低成本大模型”带来了无限可能"]}, {"cell_type": "markdown", "id": "a8d52714-321f-47fe-a560-4438a70792a9", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "90f3321d-9116-48a0-a234-b99db29786c4", "metadata": {}, "source": ["- **DeepSeek推荐预习内容**"]}, {"cell_type": "markdown", "id": "1030c804-c5ce-4485-990e-95cbe623546e", "metadata": {}, "source": ["DeepSeekv3和DeepSeek R1都是在现有成熟模型基础上进行创新改造的模型，二者无论是模型架构还是训练流程、都与大模型领域积累的各种基础息息相关！有不少来到这节课的小伙伴是初学大模型、为了能够帮助大家更顺畅地理解直播课、在DeepSeek直播开始之前，️️我建议小伙伴们按照下面的路线先对大模型训练领域技术进行预习️️️——\n", "\n", "> **1 【Transformer】1～4、10～16**\n", "\n", "如果你不理解参数超参数的区别、如果你不理解QKV是如何诞生、你不能轻松说出Decoder-only架构的训练和推理有什么区别、你不能理解生成模型的输出层是怎么输出文字的、那你需要学习Transformer内容。\n", "\n", "> **2 【llama】1～2、4～6、8～9、11～14、17～19**\n", "\n", "DeepSeek架构与我们实现的LlaMA架构高度相似、在LlaMA架构中我们详解了MOE混合专家模型、详解了KV缓存机制、详解了门控机制、这些都是DeepSeek模型的关键基础，掌握LLaMA架构能让你在学习deepseek时事半功倍！\n", "\n", "> **3 【分布式预训练】0.1、0.2、1～4，12~后续全部内容**\n", "\n", "在分布式预训练章节中，我们搭建了基于Llama + MOE架构自建的【对话模型MateConv】、其环境搭建、数据收集、训练流程和训练脚本将会在后续的DeepSeekv3课程和DeepSeekR1课程中发挥巨大作用，了解经典的预训练流程将会对你有很大的帮助！如果你有足够的时间，可以将整个分布式预训练流程看完。\n", "\n", "> <font color =\"red\">**不看预习内容、对后续学习有什么影响？**</font>\n", "\n", "好的基础是成功的一半、预习内容是对DeepSeek非常好的铺垫和补充，如果不看预习内容、debug难度将会增加、同时后续学习难度可能会上涨、<font color =\"red\">**同时老师在沉浸式研发时可能无法及时帮助你解决由于基础缺乏导致的debug以及解决过于基础的问题**</font>。\n", "\n", "**不过，不看预习内容并不影响你速通代码、快速落地，如果你的需求是快速落地的需求，可以直接速通**。"]}, {"cell_type": "markdown", "id": "24a7e102-1b93-4ac1-a31f-a695d339fa4f", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "496161be-d51c-47e4-ae32-bf786e7a0d11", "metadata": {}, "source": ["- **DeepSeek课程模块与安排**"]}, {"attachments": {}, "cell_type": "markdown", "id": "eab2d317-15e7-4659-8737-272f3274f35d", "metadata": {}, "source": ["| **#** | **课程模块** | **主要内容** |**只学这一章可以吗？** |\n", "|------|------------|----------------|---|\n", "| **1** | **DeepSeekR1基<br>础：v3架构精讲** | ✅ DeepSeekv3技术报告解读 <br> ✅ 推理优化：Low-rank KV缓存、MLA注意力<br> ✅ 架构创新：无负载均衡MoE、多Token预测、FP8混合精度 <br> ✅ 【实战】复现DeepSeek v3全架构脚本 |✅代码支持独立运行<br><br>✅支持完全独立学习|\n", "| **2** | **从0训练Mini DeepSeek<br> v3 Base模型** | ✅ 预训练语料构建（FIM、PSM）<br> ✅ 训练环境配置与显卡推荐 <br>  ✅ 训练优化：专家并行、DualPipe并行、3D并行） <br>✅ 推理优化：YaRN低温推理增强 <br> ✅ 【实战】DeepSeekv3全架构与预训练脚本 <br> ✅ 【实战】ZeRO-1数据并行与管道并行 <br> ✅ 【实战】双卡分布式训练与batch_size调度 |✅代码支持独立运行<br><br>❌先掌握v3架构|\n", "| **3** | **DeepSeek R1-Zero<br>模型训练流程复现** | ✅ R1-Zero与DeepSeekR1报告解读 <br> ✅ GRPO优化策略（分组相对策略、“顿悟时刻”） <br> ✅ 数学/编程CoT的奖励函数 <br> ✅ 强化训练框架（veRL、OpenRLHF、TRL） <br> ✅ 【实战】GRPO语料合成、结构/准确度奖励函数 <br> ✅ 【实战】OpenR1构建语义相似度/重复惩罚 <br> ✅ 【实战】OpenR1基于Qwen的GRPO训练 |✅代码支持独立运行<br><br>❌先掌握v3架构|\n", "| **4** | **DeepSeek R1模型<br>训练流程复现** | ✅ 冷启动：基于长思考链 LongCoT 训练 <br> ✅ 第一轮SFT+RL：强化推理能力 <br> ✅ 第二轮SFT+RL：人类偏好对齐 <br> ✅ language consistency 奖励函数 <br> ✅ 拒绝采样法：筛选高质量推理路径 <br> ✅ 推理与非推理数据集构建策略 <br> ✅ 【实战】SimpleRL复现 R1 冷启动+RL <br> ✅ 【实战】Logic-RL复现rollout与高温采样优化 |✅代码支持独立运行<br><br>❌先掌握V3架构<br><br>❌先掌握R1-Zero训练|\n", "| **5** | **DeepSeek R1蒸馏/<br>微调流程复现** |✅ R1蒸馏 vs 传统 LLM 蒸馏对比 <br> ✅ 教师/学生模型选型与对比 <br> ✅ 【语料】基于R1实现蒸馏数据集的合成 <br> ✅ 【语料】推理/强化语料获取、数据清洗 <br> ✅ 【实战】Llama-Factory基于CoT的蒸馏 <br> ✅ 【实战】Unsloth 提升推理模型加速 <br> ✅ 【实战】基于 Unsloth 完成 DeepSeekR1 微调 |✅代码支持独立运行<br><br>✅支持完全独立学习|"]}, {"cell_type": "markdown", "id": "22dc2dce-e10c-4c47-a9a4-06d44e6fd778", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "ec7f89f9-0a12-42a5-9594-b1f0c346163f", "metadata": {}, "source": ["- **DeepSeek内容基础配置**"]}, {"cell_type": "markdown", "id": "2d2d2d04-f146-4dfb-9db4-888074f85982", "metadata": {}, "source": ["<center>‼️只支持linux、脚本无法在Win或Mac上跑通<br><br>\n", "    \n", "<center>‼️Mini模型暂定2B、可选择双卡4090、1T硬盘或4卡4090、2T硬盘"]}, {"cell_type": "markdown", "id": "67e1a881-318a-4b48-9537-bf42e0281735", "metadata": {}, "source": ["核心库版本 ↓\n", "```\n", "torch==2.2.0\n", "triton==2.2.0\n", "sentence_transformers==2.3.1\n", "transformers==4.44.0\n", "datasets==2.16.1\n", "deepspeed==0.15.1\n", "trl==0.8.6\n", "safetensors==0.4.5\n", "```"]}, {"cell_type": "markdown", "id": "ad7bfb40-7ab9-4bbf-aa39-5670ed460236", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "c32eaf40-d88b-498e-a574-d7071130c266", "metadata": {}, "source": ["- **DeepSeek未来会带来什么趋势？**\n", "\n", "  - <font color=\"red\">**超低成本、比肩顶尖大模型的效果**</font> ➡️ 垂类大模型研发需求激增、降本90%后，从公司到个人都渴望复现deepseek的流程、打造属于个人和小型企业的大模型\n", "\n", "  - <font color=\"red\">**有了比海外模型更好用、更安全的国产模型，许多app一度以“接入deepseek”为宣传点**</font> ➡️ 从技术尝试走向了真正技术落地和技术应用、一些更硬核的需求（例如分布式、蒸馏与量化）需求开始在企业用人端逐渐显现\n", "\n", "  - <font color=\"red\">**全新的训练范式、全新的技术路线、全新的竞争**</font> ➡️ 深度理解DeepSeek并实现DeepSeek各个环节的技术需求激增、原本做大模型的企业、原本不做大模型的企业都进入了当前的风潮\n", "\n", "  - <font color=\"red\">**技术狂潮引发岗位JD和面试重点发生变化**</font> ➡️ DeepSeek、以至于分布式预训练、顶尖训练框架成为必须掌握的核心技能\n", "\n", "  - 并且以上趋势会构成长期趋势、大模型领域将发生翻天服务的变化、大模型技术发展将长期因此收益"]}, {"cell_type": "markdown", "id": "4bc6e89f-e370-4367-86d8-a23ca05abd47", "metadata": {}, "source": [" # <center> Part 1 DeepSeekv3深度原理与架构精讲"]}, {"cell_type": "code", "execution_count": null, "id": "867f1658-27de-49d5-9a81-f23d7bde9b15", "metadata": {}, "outputs": [], "source": ["import math\n", "from dataclasses import dataclass\n", "from typing import Tuple, Optional, Literal\n", "\n", "import torch\n", "from torch import nn\n", "import torch.nn.functional as F\n", "import torch.distributed as dist"]}, {"cell_type": "markdown", "id": "a27520eb-6ab0-419b-9671-d134d6d0ac73", "metadata": {}, "source": ["## 1 DeepSeekv3架构图解与基本参数配置"]}, {"cell_type": "markdown", "id": "d6c553bd-593e-4ed9-a757-577752d3a513", "metadata": {}, "source": ["根据DeepSeekv3开源架构以及技术报告，我们绘制了DeepSeekv3基本架构图——\n", "\n", "<center><img src=\"https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/73-.png\" alt=\"image-20250107200502389\" style=\"zoom:30%;\" />"]}, {"cell_type": "markdown", "id": "c67a0fd8-b62e-4cb2-84bf-5ba42def2fc1", "metadata": {}, "source": ["整体架构明显是一个类llama的decoder-only架构、具体架构板块如下——"]}, {"cell_type": "markdown", "id": "e8ab9e5d-f8e1-48b1-a107-c863dae2496b", "metadata": {}, "source": ["- **1. 输入嵌入层**：模型以 **ParallelEmbedding**（并行嵌入层）作为起点，将输入的 token 映射到高维向量空间。\n", "<br><br>\n", "- **2. 旋转位置编码 (Rotary Positional Encodings, RoPE)**：使用 **RoPE** 为输入嵌入添加位置信息，使模型能够有效地捕捉序列中 token 的顺序关系。\n", "<br><br>\n", "- **3. RMSNorm 层归一化**：每个注意力层和前馈层前均使用 **RMSNorm**（均方根归一化）进行规范化。\n", "<br><br>\n", "- **4. 多头注意力机制 (Multi-Head Attention) 与 KV 缓存**：这是带有KV缓存加速的多头注意力机制、提供两种缓存策略：<br><br>\n", "    1. **Naive Cache**（简单缓存）：适合较短序列或常规场景。\n", "    2. **Absorb Cache**（密集缓存）：优化内存使用和推理效率，特别适用于长序列处理。\n", "<br><br>\n", "- **5. 前馈网络 (FFN) 和混合专家模型 (MoE)**：在注意力模块之后，模型在以下两种模块之间进行 **互斥选择**：<br><br>\n", "  1. **FFN（前馈网络）**：使用 **SiLU（Sigmoid Linear Unit）** 作为激活函数，结合并行计算实现高效特征转换。\n", "  2. **MoE（专家混合模型）**：通过门控机制动态路由输入到部分专家网络。这种设计在不增加显著计算成本的前提下，提升了模型的参数效率和灵活性。\n", "<br><br>\n", "- **6. 堆叠的 Transformer Blocks**：模型由多个 **Transformer Blocks** 堆叠而成（图中标注为 *N）。每个 Transformer 块包含注意力模块、RMSNorm 层以及 FFN 或 MoE。\n", "<br><br>\n", "- **7. 输出层**：堆叠的 Transformer 块输出经过、使用**RMSNorm** 再次进行规范化、**ColumnParallelLinear**（列并行线性层）将特征映射到词汇表的概率分布、**Softmax** 层计算最终的概率分布。"]}, {"cell_type": "markdown", "id": "0d67b957-a9ba-4827-b06f-12faf9596dea", "metadata": {}, "source": ["在开源时，DeepSeekv3提供了**三种不同规模的模型参数，但只提供了671B模型的权重**——"]}, {"cell_type": "markdown", "id": "48ebab9c-2b24-48cd-9478-891744e331f3", "metadata": {}, "source": ["| **参数名称**         | **含义描述**                                | **16B**           | **236B**           | **671B**           |\n", "|----------------------|---------------------------------------------|-------------------|--------------------|--------------------|\n", "| **vocab_size**       | 词汇表大小，支持的最大 token 数量            | 102400            | 102400             | 129280             |\n", "| <font color=\"red\">**dim**              | 词向量维度，每个 token 的表示维度              | 2048              | 5120               | 7168               |\n", "| <font color=\"red\">**inter_dim**        | FFN 的中间层维度                            | 10944             | 12288              | 18432              |\n", "| <font color=\"red\">**moe_inter_dim**    | MoE 模型中专家的中间层维度                  | 1408              | 1536               | 2048               |\n", "| <font color=\"red\">**n_layers**         | Transformer 块的层数                        | 27                | 60                 | 61                 |\n", "| **n_dense_layers**   | 使用 FFN 的 Transformer 块数量               | 1                 | 1                  | 3                  |\n", "| <font color=\"red\">**n_heads**          | 注意力头的数量                              | 16                | 128                | 128                |\n", "| <font color=\"red\">**n_routed_experts** | MoE 模型中所有专家的总数量                  | 64                | 160                | 256                |\n", "| **n_shared_experts** | MoE 模型中共享专家的数量                    | 2                 | 2                  | 1                  |\n", "| <font color=\"red\">**n_activated_experts** | 每个输入激活的专家数量                   | 6                 | 6                  | 8                  |\n", "| **n_expert_groups**  | 专家分组的数量                              | -                 | 8                  | 8                  |\n", "| **n_limited_groups** | 路由到的分组数量限制                        | -                 | 3                  | 4                  |\n", "| **route_scale**      | 专家路由得分的缩放因子                      | 1.0               | 16.0               | 2.5                |\n", "| **q_lora_rank**      | Q 投影的低秩分解维度                        | 0                 | 1536               | 1536               |\n", "| **kv_lora_rank**     | KV 投影的低秩分解维度                       | 512               | 512                | 512                |\n", "| **qk_nope_head_dim** | 无位置嵌入的 Query/Key 头维度               | 128               | 128                | 128                |\n", "| **qk_rope_head_dim** | 使用旋转位置编码的 Query/Key 头维度          | 64                | 64                 | 64                 |\n", "| **v_head_dim**       | Value 投影的头维度                          | 128               | 128                | 128                |\n", "| **mscale**           | RoPE 编码的缩放因子，用于扩展序列长度        | 0.707             | -                  | -                  |\n", "| **score_func**       | 专家路由的打分函数类型（如 softmax/sigmoid）  | -                 | -                  | sigmoid            |\n", "| **dtype**            | 数据类型（如 bf16、fp8）                     | -                 | -                  | fp8                |\n"]}, {"cell_type": "markdown", "id": "f207259a-d131-48ef-b871-f5b0b33cd61e", "metadata": {}, "source": ["在整个模型代码的最初进行参数配置可以使关键超参数显而易见，方便开发者快速了解模型的整体设置，而无需额外打开配置文件。这种方式对于调试或学习模型代码尤为重要。一些参数（如 `world_size`、`rank` 等分布式相关配置）需要与具体代码逻辑或硬件环境动态结合，因此直接在代码中定义更加直观，也能避免额外的动态加载过程。"]}, {"cell_type": "markdown", "id": "d80f3514-a5e5-4bfc-9ccd-3f21f14bf538", "metadata": {}, "source": ["‼️本次课程中我们讲解的是`deepseekv3_model.py`模型脚本、在原本开源的脚本基础上"]}, {"cell_type": "markdown", "id": "096a8558-d2cf-4e39-b708-1311c5a649d1", "metadata": {}, "source": ["## 2 MLA潜在注意力机制与低秩KV缓存"]}, {"cell_type": "markdown", "id": "e927e298-fc9e-476a-992a-7cee3f350c55", "metadata": {}, "source": ["潜在注意力机制（Multi-head Latent Attention）是在原始多头注意力机制的KV缓存上改进得到的全新注意力机制。KV缓存是在传统注意力机制的推理过程中用于推理加速的一种机制，它的核心哲学是**用内存换效率**，它会保存推理时需要的中间变量、从而为推理过程加速。"]}, {"cell_type": "markdown", "id": "e07682a5-09d3-400e-9d33-422dc4991466", "metadata": {}, "source": ["### 2.1 经典的KV缓存"]}, {"cell_type": "markdown", "id": "d288292a-8a9b-4a71-ac4b-3321008d49ff", "metadata": {}, "source": ["在经典注意力机制中，我们所使用的预测方式是**自回归预测**——\n", "\n", "seq = 蒹葭苍苍\n", "\n", "t=0、将\"蒹葭苍苍\"输入解码器、输出新token0。\n", "\n", "t=1、将\"蒹葭苍苍token0\"输入解码器、输出新的token1。\n", "\n", "t=2、将\"蒹葭苍苍token0token1\"输入解码器、输出新的token2。\n", "\n", "……\n", "\n", "这是一个按顺序执行的循环流程、必须完成上一步之后才能执行下一步，因此是一个很缓慢的过程。大语言模型正式通过这样“一个字、一个字地输出”的流程为我们逐步生成一段话。\n", "\n", "在每次将一个序列输入解码器时，这个序列都需要经过注意力机制的拆解、并最终输出全新的token。**注意力机制是通过计算单词之间两两相关性来理解信息的计算方式**，其中的公式是——"]}, {"cell_type": "markdown", "id": "180f97ba-20d6-4bc5-99af-74c8ca19330a", "metadata": {}, "source": ["$$\n", "\\text{Attention}(Q, K, V) = \\text{softmax} \\left( \\frac{QK^T}{\\sqrt{d_k}} \\right) V\n", "$$\n", "其中：\n", "- $ Q $（Query）：查询矩阵，构成相关性计算中的主动发起乘法的词向量\n", "- $ K $（Key）：键矩阵，构成相关性计算中的被乘的词向量\n", "- $ V $（Value）：值矩阵，用于携带单词本身的语义信息\n", "- $ d_k $：词向量的维度\n", "- $ \\frac{1}{\\sqrt{d_k}} $ 是缩放因子，用于稳定梯度"]}, {"cell_type": "markdown", "id": "809bb548-1eec-401b-a8af-2f5241db4de6", "metadata": {}, "source": ["如公式所示，每个序列进入注意力机制后，都会有自己的Q、K、V矩阵，其中Q与K矩阵一般呈现如下结构——"]}, {"cell_type": "code", "execution_count": null, "id": "4e81ffe1-8e3d-4481-aa84-85848b8e7762", "metadata": {}, "outputs": [], "source": ["X (4,3) => Q (4,3)"]}, {"cell_type": "markdown", "id": "59180d2f-e0ba-449e-a07c-f3091a5c3aaf", "metadata": {}, "source": ["  $$\n", "  Q = \n", "  \\begin{bmatrix}\n", "  q_1^1 & q_1^2 & \\cdots & q_1^d \\\\\n", "  q_2^1 & q_2^2 & \\cdots & q_2^d \\\\\n", "  \\vdots & \\vdots & \\ddots & \\vdots \\\\\n", "  q_n^1 & q_n^2 & \\cdots & q_n^d\n", "  \\end{bmatrix}\n", "  $$\n", "\n", "其中，$ q_i^j $ 表示 Query 矩阵中第 $ i $ 行、第 $ j $ 列的元素。同理，一个(n,d)结构的K矩阵如下——\n", "\n", "  $$\n", "  K = \n", "  \\begin{bmatrix}\n", "  k_1^1 & k_1^2 & \\cdots & k_1^d \\\\\n", "  k_2^1 & k_2^2 & \\cdots & k_2^d \\\\\n", "  \\vdots & \\vdots & \\ddots & \\vdots \\\\\n", "  k_n^1 & k_n^2 & \\cdots & k_n^d\n", "  \\end{bmatrix}\n", "  $$\n", "\n", "**假设现在Q是4行3列、K.T是3行4列**，二者相乘、我们会得到——\n", "\n", "$$\n", "QK^T = \\begin{bmatrix}\n", "       q_\\boldsymbol{\\color{green}{1}} \\cdot k_\\boldsymbol{\\color{green}{1}}^T & q_\\boldsymbol{\\color{green}{1}} \\cdot k_\\boldsymbol{\\color{red}{2}}^T & q_\\boldsymbol{\\color{green}{1}} \\cdot k_\\boldsymbol{\\color{red}{3}}^T & q_\\boldsymbol{\\color{green}{1}} \\cdot k_\\boldsymbol{\\color{red}{4}}^T \\\\\n", "       q_\\boldsymbol{\\color{green}{2}} \\cdot k_\\boldsymbol{\\color{green}{1}}^T & q_\\boldsymbol{\\color{green}{2}} \\cdot k_\\boldsymbol{\\color{green}{2}}^T & q_\\boldsymbol{\\color{green}{2}} \\cdot k_\\boldsymbol{\\color{red}{3}}^T & q_\\boldsymbol{\\color{green}{2}} \\cdot k_\\boldsymbol{\\color{red}{4}}^T \\\\\n", "       q_\\boldsymbol{\\color{green}{3}} \\cdot k_\\boldsymbol{\\color{green}{1}}^T & q_\\boldsymbol{\\color{green}{3}} \\cdot k_\\boldsymbol{\\color{green}{2}}^T & q_\\boldsymbol{\\color{green}{3}} \\cdot k_\\boldsymbol{\\color{green}{3}}^T & q_\\boldsymbol{\\color{green}{3}} \\cdot k_\\boldsymbol{\\color{red}{4}}^T \\\\\n", "       q_\\boldsymbol{\\color{green}{4}} \\cdot k_\\boldsymbol{\\color{green}{1}}^T & q_\\boldsymbol{\\color{green}{4}} \\cdot k_\\boldsymbol{\\color{green}{2}}^T & q_\\boldsymbol{\\color{green}{4}} \\cdot k_\\boldsymbol{\\color{green}{3}}^T & q_\\boldsymbol{\\color{green}{4}} \\cdot k_\\boldsymbol{\\color{green}{4}}^T\n", "     \\end{bmatrix}\n", "$$"]}, {"cell_type": "markdown", "id": "70434022-7c13-4f57-a8b6-22a35dc63950", "metadata": {}, "source": ["加上前瞻掩码、使用更简化的写法，你会发现脚标是这样构成的：\n", "\n", "$$\n", "\\text{QK.T after casual masked} = \\begin{bmatrix}\n", "       \\boldsymbol{\\color{green}{1}}\\cdot\\boldsymbol{\\color{green}{1}} & 0 & 0 & 0\\\\\n", "       \\boldsymbol{\\color{green}{2}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{2}} \\cdot \\boldsymbol{\\color{green}{2}} & 0 & 0\\\\\n", "       \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{2}} & \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{3}} & 0\\\\\n", "       \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{2}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{3}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{4}}\n", "     \\end{bmatrix}\n", "$$"]}, {"attachments": {}, "cell_type": "markdown", "id": "0f81aadf-04cf-4d8f-aa0a-cfb3f285ba24", "metadata": {}, "source": ["**现在让我们回到自回归的流程中——**\n", "\n", "- t=0、将\"蒹葭苍苍\"输入解码器、输出新token0。\n", "\n", "\n", "    先将序列seq经过线性层转化为序列的Q、K、V，然后求解——\n", "\n", "$$\n", "\\text{QK.T after casual masked} = \\begin{bmatrix}\n", "       \\boldsymbol{\\color{green}{1}}\\cdot\\boldsymbol{\\color{green}{1}} & 0 & 0 & 0\\\\\n", "       \\boldsymbol{\\color{green}{2}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{2}} \\cdot \\boldsymbol{\\color{green}{2}} & 0 & 0\\\\\n", "       \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{2}} & \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{3}} & 0\\\\\n", "       \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{2}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{3}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{4}}\n", "     \\end{bmatrix} ==> token0\n", "$$\n", "\n", "- t=1、将\"蒹葭苍苍token0\"输入解码器、输出新的token1。\n", "\n", "    先将序列seq经过线性层转化为序列的Q、K、V（相比上次带了一个新的token1），然后求解——\n", "\n", "$$\n", "\\text{QK.T after casual masked} = \\begin{bmatrix}\n", "       \\boldsymbol{\\color{green}{1}}\\cdot\\boldsymbol{\\color{green}{1}} & 0 & 0 & 0 & 0\\\\\n", "       \\boldsymbol{\\color{green}{2}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{2}} \\cdot \\boldsymbol{\\color{green}{2}} & 0 & 0& 0\\\\\n", "       \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{2}} & \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{3}} & 0& 0\\\\\n", "       \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{2}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{3}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{4}} & 0 \\\\\n", "       \\boldsymbol{\\color{red}{5}} \\cdot \\boldsymbol{\\color{red}{1}} & \\boldsymbol{\\color{red}{5}} \\cdot \\boldsymbol{\\color{red}{2}} & \\boldsymbol{\\color{red}{5}} \\cdot \\boldsymbol{\\color{red}{3}} & \\boldsymbol{\\color{red}{5}} \\cdot \\boldsymbol{\\color{red}{4}} & \\boldsymbol{\\color{red}{5}} \\cdot \\boldsymbol{\\color{red}{5}}\n", "\\end{bmatrix} ==> token1\n", "$$\n", "\n", "- t=2、将\"蒹葭苍苍token0token1\"输入解码器、输出新的token2。\n", "\n", "    先将序列seq经过线性层转化为序列的Q、K、V（相比上次带了一个新的token2），然后求解——\n", "\n", "$$\n", "\\text{QK.T after casual masked} = \\begin{bmatrix}\n", "       \\boldsymbol{\\color{green}{1}}\\cdot\\boldsymbol{\\color{green}{1}} & 0 & 0 & 0 & 0 & 0\\\\\n", "       \\boldsymbol{\\color{green}{2}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{2}} \\cdot \\boldsymbol{\\color{green}{2}} & 0 & 0& 0 & 0\\\\\n", "       \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{2}} & \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{3}} & 0& 0 & 0\\\\\n", "       \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{2}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{3}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{4}} & 0 & 0 \\\\\n", "       \\boldsymbol{\\color{green}{5}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{5}} \\cdot \\boldsymbol{\\color{green}{2}} & \\boldsymbol{\\color{green}{5}} \\cdot \\boldsymbol{\\color{green}{3}} & \\boldsymbol{\\color{green}{5}} \\cdot \\boldsymbol{\\color{green}{4}} & \\boldsymbol{\\color{green}{5}} \\cdot \\boldsymbol{\\color{green}{5}} & 0 \\\\\n", "       \\boldsymbol{\\color{red}{6}} \\cdot \\boldsymbol{\\color{red}{1}} & \\boldsymbol{\\color{red}{6}} \\cdot \\boldsymbol{\\color{red}{2}} & \\boldsymbol{\\color{red}{6}} \\cdot \\boldsymbol{\\color{red}{3}} & \\boldsymbol{\\color{red}{6}} \\cdot \\boldsymbol{\\color{red}{4}} & \\boldsymbol{\\color{red}{6}} \\cdot \\boldsymbol{\\color{red}{5}} & \\boldsymbol{\\color{red}{6}} \\cdot \\boldsymbol{\\color{red}{6}}\n", "\\end{bmatrix} ==> token2\n", "$$"]}, {"cell_type": "markdown", "id": "21346191-1979-413b-b440-d38db1502d82", "metadata": {}, "source": ["你发现了吗？<font color=\"red\">**事实上在每次推理时，QK.T矩阵只是比上一次多了一行，而这全新的一行是由新token 的Q和历史的K组成的**！</font>因此不难发现，在进行推理时、**与其每次都使用线性层对完整的序列进行转化、不如直接保留历史的K和V供后续的计算**。保存历史的K和V之后、在每个时间步中就只需要为K和V增加最新的Token即可、无需再重新进行完整的线性计算。"]}, {"cell_type": "markdown", "id": "d2d54180-5b51-49f5-92fc-f21aa1ad5dfe", "metadata": {}, "source": ["如果**没有 KV 缓存**，我们每次都要计算：\n", "1. **Q, K, V**\n", "\n", "        从 `X` 线性变换得到：\n", "   $$\n", "   Q = X W_Q, \\quad K = X W_K, \\quad V = X W_V\n", "   $$\n", "\n", "<br>\n", "\n", "2. **为QK加上位置编码**\n", "\n", "    $$\n", "    Q = ROPE(Q), \\quad K = ROPE(K)\n", "    $$\n", "\n", "<br>\n", "\n", "3. **计算完整的 QK.T**\n", "   $$\n", "   QK.T = \\text{matmul}(Q, K.T)\n", "   $$\n", "\n", "---\n", "\n", "如果**缓存了 K/V**，我们不需要每次都计算 K/V 了，而是：\n", "\n", "1. **仅仅计算Q、以及新的token的K和V**\n", "\n", "   $$\n", "   Q = X W_Q, \\quad K_{new} = X_{new} W_K, \\quad V_{new} = X_{new} W_V\n", "   $$\n", "\n", "   其中$X_{new}$仅包含当前新加入的 token（通常是 $1$ 个 token），因此$K_{new}$和$V_{new}$计算成本比全部的$K$和$V$矩阵的计算成本小得多。\n", "   \n", "<br>\n", "\n", "2. **为QK加上位置编码**\n", "    $$\n", "    Q = ROPE(Q), \\quad K = ROPE(K_{new})\n", "    $$\n", "   - 只需要对$K_{new}$做位置编码，之前缓存的K矩阵是带位置编码的。\n", "\n", "<br>\n", "\n", "3. **拼接已有的 KV 缓存**：\n", "   $$\n", "   K = \\text{concat}(K_{\\text{cache}}, K_{\\text{new}}), \\quad V = \\text{concat}(V_{\\text{cache}}, V_{\\text{new}})\n", "   $$\n", "   - $K_{\\text{cache}}$ 和 $V_{\\text{cache}}$ 直接从缓存中取出，无需重新计算。\n", "\n", "<br>\n", "\n", "4. **计算完整的 QK.T**\n", "   $$\n", "   QK.T = \\text{matmul}(Q, K.T)\n", "   $$"]}, {"cell_type": "markdown", "id": "e92e6572-a306-4241-8b13-9bbf6f6c2a88", "metadata": {}, "source": ["因此、对传统的KV缓存来说，我们的代码有如下流程 ↓ （以llama为例子）"]}, {"cell_type": "markdown", "id": "730ce57f-8f12-4273-bdab-95e391101578", "metadata": {}, "source": ["```python\n", "def forward(self, x: torch.Tensor, pos_cis: torch.Tensor, kv_cache=False):\n", "        bsz, seqlen, _ = x.shape\n", "\n", "        # 通过线性层\n", "        xq, xk, xv = self.wq(x), self.wk(x), self.wv(x)\n", "\n", "        xq = xq.view(bsz, seqlen, self.n_local_heads, self.head_dim)\n", "        xk = xk.view(bsz, seqlen, self.n_local_kv_heads, self.head_dim)\n", "        xv = xv.view(bsz, seqlen, self.n_local_kv_heads, self.head_dim)\n", "\n", "        # 旋转位置编码\n", "        xq, xk = apply_rotary_emb(xq, xk, pos_cis)\n", "\n", "        # 如果使用kv缓存的话\n", "        if kv_cache and self.eval():\n", "            # 如果现在序列长度==1，且之前已经保存了k_cache和v_cache\n", "            if seqlen == 1 and all(cache is not None for cache in (self.k_cache, self.v_cache)):\n", "                #就进行合并\n", "                xk = torch.cat((self.k_cache, xk), dim=1)\n", "                xv = torch.cat((self.v_cache, xv), dim=1)\n", "            # 如果序列长度不等于1，说明现在是第一次推理，t=0\n", "            # 那就直接保存生成的k和v作为缓存\n", "            self.k_cache, self.v_cache = xk, xv\n", "\n", "```"]}, {"cell_type": "markdown", "id": "b9bc905c-1c1e-4b30-97c7-a8771dc4df91", "metadata": {}, "source": ["- **进一步探讨：为什么只缓存KV不缓存Q？**"]}, {"cell_type": "markdown", "id": "b0654dae-bfe1-42f0-bb9e-223647dda548", "metadata": {}, "source": ["在进行推理时，由于我们只需要不断预测下一个token、因此我们事实上只需要QK.T矩阵的最后一行。"]}, {"cell_type": "markdown", "id": "8584c6b4-9a49-4b29-a0c0-a33de734d274", "metadata": {}, "source": ["$$\n", "\\text{QK.T after casual masked} = \\begin{bmatrix}\n", "       \\boldsymbol{\\color{green}{1}}\\cdot\\boldsymbol{\\color{green}{1}} & 0 & 0 & 0 & 0 & 0\\\\\n", "       \\boldsymbol{\\color{green}{2}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{2}} \\cdot \\boldsymbol{\\color{green}{2}} & 0 & 0& 0 & 0\\\\\n", "       \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{2}} & \\boldsymbol{\\color{green}{3}} \\cdot \\boldsymbol{\\color{green}{3}} & 0& 0 & 0\\\\\n", "       \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{2}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{3}} & \\boldsymbol{\\color{green}{4}} \\cdot \\boldsymbol{\\color{green}{4}} & 0 & 0 \\\\\n", "       \\boldsymbol{\\color{green}{5}} \\cdot \\boldsymbol{\\color{green}{1}} & \\boldsymbol{\\color{green}{5}} \\cdot \\boldsymbol{\\color{green}{2}} & \\boldsymbol{\\color{green}{5}} \\cdot \\boldsymbol{\\color{green}{3}} & \\boldsymbol{\\color{green}{5}} \\cdot \\boldsymbol{\\color{green}{4}} & \\boldsymbol{\\color{green}{5}} \\cdot \\boldsymbol{\\color{green}{5}} & 0 \\\\\n", "       \\boldsymbol{\\color{red}{6}} \\cdot \\boldsymbol{\\color{red}{1}} & \\boldsymbol{\\color{red}{6}} \\cdot \\boldsymbol{\\color{red}{2}} & \\boldsymbol{\\color{red}{6}} \\cdot \\boldsymbol{\\color{red}{3}} & \\boldsymbol{\\color{red}{6}} \\cdot \\boldsymbol{\\color{red}{4}} & \\boldsymbol{\\color{red}{6}} \\cdot \\boldsymbol{\\color{red}{5}} & \\boldsymbol{\\color{red}{6}} \\cdot \\boldsymbol{\\color{red}{6}}\n", "\\end{bmatrix} ==> token2\n", "$$"]}, {"cell_type": "markdown", "id": "9b658296-1221-40e3-8b28-a2da525af06a", "metadata": {}, "source": ["如果我们让注意力机制转变为让最新的一行输出、而忽略其他历史信息，因此实际上我们可以只计算——\n", "\n", "1. **新token的Q, K, V**——\n", "\n", "        从 `X` 线性变换得到：\n", "   $$\n", "   Q = X_{new} W_Q, \\quad K = X_{new} W_K, \\quad V = X_{new} W_V\n", "   $$\n", "\n", "    其中$X_{new}$仅包含当前新加入的 token（通常是 $1$ 个 token）。\n", "<br>\n", "\n", "2. **为QK加上位置编码**\n", "    $$\n", "    Q = ROPE(Q_{new}), \\quad K = ROPE(K_{new})\n", "    $$\n", "   - 只需要对$K_{new}$做位置编码，之前缓存的K矩阵是带位置编码的。\n", "\n", "<br>\n", "\n", "3. **拼接已有的 KV 缓存**：\n", "   $$\n", "   K = \\text{concat}(K_{\\text{cache}}, K_{\\text{new}}), \\quad V = \\text{concat}(V_{\\text{cache}}, V_{\\text{new}})\n", "   $$\n", "   - $K_{\\text{cache}}$ 和 $V_{\\text{cache}}$ 直接从缓存中取出，无需重新计算。\n", "\n", "<br>\n", "\n", "4. **计算单一token的QK.T**\n", "   $$\n", "   QK.T = \\text{matmul}(Q_{new}, K.T)\n", "   $$"]}, {"cell_type": "markdown", "id": "a83b78b4-fe5c-445c-9285-eb67308c7a6d", "metadata": {}, "source": ["在我们的推理流程中，我们也会去实现类似的代码 ↓"]}, {"cell_type": "markdown", "id": "212d6118-8c97-44cb-a9ef-2ce0275804bd", "metadata": {}, "source": ["```python\n", "# 记录新token开始的位置\n", "prev_pos = 0\n", "\n", "# 按序列长度进行循环，例如输入序列为10个token，则cur_pos第一个值为10\n", "\n", "for cur_pos in range(min(prompt_lens), max_len):\n", "    \n", "    # 每次只输入[prev_pos:cur_pos]之间的样本\n", "    # t=0时，输入的是10个token的序列\n", "    # t=1时，输入的就是序列索引为[10:11]的部分\n", "    # t=2时，输入的是序列索引为[11:12]的部分\n", "    # 除了第一次外、仅仅输入最新生成的token\n", "    logits = model.forward(tokens[:, prev_pos:cur_pos], prev_pos)\n", "    \n", "    # 用argmax取出概率最高的值\n", "    next_token = logits.argmax(dim=-1)\n", "\n", "    # 把取出的最新的值赋值给原本的token\n", "    tokens[:, cur_pos] = next_token\n", "    \n", "    # 更新 prev_pos，t=0时prev_pos=10\n", "    # t=1时 prev_pos = 11，以此类推\n", "    prev_pos = cur_pos  \n", "```"]}, {"cell_type": "markdown", "id": "1f44c4e0-e0f6-4a62-9a1c-803dbb321c06", "metadata": {}, "source": ["### 2.2 低秩KV缓存与代码实现"]}, {"cell_type": "markdown", "id": "18d78e59-ccdc-4a8e-83db-68ce9b0a263a", "metadata": {}, "source": ["KV缓存是典型的“牺牲内存加速运算”、对于不得不使用循环的推理流程来说确实是一个可靠的方案，然而它对内存的占用也是不可忽略的。DeepSeek所提出的“潜在注意力机制”就是尝试降低KV缓存所需的内存、同时也不损失太多精度的方案。\n", "\n", "有哪些降低所需存储空间的基本方法呢？对于一个矩阵来说，要降低其所需的存储空间，要么就要缩小其尺寸压缩其信息、要么就降低其精度同时压缩其信息。对于潜在注意力机制而言，它选择的是“缩小尺寸、压缩信息”的方案，**简单来说就是“先缩小再放大、中间损失忽略不计”的基本逻辑**。\n", "\n", "<font color=\"red\">**首先我们来看DeepSeek对K矩阵和V矩阵执行的操作——**\n", "\n", "- **1. 使用低秩投影（Low-rank Projection）让原始的X序列矩阵变成小尺寸矩阵C、将带有原始信息的小尺寸矩阵存储起来**。这个方案不仅将原本需要保存的K、V两个矩阵转化为了一个携带原始信息的矩阵C，还降低了需要存储的矩阵的维度，因此大幅降低存储所需的内存。<br><br>\n", "- **2. 在原始的大尺寸X矩阵上计算位置编码、并将该大尺寸位置编码存储起来**，但由于位置编码只需要“薄薄的表单”就可以存储，因此几乎不占用内存。<br><br>\n", "- **3. 在需要计算的时候、再从小尺寸矩阵C分别投影回大尺寸矩阵K和V，将大尺寸K结合位置编码后、基于大尺寸的K和V进行注意力计算**，这样即可以保留足够的信息量、又可以存储较小的信息。\n", "\n", "这个过程就是低秩KV缓存，具体来看 ↓"]}, {"cell_type": "markdown", "id": "ea2b9b68-a7db-4c2e-b066-1416016eaa70", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/103_.png)"]}, {"cell_type": "markdown", "id": "cdfddd9e-f21d-4f1e-befc-e86065e27d7b", "metadata": {}, "source": ["从数学公式来看，就是如下流程——"]}, {"cell_type": "markdown", "id": "ba7a3523-1733-464f-a2a9-e1ca3746cc3c", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/104_.png)"]}, {"cell_type": "markdown", "id": "396e4c9f-9cdb-4088-9a49-bd4cb3c7e1fa", "metadata": {}, "source": ["在这个过程中，**低秩转换实际上就是线性投影**，将原本`(seq_len, 2048)`结构的矩阵通过线性层投影为`(seq_len, 512)`维的矩阵C就可以存储起来。同时，用于保存位置编码的矩阵可能只有`(seq_len, 64)`尺寸大小，这样需要保存的矩阵就比原来需要的 `2 x (seq_len, 2048)`要小得多了，比原来节省了75%的存储成本。并且，由于C变小、因此GPU带宽压也会变得更小，在数据交换过程中变得更加容易。"]}, {"cell_type": "markdown", "id": "834ed9d7-b909-4850-9708-b3d80d55bc23", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "45c8e60e-6f5f-4765-9c24-d5b8c3fcca81", "metadata": {}, "source": ["<font color=\"red\">**对于Q矩阵，DeepSeek也支持完成低秩处理——**"]}, {"cell_type": "markdown", "id": "dbac537c-5d1b-4468-bc5b-b3568889563c", "metadata": {}, "source": ["- **1. 使用低秩投影（Low-rank Projection）让原始的X序列矩阵变成小尺寸矩阵C**。不过并不保存任何的中间变量。<br><br>\n", "- **2. 在原始的大尺寸X矩阵上计算位置编码**，但同样的，不保存任何变量。<br><br>\n", "- **3. 在需要计算的时候、再从小尺寸矩阵C投影回大尺寸的Q，将大尺寸的Q结合位置编码、再用大尺寸的Q进行注意力计算**。"]}, {"cell_type": "markdown", "id": "d9c8abc1-2503-4c70-a3b1-3ba7e04ebe92", "metadata": {}, "source": ["不难发现，这个过程其实和K的处理过程一模一样，因此其数学过程也是 ↓\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/105_.png)"]}, {"attachments": {}, "cell_type": "markdown", "id": "51f50239-215a-400a-8e1c-0aedac562dd6", "metadata": {}, "source": ["**在训练过程中**、如果我们对Q也执行低秩转换、可以帮助我们减少少许训练中所需的**激活值内存**。一般来说，激活值（Activation）是指向前传播中计算出的中间结果，这些指在反向传播时被用于梯度计算，这些激活值包括了——\n", "\n", "  - 线性层的中间输出\n", "  - 注意力计算时的 `Q`、`K`、`V` 矩阵\n", "  - softmax 计算出的注意力分数\n", "  - 反向传播需要用到的梯度信息\n", "\n", "对于大模型来说，激活值占用了训练过程中相当大的一部分 GPU 内存，特别是在注意力层（self-attention）里，`Q`、`K`、`V` 矩阵和注意力分数矩阵（大小为 `(batch, heads, seq_len, seq_len)`）都需要存储，导致内存消耗极大。\n", "\n", "**不过在推理过程中，Q做低秩缓存没有太大的意义，因此推理过程中一般没有Q的低秩缓存**。"]}, {"cell_type": "markdown", "id": "ff62cd63-de07-41d4-a742-27908d43ff80", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "5912c0d0-9de3-4f42-801c-89d63379b72a", "metadata": {}, "source": ["- **具体的代码实现**"]}, {"cell_type": "markdown", "id": "1e5bbd0a-618c-4690-b411-572a9efde4b8", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/103_.png)"]}, {"cell_type": "markdown", "id": "f3d92a77-6b90-4ddf-9861-009586a028e6", "metadata": {}, "source": ["在这个过程中，位置编码链路和低秩矩阵压缩链路看起来是两条数据流，但实际代码在实现DeepSeek却只使用了一个线性层。我们是规定好低秩压缩的维度、以及用于承载旋转位置编码的矩阵所需要的维度后，使用一个线性层一次性将原始数据X（或中间值h）转换为所需的矩阵，再将两个矩阵分开做各自的运算。"]}, {"cell_type": "markdown", "id": "0aa4b64f-a52b-448d-ae8e-4335b3ab3e96", "metadata": {"collapsed": true, "jupyter": {"outputs_hidden": true}}, "source": ["```python\n", "import torch\n", "import torch.nn as nn\n", "import math\n", "from typing import Optional\n", "\n", "#############\n", "# Multi-Headed Attention Layer (MLA)\n", "# 多头注意力层（MLA），支持低秩投影、旋转位置编码、缓存优化\n", "#############\n", "class MLA(nn.<PERSON><PERSON><PERSON>):\n", "    \"\"\"\n", "    Multi-Headed Attention Layer (MLA).\n", "\n", "    Attributes:\n", "        dim (int): 输入特征的维度\n", "        n_heads (int): 注意力头数\n", "        n_local_heads (int): 分布式计算下的本地注意力头数\n", "        q_lora_rank (int): 低秩查询投影的秩\n", "        kv_lora_rank (int): 低秩键值投影的秩\n", "        qk_nope_head_dim (int): 非旋转位置编码的查询/键特征维度\n", "        qk_rope_head_dim (int): 旋转位置编码的查询/键特征维度\n", "        qk_head_dim (int): 查询/键的总特征维度\n", "        v_head_dim (int): 值（Value）投影的维度\n", "        softmax_scale (float): softmax 计算的缩放因子\n", "    \"\"\"\n", "    def __init__(self, args: ModelArgs):\n", "        super().__init__()\n", "        \n", "        #############\n", "        # 基本参数初始化\n", "        #############\n", "\n", "        # 模型并行：将不同的注意力头放在不同的线程上\n", "        self.dim = args.dim\n", "        self.n_heads = args.n_heads\n", "        self.n_local_heads = args.n_heads // world_size\n", "\n", "        # Q矩阵的低秩维度\n", "        self.q_lora_rank = args.q_lora_rank\n", "        \n", "        # K和V矩阵的低秩维度\n", "        self.kv_lora_rank = args.kv_lora_rank\n", "\n", "        # QK矩阵不需要位置编码的维度\n", "        self.qk_nope_head_dim = args.qk_nope_head_dim\n", "\n", "        # QK矩阵需要位置编码的维度\n", "        self.qk_rope_head_dim = args.qk_rope_head_dim\n", "\n", "        # 每个头上最终QK矩阵的维度\n", "        self.qk_head_dim = args.qk_nope_head_dim + args.qk_rope_head_dim\n", "\n", "        # 每个头上最终V矩阵的维度\n", "        self.v_head_dim = args.v_head_dim\n", "        \n", "        #############\n", "        # 定义计算 Query, Key, Value 的各种层\n", "        # 包括下采样/低秩投影层\n", "        # 上采样/反向投影层\n", "        # 归一化层\n", "        \n", "        # 如果 q_lora_rank = 0，则对Q使用标准的全维投影\n", "        # 否则使用低秩投影 LoRA 计算 Query\n", "        #############\n", "        if self.q_lora_rank == 0:\n", "            self.wq = ColumnParallelLinear(self.dim, self.n_heads * self.qk_head_dim)\n", "        else:\n", "            self.wq_a = Linear(self.dim, self.q_lora_rank)\n", "            self.q_norm = RMSNorm(self.q_lora_rank)\n", "            self.wq_b = ColumnParallelLinear(self.q_lora_rank, self.n_heads * self.qk_head_dim)\n", "        \n", "        # 低秩投影计算 Key/Value\n", "        self.wkv_a = Linear(self.dim, self.kv_lora_rank + self.qk_rope_head_dim)\n", "        self.kv_norm = RMSNorm(self.kv_lora_rank)\n", "        self.wkv_b = ColumnParallelLinear(self.kv_lora_rank, self.n_heads * (self.qk_nope_head_dim + self.v_head_dim))\n", "        \n", "        # 输出投影层\n", "        self.wo = RowParallelLinear(self.n_heads * self.v_head_dim, self.dim)\n", "\n", "        # 计算 Softmax 归一化缩放因子\n", "        self.softmax_scale = self.qk_head_dim ** -0.5\n", "        if args.max_seq_len > args.original_seq_len:\n", "            mscale = 0.1 * args.mscale * math.log(args.rope_factor) + 1.0\n", "            self.softmax_scale = self.softmax_scale * mscale * mscale\n", "\n", "        #############\n", "        # 注意力缓存管理\n", "        # - naive 版本：虽然进行低秩操作、但是不保存低秩矩阵，而使用经典的KV缓存\n", "        # - absorb 版本：使用低秩缓存\n", "        # - register_buffer: 注册不需要梯度更新的参数\n", "        #############\n", "        if attn_impl == \"naive\":\n", "            self.register_buffer(\"k_cache\", torch.zeros(args.max_batch_size, args.max_seq_len, self.n_local_heads, self.qk_head_dim), persistent=False)\n", "            self.register_buffer(\"v_cache\", torch.zeros(args.max_batch_size, args.max_seq_len, self.n_local_heads, self.v_head_dim), persistent=False)\n", "        else:\n", "            self.register_buffer(\"kv_cache\", torch.zeros(args.max_batch_size, args.max_seq_len, self.kv_lora_rank), persistent=False)\n", "            self.register_buffer(\"pe_cache\", torch.zeros(args.max_batch_size, args.max_seq_len, self.qk_rope_head_dim), persistent=False)\n", "\n", "    #############\n", "    # 前向传播计算\n", "    #############\n", "    def forward(self, x: torch.Tensor, start_pos: int, freqs_cis: torch.Tensor, mask: Optional[torch.Tensor]):\n", "        \"\"\"\n", "        Args:\n", "            x (torch.Tensor): 输入张量 (batch_size, seq_len, dim)\n", "            start_pos (int): 用于追踪当前token所在的位置，是推理中每一轮新token的起始位置\n", "            freqs_cis (torch.Tensor): 预计算的旋转位置编码\n", "            mask (Optional[torch.Tensor]): 用于屏蔽某些位置的掩码\n", "        \n", "        Returns:\n", "            torch.Tensor: 计算后的输出张量，形状与输入相同\n", "        \"\"\"\n", "        bsz, seqlen, _ = x.size()\n", "        end_pos = start_pos + seqlen\n", "\n", "        #############\n", "        # 计算 Query\n", "        #############\n", "        if self.q_lora_rank == 0:\n", "            q = self.wq(x)\n", "        else:\n", "            q = self.wq_b(self.q_norm(self.wq_a(x)))\n", "\n", "        # 改变结构后进行分割、只给少部分赋予旋转位置编码\n", "        q = q.view(b<PERSON>, seqlen, self.n_local_heads, self.qk_head_dim)\n", "        q_nope, q_pe = torch.split(q, [self.qk_nope_head_dim, self.qk_rope_head_dim], dim=-1)\n", "        q_pe = apply_rotary_emb(q_pe, freqs_cis)\n", "\n", "        #############\n", "        # 计算 Key/Value\n", "        #############\n", "        # 低秩投影\n", "        kv = self.wkv_a(x)\n", "        # 将矩阵分割为小矩阵C和位置编码矩阵\n", "        kv, k_pe = torch.split(kv, [self.kv_lora_rank, self.qk_rope_head_dim], dim=-1)\n", "        k_pe = apply_rotary_emb(k_pe.unsqueeze(2), freqs_cis)\n", "\n", "        #############\n", "        # 计算注意力得分并应用 Softmax\n", "        #############\n", "        if attn_impl == \"naive\":\n", "            # 合并Q和Q的位置编码\n", "            q = torch.cat([q_nope, q_pe], dim=-1)\n", "\n", "            # 对小矩阵进行归一化、并放大\n", "            kv = self.wkv_b(self.kv_norm(kv))\n", "\n", "            # 改变大矩阵的结构\n", "            kv = kv.view(bsz, seqlen, self.n_local_heads, self.qk_nope_head_dim + self.v_head_dim)\n", "\n", "            # 从大矩阵中分割出K和V\n", "            k_nope, v = torch.split(kv, [self.qk_nope_head_dim, self.v_head_dim], dim=-1)\n", "\n", "            # 让K与旋转位置编码合并\n", "            k = torch.cat([k_nope, k_pe.expand(-1, -1, self.n_local_heads, -1)], dim=-1)\n", "\n", "            # 在缓存的k_cache和v_cache中添加新token的值\n", "            self.k_cache[:bsz, start_pos:end_pos] = k\n", "            self.v_cache[:bsz, start_pos:end_pos] = v\n", "            # 求解QK.T以及softmax部分，爱因斯坦求和法、适用于高效矩阵运算\n", "            scores = torch.einsum(\"bshd,bthd->bsht\", q, self.k_cache[:bsz, :end_pos]) * self.softmax_scale\n", "        \n", "        else:\n", "            # 如果是absorb模式，则使用低秩kv缓存\n", "            # 对反投影矩阵进行量化处理\n", "            wkv_b = self.wkv_b.weight if self.wkv_b.scale is None else weight_dequant(self.wkv_b.weight, self.wkv_b.scale, block_size) \n", "            wkv_b = wkv_b.view(self.n_local_heads, -1, self.kv_lora_rank)\n", "            \n", "            # 计算注意力分数时，用wkv_b回复大矩阵\n", "            q_nope = torch.einsum(\"bshd,hdc->bshc\", q_nope, wkv_b[:, :self.qk_nope_head_dim])\n", "\n", "            # 缓存的是归一化后的C以及位置编码矩阵\n", "            self.kv_cache[:bsz, start_pos:end_pos] = self.kv_norm(kv)\n", "            self.pe_cache[:bsz, start_pos:end_pos] = k_pe.squeeze(2)\n", "\n", "            # 求解QK.T以及softmax部分，用wkv_b恢复为大矩阵\n", "            scores = (torch.einsum(\"bshc,btc->bsht\", q_nope, self.kv_cache[:bsz, :end_pos]) +\n", "                      torch.einsum(\"bshr,btr->bsht\", q_pe, self.pe_cache[:bsz, :end_pos])) * self.softmax_scale\n", "\n", "        # 前瞻掩码\n", "        if mask is not None:\n", "            scores += mask.unsqueeze(1)\n", "        scores = scores.softmax(dim=-1, dtype=torch.float32).type_as(x)\n", "\n", "        #############\n", "        # 计算最终输出\n", "        #############\n", "        if attn_impl == \"naive\":\n", "            x = torch.einsum(\"bsht,bthd->bshd\", scores, self.v_cache[:bsz, :end_pos])\n", "        else:\n", "            x = torch.einsum(\"bsht,btc->bshc\", scores, self.kv_cache[:bsz, :end_pos])\n", "            x = torch.einsum(\"bshc,hdc->bshd\", x, wkv_b[:, -self.v_head_dim:])\n", "        \n", "        x = self.wo(x.flatten(2))\n", "        return x\n", "\n", "```"]}, {"cell_type": "markdown", "id": "871dd176-4a1e-4b98-bb87-619f1db0a572", "metadata": {}, "source": ["## 3 DeepSeekMOE"]}, {"cell_type": "markdown", "id": "7fd67984-75aa-4096-96aa-0d982162d17c", "metadata": {}, "source": ["门控是一种经典的深度学习机制。许多时候，我们使用线性层来“解读”原始信息，**在原始信息上乘以一个常数、以常数的大小控制原始信息向下一层的流通，这被称为“门控机制”，控制常数的层被称之为“门”**。\n", "\n", "**当我们有多个线性层对信息进行不同的“解读”，同时又赋予每个信息对应的门时**，这样的模型被称为混合专家模型——"]}, {"cell_type": "markdown", "id": "f9184aae-15a6-4fab-84ba-9397ac8234d1", "metadata": {}, "source": ["<center><img src=\"https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/16.png\" alt=\"描述文字\" width=\"400\">\n", "\n", "当我们使用多个门同时控制信息的流通时，这些多个门构成的结构被称为是**路由器**。如图所示，**混合专家模型 (MoE)** 是一种**动态路由**策略，通过为不同的输入选择不同的子模型（专家模型）进行计算。相比于传统的全连接前馈网络（FFN），**MoE 在每次前向传播时只激活部分专家模型**，从而实现参数高效和计算高效。\n", "\n", "MoE 已经广泛应用于大规模自然语言处理模型（如 GShard、Switch Transformer 等），并在计算机视觉等领域表现出色。其核心优势在于：**模型具有超大参数量**，但每次推理或训练时，只使用部分参数来进行计算。这是为什么针对DeepSeek这样的模型，我们会有全量参数和激活参数的区别。"]}, {"cell_type": "markdown", "id": "16e949d4-50e7-44b7-be75-45fb7cc6bc0a", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/106.png)"]}, {"cell_type": "markdown", "id": "6910aa76-ac94-4322-8fef-1318b854408f", "metadata": {}, "source": ["### 3.1 经典MOE模型速览"]}, {"cell_type": "markdown", "id": "4342a28e-0672-4a68-9f3b-e07c276fe51a", "metadata": {}, "source": ["在混合专家模型的世界中、我们可以针对一个序列、一张表单或任意单独的一段信息设置一扇“门”（一个权重），我们也可以针对每一个token设置一个权重，**大部分用于大模型的MOE使用的是token-level的路由设置**。当我们将文字序列输入MOE时，常规的数据流如下所示 ↓ \n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/107_.png)"]}, {"cell_type": "markdown", "id": "9ffacd52-18a5-4720-9c90-947ccdccec86", "metadata": {}, "source": ["当上述流程转换为**数学公式**时，我们则可以有如下的表示 ↓\n", "\n", "假设：\n", "- 假设输入向量为 `u_t`，`u_t` 是 token `t` 经过注意力机制后的隐藏状态（图中的数据X是完整的携带所有token的矩阵，`u_t`则是一个token的向量）\n", "- 共有 `N` 个专家，每个专家都是一个带有门控的前馈网络（SwiGLU FFN），也就是至少会含有3个线性层。\n", "- 门控网络（路由器）是一个线性层，其结构是(dim, n_experts)。\n", "- 只有 `K` 个专家会被选择（Top-K 机制）。"]}, {"cell_type": "markdown", "id": "d3c1feec-2330-43f4-ad82-1f43f92441ec", "metadata": {}, "source": ["根据上述假设，混合专家模型的一般流程为：\n", "\n", "- **1. 通过门控计算专家的权重分数 `g'_{i,t}`**\n", " \n", "   - 将$u_t$输入门控网络，通过门控网络（Gating Network）计算 token 对每个专家的偏好：\n", "     $$\n", "     g'_{i,t} = \\text{Softmax}(W_g u_t)\n", "     $$\n", "   - 其中 `W_g` 是门控网络的权重矩阵，$i$则代表特定的专家。"]}, {"cell_type": "markdown", "id": "00c48db3-9880-4514-969f-fa4925c1151a", "metadata": {}, "source": ["- **2. 选出 Top-K 最高分的专家**\n", "  \n", "   - 选择分数最高的 `K` 个专家，并将其他专家的权重通过掩码设为 `0`：\n", "     $$\n", "     g_{i,t} =\n", "     \\begin{cases}\n", "     g'_{i,t}, & i \\in \\text{Top-K}(g'_{i,t}) \\\\\n", "     0, & \\text{otherwise}\n", "     \\end{cases}\n", "     $$\n", "   - 这样可以确保每个 token 只经过 `K` 个专家。"]}, {"cell_type": "markdown", "id": "60eaa50e-3db1-4b79-acca-9a4fab96278f", "metadata": {}, "source": ["- **3. 计算被选中的专家输出**\n", "\n", "   - 令 `FFN_i(·)` 表示第 `i` 个专家的前馈网络：\n", "     $$\n", "     h'_t = \\sum_{i \\in \\text{Top-K}} g_{i,t} FFN_i(u_t)\n", "     $$\n", "   - 这里 `FFN_i(u_t)` 是`u_t` 被输入专家 `i`的输出结果。\n", "   - `g_{i,t}` 作为权重调整不同专家的贡献。\n", "   - `h'_t` 作为当前层 MoE 的输出，将传递到 Transformer 的下一层。"]}, {"cell_type": "markdown", "id": "f9dadb04-cd77-4460-b5b0-251a69a54b91", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "c49d8993-1f30-495e-a360-d7ec15f17da3", "metadata": {}, "source": ["### 3.2 DeepSeekMOE的基本流程"]}, {"cell_type": "markdown", "id": "269f89ae-328c-4675-98b8-f8c2bdbeb0d0", "metadata": {}, "source": ["DeepSeekMoE（DeepSeek Mixture of Experts）是一种改进的 MoE（Mixture of Experts）架构，它从GShared等已经优化过的MoE架构上改造而来，又做出了许多细致的优化。从今天的眼光来看，DeepSeekMoE流程与常规MoE的区别包括以下的3点——\n", "\n", "<font color=\"green\">**1. 引入了共享专家（Shared Experts）机制**</font>：为模型增加了固定必须要通过的专家、以确保MoE模型的学习能力。\n", "\n", "<font color=\"green\">**2. 进行了更细粒度的专家划分（Fine-Grained Experts Segmentation）**</font>：具体地来说——\n", "> - 将每个专家的**FFN隐藏层维度减少为原始大小的1/m倍**，实现更小、更细粒度的专家<br><br>\n", "> - 专家变得更小后，每个专家的计算能力会大幅下降，因此为了补偿专家能力的下降、**提出了“分组”技术**，以前由TopK机制选择K个专家，现在则是选择K组专家，允许每组中存在m个小专家，一组专家被赋予一个权重<br><br>\n", "> - 这样就大幅增加了专家数量、但涉及的总参数量和计算量不变，相当于将大的专家拆分成更多的小专家，同时也允许更多的小专家被激活，这样保证了计算成本一致，同时也增强了模型在处理多样化知识时的能力。<br><br>\n", "> - 出自论文《DeepSeekMOE：Towards Utimate Expert Specialization in MoE language Models》[https://arxiv.org/abs/2401.06066] <br><br>\n", "> ![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/108.png)\n", "\n", "<font color=\"green\">**3. 将原始的softmax函数修改为Sigmoid函数**：</font>让各个专家之间的计算变得独立、更便于进行并行计算，同时让专家之间的概率不再相互影响之后，softmax带来的概率可能是更接近“1高多0”的情况，而sigmoid带来的概率会更加显著（有效的专家与无效的专家之间的差异更加明显），因此更有可能进一步让更多的专家同时被激活，避免了 Softmax 归一化导致的极端情况（即过度偏向某几个专家）。"]}, {"cell_type": "code", "execution_count": 13, "id": "2df0b115-585e-4386-92c1-9b732bef5f02", "metadata": {"jupyter": {"source_hidden": true}}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAjgAAANYCAYAAADJ9pcYAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjguMiwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy8g+/7EAAAACXBIWXMAAAsTAAALEwEAmpwYAAEAAElEQVR4nOydd3QU5dfHvzch9CYEkBqKdAjEAHZERZqAIir4omJDxR+KXRELltgVGxYEEQERREFQEKRICR1Feu9FqdJDSHLfP+4O2Wy278zO7uZ+zpmz2dnZZ+5OptznVmJmKIqiKIqixBJxdgugKIqiKIpiNqrgKIqiKIoSc6iCoyiKoihKzKEKjqIoiqIoMYcqOIqiKIqixByq4CiKoiiKEnOogqMoSsxBRIOIaLTdciiKYh+q4CiKYhpEdCURLSSiY0R0hIjSiail3XIFAhH1I6LlRHSWiL6xWx5FUYKjkN0CKIoSGxBRaQC/AOgLYDyAwgCuAnDWTrmCYB+A1wG0B1DMZlkURQkSteAoimIW9QCAmccyczYzn2HmGcy8CgCIqA4RzSaiw0R0iIjGEFFZ48tEtIOIniaiVUR0ioiGE1ElIppGRCeIaCYRXeDYtiYRMRE9QET7iGg/ET3lSTAiutRhWfqPiP4mojaetmXmn5h5EoDDphwVRVFsQRUcRVHMYhOAbCIaSUQdDWXECQLwJoAqABoCqA5gkMs23QFcD1GWugCYBuB5ABUg96tHXba/BkBdAO0APEtEbV2FIqKqAH6FWGXKAXgKwI9EVCG4n6koSjSgCo6iKKbAzMcBXAmAAXwF4CARTSaiSo7PtzDz78x8lpkPAvgAwNUuw3zCzP8y814A8wEsYea/mDkDwEQAKS7bv8LMp5h5NYARAG53I9odAKYy81RmzmHm3wEsB9DJnF+uKEokogqOoiimwczrmfluZq4GoAnEWvMhADjcTd8T0V4iOg5gNIBElyH+dfr7jJv3JV223+30907H/lxJAnCrwz31HxH9B1HEKgf04xRFiSpUwVEUxRKYeQOAbyCKDgC8AbHuNGXm0hDLCoW4m+pOf9eABAi7shvAKGYu67SUYOa3Qty3oigRjCo4iqKYAhE1IKIniaia4311iMtosWOTUgBOAjjmiIt52oTdvkhExYmoMYB7AIxzs81oAF2IqD0RxRNRUSJqY8jp5ncUIqKiAOIBGNtrxqmiRBmq4CiKYhYnAFwCYAkRnYIoNmsAPOn4/BUAFwM4Bgn6/cmEfc4FsAXALADvMfMM1w2YeTeAGyHBygchFp2n4fn+9wLEHfYcxMp0xrFOUZQogpjZbhkURVECgohqAtgOIIGZs2wWR1GUCEQtOIqiKIqixByq4CiKoiiKEnOoi0pRFEVRlJhDLTiKoiiKosQcUZf6mJiYyDVr1rRbDEVRFEVRIoAVK1YcYuZ8rVeiTsGpWbMmli9fbrcYiqIoiqJEAES00916dVEpiqIoihJzqIKjKIqiKErMoQqOoiixxZgxQM2aQFycvI4ZY7dEiqLYQNTF4Ljj3Llz2LNnDzIyMuwWJSooWrQoqlWrhoSEBLtFURRzGTMGeOAB4PRpeb9zp7wHgF697JNLUZSwE3V1cFq0aMGuQcbbt29HqVKlUL58eRCF2pw4tmFmHD58GCdOnECtWrXsFkdRzKVmTVFqXElKAnbsCLc0iqKEASJawcwtXNfHhIsqIyNDlRs/ISKUL19erV1KbLJrV2DrFUWJWWJCwQGgyk0A6LFSYpYaNQJbryhKzBIzCo6iKArS0oBixfKuK15c1iuKUqBQBcdE0tLS0LhxYyQnJ6N58+ZYsmSJx23nz5+Pxo0bo3nz5li0aBGmTp0aRkkVJUbp1Qt45pnc9xdeCAwdqgHGilIAKZAKjhVZpIsWLcIvv/yCP//8E6tWrcLMmTNRvXp1LzKMwYABA7By5Ups3LhRFRzFHDRFGrjooty/331XlRtFKaDERJp4IFiVRbp//34kJiaiSJEiAIDExEQAwKxZs/DUU08hKysLLVu2xOeff45Ro0Zh/PjxmD59On799Vekp6fjzJkzWLBgAQYMGID169dj+/bt2LZtG3bt2oXBgwdj8eLFmDZtGqpWrYopU6YgISEBr776KqZMmYIzZ87g8ssvx5dffons7GxcdtllePfdd9GmTRsMGDAAcXFxSFMTfeyjKdLC1q0AkSh569fbLY2iKHbBzFG1pKamsivr1q07/3f//sxXX+15KVKEGci/FCni+Tv9++fbZT5OnDjBzZo147p163Lfvn35jz/+4DNnznC1atV448aNzMx855138uDBg5mZuXfv3vzDDz8wM/OIESP4f//73/mxXn75Zb7iiis4MzOTV65cycWKFeOpU6cyM/NNN93EEydOZGbmw4cPn//OHXfcwZMnT2Zm5jVr1nCDBg34999/5+bNm/PZs2e9HjMlRkhKcn9yJyXZLVl4ufNO5mrVmOvXZ77pJrulURTFYgAsZzf6QoFzUZ09G9h6fylZsiRWrFiBoUOHokKFCujRowe+/PJL1KpVC/Xq1QMA9O7dG/PmzfNrvI4dOyIhIQFNmzZFdnY2OnToAABo2rQpdjjqecyZMweXXHIJmjZtitmzZ2Pt2rUAgMaNG+POO+9E586d8fXXX6Nw4cKh/TglOtAUaWHrVqBOHaBRI7XgKEoBJuZcVB9+6P1zb3XA/vgjtH3Hx8ejTZs2aNOmDZo2bYohQ4YEPZbh6oqLi0NCQsL51O64uDhkZWUhIyMDDz/8MJYvX47q1atj0KBBeWrbrF69GmXLlsWBAwdC+1FK9FCjhvuTu6ClSG/bBnTqJAHGkycDmZmAKvmKUuAocBactDTJGnXGjCzSjRs3YvPmzeffr1y5EnXq1MGOHTuwZcsWAMCoUaNw9dVX5/tuqVKlcOLEiYD2ZygziYmJOHnyJCZMmHD+s59++glHjhzBvHnz8Mgjj+C///4L4hcpUUdaGlDIZc5S0FKkT50C/vlHLDgNGwLZ2YDj+lMUpWBR4BScXr0kazQpSeIQk5LMySI9efIkevfujUaNGiE5ORnr1q3DW2+9hREjRuDWW29F06ZNERcXh4ceeijfd6+55hqsW7cOzZs3x7hx4/zaX9myZdGnTx80adIE7du3R8uWLQEAhw4dwnPPPYdhw4ahXr166NevH/r37x/aj1Oigx49pAaMw/qHsmULXor0tm3yarioAGDdOvvkURTFNiztRUVEHQB8BCAewDBmfsvl8xoARgIo69jmOWb2mi/trhfV+vXr0bBhQxMlj330mMUgv/8OtGsH/Pgj0K8fcP31wMiRdksVXiZNArp1A5YtEwtOyZLAq68CL75ot2SKolhE2HtREVE8gCEAOgJoBOB2ImrkstkLAMYzcwqAngA+s0oeRYl5xo+XB3rHjkCzZsCqVXZLFH62bpXXOnWAEiXERKuBxopSILHSRdUKwBZm3sbMmQC+B3CjyzYMoLTj7zIA9lkoj6LELufOAT/9BNx4o7ipkpPFNXPunN2ShZetW4ELLpAFECuOKjiKUiCxUsGpCmC30/s9jnXODAJwBxHtATAVwCPuBiKiB4hoOREtP3jwoBWyKkp0M3MmcOQIcNtt8j45WbKHNm60V65ws3UrULt27vtGjYANGyTYWFGUAoXdQca3A/iGmasB6ARgFBHlk4mZhzJzC2ZuUaFChbALqSgRz/jxQJkyQPv28j45WV4LmpvKqIFj0LAhkJHhPn1eUZSYxkoFZy8A52ZM1RzrnLkPwHgAYOZFAIoCSLRQJkWJPc6eBSZOBG66KTeDqn59ICGhYCk4WVmiyLgqOIC6qQJFe5opMYCVCs4yAHWJqBYRFYYEEU922WYXgOsAgIgaQhQc9UEpSiDMmAEcO5brngKksF3DhgVLwdm9W5QcVXBCw+hptnOnNPswepqpkqNEGZYpOMycBaAfgOkA1kOypdYS0atE1NWx2ZMA+hDR3wDGAribrcxbt5i0tDQ0btwYycnJaN68OZYsWYL7778f6yyuw9GpUye3xfwGDRqE9957z9J9KxHA+PESVNu2bd71BS2TyjmDyqBcOaBSJa2FEwgDB+Y2bDU4fVrWK9FFAbfEWdqqwVHTZqrLupec/l4H4AorZXDLmDFyse7aJWXs09JCLoa2aNEi/PLLL/jzzz9RpEgRHDp0CJmZmRg2bJhJQntm6lSvpYOUWCYjA/j5Z7HeuLYjSE4GRo0CDh8Gype3R75w4k7BATSTKlC0p1lsYFjiDGXVsMQBBab4p91BxuHHIvPr/v37kZiYeL6HVGJiIqpUqYI2bdrAKEw4fPhw1KtXD61atUKfPn3Qr18/AMDdd9+Nvn374tJLL0Xt2rXxxx9/4N5770XDhg1x9913n9/H2LFj0bRpUzRp0gTPPvvs+fU1a9bEoUOHAIgVqV69erjyyiuxsaBl0BREpk0DTpzI654yKGiBxlu3ipJX1SVZ01Bwotc4HF489S4raD3Noh21xMVes0089hiwcqXnzxcvzt86/PRp4L77gK++cv+d5s19dvFs164dXn31VdSrVw9t27ZFjx498vSd2rdvH1577TX8+eefKFWqFK699lo0a9bs/OdHjx7FokWLMHnyZHTt2hXp6ekYNmwYWrZsiZUrV6JixYp49tlnsWLFClxwwQVo164dJk2ahJtuuun8GCtWrMD333+PlStXIisrCxdffDFSU1O9yq1EOePHA4mJwLXX5v/MWcG55prwymUHW7cCtWqJOd6Zhg0lRumff4DKle2RLZpISwPuvlvimQzi4wtWT7NYQC1xBdCC46rc+FrvJyVLlsSKFSswdOhQVKhQAT169MA333xz/vOlS5fi6quvRrly5ZCQkIBbb701z/e7dOkCIkLTpk1RqVKl872rGjdujB07dmDZsmVo06YNKlSogEKFCqFXr16YN29enjHmz5+Pbt26oXjx4ihdujS6du0KJcyE0+d9+jQwZQrQvXv+JpuAxJ5UqFCwLDiu7ilAe1IFSq9eQJUqkpFHBJQuLXWE6te3W7Lowu74F7XExaAFx4elBTVruq+JkZQE/PFHSLuOj49HmzZt0KZNGzRt2hQjA+gDZLi24uLizv9tvM/KykJCQkJIsilhINw+76lTpXu2O/cUIA+n5OSCoeAwS6PN1q3zf+acSXXddeGVKxr591+Z5b/xBjBgAHD8OFC3rljH58+X80rxTiTEv6SlAXfemdc1W7x4gbLEFTwLTlqa/JOdMeGfvnHjRmzevPn8+5UrVyIpKen8+5YtW2Lu3Lk4evQosrKy8OOPPwY0fqtWrTB37lwcOnQI2dnZGDt2bB4XGAC0bt0akyZNwpkzZ3DixAlMmTIlpN+kBEi4fd7jxgEVKwIu50EemjUD1qyJ/Uq+hw5JLJI7C07lymKF0EBj//j9d3k1ikaWLg28/jqQng788IN9ckUTkRD/0qKFKDdlysj70qWBoUMLTIAxUBAVnF695J+clCQzkaQkU/7pJ0+eRO/evdGoUSMkJydj3bp1GDRo0PnPq1atiueffx6tWrXCFVdcgZo1a6KMceL5QeXKlfHWW2/hmmuuQbNmzZCamoobb8zb2uviiy9Gjx490KxZM3Ts2BEtW7YM6TcpARJOn/fJk8CvvwK33CLxEZ5ITpZMqy1bzJchkvCUQQXIda6ZVP4zfbq4Nps3z113771yLj3zDHDmjG2iRQ2REP9iTHD//hvo1AkoWxa4/fbw7T8SYOaoWlJTU9mVdevW5VsXiZw4cYKZmc+dO8edO3fmn376yTZZouWYRRXVqzPLnCnvkpRk/r7GjpWx5871vt2ff8p248aZL0MkMXq0/E5P5/U99zBXqhRemaKR7Gw5Tv/3f/k/mz1bjnFaWvjlijaSksJ3L/DEVVcxN2smf3/7rew/PT18+w8jAJazG32h4FlwbGTQoEFo3rw5mjRpglq1auXJgFJigIsvzr/OKp/3uHHiernySu/bNWwoFp5Yj8MxLDi1arn/vGFDiS05ejR8MkUjq1bJcWrXLv9n11wj7UDeeAPYvz/sokUVaWn5LavhjH85dEhcikaiyY03StD499+HZ/8Rgio4YeS9997DypUrsWHDBnz88ccgDdaLHXbsAH77Dbj8cnF7Gjz7rPk+7+PHpf7NrbfmT4l2pWhRyX4pCApO1arye92hLRv8Y8YMeXWn4ADAu+9Kl/oCVEslKG67Tc7F4sVzg7L79w9f/MvUqUBOTq6CU7o0cMMNUlYi1uPxnIgZBYe1iJff6LGygGeekRnbuHGi7Bw/LgHAs2aZX2Bu8mQpa9Cjh3/bF4RMKk8p4gaq4PjH9OlA06ae6wVddJE8qL/5BlixIqyiRRV//CEZjt99J/FyxYuH13o4ebKk+jtblW+/Xaxzc+eGTw6biQkFp2jRojh8+LA+uP2AmXH48GEU9TTTVQJn7lzJLnnuOaBaNVlXqhTwyivAvHnAL7+Yu79x44Dq1YFLL/Vv+2bNJE312DFz5YgkfCk4NWvKjFpr4Xjm1ClgwYLc7ClPvPCCFJd8/HGtDu2J8ePlHtC+vSg3nToBEyeGx3qSkSHW5C5d8lp4O3UCSpYMr5vK5lpAMVEHp1q1atizZw8OHtRG5P5QtGhRVDMexEpoZGdLfZAaNYCnnsr72X33SV2mZ54BOnZ0X4wvUI4elVn2I4/4dk8ZGBWNV6/2HbMTjZw+LVWKvSk48fHiqlMLjmfmzhX3kyf3lEGZMsBrrwEPPQT8+KNk8im5nDsnykzXrrku01tuASZMABYuBK66ytr9z5kjyqpLli2KF5d1EyYAn36av3ed2URCLSB3kceRvLjLolIU2xg6VLITvv/e/ecTJ8rnX35pzv5GjJDxlizx/zu7d8t3Pv3UHBl8MXq0ZIsQyevo0dbub/Vq+X1jx3rfrmdP5po1rZUlmnn0UeaiRZnPnPG97blzzE2byvH0Z/uCxPTpcj5OmpS77vhx5iJFmPv3t37/Dz3EXKKE+//LlCki2y+/WC9HGDPJoFlUimIyx45JsOWVV3quJnzjjfL5yy+LLz5Uxo0TU28gNY6qVgUuuCA8cTgWNbP1ircaOM40bCjyuBZgU4QZM6RopD/u60KFgMGDJd7MV/X4gsYPP+S6pwyM9z/+KMG/VsEs8Tft27v/P7ZrJ/eCcLipIqAWkCo4ihIsr70m6ZgffeS5fD2RZJ788w/wwQeh7e/wYWDmTFGmAsnAC2fLBjsquPqr4DRqJA+AjRutkyVa2bUL2LDBd/yNM9ddJ26YtDQ5vxVxT/30U173lEH37sCePcCyZdbt/88/gX37crOnXClcWOSYNMn6go0R0AtLFRxFCYZNm4CPP5YKr+7q3zhz6aXig3/nndAeBBMnSodnf7OnnGnWTGJwrJw9AvbM2rZulSqt5cp5387IpNJA4/z4Sg/3xHvvSUbfCy+YL1M0MmcOcOSIe4tuly5AQoJYcaxi8mSJzevUyfM2PXuKNXnqVOvkAETxdZ2IhbkXlio4imB359to48knZYbm78X6xhvyIHjlleD3OW6cWClSUgL/bnKyBB5u3x78/v3Bjlnb1q1A7dq+t6tbV4KNNdA4P9OniyvT6LzuL3XrSsD7118Df/1ljWzRhJE95U5RvOACsXr9+KN12WeTJ0strgoVPG/Tpg1QqZL1bqrKleV3litnalukQFAFR7EnbiKamT5dUr9ffFFuFP5Qty7Qty/w1VfiCgiUgweB2bPFehNMgUgjk8pqN5U7Bc7qWZuvFHGDwoVlO1Vw8pKdLa7P9u2DO7defFEeYgU9bdxd9pQr3btL1/uVK83f/65dMq4n95RBfLwUCf3lF6nXZRXvvCP3x717xXK8Y0fYG32qgqNERufbaOHcObmR16kDPPpoYN998UV52D/3XOD7NYITg3FPAUDjxvLw+vvv4L7vL0arBCMlvkYNa2dt2dly4/RHwQHEQqEKTl6WLQP++y9w95RB2bISjzZ3rjzgCyqzZ3t2TxnceKNYya1wU02eLK++FBxA3FQZGbnfMZu//5aJYP/+/gWtW4QqOEpERLtHDV98IQ/I99+X3i6BUKGCKDc//wzMnx/Yd8eNkzouTZsG9j2D4sXFimS1BWfWLLmBGwHV06ZZO2vbvVvikvxVcBo2BDZvFkVVEWbMEOW3bdvgx+jTR5Top54SV6wZRJvb3Mie8qYoVqggLiKrFJz69WXxxWWXSbFQq9xU77wjRQX79rVmfD9RBUfxHB9RtWp45Yh0Dh+WdO+2bf2bJbnjscfkuD79tP/m/H/+kdlxsO4pg3BkUs2aBaSmSmNGwPq4DH8zqAwaNhSFaMsW62SKNqZPB1q0AMqXD34MI218+3bJKgyVaHObG+6pG2/0bbHo3l3c1GYGux87Ju0h/L0vxcWJFWf6dLE6mcmOHTIhe/BBse7ZiCo4isRHFC+ef31mZnDxIrHKyy/LjWTw4OAVjeLFxZy/ZIlUFPWHCRPkJu/N9O0PzZqJQmBGPR53nDwpv+u664AGDeRGH4kKDqBuKoP//pP/WSDp4Z64/nqgc2fg9del51EoRJvb3HBP3Xqr7227dZP7h7/Xvz9Mny5KViATr549Rdn/6Sfz5ABy74+PPWbuuEGgCo4iLoTPPst9n5QkD3NmMWXOnm2fbJHCmjXA55+LybVJk9DGuusucTUNGCBKpC/GjRPzf+PGoe3XCDResya0cTwxf77cMK+7Tmb0TZuGR8EpXNh/a2ODBvKqCo4we7bEMQUbf+PKe+9JfZUXXwz8u9nZomy9+qpYbNwRqW7z8eOlY7c/x7FyZcl0MtNNNXmyWOAuu8z/76SkiNvaTDfV4cPAsGHyTImAdkCWKjhE1IGINhLRFiJyG1lJRLcR0ToiWktE31kpj+IF48Y/YYKYGAcNkptNlSoyu/v6azulsxdmmY2UKRNamrdBfLz4qLduBb780vu2e/dKA8Rgg4udsTqTatYsUTauuELep6SIgmNlZs3WrRKfER/v3/YlS4pLVmvhCDNmSNyIv41bfVG/PtCvnzzk/MkU2rcPGDFCrAkVK4ocgwZ57pMUxiJxfuNP9pQr3bvLdWiGq/TcOeDXX8V65u91AIiVpWdPqd1jVqHGIUPE0vb00+aMFyKWKThEFA9gCICOABoBuJ2IGrlsUxfAAABXMHNjAI9ZJY/igwUL5NV4OAGSEbNwocRT3HefBMhaXSguEpk8WR7egwaFFqfgTPv2Yul49VXvXb5/+EFeQ3VPAWKZK1XKukyqWbNkZlqsmLxPSZHmoFYX+fPXPWXQsKFacABRPKdPB669VgrQmcVLL4kr9tJL8wcInz0rKelPPy0Kd9WqUixz7lwphPfdd8CBAzKhcnWbJySEtUic38yaJed5INdo9+7yaoYVJz1dXI3BxAX27Cn3dOM+EwqnTwOffCKKVqjWZpOw0oLTCsAWZt7GzJkAvgfg0t4UfQAMYeajAMDMByyUR/HGggXARRcBF16Yd32ZMjI7ePBB4O235SIuSL18zp6Von4NG5qbEUAkVpxDh+S4emLcOImd8Sczwp99WhVofOiQzNivuy53nVGQ0Co3FbPUFAlGwdmwoWAq685s3izWWjPib5yZOlVcr2fP5gYI33MP0Ly51Mu5/nqpAl6hgpz7K1eKJeebb4DbbwcSE8XFMXSoKOVEojSfOxdakL1V/PCD/+4pgxo1pJ+cGXE4kyeLxSsYN2OjRnJPMMNN9c03ch945pnQxzIJKxWcqgB2O73f41jnTD0A9YgonYgWE1EHdwMR0QNEtJyIlh88eNAicQswzDILcLbeOJOQIPEnH3wgAWlt2gD794dVRNv4+GOxEgwebO4sF5AWD716ydh79uT/fOdOYPFic9xTBoaCY7bbaM4ceXVWcJo2lRm8VQrO4cNSqCxQBadRI4kTidR4jnARbHsGXwwcmD8N/9w5YO1asdZMmSL/u1mz5GHYrJl7xaVXL1HAcnIkgPfqq4HevaX0QKTg7J4KtGxE9+7A8uWe4438gVnKTlx3nbhfg6FnT7HUhyJHVpaUzrj0UmkuHCHYHWRcCEBdAG0A3A7gKyIq67oRMw9l5hbM3KKCtxLUSnBs3iyVcr2dmERS4G7SJLlRXXKJ9DaKZf79VzKeOnc2f5ZrkJYmN/CXXsr/mZnuKYNmzUQpMPvhPnu2uL+cu5wXLy6xXVYpOIFmUBloTyph+nQ5doEeP194Oreys3NdGIE+jIsWFUtF06aiGCxcGLqcZhCMe8rAcFOFksW0bp1YMW90dY4EgDGBGj8++DF+/FHkeOaZiLKyWang7AVQ3el9Ncc6Z/YAmMzM55h5O4BNEIVHCSdG/I0/mnfXrpItk50tFp9Imk2ZzcCBUu3z/fet20dSklRE/uab/K6jceOkpoyZDyCrAo1nzZIZtlHB2MAINLaCUBWcghyHk5kpVjezrTeAdf3ISpcGfvtNsnNuuCEyJljBuKcMLrpIJhyhxOEYlYg7dw5+jNq1gVatgndTMYu7vV690BQtC/BLwSGiqkR0ORG1NhY/vrYMQF0iqkVEhQH0BOBaF3oSxHoDIkqEuKy2+Su8YhILFkjwrL9xHhdfDCxdKhdo587Ap59aK58d/PmnBDo+8ohcuFby/PNSEOvZZ3PXbdsm5msz3VNAboq7mQrO7t1iBbz22vyfpaSI++3QIfP2Z2AoOEZ7CH8pX17iPwqygrNwoTRftcIy6a6ulln9yCpWFNda8eIiu9XNY73hXNwvUPeUQffuEh6wb19w3588WYo0hlqUtWdPuedt2hT4d2fPlu8+/bS4pCMIn9IQ0dsA0gG8AOBpx/KUr+8xcxaAfgCmA1gPYDwzryWiV4nICPeeDuAwEa0DMAfA08x8OKhfogSPEX8TiGmxalVg3jyZST3yiFghRo2KrtLqrjiXhr/8cjGjB1PPI1AuuAB44QWZnc6cKesMc7E/hcMCoVQpmbGZmUk1a5a8OsffGFgZaLx1q5yHRtZWIBT0nlQzZoi1zag4bSauAcJmd5GuWVPkz8iQgOVQiwoGi+GeCuUaNdxUwfTw+ucfKeURbFV1Z267Tf5XwVhx3nlHklPuuCN0OcyGmb0uADYCKOJru3AtqampbAmjRzMnJTETyevo0dbsJ9L4919mgPmdd4L7flYW8+OPyxjx8fJqLMWLR89xHD1a5HWWv3Dh8MmfkcFcsyZzSgpzdra8XnKJNfu66Sbm+vXNG++OO5grVBC5XTl8WI7l22+btz+DK69kvuqq4L770EPMZcsy5+SYK1O0cPHFwR+7SGHRIrlmmzVjPno0/Pu/5x7m0qXl2g2FBg2Yr7km8O8NGybX1sqVoe3f4OqrRZZArok//xQZ3nzTHBmCBMBydqMv+GNP2gbA5PSRCMOMvifR1hjOID1dXj1lUPkiPl6yq8qVk7gcZyK5tLor7krDZ2aGT/4iRcSE/9dfciz/+kvMxVacR8nJ4lIyI92fWUzU117r3jxdrpzEXlhlwQk2PqlhQ6kdYtfs304OHhSXglWB8+Hi0kslQHfdOrFinDkTvn1nZkrCRSjuKYNbbpE6QIFmCE+eLNeWEVcXKj17SvmEQGKb3n1XrMIPPWSODCbjj4JzGsBKIvqSiD42FqsFCyue+p707y91CmbMkHTddesk3uDYsbwP82hrDOdMerpcoKmpoY1z9Kj79dGSihsJHdVzcsRMbBT+O3rUmvOoWTPZlxlZRBs3SvyAO/eUgRWBxqdPS6mCYBWcRo6aowXRTfX77/JqRYBxuGnfHvj229xq3+HqEh9K9pQr3bvL9Thpkv/fOX1a/o9du5qXtdS9u0xY/XVTbd8urvQIaKrpCX8UnMkAXgOwEMAKpyV28PQQO3xY/Kvt20uPj8aNRWMuW1b81yVLSiuDu++OrsZwzixYIBH0oc5CrMqcCBeRIP8LL+SvT2PFeWRmJpW3+BuDlBSxRpnZ5NMILg3FggMUTAVnxgwJtL74YrslMYeePaVFwJQpwP33h6eAo5E9df31oY/VrJnExQWSTTVzpliszIi/MahQAWjbVhQcf+pkffCBWG379zdPBpMp5GsDZh7pyIIyUkk2MnOY1OQwUaOG+yJHVatKGvTx47KcOJH7t/O6YcPcjxvp1ovTp4EVK8zpG5KWJtYGZ0XPrMyJcJCWJjfHjIzcdeGWP1xWpNq15beZpeAkJXnPZEpJkRvmqlUSvG0GwaaIG1SpIqb1glYLh1kUnLZtA+tbFOn07SuZei+9JMrb++9bV48lM1OCgm+6KfSJISBydu8uBT+PHpWkA19MniwK1tVXh75/Z3r2lKrTS5dKrTNPHDoEDB8eMU01PeIuMMd5gaRx7wQwF8A8ANsBtPb1PasWS4KM3QWYBhIgm5SU97vGkpRkvqxmMmeOyPnLL+aMZwRqAxKsPWyYOeOGi7Ztc2W3I9A8nOfRJZcwt2kT2hhZWRKoe++93rfbtUt+x6efhrY/Zz74QMY8dCj4MVq1Yr72WvNkigb+/luO29df2y2J+eTkMD/6qPy+N96wbj9Tp8o+pkwxb8wlS2TMkSN9b5udzVyxInOPHubt3+C//yS54rHHvG83aJDIu3at+TIEAUIIMn4fQDtmvpqZWwNoD2CwJdqWXYSa1uiu7gNgjvnSSowAY7Nm1UZp9cWL5dF84oQ544aDM2eAZcuA//s/MXHv2GFeWqu/WFk/xBUzWjasXCmBut7cU4DM8MqXNzcOZ+tW6ZNWrlzwYxTEVHGjPUOk35uCgUisIL16SW2p++6zJvFj/Hjz3FMGLVsC1av715tq6VJpSGqme8qgTBmgUycpMuqaNGJgNNXs0iU3li1Scaf1OC8AVvmzLlyLZWnioeKcZl69OnPTpqLhfvWV3ZJ5pkMH5iZNrBn7qquYa9RgPnfOmvHNZvRo+X/NmmW/HOEoV/DJJ/J79+wJfoy335Yx9u/3vW3btpKabBYdOkgqfSgY8tuRYmwXbdsyN25stxTWkpnJnJyc3xJqRtmKs2fFannXXebI6kz//sxFijAfP+59uwEDpCTHkSPmy8DM/P33crz++MP958a9Y/58a/YfBAjBgrOciIYRURvH8hWA5RbqXNGJc2O4XbtEy+7QAejTR6xBkUZ2tlQzDTY93BdPPy3HwYxuueHg668ljqRNG3vlcD6PrLQiNWsmr6HE4cyaJYH3rh3o3ZGSAqxZY16WSygp4gYFLdD49GlpsxIL2VPeSEhwn9VpRsD+zJlitTS7ACcgcThnzwK//up9u8mTgdat/YvVCYbOncVy7C6bymiqedll1j07TMQfBacvgHUAHnUs6xzrFG8ULSqBaJ06SRrdF1/YLVFe1q6VIGmrOr/ecIO0fnj3XfM7V5vNtm1Sy+XeeyOu1LhlNG0qr8EqOGfPysPSXXsGd6SkSHCmGUG92dmi/KmCExjz5sn/Ldrr3/jDnj3u14casP/DD+LGscLFd/nlMlnwlk21davcu61wTxmUKCHjT5iQf0IyYYJcexHWVNMTPu/mzHyWmT9g5psdy2BmPhsO4aKeokWlEFXnzhLl/9lndkuUSyANNoMhLg548kkpKDZ3rjX7MIsRI+RivftuuyUJH2XLSvZgsArO4sUSt+Qr/sbAzJYNe/bIjTdUBadWLcmCKSgKzowZ8nuvuspuSazHU3mHUDJ+zCzu5474eKBbN2DqVM9FOI3mmlYqOIBkUx06lFsGAshtqlm/vvX7NwmPCg4RjXe8riaiVa5L+ESMcooUEa23a1fgf/+LnMaUCxZIqmxSknX7uPNOaY733nvW7SNUsrOlk3f79pGd7mgFycnB96SaPVuUWH/TVOvWFbO3GQpOqCniBvHxcrMuKArO9Oni2nCXEBFreEr8yMnJPX8CxXBPmVHczxPdu4ty89tv7j+fPFka5taubZ0MgIRXlCmT1001c6ZcvxHYVNMT3qQ0qvd0BtDFzaL4S5EiYtq88UZpTPnRR3ZLJArOlVdaa2YsWhTo1098ypFab2TGDLEI3Hef3ZKEn+RkKc1+NgiD7KxZ0sXY3wqm8fES9xNJCg4gbqpIPTfNZM8e+Z0FwT0FuM+Mfe45sTq2aCHKXqCMH2+de8rg6qsl49Cdm+rIEXELh8N6UqQIcPPNEmZh1AaL5KaaHvCo4DDzfsefDzPzTucFwMPhES+GKFxYLpBu3YDHHpN0RrvYtUtaTljlnnKmb1/p9vzBB9bvKxi+/hpITIwak6upNGsmFqxALRgnT0oXY3/dUwYpKZJaHmql2a1bJZDUDItbw4YSUxDOPkZ2YKSHx3qAsTOuAftvvgksXy7uq06d5IHtb3yg4Z666Sa5l1tFoUKyjylT8k88pk2T6zVc96qePSVO87ffJNRg5kx5dlnhnrMIf+xM7tTVjmYLUiAoXFjqC3TvDjzxhH0PfaP+TTgUnMREqYw5ahTwzz/W7y8QDh4Efv5ZXGlW3rQilWBbNsybJ9kU/gYYG6SkSG2kbdsC+54rW7dKXRMzKvE2aiQPuY0bQx8rkpkxA6hcWdwbBZlatSR79JZbgGefBW6/HTh1yvf3fv9desRZkT3lSvfucp0YPcMMJk8GKlWSmjnh4NprpR1Rr17Sq5AotLpTNuAtBqcvEa0GUN8l/mY7AI3BCZaEBGDsWLlQnnzSnviUBQvkxDUyaazm8cclKDRS4o8MRo8Wue69125J7OGii8SNGKiCM2uWzOICTRM1K9DYjBRxg4KQSZWdLQ/Ldu2iIvPFckqUkNiSt94Sq/oVV4iFxxtWZk+5ct11si9nN1VmplhwunQJX/zLuHHinjICnpnFghMNTaQdeDtS30FibSYjb+xNKjNHjxMuEklIAL77TrrfPv008Pbb4d1/errUMSjksxWZOVx0kbjmPvvMv9lSOGCWXiqtWhXcWW2hQlLHJlAFZ/ZsSWktViyw7zVpIvsMRcFhNlfBqVtXHhixHIezYoXEbxSU+Bt/IBILzq+/inLTooWc1+4Il3vKoHBhUWR+/jk3TXvuXLHqhNOVPnCgWGqdiZYm0g68xeAcY+YdzHy7I+7mDAAGUJKIoqRFdARTqJBYEG6/XYLfbrvNmrLirhw7Jg+0cLinnHnqKSm+NWJEePfriaVLpZ5EQQwudibQTKpDhySOJtD4G0CsPo0ahabgHDkicQFmKThFishYsWzBmTFDHuht29otSeTRsaO0aKlUSSxcH36YPy4nnO4pg1tukfvlH3/I+59/lglFMNddsISr+a+F+LR1EVEXItoMabI5F8AOANMslqtgUKgQ8O23Mhv+4QfpaM4srw88YI2Ss2iR7CPcVSgvu0x+5wcf5J8V2MHXX0saac+edktiL8nJ0tfm33/9237OHHkN9kabkhKagmNmBpVBrPekmj4duPhioEIFuyWJTOrWlbpOXbqIO/3uu/MGnYfTPWXQrp240iZMkPv15Mmy/3Cm+HuqJeRpfQTijzPvdQCXAtjEzLUAXAdgsaVSFSQKFXJfddMqU2B6ugRnXnKJ+WP74qmngO3bJfXQTk6dyo2DKl3aXlnsJtCWDbNmAaVKiUk/GFJSRJnav9/3tu6wQsFp2BDYvNm8NhKRxPHjMqkpSNlTwVCqlMS8vPqqTDpbtwY+/ljSy0eOlEnZDz+ET55ixaQa/KRJksG0e7eUGQkn4Wz+axH+KDjnmPkwgDgiimPmOQCCvLspbtm92/16K0yBCxbIQ6ZkSfPH9kXXrhKPY3f7hgkTxJ9dUIOLnQm0ZcOsWVKrI9j4rVADjQ0Fx8xCZw0binITbAG4SGb2bAky1vgb38TFAS++KO6g1auB/v1z78GnTllnVfdExYpiXTUmE0Y9mnDhrpbQ0KHW9cezAH8UnP+IqCSAeQDGENFHACIkUjRGCJcpMDNT6pfY1SQtPl7S45cty20VYQfDh4tZuiCUrPdFYqJUtPZHwdm1C9iyJbQ4gObN5TUUBadKlcADnL0Ry5lUM2bIZOayy+yWJHro2lWK7bkSzgDbMWPkPuXM00+HP4MpXM1/LcIfBedGAKcBPA7gNwBboZWMzSVcpsC//hLfcrgDjJ3p3VtuHna1b9i0SaqB3nuvpswaJCf7p+AYWSahKDilS4t7KRQFx+wy9Q0ayGssKjjTpwPXXFMw6zyFgicXargCbAcOzF98MsoymCIBfxScBwFUZuYsZh7JzB87XFaKWbiaAgHgoYfM15aNAn92trkvXlx6ck2ebE9xtREjxJLUu3f49x2pJCdLmrSvGJRZs8RsHmpafSiBxmamiBuUKgVUrx5bCs6YMUDVqlJUccGCqKpdEhHYHWAbAxlMkYA/Ck4pADOIaD4R9SOiSv4OTkQdiGgjEW0houe8bNediJiICm5sj2EKzMyUm22wTRC9sWCBzH4rVzZ/7ED43/+kwFy4KzlnZUnAYKdO9h+DSCI5Wc47bwonsyg4114buuUrJUUevMeOBfa9M2eAffvMV3CA2OpJNWaMxIvs2yfvjx4Nf/xItGN3gK3dClaM4FPBYeZXmLkxgP8BqAxgLhHN9PU9IooHMATS1qERgNuJqJGb7UpBGnsuCVD22KRQIeDhh+VhYuYNlzm3wabdVKwoFpSRIyWILlxMmyamZw0uzos/mVQbNsixC7Q9gzuMQOOVKwP73vbt8mqVgrNhQ+h9siKBgQNzq88aqHsjMOwOsLVbwYoRAqn5fADAPwAOA6jox/atAGxh5m3MnAnge0g8jyuvAXgbQJhDxCOY+++XAmSffGLemFu2SO+lSFBwAKk3kZkJDBkSvn0OHy4FvW64IXz7jAbq15fq2t4UnFmz5NWMQmPBZlJZkSJu0KiRKAGeMhqjCXVvmIOdAbZ2K1gxgj+F/h4moj8AzAJQHkAfZk72Y+yqAJzvFnsc65zHvhhAdWb+1YcMDxDRciJafvDgQT92HeUkJgL/939Sj+G//8wZ08haihQFp359yVYYMiT/bNMK/vkH+OUX4K675GGu5JKQIA94bwrO7NlSYduMAN8LL5QlkhScWMmkOnXKc4aZujeiiyjPYIoE/LHgVAfwGDM3ZuZBzGyK34SI4gB8AOBJX9sy81BmbsHMLSoUlGqcjzwiD/6vvzZnvAULpBNs/frmjGcGTz0FHD4sriqrGTVK6oGoe8o93jKpsrOlgrGZZeKDCTTeulWysNyl8IZKLCg4u3bJBOb06fxKvLo3lAKIt27iRonXdwHsIqJyzosfY++FKEcG1RzrDEoBaALgDyLaAamWPLlABxo7k5IiN6shQ+QBEyrp6ZI9Fa5OtP5wxRVSUfmDD8z5jZ4wGmtecUVuSrCSl+RkYO9eUThd+esvsSSareCsWxdY8TIjRdyK9P7ERFmiNdB43jwpCLdtmzSQHDFC3RtKgcdXN3EAWAFgueN1hdN7XywDUJeIahFRYQA9IZ3JAZxv5pnIzDWZuSak/UNXZvZn7ILBo4/KDWvq1NDGOXhQMmQixT1lQCRWnC1bJG3cKhYulN+v1hvPJDu8zu6sOEb8jRkBxgYpKaLUrlnj/3esSBF3Jlp7Un3xhSifF1wgTWQ7dVL3hqLAezfxzo7XWsxc2/FqLD4d8cycBaAfgOkA1gMYz8xriehVIgpjz/co5qabpJZFqMHGkVD/xhPdugG1allb+G/4cKnmettt1u0j2vGWSTVrFtC4sQRom0WgmVTZ2fKgtlLBadhQFBw724gEQmYm0LevLNdfL1XKI8kFrSg240+Q8Sx/1rmDmacycz1mrsPMaY51LzFzvuk6M7dR640LCQmSMv7776HNLBcskKysYBskWonRvmHhQlnM5sQJYPx4oEcPe/pvRQuVKkn6vquCc/asnD9muqcAUWpLlfI/DmfvXnmgW63gHDkiFs9I58ABUWq++AJ49llgyhSgbFm7pVKUiMJbDE5RR6xNIhFd4BR/UxMu2VCKhfTpI8rJp58GP0Z6OtCypYwTidxzj5jX33/f/LHHj5fMkvvuM3/sWMNdoPHixVJgz2wFJy5O+lL5q+BYmUFlYAQaR3oczsqVcj0vXSrF+956SyYKiqLkwZsF50FIvE0D5I2/+RlACE9bJSAqVABuv10yjQKt/ApIRsWKFZHpnjIoUUIsVRMnSjyOmQwfLoHFl15q7rixSHKyxMQ4B3zPmiXKyNVXm7+/lBSp2O1PgHk4FJxGjjqkkRyHM348cPnlEluzYIGUk1AUxS3eYnA+YuZaAJ5yicFpxsyq4ISTRx4RK8SIEYF/d9ky6TEUaQHGrvTrJw/SlBR5rVkz9NLy69cDixaJ9UYba/omOVmympyVzFmzxFpQpoz5+0tJEQV882bf227dKlW+q1f3vW2wVK0qbrNIVHBycoAXXhBXa0qKXNepqXZLpSgRjT85wzlEVNZ443BXPWydSEo+Lr5YLDCffhp4KXkjwPjyy82Xy0yMTJ2TJyXIc+fO0PvnDB8uD8W77jJHxljHyKQy+qCdOCFuEDOzp5wJpKLx1q2i9FrpiiESa1+kKTjHj0vCQVqaVDmfPVsKJSqK4hV/FJw+zPyf8YaZjwLoY5lEinseeURu8tOmBfa9BQskA6acP6WLbGTgwPyuilD652RmSiXoLl0keFbxTaNGokAYcTjz50uDUrPjb5z3V7iw/wqOle4pg0hpujlmjCh0cXFS2PCXX2SCM3Ro5MbSKUqE4Y+CE0+Ua993NNEsbJ1IiltuvhmoUgX4+GP/v5OdLZlJkRx/Y+Ctf05WVuDj/fqrZMNocLH/FCkiFgxDwZk1S9ZZZf1LSACaNPGt4DCHT8Fp1Ei6cAcT72YWRjfwnTvlt2dliSJYtqy6WhUlAPxRcH4DMI6IriOi6wCMBRCgGUEJmYQEqXcxY4Z0PfaHtWvlRh3p8TeA5z45zDKTfeklueH7y/DhQOXKQPv2pohXYHDOpJo1S5RjT72NzMBo2eCt9szRo3Ieh8uCA/h/jZlFdra4Bj/9VJQb1/5sZ89qN3BFCRB/FJxnAcwG8JBjWQ3Awjue4pEHHpCZnL8p40b8TTQoOGlp0i/HmeLFgcceA5o2BV5/XWqndOok2Vbnznkea+9eceXdfbfE4Cj+k5wsiuSWLfLAtco9ZZCSIu0h9uzxvE04MqgMzOhJ5exe8hQsn5Eh7uM335Rzunx5SZs3etC5Q7uBK0pA+Lz7M3MOES0BUAfAbQASAfxotWCKGypWBHr2lJTxtDTfmS0LFogVo2bNsIgXEkYp+YED5UZeo4b8RmP9zp1ilfn6a3HXXXih1M+5//78Ha5HjpRgbG3NEDhGoLHhCg2HggOIFcdThlQ4FZxatWQSEWwcjuFeMpQUI1j+9GnJ0po/X67LpUslTgwQt1iPHsBVV8nSurV7ZUa7gStKYDCz2wVAPQAvA9gAYAGARwDs9LR9uJbU1FQu0Cxfzgwwf/ih721r1GC+9VbrZQon584xT57M3Lkzc1ycHIu2bZnHj2f+5hv5zQBzkSLMo0fbLW30sXu3HL8SJZhLl5bjbSUnTjATMQ8a5Hmb118XmU6dslYWg6ZN5fwKhqQkkdXTUqgQ8yWXMD/1FPPPPzMfPJh/jNGjmYsXz/u94sX1fFYUDwBYzm70BW8WnA0A5gPozMxbAICIHrdU21J8k5oKXHaZuKkeecRzd/Ddu2UW+OST4ZXPagoVksyoLl3ErfH112LZce0zdfaszJwBbTQYCHPnyjl16pTE3owbZ+3xK1kSqFfPe6Dx1q1iiXR1YVpFw4ZSHDMYvLmRZs8GLrnE9+/wZc1UFMUvvMXg3AxgP4A5RPSVI8BYQ/gjgUcflRiJ337zvE0kN9g0i2rVJPh42zb3qeChpJkXRAz3ilFr6cyZ0GsR+YMRaOyJrVvzuyGtpGFDYPt2+f3+kJkpiuDVV3sOlk5KAq65xn8lTbuBK0rIeKtkPImZe0JaNcwB8BiAikT0ORG1C5N8iju6d5cZrbcu4wsWSAsEo0t0LBMf77lBogZm+s/AgfkDXMOhJKakyP/p8GH3n4crRdygUSNRLDZt8r7drl1SXbhGDYmN271bXl2zzooXFwuMoihhxWcWFTOfYubvmLkLgGoA/oJkVil2YaSM//YbsHGj+23S08WVVVCyiDwFYGpgpv94q0VkJUag8cqV+T/LyJCsuHAqON4yqXJygOnTgRtvlIDkN94AWrUCpk4Vq+rYscBXX4nFhkhehw5VC4yi2IA/aeLnYeajzDyUmS1OrVB8YqSMDxmS/7Njx6SWSSy7p1zxlGauM2f/sUtJ9NayYft2eQ2ngvPnn/J6++25ad6HDwPvvSfxQh06SJf1554T+SZPBjp2zI2HU/eSokQEASk4SgRRqZKklo4YIb1qnFm8WG6u0VD/xix69ZKZss6cg8cuJTExUeKp3Ck44UwRB0SZedip1d7OnUDv3nK9Pf20VBMfO1bcUWlpcp4pihKRqIITzTzyiDSnHDky7/oFCyQu5ZJL7JHLLnTmHBp2KomeAo3DreC4i0PKzpa4mlWrgHnzJM6msHarUZRIRxWcaKZlS+DSSyXY2LnLeHq6VEUtVco20ZQoxS4lMSVF4slclYutW+U8TkwMjxye4o1OnZKK2oqiRA2q4EQ7jzwCbN4sPaoAaWGweHHBir9Rop+UFFGqjD5YBkaKeLiaTGqwuqLEDKrgRDu33CJtC4zS+n/9JfU7ClL8jRL9eAo0DneKuAarK0rMoApOtFO4MPDQQ9JccvPmglHgT4k9atQALrggr4KTkyNZSuFUcDRYXVFiBlVwYoEHH5TaOJ9+KgHGtWpJtoeiRAtE+QON9+6VKsHhVHAADVZXlBhBFZxY4MILJeD4k0+An34CDhywvry+ophNSgqwerXEkQHhz6BSFCWmsFTBIaIORLSRiLYQ0XNuPn+CiNYR0SoimkVEWlQiGMaMkeJkRh+cU6fC00NIUcwkJUWapG7YIO9VwVEUJQQsU3CIKB7AEAAdATQCcDsRNXLZ7C8ALZg5GcAEAO9YJU9MM3CglLR3RhtNKtGGa6Dx1q3SaqR6dftkUhQlarHSgtMKwBZm3sbMmQC+B3Cj8wbMPIeZjcIXiyG9rpRAsauHkKKYSf36UlDPWcFJSio4/dQURTEVKxWcqgB2O73f41jnifsATHP3ARE9QETLiWj5QU9dowsyWrtDiQXi44Hk5LwKjrqnFEUJkogIMiaiOwC0APCuu88dDT5bMHOLChUqhFe4aEBrdyixQkqKdBVnVgVHUZSQsFLB2QvA2XlezbEuD0TUFsBAAF2Z+ayF8sQuWrtDiRVSUoBjxyRo/r//VMFRFCVorHRuLwNQl4hqQRSbngD+z3kDIkoB8CWADsx8wEJZYp9evVShUaIfI9D4xx/lVRUcRVGCxDILDjNnAegHYDqA9QDGM/NaInqViLo6NnsXQEkAPxDRSiKabJU8iqJEAU2aSCzOhAnyPggFZ8wYoGZNIC5OXrVagqIUTIiN2ilRQosWLXj58uV2i6EoilU0aQKsXSt/nzwJlCjh91fHjJESUM5NyYsXV4+tosQyRLSCmVu4ro+IIGNFUZTzGG6qCy8MSLkBpPSTs3IDaEkoRSmoqIKjKEpkkZMjr//8E7CPSUtCKYpioAqOoiiRw5gxuQHGALBzZ0BtRypVcr9eS0IpSsFDFRxFUSKHgQOlH5UzfvqYMjMlPpko7/q4OOC110yUUVGUqEAVHEVRIocQfExvvQXs3Qs88URuSajERPF4HdAiFIpS4FAFR1FiiKhPkQ6y7ci6dcDrrwM9ewLvvQfs2JGr2Nx4I/DCC8CmTeaLqyhK5KIKjqLECEaK9M6d0ukgwPCVyCCItiM5OUCfPkCpUsBHH+X9jAj47DOgaFHgvvty45cVRYl9VMFRlBghJlKkg2g78sUXwMKFwAcfABUr5v+8ShVg8GBgwQJRdhRFKRhooT9FiRHi4sRy4wpR7Foudu8GGjcGLrkEmDEjf4CxATPQsaMoOatXA7VqhVdORVGsQwv9KUoMwwyULu3+s1hNkWYGHn4YyM4GvvzSs3IDyGdDh4oS2KePe0VQUZTYQhUcRYlyMjOBu+6SJtzx8fk/v+228MsUDn74AfjlF0kBr13b9/Y1agDvvgvMmgUMH269fIqi2Iu6qBQlivnvP+Dmm4E5c+RBX6uWxNzs2gVUrSoKz7//Ar/+Clx7rd3SmseRI0DDhqK0LFoEFCrk3/dycoC2bYEVK6TdVbVq1sqpKIr1qItKUWKMnTuBK66QuJJRoyQVulev3BTp3bvlQX7RRUCXLrJdrPDUU8Dhw8CwYf4rN4C4qL76CsjKAh58UF1Vnoj6cgOKAlVwFCUqWbECuPRSKWw3fTpwxx3utytfHpg5E6heHejUCVi6NLxyWsHMmcCIEcAzzwDNmgX+/Tp1gDfeAKZOBUaPNl++aCcmyg0oAFRRVQVHUUwkHDeUX38Frr4aKFwYSE8HrrnG+/aVKkncSYUKQPv2wMqV5ssULk6fFstL3brAiy8GP06/fsDllwP9+0tPTyWXmCg3EAHYrVyooqoKjqKYRjhuKF98AXTtCtSvDyxeLCnS/lC1KjB7thTDu/56iT+JRl5+Gdi2TdxMxYoFP058vAQanz4tmVjqqhKYPXfF2LkTyMgIrzzRSiQoF6qoqoKjKKZh5Q0lJwd49lmgb1+p5zJ3LlC5cmBjJCWJkpOQIIG2mzeHLlc4WbFCivk98IBYsEKlQQPglVeAiROBCRNCHy/aWbRIYrq8KXtVqwKPPy6tMRTP2K1cLF8uSpU7/GjrZhp2W7HAzFG1pKamsmI+o0czJyUxE8nr6NF2SxQ9bNjA/N57zPJoyL8QMefkBD/+mTPMPXrIWA89xHzuXGjyrlvHXKECc7VqzNu2hTaWO6w4lzIzmZs3Z65cmfno0dDHMzh3jrlFCzkeBw+aN240sXUr8623yvlVuTLz/fczFy+e9xwuXpz5ueeYb7uNOSFB1l1+OfM33zCfOmX3L4gstm2z7l7gi3nzmNu3z92XOxni4pjT0piPHLFODma57t2dR1Y8WwAsZzf6gu0KS6CLKjjmE84TMRbIzGSePZv5iSeY69bNPWbGjd/dUrMm86OPMs+aJd/3l0OHmK+8UsZ4+23zbo4rVzJfcAFzrVrMu3ebMyazdefSW2/JWD/9ZI6czqxaJf+7//s/88eOZI4ckXM4IUH+R4MGMZ84IZ95U1IPHBCFvl49+Z+UKcP88MPMf/1lrnzRNOnKymKeMoW5UyfPioWxNGzI/MknzMeOmbPvnBzm6dOZr7pKxq9YUa6Xr77Kfy0WKcLcpIn8XaIE82OPMe/YYY4criQluf/9SUnm70sVHB+EejFF08XoiqcTsUaNwMaJ5mPA7F3+w4flfc+ezGXLyvEpXJi5QwfmTz+Vm4S7h3uxYsz33cfcubPcXAD5fq9ezOPHMx8/7lmGKlWYL7xQ9vP99+b/3mXLmEuXlgfV/v2hj7d2rTzszL6pbdrEXLQo8803hy6jJ155ReT8+Wfr9hEpnD3LPHiwKLhEcn7u3Rv4ODk5zHPnMt9xR+653aIF89ChzMOGhX4/jYZJ17//Mr/5Zu49tHJl5pdfFgXG3b3ggQeYW7aU9yVLMvfty7x6dXD7zs5mnjRJjjkgFtmPPsprUfN0T1u5Uv5vhQoxx8eLcm+GgnrqFPOMGWLt82bFMhtVcLwQ6sUULRejO/bt83wiAsy1azO3bi0XwLPPyoU7cSLz8uXM//wjFxlzdB8DZvfyFy0qCk3r1nITMGZH99wjlgRjtus6jqcb+8mT8r3evZnLl8+rJH3+ufubIsD84ovW/e70dJnJNW4cnItm/XpRDho39n4eAXLjM84Xf8nJYW7TRhSnYB7C/nL2LHNysvkusEgiJ4d5wgTmOnXk/3H99cx//23O2IcPy8PVsA64LkWKiHt16FDmDz9kfuMNOa+ffFIe8r17i/urc2fma6+V68LdOOXLi1K1Y4dvV61VE66cHOb58+WeaFhtr71Wjq2zddbb/pculd9sKIatWzOPG+efdTcri/m773KPde3aclwzMgL/Lbt2iRWvZEkZq21bsQb5aynOzJR7yGuvyXVq/N8KFcr9bWrBsVnB8WTBKFdOzHzDhjF//TXziBHic/72W+ZRo+SE/e475sRE99+vXt1/GcJp/Vi/XmYdl1zi/YFUurRcxFddJa4Mdy6YwoXlM08nc6BWILuoUcPzcUhOZh44kHnx4sAf0J44d05u1E88kfvA8bRYcUNwZs4cUeaaN/fPL79xo9zQmjbl8zOy1q3FklWtmvvfEBeXez689JL/sT/Dhsn3vvoqpJ/oF8uXiyJ7773W78tqXO8ngwYxX3GFHMvGjZmnTbNmvzk5YnX0pewa503x4nL/rFGDuX595pSUXDl9LfHx8ttat2a+6y5RmIYNY545k/n990OfcLkew2HDZCJinPdlyjD37y/302A5eFBczzVrypiGBWjv3vz7/+Yb5uHDc93iDRvKcyjUmDxmUerfekv2DzA3ayb7HzkyrwyjRon154MPmG+4IVcxIpL/3VNPybl14oTG4AS1WKHg+PKZhrIULy4nRosWzB07Mt95pzzU3nxTbtqTJskJXbSodSdCdjbzokVigalfP3cfLVtKsNnbb/t3ImZni9Vm+XKR+5NPZMxevbwfg0sukRtQWhrzjz8yr1njfrYRDjdhZqa4UsaPlwdt9+7MDRp4vwlbTU6OHBM7ZfjtN1FWa9cWxdz1GG7eLP+/Zs1y5brySuaPP85rWfF0UxsxQmao7dvnXm/XXivbnz7tXqZ9+8Sd16aNtYGZzgwYILJVrGivuzqUMdz9D4wJy1dfmfNA9Ian+ykR8549Yu05c8b7/9TTpLNKFbEEfvWVTDruuEMmYNWr5yrR3pYSJZj79ZPvvvOOWD7GjROrxeLFoqzs3y8TWnfHEJAH+VdfiUXWLIwYno4dc4+VYTV2Pn7G/idMMG+y5UxGhvz2Ro3y7tPdUq+eWN8mTJBYQXeEa+Jui4IDoAOAjQC2AHjOzedFAIxzfL4EQE1fY4bTglO1qgRg7twpJtFt2yTjYMsWiQvYuFEyaAyt13UpW5b58cdFqenQgTk1VWYqxYr5vhCNB8Mzz+S6hZYtk4vP3YntTtufOlV8vsaMqlAhMUsPGZI/sDTUE9HTMSxZUh5kVavmXR8XJ5aLTp3kGN17b34rUKhuwqJFJYjutdfE1dSkSV4rFJHMhm66yZrYkUAJZ1CeO554Iv++ExLyynXZZRK/4S0w2de5tGuX/E9q1ZIxy5QR98XSpfLQM75v7PO996z6xfkZMSL/TT3c7mpPsVwffyz3mxUrxEXy228yYRg1ivmLL8Rq8dprosi4O48CsSiHghnncTDHMTOTeft2sUh6u6+WK+efMuRuufBC65XtLVuYS5Vyv/8KFcKj7Gdny77cyVC+vLmJCWbgScGxrNkmEcUD2ATgegB7ACwDcDszr3Pa5mEAycz8EBH1BNCNmXt4G9eKZptGUSbnugXFiwNDh0pvHyu+f/o0cPCgLK1ayanjjoQE4Ny5vOsKFZJ6FNWqyXLiBPD77/m3A4CSJaVE/003Sf2UsmV9/55g8OcYnDgBbNoEbNwoy4YN8rppE3DmjPtxiYDSpeU4FCoki/G387o1a6Srtidq1gSaNJHCeMZrgwa5xeJCPQfMwG4ZatZ0XzujcGHgzTeBW26R5pZmkZMDzJsHfP211KE5c0bO53//zXsuR8IxKFVK2mFkZeUu587lfZ+VBfzxh/tieAkJUpwxJwfIzpbF099Hjsh7syGyZlxXzDqPx4zJbRxbowaQlub/9z39H5OSpFcbM3DqFHDsmDSsPXYs7999+7ofN1zHMC7O/TMhXPuPFBn8xVOzTSsVnMsADGLm9o73AwCAmd902ma6Y5tFRFQIwD8AKrAXoazqJh7KxRTq971djNu2AYcOAXv2eF48FWyrWFHkKVLE/98RCsEeg5wcUVI8/df798/7QHH3+uuv7r9LBBw/LoqeVfKbiZ0y2HlDO3YMGDcOeOQR94qq8WCyGk/HAJBWF66KtevirddXt25SQTkuTl49/f35557HGDMGKFFCFIYSJdz/Xbeu94d7OLD7WgpVyfKlIFmN3fuPFBn8xZOCk8+kY9YC4BYAw5ze3wngU5dt1gCo5vR+K4BEN2M9AGA5gOU1oiVqNQBCNWt783lHC6Gate1278QCkXAM7T6XI+E8DHWMaM9oNAuz45jCeQzt3n+kyOAvCHcMjpkKjvMSq4X+QrkYI+HBFCoFOVU/UoiEY2j3uRwJ56FZY0RzTapIwO5jaPf+I0UGf7BDwbkMwHSn9wMADHDZZjqAyxx/FwJwCA63maclVhWcUIiEB5MZFORii5GC3ccwEs7lSDgP7f4/KEo04UnBsTIGpxAkyPg6AHshQcb/x8xrnbb5H4CmnBtkfDMz3+ZtXKticKIdu33eimIWei4rihIIYQ8yduy0E4APAcQD+JqZ04joVYi2NZmIigIYBSAFwBEAPZl5m7cxVcFRFEVRFMXAk4JTyMqdMvNUAFNd1r3k9HcGgFutlEFRFEVRlIJHnN0CKIqiKIqimI2lLiorIKKDANxk55tGIiTYWQkePYaho8cwdPQYho4ew9DRYxg6vo5hEjNXcF0ZdQqO1RDRcne+PMV/9BiGjh7D0NFjGDp6DENHj2HoBHsM1UWlKIqiKErMoQqOoiiKoigxhyo4+RlqtwAxgB7D0NFjGDp6DENHj2Ho6DEMnaCOocbgKIqiKIoSc6gFR1EURVGUmEMVHEVRFEVRYg5VcBwQUQci2khEW4joObvliUaIaAcRrSailUSk/TT8hIi+JqIDRLTGaV05IvqdiDY7Xi+wU8ZIx8MxHEREex3n40pH6xjFA0RUnYjmENE6IlpLRP0d6/Vc9BMvx1DPRT8hoqJEtJSI/nYcw1cc62sR0RLHM3ocERX2OZbG4ABEFA9pDHo9gD2QxqC3M/M6WwWLMohoB4AWzKxFrQKAiFoDOAngW2Zu4lj3DoAjzPyWQ+G+gJmftVPOSMbDMRwE4CQzv2enbNECEVUGUJmZ/ySiUgBWALgJwN3Qc9EvvBzD26Dnol8QEQEowcwniSgBwAIA/QE8AeAnZv6eiL4A8Dczf+5tLLXgCK0AbGHmbcycCeB7ADfaLJNSQGDmeZBms87cCGCk4++RkJuk4gEPx1AJAGbez8x/Ov4+AWA9gKrQc9FvvBxDxU9YOOl4m+BYGMC1ACY41vt1HqqCI1QFsNvp/R7oSRkMDGAGEa0gogfsFibKqcTM+x1//wOgkp3CRDH9iGiVw4WlrhU/IaKaAFIALIGei0HhcgwBPRf9hojiiWglgAMAfgewFcB/zJzl2MSvZ7QqOIqZXMnMFwPoCOB/DreBEiIsfmT1JQfO5wDqAGgOYD+A922VJkogopIAfgTwGDMfd/5Mz0X/cHMM9VwMAGbOZubmAKpBPCwNghlHFRxhL4DqTu+rOdYpAcDMex2vBwBMhJyYSnD86/DnG379AzbLE3Uw87+OG2UOgK+g56NPHDEPPwIYw8w/OVbruRgA7o6hnovBwcz/AZgD4DIAZYmokOMjv57RquAIywDUdURpFwbQE8Bkm2WKKoiohCOoDkRUAkA7AGu8f0vxwmQAvR1/9wbws42yRCXGQ9lBN+j56BVHcOdwAOuZ+QOnj/Rc9BNPx1DPRf8hogpEVNbxdzFI8s96iKJzi2Mzv85DzaJy4Ejb+xBAPICvmTnNXomiCyKqDbHaAEAhAN/pMfQPIhoLoA2ARAD/AngZwCQA4wHUALATwG3MrEG0HvBwDNtAXAIMYAeAB51iSRQXiOhKAPMBrAaQ41j9PCSGRM9FP/ByDG+Hnot+QUTJkCDieIgRZjwzv+p4xnwPoByAvwDcwcxnvY6lCo6iKIqiKLGGuqgURVEURYk5VMFRFEVRFCXmUAVHURRFUZSYQxUcRVEURVFiDlVwFEVRFEWJOQr53kRRFMU8iCgbkkZr8D0zv2Xh/toAyGTmhVbtQ1GUyEMVHEVRws0ZRxl2y3FUPm0D6TSuCo6iFCC0Do6iKGGFiE4yc0mXdWUALAXQlZk3Ogr3zWbmr4joJKS8fTtIs8eezHyQiOoAGAKgAoDTAPow8wYi+gZABqTR4V4AlwPIBnAQwCMALoQUAswGcIyZtWeaosQgasFRFCXcFHN0CjZ4k5nHEVE/AN8Q0UcALmDmrxyflwCwnJkfJ6KXIMpJPwBDATzEzJuJ6BIAnwG41vGdagAuZ+ZsIhoE4CQzvwcARLQaQHtm3muUhFcUJfZQBUdRlHDj1kXFzL8T0a0Qq0wzp49yAIxz/D0awE+Obs2XA/hB2v8AAIo4fecHZs72sP90iCI1HsBPHrZRFCXKUQVHUZSIgIjiADSEuJsuALDHw6YMyQD9z0sszylP+2HmhxwWnxsArCCiVGY+HLTgiqJEJJomrihKpPA4pGvw/wEYQUQJjvVxyO0i/H8AFjDzcQDbHRYfkNDMdUAHJwCUMt4QUR1mXsLML0Hicqqb/1MURbEbVXAURQk3xYhopdPyFhHVB3A/gCeZeT6AeQBecGx/CkArIloDibF51bG+F4D7iOhvAGsB3Ohhf1MAdHPs6yoA7xLRasd4CwH8bcmvVBTFVjSLSlGUiMZd1pWiKIov1IKjKIqiKErMoRYcRVEURVFiDrXgKIqiKIoSc6iCoyiKoihKzKEKjqIoiqIoMYcqOIqiKIqixByq4CiKoiiKEnOogqMoiqIoSsyhCo6iKIqiKDGHKjiKoiiKosQcquAoiqIoihJzqIKjKIqiKErMoQqOoigxBxENIqLRdsuhKIp9qIKjKIppENGVRLSQiI4R0REiSieilnbL5S9EVISIhhPRTiI6QUQriaij3XIpihI4hewWQFGU2ICISgP4BUBfAOMBFAZwFYCzdsoVIIUA7AZwNYBdADoBGE9ETZl5h52CKYoSGGrBURTFLOoBADOPZeZsZj7DzDOYeRUAEFEdIppNRIeJ6BARjSGissaXiWgHET1NRKuI6JTDklKJiKY5rCkziegCx7Y1iYiJ6AEi2kdE+4noKU+CEdGlDsvSf0T0NxG1cbcdM59i5kHMvIOZc5j5FwDbAaSadpQURQkLquAoimIWmwBkE9FIIupoKCNOEIA3AVQB0BBAdQCDXLbpDuB6iLLUBcA0AM8DqAC5Xz3qsv01AOoCaAfgWSJq6yoUEVUF8CuA1wGUA/AUgB+JqIKvH0RElRyyrPW1raIokYUqOIqimAIzHwdwJQAG8BWAg0Q02aEkgJm3MPPvzHyWmQ8C+ADiCnLmE2b+l5n3ApgPYAkz/8XMGQAmAkhx2f4Vh9VlNYARAG53I9odAKYy81SHVeZ3AMsh7iePEFECgDEARjLzBv+PhKIokYAqOIqimAYzr2fmu5m5GoAmEGvNh4BYQ4joeyLaS0THAYwGkOgyxL9Of59x876ky/a7nf7e6difK0kAbnW4p/4jov8gilhlT7+DiOIAjAKQCaCfp+0URYlcVMFRFMUSHFaPbyCKDgC8AbHuNGXm0hDLCoW4m+pOf9cAsM/NNrsBjGLmsk5LCWZ+y92AREQAhgOoBKA7M58LUUZFUWxAFRxFUUyBiBoQ0ZNEVM3xvjrEZbTYsUkpACcBHHPExTxtwm5fJKLiRNQYwD0AxrnZZjSALkTUnojiiagoEbUx5HTD55AYoS7MfMYEGRVFsQFVcBRFMYsTAC4BsISITkEUmzUAnnR8/gqAiwEcgwT9/mTCPucC2AJgFoD3mHmG6wbMvBvAjZBg5YMQi87TcHP/I6IkAA8CaA7gHyI66Vh6mSCroihhhJjZbhkURVECgohqQtK3E5g5y2ZxFEWJQNSCoyiKoihKzKEKjqIoiqIoMYe6qBRFURRFiTnUgqMoiqIoSswRdc02ExMTuWbNmnaLoSiKoihKBLBixYpDzJyv9UrUKTg1a9bE8uXL7RZDURRFUZQIgIh2uluvLipFURRFUWIOVXAURVEURYk5VMFRFEVxZswYoGZNIC5OXseMsVsiRVGCIOpicBRFUSxjzBjggQeA06fl/c6d8h4Aemm3BkWJJtSCoyiKYjBwYK5yY3D6tKxXlIJGlFsz1YKjKIpisGtXYOsVJVaJAWumWnAURVEMatQIbL2ixCoxYM1UBUdRFMUgLU3M8c4ULy7rFaUgEQPWTFVwFEVRDBo0AHJygAsukPfFigFDh0aNSV5RTCMGrJmq4CiKohgMGQKUKAFs3w7cfjuQmKjKjRIcUR6gi7Q0oHDhvOuizJqpCo6iKAoAHD4MjB0L3HEHUKYMkJoK7N4NHDhgt2RKtGEE6O7cCTDnBuhGk5LTqxfQuXPeda+9FlUKv6UKDhF1IKKNRLSFiJ5z83kNIppDRH8R0Soi6mSlPIqiKB4ZMQLIyAD+9z95n5oqrytW2CeTEp3EQIAuAKBIEaBWLWDvXiA+Hjh40G6JAsIyBYeI4gEMAdARQCMAtxNRI5fNXgAwnplTAPQE8JlV8iiKongkJwf4/HPgqquApk1l3cUXy6sqOEqgxECALgBgwwaJS6tSBbjhBuCbb4Bz5+yWym+stOC0ArCFmbcxcyaA7wHc6LINAyjt+LsMgH0WyqMoiuKe6dOBbdtyrTcAULo0UK+eKjhK4HgKxK1ePbxyhEJODrBxI1C/vry//37gn3+AqVPtlSsArFRwqgLY7fR+j2OdM4MA3EFEewBMBfCIu4GI6AEiWk5Eyw9GmYlMUZQoYMgQoFIloFu3vOtTU4Hly+2RSYle0tLEveNKiRIS6xUN7NkjbrUGDeR9x45A5crAsGH2yhUAdgcZ3w7gG2auBqATgFFElE8mZh7KzC2YuUWFChXCLqSiKDHM9u0yK33ggfxZIy1ayI1eA42VQOjVC2jVCiCSJSkJeOghYOtW4JJLxPUT6RgyGgpOoULAPffItbJ3r31yBYCVCs5eAM72uGqOdc7cB2A8ADDzIgBFASRaKJOiKEpevvhCUnkffDD/ZxporARDZiawejVw113i6tmxQ2K85swBTpwALr0UmDHDbim9s3GjvBoKDgDce6/8nm++sUWkQLFSwVkGoC4R1SKiwpAg4sku2+wCcB0AEFFDiIKjPihFUcJDRgYwfDhw001AVVcPOoCUFHlVN5USCDNnAv/9B9x6a971l18OLF0qFp2OHYFPPpE08khkwwagbFmgYsXcdXXqANdeK9dMTo5tovmLZQoOM2cB6AdgOoD1kGyptUT0KhF1dWz2JIA+RPQ3gLEA7maO1P+2oigxx7hxEhPhHFzsTOnSEmSpFhwlEH74QWopXX99/s+SkoD0dKkx8+ijQN++kZmZtGGDnPtEedfff7+4defMsUeuALA0BoeZpzJzPWauw8xpjnUvMfNkx9/rmPkKZm7GzM2ZOcJtdkpME+2VR5XAGTIEaNgQaNPG8zapqargKP6TmQlMmgTceGP+mC6DkiWBiROBZ58FvvwS6NABOHIkrGL6xEgRd6VbN2llEgXBxnYHGStKZBALlUcjhWhRFJctk+Xhh/PPUp1JTZVA43//DZ9sSvTiyT3lSlwc8NZbwMiRwIIFkRV8fPw4sG+fewWnaFHgzjuBn36K+IwwVXAUBYidyqN2E02K4mefyUz6rru8b9eihbyqFUfxB2/uKXfcdRcwezZw7JgEH//+u7Xy+cOmTfLqTsEBgPvuE0vV6NHhkykIVMFRFCB2Ko/aSUYG8MQT0aEoHj4MfP+9zERLl/a+bUqKWHhUwVF84eyeclcHxxNXXCHBxzVqSPDxkCGWiegXhiXJKPLnSnKypMEPGxa5QdJQBUdRBE+VRz2tV4RDh4BvvwW6d5fO257qxezaFVk3wq+/FoXs4Yd9b1uqlFQ01kwqxRezZvnnnnJHzZoSfNypE9CvH9C2rQQk2+Hq3bBBek/VqeN5m/vvB9asEcUsQlEFR1EAqTxavHjedcWLy3olL5s3A++/D7RuLdV/e/cGFi8Wa4hzSqkzzBLM+/HH8gCwk+xsqUnSujXQpIl/39FAY8UffvhBLIL+uqdcKVVKgo87dxZlyZgYhNvVu2GDKDeegqQBoGdPqcwcwcHGquAoCiCVRz9z6vVatCgwdKisL0i4CxDOzgYWLgSeew5o1EisGU89JTEDAwdKoO7u3aI0fPCBe0XxoYekpkb//lJv5sEHgb//tuEHAvjtN0lz9ZQa7o4WLaR66z//WCeXEt1kZopyEqh7ypX4eCkS6Eo4Xb0bN3qOvzEoVQro0QMYO1aKF0YizBxVS2pqKiuKJfz9NzPAXLw4c+3adksTfkaPlt8uc0ZZ4uOZS5WSvwsVYm7blvnjj5m3b/c+TlISM5G8jh6d+9ny5cz33cdcrJiMecUVzGPGMGdkWPzjnOjUiblyZebMTP+/M3euyPvLL9bJpUQ3U6fKOTJ5cuhjEeW9Do2FKPSxfZGVxVy4MPMzz/jeduFCkWvYMOvl8gKA5exGX1ALjqIYrFkjr506yQw/I8NeecKNu0yy7GxZxo4FDh6UDI9HHhHrjid69ZLS9EaJemcrWGqqmLT37hVrz7//yuc1asj+d+2yNs182zZg2jQx9yck+P89DTRWfGG4p9q1C30sO2MCd+wQa5SnAGNnLr1UrLoR6qZSBSdSiJbaIbHMmjXy0OvaVeZLmzfbLVF48ZQxduaM+NvLljVvXxdcADz+uJjCp0+XG+Vbb0lQZe/e1qWZf/65XGN9+gT2vVKltKKx4plz54LLnvKEnTGBrk02vUEkwcaLF+dOECMIVXAigWiqHRLLrFkjD7HkZHkfKUW3woUds8a4OJnx/vyzWFdKlxaLkTNmxR6cOSPZU926ue875YvUVM2kUtwzaxZw9Ghw2VPu6NVLYgCTkuR9oULhiwn0lSLuyp13ysRw+HDrZAoSVXAiAS0yFxmsXi1ZNXXryvuCpuCkpQHFiuVdF85MsqQkz8GKZtQj+v57KYcfSHCxM6mpUt1VA40VV8x0TxkYrt6XXxal/6abzBvbGxs3AhUqAOXL+7d9YqJMGr79Fjh71lrZAkQVnEhAi8zZz4kTcjNp0kQe6klJcqEXJHr1EnMzIKbnpKTwZ5J5shZdeGHoY3/2GdC4MXD11cF9XysaK+44d06yp7p2Ncc95UpKilj2V60yf2x3GE02A+H++2XyMGmSJSIFiyo4kYAWmbOfdevk1aiL0qBBwbPgAJKiWrSoBFi7BgiHA3exB4DUzgmle/HSpeJe8tV3yhsaaKy4w2z3lCvNm8vrX39ZM74rnppseuO66yR2NMKCjVXBiQTS0vIXVNIic+HFCJBr2lReDQUnkqrvhoP0dCnB7q3Al5U4xx4YVqTBg4FatYD27aUxYTAMGSJ9p+64I3jZSpaUma3G4SjO/PCDBKGb6Z5ypkYNCcpfudKa8Z05fFiyJQNVcOLipD/VzJmSgRohqIITCfTqJSdUoUK5655/vuAVmbOT1atFqTTSnxs0AE6dknTmgsLp0zJLvOIKe+VwTTN/7DFRvFq3Bu6+W2ISAlE8Dx0Cxo2Tpoa++k75okULteAouRjZU127iuXTCojEehgOC47hlg9UwQHk2oyLk0D+CEEVnEjg2DFg/Xq5kR89Kg/arVvtlqpgsWaNxGfEOS4J4wIvSG6qZcuArCzg8svtliQ/ZcsCU6cC99wDvPqqpJL7G9A4fLhs60/fKV8Ygcb794c+lhL9zJ4tsSdWuacMUlJkEnbunLX7CUXBqVZNGoWOGCH3kQhAFZxIYNo0OXG7dZMbee/ewHffialQCQ9r1uTtS1QQFZz0dHm97DJ75fBE4cKirLz+OjBqlLisjh71/p3sbOCLL4A2bUSBDZXUVHlVK44C5Lqn2re3dj8pKaKkW30/2rBBrjNvhTy9cf/9YvWePt1UsYJFFZxIYOJEaVp46aXyvl8/OZm/+speuQoKBw9KRV0j/gaQ/0fp0gVLwVm4UBQ7f9ND7YBIyieMGQMsWiTK2LZtnrefNk3cXGZYb4DoCTTWwqHW45w9ZZV7ysAINLY6DmfDBimTER8f3PdvuEHunRESbKwKjt2cPSum9xtvzHWPNGok3Wg/+8x6k6SSG2DsbMEhkod9QUkVz8kRBcfu+Bt/+b//k7YRBw7IxGDJEvfbDRkCVKliXg2RkiXlvIhkBUcLh4aHcLmnAAluL1rU+jicYDKonElIkFicKVMiwo2rCo7dzJoFnDwp7ilnHn1UTH0TJ9ojV0HCnYIDFKxU8Q0bxN0TLQoOIEHHixaJi6BNG+DHH/N+vmWLdA4PtO+ULyK9orEWDg0P4XJPAZKAkpxsrYJz7pxYQ0NRcADJpsrODj7j0URUwbGbiRPlIrnmmrzrO3UC6tQBPv7YHrkKEmvWAOXK5S8m16ABsGeP5+q6scTChfIaiQHG3qhfX/rgNG8uM+n33svNsPriC3kwPPCAuftMTZXZaQTMUN2ihUOtx3BPdelivXvKICVFXFRWla7YulWCgwMt8udK3bpSTHPYMNvLbKiCYyfZ2cDkyeK3dK2AGRcnsTjp6ZFtDo8F1qyR+BvXAnDGTGbTpvDLFG7S06Xker16dksSOBUqiLuge3fg6afFvVujBvD++xIwOXu2ufuL9IrGWjjUeubMCZ97yqB5cyl4uWOHNeMH0mTTF/ffLwrT3LmhjxUCquDYyaJFEkPgKT7gnnuAEiWATz4Jq1gFCub8GVQGBSmTKj1drDfBVvm1m2LFpNZN587i9t29W9afPm1+/Enz5nKcItVNlZaW3yWnhUPN5YcfJB4rHO4pg5QUebUq0DjQJpve6N4dKFPG9mBjVXDsZNIkmWF27Oj+8zJlJGBr7FhRhBTz2b0bOH7cvYJTp45kE8S6gnPwILB5c/S5p1yJi5NaIa6YHX8S6YHGvXoBDRvmKjmFC4e/p1gs45w95dqc1kqaNpVz3Ko4nA0bJCA/1GKYgByXO+4AJkzwXcrBQixVcIioAxFtJKItRPSch21uI6J1RLSWiL6zUp6Iglkukuuu835C9esHZGbKDUoxH08BxoA8GGrXjn0Fx4i/iaYAY0+EK/4kkisaM4viftddUjMoM1OCsBVz+OMPaWkQTvcUIFa4Bg2sU3A2bjTHPWVw//2SJWxj9p5lCg4RxQMYAqAjgEYAbieiRi7b1AUwAMAVzNwYwGNWyRNxrF4tEeuu2VOuNGggZlBNGbcGQ8HxVASuIKSKL1wos30jtiSaCVf8iRFovG+fueOawebNMmu+9NLc+8vPP9srUywxfnz43VMGVrVsYA6ui7g3mjeXGkxPPGFbPSYrLTitAGxh5m3MnAngewA3umzTB8AQZj4KAMxccPwwkyaJH79rV9/bPvqo3Exd02AjjWgsLrZmjZQYv+AC9583aCBBxtnZ4ZUrnKSnywM7XNkgVuKuG7kV8SeRXNHYqAl0ySXiqqpXT8tNmIVz9lQ43VMGzZtL+RCzq9wfOCABzGZacMaMkQnAuXO21WOyUsGpCmC30/s9jnXO1ANQj4jSiWgxEXVwNxARPUBEy4lo+cFYaV8wcaLEPFSq5HvbDh2Aiy6K7JTxaC0utnq1e/eUQYMGYmbduTN8MoWTs2clWDYW3FOA+27kVsSfNG8uinwkKjiLF4uFoVEjOQbduolbxcZYiJjBLveUgVWBxmZmUBkMHCjuUWfCXI/J7iDjQgDqAmgD4HYAXxFRWdeNmHkoM7dg5hYVKlQIr4RWsGOHnKC+3FMGcXHAI49I1tWyZVZKFjzRWFwsK0uanPpScIDYjcP5809RcmJFwQHydyO3IrjWCDSOxEyqJUuAVq1yy+3ffLOc67/8Yq9csYCRPdXB7VzcegwFx2w3VShNNj0RAfWYrFRw9gKo7vS+mmOdM3sATGbmc8y8HcAmiMIT20yaJK+BlI+/+265sCI1ZTwCTuaA2bpVHu7eFBzDJx2rCo7RYDPaM6jsIDU18iw4Z84Af/8t7imDFi2AqlXVTRUqWVlyDDt3tsc9BUhB0ho1zFdwNmwQV261auaNGQH1mKxUcJYBqEtEtYioMICeACa7bDMJYr0BESVCXFZeOufFCBMnSspfnTr+f6d0aamL8/33wD//WCdbsETAyRwwRoCxc5NNV8qXlwJ4sazg1Knjn6tUyUuLFnItRlKg8Z9/yoPYaNwLiAX4ppukbYWrlVXxnz/+AA4dAm67zV45rAg03rBBYrXiTFQJwhUP5wXLFBxmzgLQD8B0AOsBjGfmtUT0KhEZkbXTARwmonUA5gB4mpkPWyVTRHDwILBgQXDN//r1k4CtSEwZT0vL34E20ouLrV4tMQoNG3rfLlZ7UjFHV4PNSCMSA40XL5ZXZwsOIO7wM2eAGTPCL1OsYGRP2eWeMmjeXBIfTp0yb8xQm2y6I1zxcF6wNAaHmacycz1mrsPMaY51LzHzZMffzMxPMHMjZm7KzN9bKU9EMGWKxAb4G3/jTL16UhTw88/zB2/ZTa9eYukwTLdFikR+cbE1ayR425e5OVZTxbdulewJdU8FhxFoHElxOEuWSAajq0WudWvJFPzpJ1vEinoiwT1lkJIik5NVq8wZ78wZiVUzW8EBwhMP5wW/FBwiqkpElxNRa2OxWrCYZdIk0WSbNw/u+48+KmbxCRPMlCp0DhwA/v0XGDQIePttiW1p1cpuqbzjqUWDKw0ayO87csR6mcKJEX+jFpzgKFFCrH+RZsFxtd4AUueoSxeZYGk9rcAx3FN2ZU85Y3ag8ZYtojBZoeDYjE8Fh4jeBpAO4AUATzuWpyyWKzY5eVJMxDfdFHzPn3btxJITaSnjztVwe/WSme2oUfbK5I2MDCmI5q+CA8SeFWfhQmkH0qiR720V9xiBxjZ3TQYgsUC7d+eNv3GmWzepdWJzA8So5IcfRKH11FYnnFSvLsHGZik4ZvagijD8seDcBKA+M3di5i6OxY/qdEo+fvtNLBvBuKcMjJTxJUtyC3pFAunp4pYyMjbathUFJyfHbsncs369yOYtwNggVlPF09OByy4zN7CwoJGaGjmBxs4F/tzRrp24VwpKNpVZhUezssS1FwnuKUAmxykp5tXCMe5r9eqZM14E4c+dbRuABJ9bKb6ZNEmyckJ1CfTuDZQqFVkp4wsWiHJTpIi8v+su8bnOn2+rWB7x1oPKlZo1pS9VLCk4//0HrF2r7qlQiaRA48WLxRVluDBcKV5cAmQnTYrciYdZmFl4dO7cyHFPGTRvLkkSZrgbN2yQsAnXjKcYwB8F5zSAlUT0JRF9bCxWCxZzZGZKoa2uXYFChUIbq1Qp4N57Jap//35z5AuFM2fkBu/8sLzpJsk4+PZb28Tyypo1orRcdJHvbePjgbp1Y0vBWbRIXjXAODQiqaLxkiUij7eWG926ibUpUguGOuOvBeb4cVHWp02TxIYXXgAefNB94dFnnw3cnRhJ7imDlBTxBphxT7IigypC8OdJOxn569cogTJ3LnDsWHDp4e7o10/icL78UgJ77WT5cplJOCs4JUrIjOeHH8TSFGmzgzVr5KJO8NM42aCBzJhihfR0Udw8uTMU/zACje3OpMrKEqXlvvu8b9e5s0ywJk6M7P+9YYExlJSdO2VS9/PPEn+ye7cUEd29W+6rzsTHe+4dt3evZJi1bJl3qVjRvQzPPy/7KV5cjlmkZIU6Bxr742b3BLPEFl51lTlyRRg+FRxmHuko1Gc46DYys4bhB8rEiXIzvP56c8a76CKgUyfgiy+AAQNyXUN2sGCBvLpaA+66CxgxQkzi//d/YRfLK6tXS+qsvzRoIDfXc+f8V4oimYULZbZfooTdkkQ/qanA9OnysAg2eSBU1q4VZcCX0nLBBcA118j96M037ZPXF+5av2RmyoQpMVECbevUAdq0kWKi1avnvlauLPdHd/3jLrgAuOEGUQanTcu15tSokVfh2bYN6N8/V4bTp0XhAkxTcs6dO4c9e/YgIyMjuAF++02s5OvXBy9EVpZk5JYrF9o4YaJo0aKoVq0aEvy8B/tUcIioDYCRAHYAIADViag3M88LXswCRk6OPBw7dDA3SO3RR4H27eWiv+MO88YNlPR0UQASE/Oub91afLvffhtZCs6xYzLz8yf+xqBBA7kZbN0a/ebcc+fEneFrtq/4R4sWco7v2ycB9nZgFPjzlEHlTLduwMMPywMtUjPoPDW3JfKvk3ZaWl4LECBWmE8+yVVQTp6Uys/LluUuP/7oeUyjt55JCs6ePXtQqlQp1KxZExSMokkk7rtQsp+OHxdXV716Ui0/gmFmHD58GHv27EGtWrX8+o4/MTjvA2jHzFczc2sA7QEMDkHOgseyZXLzM8s9ZXD99fKw/egj+9JUc3I8V8ONiwPuvBP4/ffIyDIxWLtWXgNVcIDYiMP5+2+5WWuAsTkYgcZ2uqmWLJEJRu3avre98UZ5jdSif19/7fkzf1u/+FNFt2RJmYQ9+aS0wNm6VYKJf/vN87gm9tbLyMhA+fLlg1NuAFHYTp8O7d5vWI+8xW1FCESE8uXLB2Tx8kfBSWDm8wVAmHkTNKsqMCZOFL/3DTeYOy6RpIwvX25fyvj69cDRo8CVV7r//M47RQn67rvwyuWNQDKoDGKp6aZzzSIldCIh0Ngo8OfPw7JKFbH0RGK6+BdfiGWxadP81u5AW78EU0W3fHmxiicluf/c5N56QSs3gByP7OzQqtpnZEjMUpS43QM9Xv4oOMuJaBgRtXEsXwGIoNrkUcCkSeIrvuAC88e+6y4p1mZX4T9f1XDr1ZOb6ciRkVEMDZD4m5IlPd/E3FGqlDwYYkHBSU+XWAUzOwcXZIoXF1ePXQrOf//JRCOQoOFu3cQ948kVZAcffwz07SuB0EuXAl99ZV8fowhoFOkTQ75QGqhmZIj1JlJjsULEHwWnL4B1AB51LOsc6xR/WL9eotRDKe7njZIlZcbzww/2uIHS04EKFbynW991l1hNzCpMFSpGi4ZAL+pYaLrJLP8ztd6YS2qqWFLtUOKNlG9/4m8MjPvRpEmmixMU774rQb033yxxMEWL2tvHKAIaRbqSL2t+osPCFaKCkzZ8OBo3bozk5GQ0b94cS7x4A+bPn4/GjRujefPmWLRoEaZOnRr8vsOATwWHmc8y8wfMfLNjGczMZ8MhXExg3EAMv7cV/O9/Yqr84gvr9uGJ9HRxT3lTFnr0kJozkVATh1ksOIG4pwwMBSdSLFHBsHu3pMqqgmMuqanSr2zv3vDv23ggtWzp/3fq1gUaN44MN9XrrwPPPAP07CmxMIUL2y2RYHOjSGfc1i18MA5jZlUKXsHJzsai5cvxyx9/4M8//8SqVaswc+ZMVK9e3YscYzBgwACsXLkSGzdujHgFx2MWFRGNZ+bbiGg1gHx3dGZOtlSyWGHiRGk6aWV2Re3aUhchLU1uFjVqyN9WX5D//COBeX19GPTKlZNGf2PGAO+8Y6+/98AB4PDh4BWcY8dkDNduzdGC4VLUAn/m0qKFvK5YEX7X3+LFUounbNnAvtetG/DGG5KVVKGCJaJ5hRl4+WXgtdckVm/ECIkHKYA89ph3A/fixZLs5Mzp08B9L1XFVz+cBkrm/07z5sCHH3rZaUYG9h86hMTERBRxlBlJdGTCzpo1C0899RSysrLQsmVLfP755xg1ahTGjx+P6dOn49dff0V6ejrOnDmDBQsWYMCAAVi/fj22b9+Obdu2YdeuXRg8eDAWL16MadOmoWrVqpgyZQoSEhLw6quvYsqUKThz5gwuv/xyfPnll8jOzsZll12Gd999F23atMGAAQMQFxeHtBBdgt4sOP0dr50BdHGzKL7Ys0fMx1a5pwzGjJHMoJyc0MuSB0Ig3ajvuktupNOnWyuTL4IJMDaIhUyqhQul9k2yzk9MpVkz8R2EO5OKWSw4wRTt69ZN7hlTppgvly+YgeeeE+XmvvsKtHLjD67Kzfn1mSTHkoNovZGRgXaXXord+/ahXr16ePjhhzF37lxkZGTg7rvvxrhx47B69WpkZWXh888/x/3334+uXbvi3XffxdixY/Hqq6+iR48eWLlyJXr06AEA2Lp1K2bPno3JkyfjjjvuwDXXXIPVq1ejWLFi+PXXXwEA/fr1w7Jly7BmzRqcOXMGv/zyCwoVKoRvvvkGffv2xcyZM/Hbb7/h5ZdfDvZwncejBYeZjR4ADzPzs86fOTqMP5v/W0oefv5ZXs1OD3dl4ED36r2JNRvckp4uvvKLL/a9bceOksb67bcSRGgXRjXiYKp/Ois4V19tnkzhJD1dYjVCbRei5MWuQONt2yS1OZD4G4OUFIktmThRqgSHC2bg8celvEXfvsCnnxb4hq9eLS2QmBt38eBJ1Rl/fLlRXI5lygS204wMlCxeHCtWrMD89HTMmTMHPXr0wIABA1CrVi3UczTf7N27N4YMGYLHHnvM55AdO3ZEQkICmjZtiuzsbHTo0AEA0LRpU+zYsQMAMGfOHLzzzjs4ffo0jhw5gsaNG6NLly5o3Lgx7rzzTnTu3BmLFi1CYRNclf6cVe5K70ZQU44IZuJEeShaXRjOU20GE2s2uGXBAnG/+XMiJiRIsb+ff5a0crtYs0bM8e5Ks/uialWxfkSrBefECamBo+4pa2jRQhSccMZo+eog7g0iseL8/rucG+EgJ0diBj/6SPwyQ4YUeOXGHzwndTnOtTNnAh80IwMoUgTxCQlo06YNXnnlFXz66aeYFELgueHqiouLQ0JCwvm07ri4OGRlZSEjIwMPP/wwJkyYgNWrV6NPnz556tqsXr0aZcuWxYEDB4KWwRmPZxYR9XXE39QnolVOy3YAq0zZeyxz9Cjwxx/WW28Az7UZTK7ZkIfTp6UPSiDBqnfdJTUbxo+3Ti5fGBlUwRAXJ2nv0argLF0qDxgNMLYGI9B4z57w7XPxYnnSBXtOd+sm1l9vxe3MIjtbXOeffy5NLz/4IGbTk83GY1LXnfEywQwm0DgjAxv37cPmzZvPr1q5ciXq1KmDHTt2YMuWLQCAUaNG4Wo3FutSpUrhRICKsaHMJCYm4uTJk5gwYcL5z3766SccOXIE8+bNwyOPPIL//vsv8N/kgjfV+TtIrM1k5I29SWVmG/sCRAm//CIXtNXxN4A9NRuWLpXWBYE8LC++WDI37MqmysmRWKVgHwZAdKeKp6fL3TEYd4biG6OicTjdVEuWiOUoWJfjFVeIRdPqqsZZWcA99wDDhwMvvRTZfbAiFI9JXUZF40BgBjIycDIrC71790ajRo2QnJyMdevW4a233sKIESNw6623omnTpoiLi8NDDz2Ub4hrrrkG69atQ/PmzTFu3Di/dlu2bFn06dMHTZo0Qfv27dHSkfl36NAhPPfccxg2bBjq1auHfv36oX///j5G8+t3sl8LgIoAahiLv98ze0lNTeWooFs35ipVmLOzw7O/0aOZk5KYHSFn/P771u7vtddkP0eOBPa9t9+W723aZI1c3ti2TfY9dGjwY7zyCjMR8+nT5skVLtq1Y27a1G4pYpdTp5jj4phfeCE8+ztzhjkhgfmZZ0Ib5777mEuVYs7IMEcuVzIzmXv0kGvv9det2UcUsm7dOnMG2ruXedky5qws/7+TkSHfOXDAHBnCiLvjBmA5u9EXfDo/iagLEW0GsB3AXEjTzWmhq1YxzOnTYvK96abw+ZcN9d6IRDtnccP39HSxxgRanblXL5m5jRpljVzeCCWDyqBBA1Ehncy6UUF2trgz1D1lHcWLyzURLgvOypVynQcTf+NMt24SgzN7tiliAchbla5MGWDcOCnmN3CgeftQBMN6H0gcThT1oAoFf56+rwO4FMAmZq4F4DoAiy2VKtr5/Xc52cLhnnKlRg0p+OWtK26oZGcDixYF97CsWhVo21YUnJwgUhtDwVBwGjcOfoxoTRVfu1Y6B2uAsbWEs6JxIB3EvXHddVIR3ayif65V6c6ckSSDypXNGV/JSzAtG1TBOc85Zj4MII6I4ph5DoAWFssV3UyaJEW37Eol7t5d6u9YlUW1dq0UvPPUYNMXvXuLtWn+fFPF8smaNaIAli4d/Bh164oFKtoUHG2wGR5SU6XeUzgCjZcskaKCVaqENk7RokCnTpLhmJ0dulwDB+Z/2J47p9Ybq0hIkBisQBWcQoWipslmsPij4PxHRCUBzAMwhog+AnDKWrGimKwsKZzVubN9J0/37vJqVeBgIAX+3HHTTTJjDHew8erVwdW/caZYMUlhiDYFJz0duPBCoFYtuyWJbZwrGlvN4sXmBYx36yYZYIsWhT6WXWUrCipEcl8KVMGJcesN4J+CcyOA0wAeB/AbgK3ws5IxEXUgoo1EtIWInvOyXXciYiKKfsvQ/PnSCiAc6eGeuOgiqVTrlIJnKqE+LEuUAG65RRqEhtIoLhDOnROlJJT4G4NozKRKTxf3lGauWEuzZlKR1+qKxv/+K1bQUONvDDp1knTjUN1UR496nthZWbaioFO8uLgC/XX7q4JzngcBVGbmLGYeycwfO1xWXiGieABDIEUBGwG4nYgaudmuFKQthOcWptHEpEly4jgqONpG9+7ilti/3/e2geJPg01f9O4tgY1GtWer2bxZlByzFJyNG8MfQxQs+/cD27ereyocFCsWnorGRoE/syw4pUtLbNzEicHHDx08CFx7rbi5HAXfzmN12YqCTvHi51O/fZKVJfdCVXAAAKUAzCCi+UTUj4j87TLYCsAWZt7GzJkAvodYg1x5DcDbAPz4z0Q4zKLgXH+9WCns5JZbRB6zuwXv3Sszx1Aflq1by4xu5EhTxPKJGRlUBg0aiOUpnAXdQkHjb8JLOCoaL1kiliJ/2qT4S7duogivCqKO6759EnO4cSMwdarUu8lXlc6+btwxgXNmWs2aeXsNBhJo7BJgnJaWhsaNGyM5ORnNmzfHkiVLcP/992PdunWmiu9Kp06d3BbzGzRoEN577z1T9uFTwWHmV5i5MYD/AagMYC4RzfRj7KoAdju93+NYdx4iuhhAdWb+1dtARPQAES0nouUHDx70Y9c2MGaMBPvt2iUPFKsbXfqiUSN5EJudTRVq/I1BXJx0EP79d7k5Ws3q1fJAMKNthjHGxo2hjxUOjJ5hKSl2S1IwMAKNd+/2vW2wLF4s7jDXAp+h0LWrXJeBxu7t3CkTlt27pTxGu3ZeqtIpQeGamebaULloUfnf+ZMq7qTgLFq0CL/88gv+/PNPrFq1CjNnzkT16tUxbNgwNGqUz+FiKlOnTkXZsmUt3UcgRVoOAPgHwGFI0b+QIKI4AB8AeNLXtsw8lJlbMHOLChUqhLpr8zFOvn/+kfeHD4enm7cvuncH5s6VZnxmkZ4uN9XmzUMf68475Qb43Xehj+WLNWskA8oMs2y0pYovXCilA0xoXqf4gdUVjbOzJUvS7IrUFSvKxCUQq+/mzcBVV8k9b+ZMUXSUwHnsMaBNG8/Lffflt86cPi3r27QBrrkGeOghuecb3/HUHDMjQyxrRYpg//79SExMPN9DKjExEVWqVEGbNm2w3BFHNnz4cNSrVw+tWrVCnz590K9fPwDA3Xffjb59++LSSy9F7dq18ccff+Dee+9Fw4YNcffdd5/f3dixY9G0aVM0adIEzz6b26O7Zs2aOOR4NqWlpaFevXq48sorsdHEiaM/hf4eJqI/AMwCUB5AH2ZO9mPsvQCqO72v5lhnUApAEwB/ENEOSK2dyVEZaOwuLdLo5m0n3bvLzdDMOJcFCySw0YwMsfr15SY9cqT1dUNC6UHlSsWKUgYgGhScM2eAP/9U91Q4MQKNrVJw1q+X+DWzAoyd6dZNrJ1bt/redu1aUWjOnAHmzLFGHkU4e9b3+rg4/+ICHU02QYR27dph9+7dqFevHh5++GHMnTs3z6b79u3Da6+9hsWLFyM9PR0bXO55R48exaJFizB48GB07doVjz/+ONauXYvVq1dj5cqV2LdvH5599lnMnj0bK1euxLJly/I181yxYgW+//57rFy5ElOnTsWyZcv8OSJ+4U8Dk+oAHmPmlQGOvQxAXSKqBVFsegL4P+NDZj4GINF471CinmJmi9MPTOaff9z3sQfsT4ts3lwynX78UTT9UDl5UrpRDxgQ+lgGd90FPPywVGW1yoVy+rTcsO8wqYUakShn0aDgLFsmAYVa4C98/PSTPGzS0oDRo+XVTBeN2QHGznTrBjzxhFhxnnrK83Z//imuqMKFgXnzgIYNzZelIPHhh94/r1nT/XMmKUmaOgPiFt25U0phuAZ5O+OUQVWyZEmsWLEC8+fPx5w5c9CjRw+89dZb5zddunQprr76apQrVw4AcOutt2LTpk3nP+/SpQuICE2bNkWlSpXQ1FGGo3HjxtixYwd27tyJNm3awPC89OrVC/PmzcNNTlnG8+fPR7du3VDc4W7t2rWr92MRAN66iRvV0N4FsIuIyjkvvgZm5iwA/QBMB7AewHhmXktErxKReb/ALs6cAd54Q9wenrA7LZJIrDgzZwImdGbFkiViETLTGtCjh9wkrayJs26dWIjMsuAA0ZMqbgQYq4ITHgx3tdEqxTVWwgwWL5YWKd7uPcFSs6ZMjLy5qRYtkmypEiVUuQkX/jRU9ifQOCdHrD5Orvr4+Hi0adMGr7zyCj799FP8GEDcpuHaiouLO/+38T4rK8vvcazCVzdxAFgBYLnjdYXTe58w81RmrsfMdZg5zbHuJWae7GbbNlFhvWEGxo6VB9zAgZJa+d574e/m7S/du8vNdsqU0MdasECUpssuC30sg3LlgC5d5AFgVf8sI4Mq1CJ/zjRoIMHRx4+bN6YVpKeLrOXL2y1JwSAc7uolS8QdZFVNo27dRIkxYgqd+eMPyRKtUEFqfl10kTUyKHnp1Usy0bxlphUrJq/eFJzMTHmGORScjRs3YrNTX72VK1ciKSnp/PuWLVti7ty5OHr0KLKysgJSfgCgVatWmDt3Lg4dOoTs7GyMHTsWV7tU+G/dujUmTZqEM2fO4MSJE5hixrPKgUcFh5k7O15rMXNtx6ux1DZNgmhi0SKZCf/f/8mDefZsmek8+aTvk88uWrWScu5mZFOlp4uSUKZM6GM5c9ddYl6dPt3ccQ3WrBGTbZ065o1pVSaVt1TQQGEWC45ab8KH1VV8T5yQ89nKeJdu3eTccY3d++03oGNHub/Nm2e/hbqg4SszLS7Od0VjlxTxkydPonfv3mjUqBGSk5Oxbt06DBo06PzmVatWxfPPP49WrVrhiiuuQM2aNVEmgPt/5cqV8dZbb+Gaa65Bs2bNkJqaihtvzFst5uKLL0aPHj3QrFkzdOzYES1btvR7fJ+4azHuvACY5c+6cC2pqam+uqmbz44dzD17MgPMlSszf/11YK3p7ebRR5mLFmU+cSL4MbKymEuVYu7b1zy5DM6eZU5MZL71VvPHZmZu3545JcXcMdevl/Nh1Cjzxhw9mrl4cRnXWIoXl/WhyDh8uHkyKt5JSsr7/zOWpCRzxp89W8abNs2c8dyRk8Ncp45cNwYTJzInJDA3b8584IB1+y5ArFu3zvxBt25lXrnS8+f79zMvW8Z87pzfQ55wPDfOnTvHnTt35p9++ilUKUPC3XEDsJzd6AveYnCKOmJtEonoAqf4m5pwqWcTsxw/Djz/vASU/vwz8OKLwKZNwD33SJZEtNC9u2juU6cGP8bq1TJ7DLbBpjcKFxar2M8/S6l3s1m92tz4G0CsQYUKmRuHY7Z7w6yaRYr/uIuVIAJee82c8Y0O4q1amTOeO4jEQjl9ulgFKlQAbr5ZigrOni3vlcikeHFx9Xty92dk5Dbn9JNBgwahefPmaNKkCWrVqpUnQDjS8fYrHwTwGIAqkLgbw+F7HMCn1oplA2PGyINk1y6genXxM0+ZIg3o7rhDAoqrV/c9TiRyxRWS2vzjj8BttwU3xoIFuWNZwV13AR9/DIwfDzz4oHnjHjkisTJmxt8AcpOoXdtcBcds98bChRJ7U69e8DIpgWG4DYx7SfnyUofKjC7dgMTf1KsnLnKrGDNGEhMAsT8dOiSKTp8+EtysRC7OgcbuXElnzgRcC8ysqsJ24C0G5yNmrgVJ3XaOwWnGzLGl4LhWidy1S0qNlykDLF0KjBoVvcoNINambt2AX3/1r9KlO9LTgapVrfO7X3yxVILu39+c+BODtWvl1WwLDmB+JpWnY1u+fHB1grTBpj04x0ocOCDWloEDgVOnQhuXWSw4VtebGTgwf92VnBzzrFDKedjs+l/eMqmMXlVR3IMq0OPlTyXjHCIqa7xxuKseDlCuyMadawCQi9zMgCc76d5dbrDBBvKa0WDTG999J4HGZ8+6L0UeLGb2oHKlQQOp5GpWOuRrr+U/vkQyg7766sAKxx06JAHQGmBsL0TABx+IFfH990Mba9cu6SJuRf0b1/0Esl4JiqJFi+Lw4cPmKjmFConL393zLCtLLIlRquAwMw4fPoyiAcjvjyOuDzMPcdrJUSLqA+CzIGSMTDxduFb2kgk3bdqIefnHH4FAfai7dsmxsDKWY+DA/H5jI/4klGy0NWukU3K1aqHJ544GDSTtcscOc9JlCxcW5a5CBVFQatQQpScjA3jhBWni2Lu3uEurVPE+1qJF8qrxN/ZzxRXS/Pbtt4H77/f9v/OEEX9jtQWnRg33ReU0a8pUqlWrhj179sD0/opHj8pkMTMz7/qMjFx345Ej5u4zTBQtWhTVArmXu4s8dl4ArAZATu/jAaz19T2rFkuyqKzOfIgU7rmHuUwZyVoKhO++k+OxYoUlYjEzM5H7/wFRaONedRXz5ZebI6MrCxeKjFOmhD5WTg5zq1bMdesyZ2fn//zYMeZnn2UuXFgyq159lfnUKc/jPfusZL2cPh26bErobNki/4977w1+jMcfl2zIzEzz5HKH2dl8Snh55RW5b7pmzQ4dKv/L7dttEctKEGgWlRO/ARhHRNcR0XUAxgKYFqDiFdn4UyUyFujeHTh2DJg1K7DvpadL1dJkf1qQBYmn2WEos0ZmseCYHWBsUL++vJpRC2fRIon3euwxmWG5Uro08NZb0oeoY0fgpZfEgjR2rPv4nPR0iWsyin8p9lKnDvDII8CIEdLuJBgWL5ZGnmb0gfOGP0XllMileXO5J6xalXf9hg3inipAljh/FJxnAcwG8JBjWQ0gtu6aBeWCbttWHpSBFv1bsECqFweQWhgw7pTMQoVCUzL37xdzrRXxN4BkslSsaE6g8QcfiAuxd2/v29WuDUyYIBVlExMlvf7yy3PdF4CYppctU/dUpPHCC/I/fvLJwIPGMzOl/5PV8TcGvorKKZGL0dPvr7/yrt+4UTLw3E2gYhSfv5SZcwAsAbADQCsA10J6S8UWBeGCLlIE6NwZmDTJ/8DY48eljozVD0tXJbNUKZExlHRYKwOMDcxourl9u1TEfvBBsZT5w9VXixLz9ddyvl52mRzDjz+WY3j2rPT3MrMHkhIaF1wAvPyyWFADrUn199/yP9WO3YovqlWTzMuVK/Ou37AhtwJ7AcFbob96RPQyEW0A8AmAXQDAzNdwrKWJFyS6dwcOHwbmzvVv+8WLRekLhzXANb22SRPg7rslcyQYVq+WVysVHDNSxT/5RGZV/foF9r34eCk6uWmTBGOPGydp9kYPoUOHzG/0qITGQw9Jk8ynnw4s+87KDuJKbEEkVhxnC05GhkykVME5zwaItaYzM1/JzJ8AMKlalWIbHTqIK8hfN1V6ujx8w31jLVpU4kuOHZOHeDCplGvWABdeKK4cq2jQQBSJQ4eC+/7x48CwYdJVvWqQBcJLlQJef11+qytmN3pUQqNwYeDddyWW6quv/P/e4sVA5crWZAMqsUfz5jLBMzJTt2yRiaMqOOe5GcB+AHOI6CtHgLFWDIt2iheXINWJE+WE98WCBUCzZvIQDTdNmkjtkGnTxMoRKGvWWGu9AUJvujl8uLTAePzx0GXZt8/9eq1fEll07SouxpdeEgXeH5YskUmGFm1U/CElReK21juiSYz7kyo4AjNPYuaeABoAmANp21CRiD4nonZhkk+xgu7dxY2xcKH37bKy5MZqZ7Dqww9L3NDTT+fPCvBGTo5UMQ6XghOMmyorC/joI6B1a8mOCRUrMtEU8yESxf3QIeDNN31vf+iQzMA1/kbxF9dAY+P+VMDatvgTZHyKmb9j5i4AqgH4C5JZpUQrN9wgAce+3FR//y3Vj61osOkvRBJIW64ccPvt/rea2LZNtrVawUlKkmMZjAVn0iQpqGaG9QYoOOUOYoHUVOm/9uGHEnfmjaVL5VXjbxR/qVdPrn0j0HjDBmk35G8SQ4wQUL4YMx9l5qHMfJ1VAilhoHRpoF07UXC8xbZY3WDTXypUAEaOBNatA556yr/vhCODCpBA33r1grPgfPCBpH136WKOLAWl3EGskJYm8W3PP+99u8WLZTszrHxKwSA+XuqWOVtwCph7CghQwVFiiO7dpf3CsmWet0lPF/dGJAQ2tmsHPPEE8Nln0uXdF4aC07ixtXIBwaWKL1kixf0ee0xuRmZREModxArVqklNnLFjc7Ok3LF4sRSrLFkyfLIp0U/z5mLByclRBUcpYHTtKoX0PLmpmHMbbEYKb7whF+2990oRP2+sWQPUqhWeh0KDBuISc+3A7I3Bg6Vb/T33WCeXEvk8+6xkvz3xhHtrak6OuKg0/kYJlJQUCWJfuBA4eTK38noBQhWcgsoFFwDXXuvZTbVjh2Tl2O2ecqZIEZntnjolFX+9ZYGtXm29e8qgQQPp0rt1q3/b79ol1YgfeEBn5QWdkiWloerChe4nGxs3ykNK42+UQDECjb//Xl7VgqMUKLp3l4eyu+yk9HR5jSQFB5CL9MMPgd9/FyuIO86eleJ34VRwAP/dVEbK+yOPWCOPEl3cc4+4oJ55Jr8V0HBdqQVHCZSmTcX9/cMP8l4VHKVAcdNNErzobuaYni7ByOFSEgKhTx+gWzdgwID8/VYAUW6ysqxrsumKYfr1R8E5cUICf2+5RbIaFCU+HnjvPak0+6lLkfjFi+U6LIAPJyVEihYFGjaUqvAlSwJVqtgtUdhRBacgU7Gi1GBxp+AYDTbNDIA1CyKpAluxoqSOnzqV9/NwZVAZlCwpAaP+pIqPGCHVi594wnq5lOihXTspwPn669JKxWDJEqBVqwLVIFExkTJl5PXkSYlJLGBtW/SqKeh07y7p1+ud+qf+958UyYs095Qz5ctLM8lNm/IrC6tXSwB1OIPq/Mmkys6Wwn6XXy4PLUVx5t13Rfl99VV5f+qUuI81/kYJhjFjcmsoAVJzq4D1prNUwSGiDkS0kYi2ENFzbj5/gojWEdEqIppFRElWyqO4oVs3eXW24ixaJIHHkazgABIk/cwz4vKZODF3/Zo1UpumcOHwyWI03fRWV2jyZMm2UuuN4o7GjcX9+tlnorivWCGB9Bp/owTDwIG5vagMClhvOssUHCKKBzAEQEcAjQDcTkSNXDb7C0ALZk4GMAHAO1bJo3igalVxRTkrOOnp4pqKhhvrq68CLVoA998P7Nkj69asCV/8jUGDBjL7Njp5u2PwYKBmTYl9UhR3vPIKUKyYKO6LF8u6aLgOlcjDUw+6AtSbzkoLTisAW5h5GzNnAvgewI3OGzDzHGY+7Xi7GNIKQgk33btLQaht2+T9ggWSYhgNZb0LFwa++06yT+66S5SM7dvDHxztK5Nq+XJg/nygf//IjGtSIoNKlSR4/uefc2faLVsWKLeCYhLam85SBacqgN1O7/c41nniPgDT3H1ARA8Q0XIiWn7w4EETRVQAADffLK8//igmzaVLI9895UzdupJ6PWcOULmyrPv44/A+FHwpOIMHS0f2e+8Nn0xKdHLhhRJIn5Ul7wtg7IRiAtqbLjKCjInoDgAtALzr7nNH/6sWzNyiQoUK4RWuIFCrFnDxxaLg/PWXNKmMpArG/lC4sFhGTjsMggcPhvehULWqWLzcKTh79gDjx4sbrXTp8MijRC+vvJI/lquAxU4oJqC96SxVcPYCcC70Uc2xLg9E1BbAQABdmTmAWveKqdxyi6SkGlUvo8mCA8jNPzs777pwPhSIxIrjLlX8008lWPTRR8MjixLdaOyEYhYFvDedlQrOMgB1iagWERUG0BPAZOcNiCgFwJcQ5eaAhbIovjAyjgYPFkvI7Nn2yhMokfBQcJcqfvIk8OWX4gasWTN8sijRi8ZOKIopWKbgMHMWgH4ApgNYD2A8M68loleJqKtjs3cBlATwAxGtJKLJHoZTrGTMGOCll3LfZ2dHn88/Eh4KDRpIvIThJgOAkSOlrpCmhiv+orETimIKxN7qdkQgLVq04OXLl9stRmxRs6Y8mF1JShKzZjQwZowoZc7KRfHi4fU5//ADcNttEsfUvLmYhevXl6KERsqvovjDmDHiXt21S5T0tLQC515QFH8hohXM3MJ1fUQEGSs2EwnunVCJhIA610yqX34BtmwBHn88fDIosUEBj51QFDMoZLcASgRQo4Z7C060+fx79bL3QVC3rihXhoIzeLAcw+7d7ZNJURSlgKIWHEV9/mZRtKik3G/YIG6qP/4AHnlE+mIpiqIoYUUVHCUy3DuxQv36kio+eLB0Gb//frslUhRFKZCogqMI6vM3B2ZpezFqlCiLv/5qt0RKgIwZI3H3cXHyGk3JhIqi5KK2c0UxizFj8tYPOnFCMrsAVRijBNdkPKNLAqD/QkWJNtSCoyhmMXAgkJmZd52W2I8qBg7MW2kA0H+hUnCJdmumWnAUxSxiId2+gKP/QkURYsGaqRYcRTGLSKimrATN2bOSCOeO6tXdr1eUWCUWrJmq4CiKWWi6fdRy4gRwww3AmTNAQkL+z6+8MvwyKYqdxII1UxUcRTELTbePSg4fBtq2lbJFI0cCI0bk/gtr1ABatQLGjgV+/NFuSZVoIprjV86cye2/7EqlSuGVJRS0F5WiKAWWvXuBdu2ArVuB8eOBrl3zb3PmDHDddVK7cc4c4NJLwy+nEl1EQmu8YMnOBm69FZg4UZQc57wJIiA+Xn7fbbfZJ6Mr2otKURTFic2bgSuuAHbvBn77zb1yAwDFigE//wxUqSLbbNsWXjmV6MNT/Mrzz9sjj78wA//7nyg3H30EfP11XoP0Z58Bl1wC9OgBvPGGbB/JqIKjKEqBY+VKias5dUqsMm3aeN++QgVg2jSZ3XbqBBw5Eg4plWjFW/zKk08CS5dGpnLw6qvAl18Czz0HPPpo/vqvDz0EzJol6wcOBO69N39ljEhCFRxFcRDNPvNIItKP4/z5otAUKSJ/p6b697169YBJk4Dt24Fu3STrSlGcOXEC6NPHs/JSrBjwySdiBalTRxSJP/+MDGXnyy+BQYOAu+8W64wnihSRQu2DBgHffCMu3ohV+Jk5qpbU1FRWFLMZPZq5eHFmudXIUry4rFf8J9KP4y+/MBctyly/PvPOncGNMWaM/K5evZhzcsyVT4le5s5lrlmTOS6OuUsXz9fB0aPMI0Ywd+jAXKiQfFa3LvPAgcyrV+eON3o0c1ISM5G8WnkNTZwocnfqxJyZ6f/3Ro9mLlyYuV495k2bLBPPJwCWsxt9wXaFJdBFFRzFCpKS8t6MjCUpyW7JwkswN9WcHOZjx+QGV6mS++NYo4a1MvjDmDHyQLn4YuYDB0IbKy1NfteLL5ojmxK9nDnD/OSTcr7WqcO8YIGs9+c8PnSIeehQ5uuuEwUDYG7UiPnmm0URD8dEYd485iJFmC+5hPnkycC/P38+c/nyzOXKyVh24EnB0SyqCGHMGPFp7tolqalpaZEfbR9LxMW5NxMTif+5IOAu86NoUYkZSE4G/v3X/XLggGQa+aJxYzm3k5Lk1XmpUkXqz1iVfTJkCPDII0Dr1sDkyUDp0sGPBci50qcPMHy4BGLec09o4ynRyZ9/AnfeCaxbJ/Ep774LlCwZ3Fj//iulCMaNA+bNc79NUpLEwpjFmjXAVVdJ6veCBUBiYnDjbN0qdaS2bZNr4s47zZPRHzxlUdlukQl0iUULTqSb9WOZkyeZn33WvdUBYI6PZ/7xx4LhivBkxXJe4uKYL7yQOTmZ+frrme+4Q2av77zDPHIkc8WK7r9XqhTzjTcyp6TIbM/duNWqyUzSTEtaTg7zq6/KGF27Mp8+bd7xysxkbttWrEIzZ5o3rhmE071REDl3jvm11+R/X6UK87Rp5o5P5P46IDJvHzt3MletKvLv2BH6eEeOMF9zTa5lM5z3TKiLKjLJyfFs1q9WzW7pYpecHFFcqleXY926NXOxYnmPf9GiuZ936RJ8zEakk53N/PPPnpUaIokNOHBAtvWGv8r6yZPM69czT5/O/NVXckO86y7vylXbtsyPPsr8xRdiCj90yLMMxsO9VCn57l13yUPJbP77j7lJE+bSpfPGT9iJTpisZcMG5lat5Ljefjvz4cPm78PTZCMujnnwYHGLhcKhQ8wNGjCXKcO8apUJAjs4e5b53ntzj02ocvqLKjgRxvr1zC+8wFyrlveb+mWXMb/xBvOaNZFvRYiWWeOWLcwdO8rxTU727jM/d475vffkAVGiBPP771vzoLSDM2dEuWjQgM9bq8ywnoRyHni6sZcowdyihbw6r69Ykfnqq5n79mX+5BPmAQPyK6qFCjGPGhXYbwiEnTvFqlWjBvO+fdbtx180nswasrOZP/5Yzq9y5ZjHjbNuX+6U1KJFmRs3lr+rVmX+/HNRKALl1CnmSy8Va+ncuebLnpPD/NZbImfdujJRt/qZoApOBLBvnzwgL76Yz2vj11/v3mQPiHadmpr7vlYtmcH+/ntwJ7aVRMOs8fRp5pdflgu7VCmZCfmrrOzYwXzDDfK7mjdnXrrUSkmt5cgRCZI1LIcpKczffcf87bf2/w99nUfZ2aJQTJsm19J998kkoEwZ7xMFqx/uK1aI8pWaGlygphkcPy7HydtxWLcu8idKBqFOmMz8ftWqEvwLyORo797AxgoGT/LPmcN8xRW5z4RvvvH/PnbuHHPnzvLs+fFHiwR38Oij+c8/q+4nquBYjKeT8dgxSQls2zY3Sj41lfmDD3Jne75u6nv2iFn+hhtyI+tLl2a+7TaZmR4+bJ/15MQJ5l9/zXUFuC7lyzNv3OjbtWE1v/7KXLs2nzedBnODyslhnjCBuXJlOc6PPCL/32hhxw7m/v1zrSAdOkjsiPMDLxKscMFmcu3bF57YBU9MmZKbIpyVZf3+mEWZ+v575m7dcuOXPFnijKVGDeYHHmD+6SdxsVmBGcqFu3vit9/K5O70abn3/PefKOwHDzL/849c17t2yeTF1ZJXrJjcR48dEytGZqZnZc/d/gFRqCNBQczJESXfmADXry/ngbf7bE5Orvvo88+tlzGclkRVcHwQygXp7mIw0u4MhaR2bYkzWL8+tP2fPCnxEvfdlzsDJ8pVnqzWlDMzxaUzaBDzVVfl1nHwtZQpw3zttczPPMP8ww/M27fnv1FY8XDdsYP5pptEhgYNmGfNCn3M//5j7tdP5KxSxdwgZCtmrStWMPfsKQ++QoUkHsVMv3skYbd75tNPZX+lSlmnJJ46JYr2rbfmPsQvvFAU7vnzZdLjTjn48EPmL78UZciYkMTHy3WclibnifGANPt+WLy4yHXkCPPmzcyLFklNopEjZbL3/PPMDz7IfMstEqiakODffcWMpVAhOY6lS8uE7MILzXPXWk1OjiiqhusqOVmeD+7uR88/L9u89FJ4ZAvnZMMWBQdABwAbAWwB8Jybz4sAGOf4fAmAmr7GtELB8ccsfvq0XJz798tDc8MG5r//FleFpyDhuDjm//2PeeFCa7T+7GzmxYvlwnS3/6JFZf8ffsg8darcWDyZMj3d0HJyJHhy8GAxbZYsmXuStmzJ/NxzYgUwgnFdl8qVmYcPZ37oIYmhcL5xJSaKFeHFF5mfeCL/jCtQJc35N9SoIRauYsVknDffNN+tt2QJc7NmImvnznJeWPFg8HcMd983FN9SpZifeop59+4gfmgUYberdPTo/Ep/MPt3PY9GjJBibLffnmuBq1iR+eGHmf/4I7/FyNd5mJkp8RfPP5/rMjfGvOIKKd7m+hu+/FLO8VWrmNPTxYIwfjzzsGGipLzyimTUGfeIQJb4eNl3w4bMV17pfdu0NInxeOcdcVN++KHEX332mVhovvrK+/fff1++/9pr8rAfMECujf795Xj26eP5u+GwBAZDVpa4mevWFTlbtpQAfuM8MOS/5prwWaAiwYJjWR0cIooHsAnA9QD2AFgG4HZmXue0zcMAkpn5ISLqCaAbM/fwNq4VdXBq1gR27sy/3uicmpUV3LjhqqHiqYYLIPU+jh/PfV+oEFC7NlC3riz16kntnY8+ylvLpHBhoEULqW/w77+yrl496arctq2Uui9XLnd7f+uXnD0LrF4NLFsGLF8ur2vXej5OJUsCDz4ov6NUKXl1/tt4nT4dePzx/A3uWrSQ2hI1avg8jEGRlSXH7qWXgHPnZJ3xCuQeg+7dgYMH3S8HDsjrb7+57+sSHw9cdJGUSC9cOO+r898TJ0pvJVfKlpXaGWXKWHEEIg87a0p5upfExUlp/nLlgPLl8y/O6+fPlxL+7moLlS8v51KPHlLTp1Ahc+T+919gxgw5B7//Pvj7VvHi+a9BZwYPzv2diYm5f5cpI/dLA0/H0d86MHZ/3y6ysoBvvwVeeUXO/7i4vP/LcHY0D2dHdU91cKxUcC4DMIiZ2zveDwAAZn7TaZvpjm0WEVEhAP8AqMBehLJCwfGmIAwYkPsgKVo0/99Fi0rDsQMH8n83XBeDt4tx+3Z5eG7eDGzaJK/Oi7ebUVwc0LOnKDTXXedbSQj2wXL6tCgynv4Hvm6a3gjX/2DnTqBhQ/cPJSLPv61QIWnkWKECsGqV5/Fvu02Un7Nnc1+d/87M9NzluiAVK7Qbb/eSnj2Bw4fzLidP+j92xYrAnj1SENFKvP2G4cM9TzZKlpTz2QzlINSHo93ft5uzZ4HKlYGjR/N/Fk4lLVyTjbAX+gNwC4BhTu/vBPCpyzZrAFRzer8VQKK3ca1wUYVqSosEs3gw+8/JkQBmOwMzDXz9D86dkx4uO3dKyvzChcy//SbxPMOHR4ZJ2dNxBJhff11Ksk+cKDFMGzeKy9PZXBzqeWh3/IkS+P8gI0OCo9esEZfRTz/Zfy5Hyv0wkrKoIrnshSci4b4eLhDuGBwzFRwADwBYDmB5jUCa2viJGRek3ReDFbVHwvlgDPV/EAm/we4Hg92KtmLO/8DuczkW7oeK/edROLFDwbkMwHSn9wMADHDZZjqAyxx/FwJwCA63maclErOoop1IeTDaGaBrBpHwYCjI53GkYFWKdLjPZT2PoptIOI/ChR0KTiEA2wDUAlAYwN8AGrts8z8AXzj+7glgvK9xI7UOTrQTCze0SPgNkSCDEv3oeaSYQUE5jzwpOJZ2EyeiTgA+BBAP4GtmTiOiVx3CTCaiogBGAUgBcARAT2b2ECopxGo3cUVRFEVRAsdTkLFJSYbuYeapAKa6rHvJ6e8MALdaKYOiKIqiKAWPOLsFUBRFURRFMRtLXVRWQEQHAbipsmAaiZBgZyV49BiGjh7D0NFjGDp6DENHj2Ho+DqGScxcwXVl1Ck4VkNEy9358hT/0WMYOnoMQ0ePYejoMQwdPYahE+wxVBeVoiiKoigxhyo4iqIoiqLEHKrg5Geo3QLEAHoMQ0ePYejoMQwdPYaho8cwdII6hhqDoyiKoihKzKEWHEVRFEVRYg5VcBRFURRFiTlUwXFARB2IaCMRbSGi5+yWJxohoh1EtJqIVhKR9tPwEyL6mogOENEap3XliOh3ItrseL3AThkjHQ/HcBAR7XWcjysdrWMUDxBRdSKaQ0TriGgtEfV3rNdz0U+8HEM9F/2EiIoS0VIi+ttxDF9xrK9FREscz+hxRFTY51gagwMQUTyATQCuB7AHwDIAtzPzOlsFizKIaAeAFsysRa0CgIhaAzgJ4FtmbuJY9w6AI8z8lkPhvoCZn7VTzkjGwzEcBOAkM79np2zRAhFVBlCZmf8kolIAVgC4CcDd0HPRL7wcw9ug56JfEBEBKMHMJ4koAcACAP0BPAHgJ2b+noi+APA3M3/ubSy14AitAGxh5m3MnAngewA32iyTUkBg5nmQZrPO3AhgpOPvkZCbpOIBD8dQCQBm3s/Mfzr+PgFgPYCq0HPRb7wcQ8VPHA3CTzreJjgWBnAtgAmO9X6dh6rgCFUB7HZ6vwd6UgYDA5hBRCuI6AG7hYlyKjHzfsff/wCoZKcwUUw/IlrlcGGpa8VPiKgmgBQAS6DnYlC4HENAz0W/IaJ4IloJ4ACA3wFsBfAfM2c5NvHrGa0KjmImVzLzxQA6Avifw22ghAiLH1l9yYHzOYA6AJoD2A/gfVuliRKIqCSAHwE8xszHnT/Tc9E/3BxDPRcDgJmzmbk5gGoQD0uDYMZRBUfYC6C60/tqjnVKADDzXsfrAQATISemEhz/Ovz5hl//gM3yRB3M/K/jRpkD4Cvo+egTR8zDjwDGMPNPjtV6LgaAu2Oo5+L/s3fe4VGU2x//noTQe1GRTiRKCT2gghoQRVBQ/KmIyEWvYsVeEa8iXuxd0SuKFQQ7AlJVmpAgAUIPvXeQ3pOc3x9nx2w2u5vZ3ZmdLefzPPtssjs78+7slO97anAw80EAMwBcBKAyEZVwvWXqHq0CR1gAoJErSrskgJsBjHd4TFEFEZVzBdWBiMoBuBLAcv+fUvwwHkB/19/9Afzi4FiiEuOm7KIX9Hj0iyu4cySAVcz8lttbeiyaxNc+1GPRPERUg4gqu/4uA0n+WQUROje4FjN1HGoWlQtX2t47ABIBfMbMw5wdUXRBRA0hVhsAKAHgG92H5iCiMQDSAVQHsBvA8wDGAfgOQF0AmwHcxMwaROsDH/swHeISYACbANztFkuieEBEHQHMAbAMQL7r5WcgMSR6LJrAzz7sAz0WTUFEzSFBxIkQI8x3zDzUdY8ZC6AqgMUAbmXmU37XpQJHURRFUZRYQ11UiqIoiqLEHCpwFEVRFEWJOVTgKIqiKIoSc6jAURRFURQl5lCBoyiKoihKzFGi+EUURVGsg4jyIGm0BmOZ+RUbt5cO4DQzz7NrG4qiRB4qcBRFCTcnXGXYbcdV+TQd0mlcBY6ixBFaB0dRlLBCREeZubzHa5UA/AWgJzOvdhXu+4OZPyGio5Dy9ldCmj3ezMx7iSgZwHAANQAcBzCAmXOI6AsAJyGNDrcDuBhAHoC9AB4AcA6kEGAegEPMrD3TFCUGUQuOoijhpoyrU7DBy8z8LRENBPAFEb0LoAozf+J6vxyALGZ+hIieg4iTgQBGALiHmdcSUXsAHwLo7PpMbQAXM3MeEQ0BcJSZ3wAAIloGoCszbzdKwiuKEnuowFEUJdx4dVEx83QiuhFilWnh9lY+gG9df48C8JOrW/PFAL6X9j8AgFJun/memfN8bH8uREh9B+AnH8soihLlqMBRFCUiIKIEAI0h7qYqALb5WJQhGaAH/cTyHPO1HWa+x2XxuRrAQiJqw8z7gx64oigRiaaJK4oSKTwC6Rp8C4DPiSjJ9XoCCroI3wLgT2Y+DGCjy+IDElp4rtDFEQAVjH+IKJmZ5zPzc5C4nDrWfxVFUZxGBY6iKOGmDBFluz1eIaLzAdwJ4DFmngNgNoBnXcsfA9COiJZDYmyGul7vC+AOIloCYAWAa31sbwKAXq5tXQLgdSJa5lrfPABLbPmWiqI4imZRKYoS0XjLulIURSkOteAoiqIoihJzqAVHURRFUZSYQy04iqIoiqLEHCpwFEVRFEWJOVTgKIqiKIoSc6jAURRFURQl5lCBoyiKoihKzKECR1EURVGUmEMFjqIoiqIoMYcKHEVRFEVRYg4VOIqiKIqixBwqcBRFURRFiTlU4CiKEnMQ0RAiGuX0OBRFcQ4VOIqiWAYRdSSieUR0iIj+JqK5RJTm9LgCgYhGEdFOIjpMRGuI6E6nx6QoSuCUcHoAiqLEBkRUEcBEAPcC+A5ASQCXADjl5LiC4GUAdzDzKSK6AMBMIlrMzAudHpiiKOZRC46iKFaRAgDMPIaZ85j5BDNPY+alAEBEyUT0BxHtJ6J9RDSaiCobHyaiTUT0BBEtJaJjRDSSiM4moslEdISIfiOiKq5l6xMRE9FdRLTDZXF53NfAiOhCl2XpIBEtIaJ0X8sy8wpmNkQZux7Joe4cRVHCiwocRVGsYg2APCL6koi6GWLEDYJYR84F0BhAHQBDPJb5PwBXQMRSDwCTATwDoAbkevWgx/KdADQCcCWAp4ioi+egiKgWgF8B/BdAVQCPA/iRiGr4+iJE9CERHQeQA2AngEl+v7miKBGHChxFUSyBmQ8D6AixeHwCYC8RjSeis13vr2Pm6cx8ipn3AngLwGUeq3mfmXcz83YAcwDMZ+bFzHwSwM8AWnks/wIzH2PmZQA+B9DHy9BuBTCJmScxcz4zTweQBaC7n+9yH4AKEBfbT4g+N5uixD0qcBRFsQxmXsXMtzFzbQDNINaadwDA5W4aS0TbiegwgFEAqnusYrfb3ye8/F/eY/mtbn9vdm3Pk3oAbnS5pw4S0UGIEKtZzHfJY+Y/AdSGxBUpihJFqMBRFMUWmDkHwBcQoQMAL0GsO6nMXBFiWaEQN1PH7e+6AHZ4WWYrgK+ZubLboxwzv2JyGyWgMTiKEnWowFEUxRKI6AIieoyIarv+rwNxGWW6FqkA4CiAQ664mCcs2Ox/iKgsETUFcDuAb70sMwpADyLqSkSJRFSaiNKNcXp8h7OI6GYiKu9atqvrO/xuwVgVRQkjKnAURbGKIwDaA5hPRMcgwmY5gMdc778AoDWAQ5Cg358s2OYsAOsgAuQNZp7muQAzbwVwLSRYeS/EovMEvF//GOKO2gbgAIA3ADzMzOMtGKuiKGGEmNnpMSiKogQEEdUHsBFAEjPnOjwcRVEiELXgKIqiKIoSc6jAURRFURQl5lAXlaIoiqIoMYdacBRFURRFiTmirtlm9erVuX79+k4PQ1EURVGUCGDhwoX7mLlI65WoEzj169dHVlaW08NQFEVRFCUCIKLN3l5XF5WiKIqiKDGHChxFURRFUWIOFTixwujRQP36QEKCPI8e7fSIFEVRFMUxoi4GR/HC6NHAXXcBx4/L/5s3y/8A0Levc+NSFEVRvHLmzBls27YNJ0+edHooUUPp0qVRu3ZtJCUlmVo+6urgtG3bljXI2IP69UXUeFKvHrBpU7hHoyiKohTDxo0bUaFCBVSrVg1E5PRwIh5mxv79+3HkyBE0aNCg0HtEtJCZ23p+Rl1U0Ux+PjBtmndxAwBbtoR3PIqiKIopTp48qeImAIgI1apVC8jiZavAIaKriGg1Ea0joqe9vF+XiGYQ0WIiWkpE3e0cT8ywZQvwwgtAw4ZA164Sd+ONunXDOy5FsQKNJ1PiBBU3gRHo/rJN4BBRIoDhALoBaAKgDxE18VjsWQDfMXMrADcD+NCu8UQ9p04B338PXHWVXPRfeAE4/3zg22+Bzz4DypYtvHxiIjBsmCNDVZSgMeLJNm8GmAviyVTkKIoSIHZacNoBWMfMG5j5NICxAK71WIYBVHT9XQnADhvHE9n4mrWuWAE8+ihQuzZw003AypXAc88BGzcCU6fKa/37AyNGSMwNEVC5MpCXB1So4OAXUpQgGDy4IFje4PhxeV1R4hi7DJvDhg1D06ZN0bx5c7Rs2RLz58/3ueycOXPQtGlTtGzZEhkZGZg0aZI1g7ALZrblAeAGAJ+6/d8PwAcey9QEsAzANgAHALTxsa67AGQByKpbty7HHKNGMZctyyxzVnmULMmcnCx/JyUx33AD85QpzLm5xa/v1Cnmpk2Z69RhPnLE/vErilUQFT4P3B8zZjAfPer0CBXFElauXGl6WW+3iLJl5fVQmDdvHl944YV88uRJZmbeu3cvb9++3efyd999N3/99dfMzPz555/z/fffH9oAgsDbfgOQxV60g9Np4n0AfMHMbxLRRQC+JqJmzJzvvhAzjwAwApAsKgfGaS/eZq2nT4t5/s03gX79gBpF2mz4pmRJ4OOPgY4dgeefl3UoSjRQt67voPlOncT12rIlcPHFQIcO8lynTuHlRo+Wc2rLFlnfsGFaLkGJaB5+GMjO9v1+ZqZEKbhz/Dhwxx3AJ594/0zLlsA77/jf7s6dO1G9enWUKlUKAFC9enUAwO+//47HH38cubm5SEtLw0cffYSvv/4a3333HaZOnYpff/0Vc+fOxYkTJ/Dnn39i0KBBWLVqFTZu3IgNGzZgy5YtePvtt5GZmYnJkyejVq1amDBhApKSkjB06FBMmDABJ06cwMUXX4yPP/4YeXl5uOiii/D6668jPT0dgwYNQkJCAoaFGGZhp4tqOwD3K09t12vu3AHgOwBg5gwApQFUt3FMkYmvbKe8PHFPBSJuDDp0AO6+W47wxYtDGp6ihI1hwwDPGhdly4pg//VX4OmngYoVgZEjgZtvFgFTpw7Quzfw7rvAiy9qDI8Sc3iKm+JeN8uVV16JrVu3IiUlBffddx9mzZqFkydP4rbbbsO3336LZcuWITc3Fx999BHuvPNO9OzZE6+//jrGjBmDoUOHonfv3sjOzkbv3r0BAOvXr8cff/yB8ePH49Zbb0WnTp2wbNkylClTBr/++isAYODAgViwYAGWL1+OEydOYOLEiShRogS++OIL3Hvvvfjtt98wZcoUPP/886F9Odhb6G8BgEZE1AAibG4GcIvHMlsAXA7gCyJqDBE4e20cU2Tia9YaahbUyy8D48bJBT4zU2a/ihLJ9O0LvPIKsHo1kJtb1ALT3ZVomZsLLFkCzJtX8PjuO+/rNGJ41IqjRCjFWVr8lTqbOTP47ZYvXx4LFy7EnDlzMGPGDPTu3RuDBg1CgwYNkJKSAgDo378/hg8fjocffrjY9XXr1g1JSUlITU1FXl4errrqKgBAamoqNrlqss2YMQOvvfYajh8/jr///htNmzZFjx490LRpU/Tr1w/XXHMNMjIyULJkyeC/mAvbLDjMnAtgIICpAFZBsqVWENFQIurpWuwxAAOIaAmAMQBuc/nT4othw8St5E7ZsqFnQVWpImdOVhbwoSaoKVFAfj6wdStw++3y96ZN3oVJiRJAmzbAAw8AY8bI1X/rVgmy94bWhFKimGHDiibKWnGLAIDExESkp6fjhRdewAcffIBx48YFvS7D1ZWQkICkpKR/0roTEhKQm5uLkydP4r777sMPP/yAZcuWYcCAAYXq2ixbtgyVK1fGnj17QvpOBrbWwWHmScycwszJzDzM9dpzzDze9fdKZu7AzC2YuSUzT7NzPBFL377AhRdKeDyRyPIRI6yZcfbuLbVynnkG2LYt9PUpip2sXg0cOiTnQ6DUru3b6qk1oZQopm/fwomyVt0iVq9ejbVr1/7zf3Z2NpKTk7Fp0yasW7cOAPD111/jsssuK/LZChUq4MiRIwFtzxAz1atXx9GjR/HDDz/8895PP/2Ev//+G7Nnz8YDDzyAgwcPBvGNCqOVjCMBZmDtWuDGG/3PWoOBSKw3eXnAgw9as05FsYuMDHkORuAA3qe6pUtrTSgl6unbV24NVt4ijh49iv79+6NJkyZo3rw5Vq5ciVdeeQWff/45brzxRqSmpiIhIQH33HNPkc926tQJK1euRMuWLfHtt9+a2l7lypUxYMAANGvWDF27dkVaWhoAYN++fXj66afx6aefIiUlBQMHDsRDDz0U8vfTXlSRwNKlQIsWwOefA7fdZs82Xn1VAjTHjQOu9SxHpCgRwl13SUHL/ft9V+guDvcsKiKgQQNgzZrg16coNrBq1So0btzY6WFEHd72m/aiimQmT5bnrl3t28ajjwLNmgEDBwIBmhUVJWxkZADt24cmRtynup99BqxfD4waZdkQFUWJDlTgRAJTpogFp2ZN+7aRlCRO223bpDaOEptEcx+nw4elcvdFF1m3zn79gHbtgKeeUmGvKHGGChynOXIE+PNPoFs3+7d10UXAPfdIvZBFi+zfnhJeor2P019/ybiDjb/xRkIC8N57wK5dGoejKHGGChyn+eMPqenhqhdgOy+/LIUD77pLtqvEDtHexykzU57bt7d2ve3bS7+2t98GXJkhiqLEPipwnGbKFGmKaaVZ3h+VK4sFZ+FCYPjw8GxTCQ++ar1ESw2YzEygcWM5Rq3m5Zel1tSjj1q/bkVRIhIVOE7CLAHGl19etNCfndx0k7jEnn1WiqMpsUE014BhFoFjl9CvWRP4z3+ACROAqVPt2YaiKBGFChwnWb1a4iTC5Z4yIBLrjdbGiS2GDZOaL+5YVe7Ubtatk9RwK+NvPHnoIeC886Sz4Zkz9m1HUezApgSCYcOGoWnTpmjevDlatmyJ+fPn484778TKlSstWb8vunfv7rWY35AhQ/DGG29Ysg0VOE4yZYo8h1vgAFIbZMgQqYsTQmluJYLo2xe4xa3dW+XK1lXEtptQC/yZoVQpicPJyQE++MC+7SjWEM0ZgVZjUwJBRkYGJk6ciEWLFmHp0qX47bffUKdOHXz66ado0qSJRYP3zqRJk1DZDne0GypwnGTKFIk5qFfPme0/8giQmir9fGIhhVYviHITr1hR3FJXXRUd4gYQ91SFCoDNF1VcfbXslyFDAIv63Sg2EO0ZgYHy8MNAerrvxx13eE8guOMO358x0Rxz586dqF69+j89pKpXr45zzz0X6enpMArqjhw5EikpKWjXrh0GDBiAgQMHAgBuu+023HvvvbjwwgvRsGFDzJw5E//+97/RuHFj3OZWsHbMmDFITU1Fs2bN8NRTT/3zev369bFv3z4AYkVKSUlBx44dsXr16oB2nT9U4DjF8ePSBtYJ642BURtn+3aJT4hm4u2C6IslS6SmUosWwLJlTo/GPJmZUq/G7o73RGLFiabssngk2jMCrebUqcBeN8mVV16JrVu3IiUlBffddx9mzZpV6P0dO3bgxRdfRGZmJubOnYucnJxC7x84cAAZGRl4++230bNnTzzyyCNYsWIFli1bhuzsbOzYsQNPPfUU/vjjD2RnZ2PBggVFmnkuXLgQY8eORXZ2NiZNmoQFCxaE9J3cKWHZmpTAmDVLDk4nBQ4gLoF77wXefx+49VagbZFq19GBvwtitFgxQiU/X9p+3HabWHEmTZJjzDU7i1iOHZNxDxoUnu1dcIHEnr39ttSFatMmPNtVzBPtGYGB8s47/t+vX18mbZ7UqycT5SApX748Fi5ciDlz5mDGjBno3bs3XnnllX/e/+uvv3DZZZehatWqAIAbb7wRa9as+ef9Hj16gIiQmpqKs88+G6mpqQCApk2bYtOmTdi8eTPS09NRo0YNAEDfvn0xe/ZsXHfddf+sY86cOejVqxfKunrI9ezZM+jv44lacJxiyhSgTBng0kudHgnw0ktA+fLAxRdHr3sn3i6I3tiwATh6FGjZEmjeXILIPWZcEUlWlozVzvgbT557TupBPfSQWPyUyCKaMwLtwFsTWYsSCBITE5Geno4XXngBH3zwAX788UfTnzVcWwkJCf/8bfyfGwF11lTgOMWUKUCnTkWzXpxg4kTg5EnJLIlW945eEMU9BYh7yjWTwtKlzo3HLOEIMPakUiUR9nPnAmPGhG+7ijmGDRN3ojvRkhFoB337SjhBvXqyX+rVsySBYPXq1Vi7du0//2dnZ6OeW0xoWloaZs2ahQMHDiA3Nzcg8QMA7dq1w6xZs7Bv3z7k5eVhzJgxuOyyywotc+mll2LcuHE4ceIEjhw5ggkTJoT0ndxRgeMEGzZId2On3VMGgwcDp08Xfi3a/N3DhhWtJRRvF8TsbIlhadoUSEmR/RENcTiZmUCjRkC1auHd7u23i3vqySfFTaZEDunpMtkqX17+r1IlejIC7cK9ieymTZbsi6NHj6J///5o0qQJmjdvjpUrV2LIkCH/vF+rVi0888wzaNeuHTp06ID69eujUqVKptdfs2ZNvPLKK+jUqRNatGiBNm3a4Nprry20TOvWrdG7d2+0aNEC3bp1Q1paWsjf6x+YOaoebdq04ahn+HBmgHnNGqdHIhDJeDwfRE6PLDBatWJOTJSxlynDPGqU0yMKLz16MDdpUvB/y5bMXbs6Nx4z5Oczn3UWc79+zmx/7lw5Xp591pntK975/nv5XebPZ05JYb7ySqdHZDkrV650egimOHLkCDMznzlzhq+55hr+6aefHB2Pt/0GIIu96AW14DjBlClAw4ZSdCwS8OXGqVMnvOMIhUOHpBP1gw8C//qXzPzca8LEA9nZEn9j0Lx55FtwNm2SdO1wtSrx5OKLZSb8+uvAxo3OjEEpSkaGuO9btgR69JBA2lgoZRGFDBkyBC1btkSzZs3QoEGDQgHCkY4KnHBz6pQ02OzWraiP2Sm8BbABQOvW4R9LsIwfL262G28EOnYE9u4F3HzLMc/ff0vbjRYtCl5r3hzYsQNw1ZqISIwGm+GMv/Hk1VeBEiWAxx93bgxKYTIyxH1YsqQInNOngWnTnB5VXPLGG28gOzsbOTk5eO+990CRct8ygQqccDN3rvj7IyX+BigawFa3rmR3jRsHfP+906Mzx/ffi8WpfXsROADw55/OjimcuAcYGxiBxpFsxcnIEHFtjNUJatUCnnkG+OknmXxEAvFctPLUKWkGfPHF8n+HDhKDY2HwaaTAmsEXEIHuLxU44WbyZJmVpKc7PZLCuAewbd4ss6WLLpKaKtnZDg+uGA4dkgaKN9wgN4QLLpCA1XgUOJ4uKiCyBU5mJpCWJhYUJ3n0UXEbP/QQ4HR6a7wXrVy0SCw2htuyRAmxeE+aJOUEYoTSpUtj//79KnJMwszYv38/SgeQeayF/sLNlCnAJZcUZAdEKqVKyYw2LQ249lpgwQLgrLOcHpV33N1TgFihOnSIL4GTnQ2cfbY8DM4+G6hePXJTxU+cABYvjgzXUOnSwJtvAr16Af/7H+AqR+8I8V60ct48eXaPy7rmGuCbb4C//nIuXstiateujW3btmHv3r1ODyVqKF26NGrXrm16eRU44WTbNmD5crGKRAPnnCNuqo4dxTry229FU7EjAXf3lEHHjiJ8du8ufNOPVZYsKWy9AUToRXKg8aJFYi1xMv7GnWuvBbp0kSKAffqEP23dIN6LVmZkSDPgc84peO2qq6QEwoQJMSNwkpKS0KBBA6eHEdOoiyqcTJ0qz5EUf1McbdoAn30GzJkjTTkjzZzq6Z4yMOJw5s51Zlzh5PRpySBzj78xSE0VUZ2fH/5xFYcTBf78QQS8+y5w8KCz8S++ZqjxULSSWSw4RvyNQZUqYvmOwTgcn8RzHJZFqMAJJ1OmyMXL7o7JVtOnD/D00xKI/NFHTo+mMJ7uKYPWrcXtEA9uqpwcqULtacEBxIJz/LgUl4w0MjNlph5JFrbFi8VScPSoc/Ev3uLz4qVo5ZYtwM6d3q00PXqIWN+0KezDCjvxHodlEbYKHCK6iohWE9E6InraxzI3EdFKIlpBRN/YOR5Hyc0Fpk8X600Updn9w3//C1x9tQRhhtDczXK8uacAiSFq1y4+BI4RBO7LggNEZhxOZmbkWG8MBg8uGmQczqre+fmyX5KTxWoBSJZXuKv4OmU9MKx6vgQOIK1lYh3tpm4JtgkcIkoEMBxANwBNAPQhoiYeyzQCMAhAB2ZuCuBhu8bjOJmZ4k6JJveUO4mJcpE77zxxB0VCUTRf7imDjh0lziPWy/AvWSLWqpSUou81bSqCOtLicLZuBbZvj7x4CqfjXyZPlvpNL75YcCP/6KPwixunrAfz5om1ysgAdKdRI+D88+PDTeX0cRgj2GnBaQdgHTNvYObTAMYCuNZjmQEAhjPzAQBg5j02jsc/ds9YpkwRkdCli7XrDSeVKolLKC9PAjKPHnV2PL7cUwYdO8pY588P77jCzZIlQLNm3lOty5YVURppFpxIKPDnDaebtr7zDnDuuSLamzcXcbp4cXi2beCk9SAjQyyvvsoGXHNNfFQ1juc4LAuxU+DUArDV7f9trtfcSQGQQkRziSiTiJwxb4RjxjJligTOBdCoLCJp1Aj49lsJav3Xv5wNXvXlnjK46CK5QcSym4q5aIsGTyIxk8ooxe/NreYk3qp6lyoVnviXFSskU/H++4GkJCklkZIiVshw4pT14PhxOZY9A4zdiYeqxn//Lb+/J/ESh2UhTgcZlwDQCEA6gD4APiGiyp4LEdFdRJRFRFm21Aywe8aye7dU5oxW95QnV14JvPEG8PPPwNChzoyhOPcUAFSuLDEosSxwduwA9u/3LxRSU4F16yLLVZeZWVCKP5LwrOqdkCAWsHC4iN57T0TfXXcVvNaqVfgtOE5ZsbKyJP7Jn9synFWNnYhD2r0b6NRJSoo88ogch4AInnjvph4Edgqc7QDcuzXWdr3mzjYA45n5DDNvBLAGIngKwcwjmLktM7etUaOG9SO1e8ZizDZiReAAwMMPSz2fF14Afvwx/Nsvzj1l0KGDWAucrk5rF0aAcXEWHGZg5cpwjKh4jFL8kRZ/Y+Be1fu118SyYgS/2sX+/cBXXwG33irFGQ1atZLr0P799m7fnWHDxGrlTjisB0aBP39uy3BVNXYiDmnbNmmRs26dxF+99ZYch4MGyRj+7//s23aMYqfAWQCgERE1IKKSAG4GMN5jmXEQ6w2IqDrEZRX+fFa7ZyxTpkgqrL+bULRBJBVfL7xQXFVGq4BwUZx7yqBjR4kVijQXjVUY+91bUKZBpGVSZWeLOI20+Btv3HOPCI4XX7R3O598Apw8CTz4YOHXW7WS53C2S+nbF+jateB/ImD4cPutBxkZ4pJzF3je6NFDmun+9Zd9Ywl3HNL69VLnZ9cumRBfcUXBe2lpMkGL9JY5EYhtAoeZcwEMBDAVwCoA3zHzCiIaSkQ9XYtNBbCfiFYCmAHgCWYO41TFhTe/e5ky1sxY8vLEldK1q29XSrRitHOoUkWCjsNVctyMe8og1htvZmdLD6WKFX0v07ChHN+RIvIiNcDYG+XKSSuJyZOlXYkdnDkDfPABcPnlRZuOGgIn3G6q48dl25MmifWgZk17t8csAseMVa9r14KqxnYRzjiklStF3Bw5Is1eO3Qo/H5amjzbdfzFMLbecZl5EjOnMHMyMw9zvfYcM493/c3M/CgzN2HmVGYea+d4fOLpdwfkQnPLLaGve9EiMS/HknvKnZo1JRZn9245SevVs99nbdY9BYgVrk6d2BU4S5YUH6ibkCBZVpFiwcnIkN+klmfOQYRy331A1ar2WXF++klS5h96qOh71atLRk04BQ6zxMO0bStFB0uXFoFnJ+vXywTJX4CxQTiqGvuy3pcrZ627cNEi4LLLZJ/PmiVxaZ7UqiVtK1TgBEyMmRRCwN3v/vLLYv78xoK6g5Mni2hyNznGGmlpwL//DaxeLTMcu33WZt1TBh07isCJtDYToXLsmNRMMZOJ1Ly5CJxI2AeRWODPHxUqSLfxCRPsERrvviuF/a6+2vv74Q40Xr9e2lWkpYklu1Mn+wWOvwJ/3rC7qrE3V1SJEnLOpaTIhDjUGKB584DOncW6OmeO1KzyBpH8FipwAkYFjjeeeEJmEvffLwXJQmHKFDk4i/MrRzu//lr0NTt81oG4pww6dpRso1gr8b5smQgWM7Fdqaky89y1y/Zh+WXnThG/kRpg7IuBAyUrz2orzl9/yc39wQd9H8+tWsnkwTMmxC6MG6nhGunWDVizRoSPXcybJ25Ws21s7K5qvHu3PNesKQKjXj3giy8Kak7dfbeI9GDjgH7/XSa9Z50lk6/zzvO/fFqaHAOHDwe3vThFBY43EhMloyE3F7j99uBrvfz9txSZ69bN2vFFIuHyWQfinjKI1Tgcfy0aPDGCkJ2Ow4mm+Bt3KlWSzMGff7bW1ffuu2Ihuu0238u0aiXXoHC5GLOyxC1lWBSM65edVpyMDLHIJiaaW97OqsZHj0rBxWuukYlRfr5Mjvr2lYnCzJlimd6+XY7jAQOAffvMr3/CBLHWJScDs2eLNbo40tJkMrNwYZBfKj5RgeOL5GRJ0/v9d8kgCIbffpOTI1bjb9wJV+2MQN1TgFyoK1WKPYGzZIl8L6NWhj8iJZMqI0Nq37Ru7ew4guHBB0WM/Pe/1qxvxw7gu++AO+7wHyQe7kDjBQvEKmgUmzvvPBEUkybZs70jR0R4m4m/cadHD3uqGo8YIdZOX9ZnIonPzMmRWjWffy5uq//9r3i31bffAtdfLxOOmTMltsYMbdvKs7qpAkIFjj8GDAC6dweefFIO5kCZMkUC4gxTbywTjgqwwbinAJkVXnxx7Amc7Gyx3php3lqtmrQAiAQLTqtWReusRANVqojI+eEHqY0TKh9+KDfEBx7wv1zdurLtcAicvDwJfPW8ZnXvDsyYAZw4Yf02//pLJoKBui2vucb6qsYnTwKvvy4ZbcVZGStWBN58syDQ/957pc2EYaX05LPPgD595Fr0228SuG6W6tWBBg1U4ASIqbsEEdUioouJ6FLjYffAIgIi4NNPJXK+Xz9J5zQLswicK680b3aNZjwz0UqUkIvyDTdYt41g3FMGHTtKOmY4C6bZSV6eiJVAaiulpjprwTlzRtwf0eaecueRR6wpenfiBPDxx0DPnpLG7w+i8AUa5+RIIK1hMTDo1k1u/jNnWr9NI8A4EKssYE9V488/lzi1QGIHmzaV9O4xY+SzF10kSRcffVRQCblqVbHUXXmluPr8Wex8oYHGAVOswCGiVwHMBfAsgCdcj8dtHlfkULOmmB6zsgK7qC1dKgGV8eCeMnDPRJs4UU72116zbv3BuKcMjDgco1pqtLN+vdyIAunl1Ly5iDynqjovXSo39mgLMHanWjUJOB47NjirrsE330jchrfUcG+0aiWCNpBJVjB4BhgbXHaZZFTZEYczb56IhMqVA/uc1VWNz5wBXn1Vjs/09MA+SwTcfLMcE088IQHJ991XUAn5wAGZ6PbpU9TSbZa0NFlfuOqNxQBmLDjXATifmbszcw/Xo2dxH4opbrhBSqj/97/mFfSUKfLsXhE0nujaFejdW0Th2rWhry9Y95RBWprEFMSKm8qoYByoBef0acmIcYJoDTD25NFH5Wb/0kvBfZ5ZgoubNzd/I23VSlpchCKqzLBggTT5PP/8wq+XLi0pzVYLnPx8OS6CFb1WVjUePVoExLPPmnP7eqNCBZnUeYutycsDnn8++PFpwb+AMXOn2ADAS2vTOOP998Wa06+fuXTNKVPk5mN3BdBI5u23JdbivvtCr78SinsKkBtS27axJXASE82n1QLOZ1JlZMj5YHfTRrs56yyJtxg9WvoGBcrMmfIbPPSQ+RtpuAKNs7Kk2Jy3SUS3bvJ9rZiwGKxeLdaNQAOMDa66ypqqxnl5IlhbtbIm69VXOYZQskpbt5bjRQWOacwInOMAsonoYyJ6z3jYPbCIo3JlMTuuXg08/bT/ZQ8flhtpPLmnvFGzphRN/O038U+HQijuKYOOHeXiYEegZLjJzgYaN5aZtVkuuEDM+k7F4RgF/oKdHUcSjz8u2WDBWHHeeUeCRgOplH7++SLS7RQ4p0/LceUrKcKOdPFAC/x5UrmyNVWNf/hBhNvgwdYcn3ZklVaoIOe8ChzTmBE44wG8CGAegIVuj/jj8ssli+L994Hp030v98cfEucQ7wIHkIJY7dpJcOaBA8GtI1T3lEHHjgWBrtGOmRYNnpQqJTdKJwTOnj0SNxTN8TfunHOOHNtffQVs3Gj+c+vXy834nnsCE6eJiWKBs1PgLFsmIsczwNigYUM5fqxMF583TwKFU1KCX0eoVY3z88WV3rgx0KtX8ONwx1tWqRXB6UagcSRUJI8Cir1bMPOXAMagQNh843otPnn5ZTnJb7/d9w17yhRR28GaXWOJxETJFtm/Hxg0KLh1hOqeMjB+j2h3U+3fD2zbFlx3+ubNnXFRzZ8vz9Eef+POk0+KRezll81/5oMP5Jy4997At9eqlVhY7Lq5GcLfX1mL7t3FxWZVVWWjwWYoE5dQqxpPnCjnxDPPWNcQ2TOrtF49+T/UjuxpaTJZCLXCfpxgJosqHcBaAMMBfAhgTdykiXujbFng66/Fx+qtfoWRHt6lS0GhrHinZUuJN/j44wKTdCBY4Z4CxC3QuHH0CxwjwDhQCw4ggcabN4tVLJxkZooY8NZMMFo591xJ/f3iC9mnxXH4MDByJHDTTfLZQGnVSn63QCxGgbBggaQzN2jge5lu3STYecaM0Ld38KBk9YVq1QulqjGzWFUaNpQsKCtxzyo1KiGHigYaB4QZufomgCuZ+TJmvhRAVwBv2zusCCctDfjPfyTI8PvvC7+3erVc7NQ9VZgXXpCuyHffHViqq1XuKYOOHYG5c4NvvxEJBNKiwRMj0Hj5csuGY4qMDBlvsCmykcpTT8nzq68Wv+wXX0jVXbOp4Z4Y1Z/tclMtWCDuKX8xKJdeKr+hFXE4RladFZbuYKsa//abZGA99ZQI8EinRQuZOKvAMYWZO0YSM682/mHmNdCsKjFnpqWJL33nzoLXjRM/XtPDfVG+vMQuLVsmQZZmsco9ZdCxo4gmKyrROsWSJRLAfdZZgX/WiZYNeXlyE4kl95RB3brirh45UnoT+SI/H3jvPbFWtGsX3LaaNRP3lh0C5/hxOSeKq7peqpTEIk6eHLqrLCNDJi3B7g93gq1qPGwYUKsW0L9/6GMIB6VKySRFBY4pzAicLCL6lIjSXY9PAMRAlGaIJCVJgOHx42KmNk72KVPEDWKmP1C8cd11Url1yBBzJn3AOveUQSw03jRaNARDnTrSvyqccTjLl0tRwlgJMPZk0CARMP6KWv76qwQYP/xw8NspXVrKAtghcLKzRYiaaSvTrRuwYUPo9ZTmzZObdfnyoa0HCK6q8Z9/ArNmSWG+aGodkpYm8VLRbIUOE2YEzr0AVgJ40PVY6XpNueACuahNniwBZMePywkTD93Dg+X998UEPnBg8TNAq91TgMQX1Kwpbqpo5PRpYNWq4AKMAdn34W7ZECsF/nxRvz7wr3/JNcDdmuvOu++KizbULB27WjYYFgFfGVTuWJEunpcngedWid5gqhoPGwbUqCE9B6OJtDSJ57KyHlGMYiaL6hQzv8XM17sebzPzqXAMLiq4/34JKH7gAbmAnToFjBol8TlKUerWlXiciROBn3/2v6zV7ilAbvAdO0avBWflSolhCtaCAxRkUoUr1TQzU24kxfVcimaeeUZ+lzfeKPresmXA77/LtSLUxINWrURE7d4d2no8ycoS4V+rVvHL1q8vVupQ0sVXrJB4GSuteoFUNc7KEmv7o49GX1yYBhqbxqfAIaLvXM/LiGip5yN8Q4xwEhLkxDpzpiBtfM8e4K67VOT44qGH5Ab94IP+gwKtdk8ZdOwoLrJoTLUMpkWDJ6mpMgMMpapqIGRkxE6BP18kJ0uWzEcfyfnvznvvSZE+KywFdlU0XrDAnHvKoHt3sVYfOxbc9oxsSitLaVx1lVhyzLipXnpJigTed5912w8XjRuLKFOBUyz+LDhGqP81AHp4eSgGb71V9LXjxwPrSBtPlCghKeM7dkg2mjfscE8ZGHE40eimys6Wm2WjRsGvI5wtG/7+WzILYzX+xp1nnhEL7ptvFry2b59YdPv1k0adoWIIWysFzuHD8huZcU8ZdOsm1tU//ghumxkZ1lv1zFY1Xr5crMcPPhhcV2+nKVFCMupU4BSLzzsHMxvO5PuYebP7A0AUyl4b8TUTDtcMORpp314y0N5/H1i0qOj7drinDIzAxmh0Uy1ZIhaYxMTg19GsmTyHIw4nFgv8+eL886WWyvDhImwAics5eTL41HBPKlUSUWClwFnoKkwfiAWnY0c5h4KNw5k3T6w3Vlv1rrmm+KrGL78MlCsnAidaSUuTY8Du7vJRjpmp8RVeXtMoWnfs6DsSD7z0kqQ633130cBAu9xTgMyALroo+gQOc2gZVAYVK0ocRTgsOJmZYoEL5OYZzQweLNbbt9+Wm8+HHwJXXBFYU9TisDrQOJAAY4NQ0sX37ZMAWTusesVVNV63Dhg7VlxTVljUnCItTYRzNJe7CAP+YnDuJaJlAM73iL/ZCEBjcNyxq+9IrFO5stwIsrLkRmBgp3vKoGNHsWCEu6JvKGzbJnFeocTfGIQrkyojQ7ZlRSpwNNCkidx8Xn5ZmnFu317gErSKVq3kRn34sDXry8oSwVu9emCf695dLCU5OYF9zo74G4Piqhq/8ooEej/6qPXbDicaaGwKf3ePbyCxNuNROPamDTPfGoaxRQ929R2JB3r3Bq68Uma+RqE0O91TBh07yswzmNYRThFKBWNPmjeXuItTNiZE5ueLiyoe3FMGo0eLcHS3anz0kbUJB0agsRFwHiqBBhgbBJsunpEhVtRALEaB4Kuq8ZYtUrtswABplhrNJCdL3R8VOH7xF4NziJk3MXMfV9zNCQAMoDwRqe/FEzv6jsQDRBKzcPp0QRE0O91TBu3bSxxLNLmpjBuaFRaB1FRxC65aFfq6fJGTI1aGeAgwNhg8WFwH7lidcGBlJtXevXK9Ckbg1KkDNG0aeLp4RoZYIcuUCXybZujRw3tV49dfF+H5xBP2bDecEIlAVIHjFzPNNnsQ0VoAGwHMArAJgAWNSBTFxXnnAc8+C/zwg2RWTJggjfjGjLFvm+XKSSZCtAmc5GTpVB8q4cikMqxj8WTBCUfCQc2awNlnew/ODxQjwDhYa0r37sDs2cDRo+aWz82VOjV2uKcMLr64aFXjXbuATz+VlgyxEhuZlibn74kTTo8kYjET4PBfABcCWMPMDQBcDiDTzMqJ6CoiWk1E64joaT/L/R8RMRHZZLNUIp46dWRWYmSfHDlify2hDh3EhXL6tH3bsBIrAowNGjWSQFE743AyM+VGk5Ji3zYijXAlHFgVaLxggZx3wXZ579ZNgql//93c8kuXikXLTquet6rGb70l5/nTPm9D0Udamnw/w3UdiYweLfFdCQnyHObacGYEzhlm3g8ggYgSmHkGgGKFCBElAhgOybhqAqAPERVJJSCiCpCaO/MDGrkSWzz/fNFsDLtrCXXsKO4EK2bCdnPkiPQysiLAGJCbQJMm9ltwYr3AnyfhSjho1UqqWocaQ7VggQTlBlsPpkMHsSiajcOZN0+e7bTgAIWrGv/9t8RB9e4t1uJYIdIDjUePlknq5s1ybd+8OewFcM0InINEVB7AbACjiehdAGbKV7YDsI6ZNzDzaQBjAVzrZbkXAbwK4KSX95R4wYlaQh06yHM0uKmM1gpWWXAAezOpDh2SG3A8uaeA8CUctGol7p7ly0NbT1ZWaMG+JUtKqxqz6eIZGcC554rF1k7cqxq/95640J55xt5thptatcRdGakCxyiZ4E6YC+CaETjXAjgO4BEAUwCsh7lKxrUAuNfC3+Z67R+IqDWAOsz8q78VEdFdRJRFRFl79+41sWkl6nCiltA558iMLhoEjhUtGjxp3lz6GhluQSv56y+54cVTgLFBOBIOrAg03r5dfv9QaxR17y4TkZUri1/WrgJ/nlSuLG7Y11+X3ndlyliXdRZJpKVFrsCJgAK4ZgTO3QBqMnMuM3/JzO+5XFYhQUQJAN4C8FhxyzLzCGZuy8xta9SoEeqmlUjEqVpCRuPNcDWeDJbsbLloWznzTU2VZzvcVJmZchNr1876dStSzbhChdAEjnFjDFXgXHWVPBfnptq1SwRfOETv6NFSKyg3V/4/cSI2+wOmpUm5h0is5xUBBXDNCJwKAKYR0RwiGkhEZ5tc93YA7lfj2q7X3NfbDMBMItoECWQer4HGcYpTtYQ6dgT275eLRCSzZIlYb6yc+dqVSTV6tFSpNlxqsXZTiQQSEuR4CEXgZGVJqYRQ3Z61a4tYLi5d3MiqC4fAGTy4aBuDWOwPaIhTIxsukhg4sOhrYS6AW6zAYeYXmLkpgPsB1AQwi4h+M7HuBQAaEVEDIioJ4GZI0UBjvYeYuToz12fm+pDMrJ7MnBXMF1FiACdqCRmNNyPZTZWXJ7EyVsbfAJJqXL26tXE4RmChUQvGgcDCuKFVKxG+nm1OzLJggfQl87ScBkP37nIO+auuPG+exOy0bh369oojAtwjYcGIn4pEN9WSJVI1unZtxwrgBlIHfw+AXQD2AziruIWZORfAQABTAawC8B0zryCioUTUM5jBKorlpKTITT6SBc66dWJitzL+BpCLTvPm1lpwIiCwMG5o1Ur27dq1gX+WOfQAY3fMpItnZEg6eqlS1mzTHxHgHgkL1aqJuzLSBM6aNcA330iT2a1bHSuAa6bQ331ENBPA7wCqARjAzKZKqTLzJGZOYeZkZh7meu05Zh7vZdl0td4oYYeoIA4nUrGyRYMnqamSiZOfb8364mXmHAmEEmi8caOkT1vVBPXiiyXV3FcczunTIqjsTg83iKf+gJEYaPzf/4qQffxxR4dhxoJTB8DDzNyUmYcws4lQeUWJIjp2lBozO3c6PRLvLFlSULfGapo3FyvAhg3WrM9Xj59YmzlHAk2aiMsnGIFjVYCxQVKSdE33lS6+eLHU7AlXVl089QdMS5MJxJ49To9EWLtWXNL33itucAfx103cqPz0OoAtRFTV/RGe4SlKGDDicObOdXYcvsjOBho3tse0bwQaWxGHwyyViz2J1Zmz0yQlSQxNsAKnZEn5vFV07y4d773V5jEK/IWzbEC89AeMtIJ/w4bJtSoCen4V100cABYCyHI9L3T7X1Fig1atpE5GpAocI4PKDpo0kRmuFQLn11+lFkq/fvExc44EjJYNgZY5yMqSY6pkSevG4i9dPCNDjoVzz7Vue4rQurVk1UWCwFm3Dhg1Crjnnojo2O6vm/g1rucGzNzQ9Ww8GoZviIpiMyVLSnfxSIzD2bsX2LHDnvgbQKwrjRqFHmh85ozM2FJSgJEj42PmHAm0aiVlDrZtM/+ZvDxJK7bKPWVw7rlynHpLF8/IiM+ij+GgfHmx8EaCwBk2TCyLTz7p9EgAmAsyLhIW7+01RYlqOnaUmbDZrsjhwqi+apfAAaxp2fDJJ0BODvDaa3KBU8JDMIHGq1fLcW5VBpU73buLJdS98NzWrSLAwhVgHI8YgcZOFixdvx74+uuIsd4A/mNwSrtibaoTURW3+Jv68Gi5oChRT8eOMrOdH2E9X8MhcJo3l4vTMTMt5rxw6JA0S01PB3pqBYiw0ry5uAIDEThZrggDqy04gKSL5+YCv7mVSgtngb94JS1NrL1OZitGmPUG8G/BuRsSb3MBCsff/ALgA/uHpihh5KKLxI8daW6q7Gwx/dvZoiQ1VWZ+K1YE9/mXXhI3yZtvxlfn8EigfHlxCwYicBYsAMqVAy64wPrxXHQRUKlS4TicefMkxs1OkR7vOB1ovGED8NVXwN13SwPQCMFfDM67zNwAwOMeMTgtmFkFjhJbVKwos+FQBc7o0UD9+iKW6tcPvYKvnQHGBqG0bNi0CXjnHQksDkeFWqUoRqCxWRYskN8qMdH6sZQoAVx5ZeF08YwMuQGr69I+mjeX/euUwHnpJfntI8h6A5irg5NPRJWNf1zuqvvsG5KiOETHjnIxNhr0BYrRpmDzZrm4h9qm4NQpYNUq+2e+DRrIjD6YOJxBg+RGqWngztGqlbgm9pvogXzmjFgF7XBPGXTvLoHxS5dKBe5Fi9Q9ZTelSsl1wgmBs3Ej8OWXcq2LsCw5MwJnADMfNP5h5gMABtg2IkVxio4dJQ7FiHsxy6lTYoYfONDaNgUrV4rYstuCk5Ag9VACteBkZgJjx0q10tq17RmbUjxGoLFR8dofy5fL8WqnwHFPF1+4UI5hDTC2n7Q02d9WVSU3y0svySTnqafCu10TmBE4iUQFjnUiSgRgYfEERYkQOnSQ5+LcVPv2AePHywndsaPEHHToABw86H35YAP/7GzR4ImRSWU2C4MZePRRyZaIMLN03BFIJpURYGxHBpXBOefImCZNKggwvvBC+7anCGlp0ux0zZrwbXPTJuCLL4ABA4BakZd7ZEbgTAHwLRFdTkSXAxgDwEfDEUWJYmbNkpnIww8XxM8wywXj88+BO++UwMwaNYBrrwXeflsyrwYOBH76ybcVo06d4MazZInUqTnvvGC/kXmaNxcXx65d5pb/4Qe5eb34ogS6Ks5RvbocY2YEzoIFUm06OdneMXXvLlbNSZPk+D2r2P7MSqg4EWj80ktiAX766fBtMwBKmFjmKQB3AbjH9f9SAJGR5K4oVmHEz+Tlyf+bNwP9+0s/lSNH5LUqVcTUftttYrFp21ayQwyOH5d1eLqpqlWT1zyb/xVHdrZYVuwIBvUkNVWely4tPgvi1CmxXqWmArffbv/YlOIxG2i8YIEct3ZnuyUmyrk0c6bEd40erQUf7aZxY9nXCxZI0L/dbN4sE7+7745I6w1gwoLDzPkA5gPYBKAdgM4AVtk7LEUJM4MHFxUmeXnizx4xQlKo9+0DJk6U2collxQWN4D3Bn/9+4tQ6dxZPm8W5vBkUBkYAsdMHM7770tg4Ztvhkd8KcXTqpUU8PM8ht05cUJicOx0TwEiZt54o+D/Y8dCC7ZXzJGYKNlx4bLgRLj1BvBf6C+FiJ4nohwA7wPYAgDM3EnTxJWYw1eczPHj4l9u0kRO5uLwbPD3xRfAjz+KWLn4YhEGZsdz8GD4aodUqyYZEMVlUu3bB/z3v1LQ7YorwjM2pXhatZJjzt/vt2SJBPzaGWAMeJ8shBJsr5gnLU0mVGfO2LudLVsK3PYRnGDg74qdA7HWXMPMHZn5fQB54RmWooSZunUDez0QevWSyq779km67KJFxX/GyOQKlwUHkDic4iw4Q4eKy+7118MzJsUcZgKNjZm93QLH12TBySq78UJaGnDypPeO7lby8svyHMHWG8C/wLkewE4AM4joE1eAsZYpVWKTYcOKxsiULWtdfZcOHaRHT6lSwGWXAdOm+V8+O1vcXIbrKBykpkpquq/Z3+rVwEcfibuhadPwjUspnjp1gKpV/QucrCzg7LPtj5ewc7Kg+CccgcZbtkhD3TvuCD6BIkz4q2Q8jplvhrRqmAHgYQBnEdFHRHRlmManKOHBW/zMiBHWBkY2biyZRw0bAldfLY3pfLFkiWSfhDNDqXlz4PRpYO1a7+8/+aTEHb3wQvjGpJiDqPhA4wUL5AZod4Cx3ZMFxTcNG4rQtVPgvPKKPA8aZN82LMJMkPExZv6GmXsAqA1gMSSzSlFiC8/4GTuyPs49F5g9G7j0UuBf/5KLhbfaM9nZ4e/d455J5cnMmVL7Z9AgTfmNVFq1EhejNwvckSPS7d1u9xQQnsmC4h0iCSK3S+Bs3SrWm3//Oyoscmbq4PwDMx9g5hHMfLldA1KUmMdoRtinjwiGBx4oSE8HpFjXhg3hFzgXXCD9ZDzjcPLzgccekwvaww+Hd0yKeVq1khT+nJyi7y1aJELa7gwqg3BMFhTvpKVJDI6/jLpgMSZkUWC9AQIUOIqiWETJksCoUdLmYPhw4KabJI0XKLCghDPAGJD4oPPPL2rBGTVKbpAvv1w0NV6JHPwFGhsz+nAJHMU50tJkwmSmdUcgbNsGfPqp1L6qV8/adduEChxFcYqEBMlGevtt4OefpQvziBFAz57y/j33hL92iGcm1fHjwDPPyEXz5pvDOxYlMFJSJNbFl8CpW1fdi/GAXYHGr7wiFrkosd4A5ioZK4piJw8/LLE5t9wimVZGTM727ZKxBITPxJ+aCowZAxw6JK60N9+UcYwda64OkOIciYkiUL2VIcjKCk/8jeI8554rDysFzvbtwCefiPWmfn3r1mszesVSlEjgppukp5BnwHG4C6Q1by7Py5cDO3cCr74KXH+9NBVVIp9WrcQ14d5Rev9+ielSgRM/pKVZI3BGjxZBU7u2ZFg2aRL6OsOIChxFiRT27PH+ejgLpLlnUj33nFzUXn01fNtXQqNVKwlSd6+YHY4O4kpkkZYmTYIPHgx+HUZ/vs2bC14bPDiqWm7YKnCI6CoiWk1E64ioSMlDInqUiFYS0VIi+p2IoiNySVHsIBIKpM2ZI6mm990nAYWXXx6ebuaKNXgLNDYETps24R+P4gyGtW7hwuDXEQMtN2wTOESUCGA4gG4AmgDoQ0Se9q3FANoyc3MAPwB4za7xKErE43SBNGPG5u4mmzUrqmZscU+zZhKL4y5wFiyQAOTKlR0blhJmDGtdKG6qGGi5YacFpx2Adcy8gZlPAxgL4Fr3BZh5BjMbEjETUkhQUeITpwukeZuxnTgRVTO2uKd0aYmT8BQ46p6KL6pWBZKTgxM4Bw4ADz7ovQApEBUF/gzsFDi1AGx1+3+b6zVf3AFgso3jUZTIx8kCaTEwY1NQuGXDjh3y0ADj+CPQQOO8PODjj4FGjaQ21+WXF617FWUtNyIiyJiIbgXQFoDXFsVEdBcRZRFR1t69e8M7OEWJFyIhBkgJnVatgF275GHE36jAiT/S0qS1wu7dxS87Z45Y+e65R9ycixYBv/0mqeFR3HLDToGzHYB7q9HartcKQURdAAwG0JOZT3lbkas9RFtmblujRg1bBqsocY/TMUCKNbgHGi9YIPWLwl0VW3EeMwX/tm6VAp6XXirlBL79Fpgxo6BNTJS33LBT4CwA0IiIGhBRSQA3AxjvvgARtQLwMUTc+MiRVRQlLDgdA6RYgyFmFi8WC07TpkC5co4OSXGA1q1F3HoTOCdOAC++KK1ZfvkFeP556WF20032d5sPI7ZVMmbmXCIaCGAqgEQAnzHzCiIaCiCLmcdDXFLlAXxPslO3MHNPu8akKEox9O2rgibaqVQJaNiwwIJz7bXFf0aJPcqVk4Bzd4HDDPz0k/TA27QJuOEG4I03oqa3VKDY2qqBmScBmOTx2nNuf3exc/uKoihxSatWwNSpwJEjmkEVz1StKsdBQgJwzjlSKmDVKino+ccfQKdOTo/QVrQXlaIoSqxBJOIGAIYOBSpWVMtcvDF6NJCZWdC2Y+dOefTvL0U8S8T+7T8isqgURVEUixg9GpgwoeD/XbukgKMWbIwvBg+WViuezJwZF+IGUIGjKIoSWwweDJzySEiNshL7igVoXSsVOIqiKDGF3tgUQOtaQQWOoihKbKE3NgXQulZQgaMoihJb6I1NAbSuFTSLSlEUJbYwbmCDB4tbqm5dETdxdGNTXMR5XSsVOIqiKLFGnN/YFAVQF1XMMHo0UL++1HOqX18zQhVFUZT4Ri04McDo0VLm4vhx+X/zZvkf0EmcoiiKEp+oBSeK2b9f+qTde2+BuDHQshdKtKLWSEVRrEAFToRg5qK+ZYu8fs890iC4enXguusKKrJ7W15RognDGrl5s/QFNKyRKnIURQkUFTgRgK+L+muvAR9/DNx6q2T41asnf3/zTUFixOzZQJ063tfLDNx+u1h6FCUaGDw4dGukWoAURQEAYmanxxAQbdu25aysLKeHYSn164uo8cXZZwOXXFLwaN4cSEwseN8zBgcAypQBrrgC+PVXaSj73ntA795SDkFRIpWEBBHm3mjZUoR93boi6t3/rllT2ut4OxfKlo278h+KElcQ0UJmblvkdRU4zuPvor5mDXDeecULk9GjvZe9WLIEuPNOICsL6N4d+OgjLWiqRCYHDgDnngucPFn0vfLlgcsuk+N7yxbg0KHC7ycmArVqAbt3F23DBIj1c9MmW4atKIrD+BI46qJymD17gNKlvb9Xrx7QqJE5q0vfvnIBz8+XZ2O22qIFkJkJvPWWNJFt0kSsOXl5Fn0BJaKIVvdMTg7Qrp00Py5ZsvB7ZcsC//sfMHEisHQpcPCgCJzly4FJk8SN+/TTIoC8iRtA49EUJR5RgeMgEycCqalyUU9KKvyelZXVExOBRx4BVqwAOnYEHnoI6NABWLbMmvUrkUG0BuhOngy0bw8cPiwxZZ99Vnx1+YoVJdC+Wzf5jv/9L/DVV7K8NxISgDfflG0oihIfqMBxgGPHJBOqRw+JHViyBPj8c/tbhtSvLzeTUaOA9euB1q2BZ5/17hKIRqLVemEVVgTohhNmsSxecw3QoAHw118ivH1ZI83grQ1TqVJASgrw+OPinh00CNi508pvothBvJ/PigUwc1Q92rRpw9HMX38xN2rETMT8xBPMJ086M469e5n79WMGmFNSmJ99lrlePRlXvXrMo0Y5M65gGTWKuWxZ+T7Go2zZ6PseoUBU+PsbDyKnR1aUkyeZb7tNxnf99cxHjli37lGjvB/Lf/3FfOONzAkJzCVLMt95J3NOjnXbVaxDz2clEABksRe94LhgCfQRrQLnzBnmF19kTkxkrlOHecYMp0ckTJ3KXL160ZtitF1M6tXzfnOvV8/pkdnPsWPM//mP9+8PMFesaK2ACJVdu5gvvljG9txzzHl54d3+2rXM997LXLq0iKBevZgzMsI7huLwJdLihTp14vd8VgJHBU4x2HlBWbeO+aKLZG/fcgvzgQPWrdsKfF1Mzj478sbqjU2bfN/cI9F6YRX5+cy//MJcv75814suYi5TpvD3T0yU51q1mL//Xj7jJIsWyfFWpgzzd985O5bdu8VyWaWK7KNLLmGeMIH566+dFReRYL1wSmAdOMD83//6P5+dPobDRbyL3EBQgeMHuy4o+fnMI0cyly/PXKkS8zffWDJcy/Hl2jAeycli2n/5ZbH47N3rfT3hPCGPHmX+6ivmzp39j52I+f77mdessW8sTrBuHXP37vIdmzQpsAh6+w3mzWNu0UKW7dpVLBhO8P33cl7Vrs28cKEzY/DGkSPM77zDXLduwTHjlLjIy2OuWdP7sRwu64UTAmvnTuYnn2SuUEG2V7q073O6eXPm//3PfqukkwIjEkRuNKECxw92uDf27pXYAoA5PZ1582arRms9vr7/WWcxv/QS8w03MDdoUPi9OnWYr72W+YUXZNb7/vv2n5D5+cyzZzP/+98iGgHmhg2Zhw5lfvvtotsvXVpm5UlJcpG69lrmWbOiewZouKNKlZJ98OabzKdPF/+5M2eY331XbiClSjE//zzz8eO2D5eZ5ab9/PPym1x4odzMIpHTp5mrVfN+LtSoIa41q8nPZ16xgvmDD+R6UbWqf7EejmPXEHrhEFjr1zPffbcckwkJzL17My9e7P0GX6YM8x13MLdsKf9XqMA8cKDsP6uxQmCYFUi5uczbtjHPncs8ZgzzK68UXN88H3XrWvHtYg8VOH7wZ8Ho35952DCZfS5Z4vum4H4wn3WWxD0kJTG//nr4YwwCxezJ/PffzL//Lt+pTx/m888v3vpTq5acwGbH4e2CsGmTiJiGDWWd5cuLyJk9u/AF39fnd+wQV4Rx82rdWt4zIwwihfx85nHjCtxRffowb98e+Hp27JDPGuJw0iTrx+rO0aPM//d/sr1//Yv5xAl7txcqxR3P9eqJNfP110Use7Mi+Lux5eeLBe3jj5lvvlncwO7rvv123yILYL7gAuY33mDes8fa733mDPO0aRJ47e/7P/UU88yZoZ87S5bIcWgEfN91V1HLoq/9mJ8vVslbb5XPAsyXXcb87bfMp04FN578fOYtW2Sy9uKLRa+HxiMxkblNG+YuXZhvuon5nnuYBw2S4+HTT5l/+kn2z0svFXUXlyolAu3ZZ+VcuOwymTgmJfnf556Phx6SmLFomKiFywqmAscPviwYpUrJDdqbiu7Shfm++2RW/MQTRQ9mIjnIo4VgD8TDh5nnzPF/QpYsKRfma65hfvhh5uHDxdW1fr1cWI3te15USpYU94vxf+fO4pY6ejS473jsmNxYLrigQHy9/DLz/v3Brc8bdpzQvtxRofD77yJQAbEcbNkS+joN3PeBcfF+443ouCD7uhacfbZYy3r3LmzNTEgQl8mddzKPGCHnvDfLw113yU2tdu2C12vWZO7bV26MGzYUjMGX9WLAgIJYvqQkucFOmxb8BOrMGebp02W9hqgqX973zb1UKeYSJfgf60mvXnI+BXLs/Pkn89VXF2zr8ceDE+oGe/Ywv/pqgfA/5xyxcG7d6vtcPHlSYsE+/1yuR+npBXFYZh7du4sl8vzzxbJn7BOzj4QEsYB37CgxmYMGMX/0EfOvvzIvX8586JDv47BMmQJRV6+euPUWLozMcyucbjYVOH4o7oc4ckROiLFjxZJw663MaWlipfF3IMdTxL+vE7JqVTkJr79ebgSe+zkpSdLUffncS5SQfb5xo3VjzcsTy0WXLgW/9f33y004FHFitVm7Th3m664L3B1llpMnC2aaZcvKjeLLL63fB6VKRU/sgNnfcM8euSE9/zzzVVf5dy0Zj+rVxfrz4YeSnu7vpuRPKC9bJjdmY5sNGkhgrrtQ8PX5M2eYf/tN3EJG9mS5cmJN+flnsVD72weHD8tyd99d2JXVpAnzY4+JYDp5svD269aVSeAll8iy1arJOW3lxCI3V36Pq68usMIZAfbGIzFRBKa7IClThrldOxGgw4eLAPMnMLxd0/PzZb9s2iTutd9/91+ywZjU+cPfb3DwoJyn3boVfJdGjcQytHx50fWEej4H8/n8/PBmwjkicABcBWA1gHUAnvbyfikA37renw+gfnHrjKQsqvx8ycSIpvojdmH2xpCfLxfiWbMkAPvppyXGx9dNwe59uGSJ1GPxvBgaN+ZBg8TaNGmSmK9/+UXM0N9/L4J39GjJuvniC9+uhbPOYs7MlG2tXSvf/8ABMad7uti8zZ4vuii0WW5xbNzI3LNnwf729hvm5THv28e8apW4Bn/8UWadQ4cyP/CAuFsuv9y3uT2axH6w14J16/wfx1a7qk+ckMSFTp0KbuA9ezI/+qh3kXn55WJxMH7X3r3ld/TmdjezD/LzmVeuFOHdpUuBZSEpyfv5VLWqWLyDtcCaZcMG35PP0qXlnB47VkSmL/d5qJMVK+I6zfwG+/aJ5fDyy8UyBDA3bSputjfeCO07+NoHH38scU/Tp4tF/dVXRXDfdJOI2PPO820FtOua7kvg2NZsk4gSAawBcAWAbQAWAOjDzCvdlrkPQHNmvoeIbgbQi5l7+1tvJDbb9NUNPN4a/Plq+GkGp/dhnTrAtm32b8eTxESpvFu2LLBvn/ceYeHaB2edBezdW/T1hASpsO2rf1nFikCNGvLIzPS+DJFUJo51nDqO160DRo6Uiui7d3tfhgi48UbgppukxYVnxedQOXYMmDED6NMHOHq06Pt163rfN3bgq4FxIMdhKNczJ7ra794N/PADMHYs8OefvperXBl48kngzJmCx+nTRf//6aeildF9Ub68VOWvWVMa5tasKcfiwYNFl7XjXAh7N3EiugjAEGbu6vp/EAAw88tuy0x1LZNBRCUA7AJQg/0MKhIFjhMHc6zh9D70d0H88095PzGx4OH5f2IicMklwPbtRddx9tlysh8/XvA4caLw/8ePS9NIb4RLHPjraj94cIGIcX9Ury6tEAycFqpO4/RxfOaM/B6h3txDwQpxESqRcByGIpBCZetW2WZxEElz26Skwo+SJYGNG31/bsyYAkFTsyZQoULRZcJ5LvgSOHa6p24A8Knb//0AfOCxzHIAtd3+Xw+gupd13QUgC0BW3QjNk9OiTKHj5D60yqTstFk7FCJhH8QCTl8LYuE4ChU9Dn3/DnXqiHuzuOzWcLnZrADhjsGxUuC4P6K1VYMS2Vh1QQzlhHb6ohwJ+0AJnVg5jqwYRzwfh6H+DpHyO5rBCYFzEYCpbv8PAjDIY5mpAC5y/V0CwD643Ga+HipwFLuIhAui02NwevuKNTj9Ozq9fUVwKosq3PgSOHbG4JSABBlfDmA7JMj4FmZe4bbM/QBSuSDI+HpmvsnfeiMxBkdRFEVRFGfwFYNTwq4NMnMuEQ2EWGkSAXzGzCuIaChEbY0HMBLA10S0DsDfAG62azyKoiiKosQPtgkcAGDmSQAmebz2nNvfJwHcaOcYFEVRFEWJPxKcHoCiKIqiKIrV2BaDYxdEtBeAneWiqkOCnZXg0X0YOroPQ0f3YejoPgwd3YehU9w+rMfMNTxfjDqBYzdElOUtWEkxj+7D0NF9GDq6D0NH92Ho6D4MnWD3obqoFEVRFEWJOVTgKIqiKIoSc6jAKcoIpwcQA+g+DB3dh6Gj+zB0dB+Gju7D0AlqH2oMjqIoiqIoMYdacBRFURRFiTlU4LggoquIaDURrSOip50eTzRCRJuIaBkRZROR9tMwCRF9RkR7iGi522tViWg6Ea11PVdxcoyRjo99OISItruOx2wi6u7kGCMdIqpDRDOIaCURrSCih1yv67FoEj/7UI9FkxBRaSL6i4iWuPbhC67XGxDRfNc9+lsiKlnsutRFBRBRIqRv1hUAtkH6ZvVh5pWODizKIKJNANoys9Z8CAAiuhTAUQBfMXMz12uvAfibmV9xCe4qzPyUk+OMZHzswyEAjjLzG06OLVogopoAajLzIiKqAGAhgOsA3AY9Fk3hZx/eBD0WTUFEBKAcMx8loiQAfwJ4CMCjAH5i5rFE9D8AS5j5I3/rUguO0A7AOmbewMynAYwFcK3DY1LiBGaeDenF5s61AL50/f0l5CKp+MDHPlQCgJl3MvMi199HAKwCUAt6LJrGzz5UTOJqEH7U9W+S68EAOgP4wfW6qeNQBY5QC8BWt/+3QQ/KYGAA04hoIRHd5fRgopyzmXmn6+9dAM52cjBRzEAiWupyYalrxSREVB9AKwDzocdiUHjsQ0CPRdMQUSIRZQPYA2A6gPUADjJzrmsRU/doFTiKlXRk5tYAugG43+U2UEKExY+svuTA+QhAMoCWAHYCeNPR0UQJRFQewI8AHmbmw+7v6bFoDi/7UI/FAGDmPGZuCaA2xMNyQTDrUYEjbAdQx+3/2q7XlABg5u2u5z0AfoYcmEpw7Hb58w2//h6HxxN1MPNu14UyH8An0OOxWFwxDz8CGM3MP7le1mMxALztQz0Wg4OZDwKYAeAiAJWJqITrLVP3aBU4wgIAjVxR2iUB3AxgvMNjiiqIqJwrqA5EVA7AlQCW+/+U4ofxAPq7/u4P4BcHxxKVGDdlF72gx6NfXMGdIwGsYua33N7SY9EkvvahHovmIaIaRFTZ9XcZSPLPKojQucG1mKnjULOoXLjS9t4BkAjgM2Ye5uyIogsiagix2gBACQDf6D40BxGNAZAO6Zi7G8DzAMYB+A5AXQCbAdzEzBpE6wMf+zAd4hJgAJsA3O0WS6J4QEQdAcwBsAxAvuvlZyAxJHosmsDPPuwDPRZNQUTNIUHEiRAjzHfMPNR1jxkLoCqAxQBuZeZTftelAkdRFEVRlFhDXVSKoiiKosQcKnAURVEURYk5VOAoiqIoihJzqMBRFEVRFCXmUIGjKIqiKErMUaL4RRRFUayDiPIgabQGY5n5FRu3lw7gNDPPs2sbiqJEHipwFEUJNydcZdhtx1X5NB3SaVwFjqLEEVoHR1GUsEJER5m5vMdrlQD8BaAnM692Fe77g5k/IaKjkPL2V0KaPd7MzHuJKBnAcAA1ABwHMICZc4joCwAnIY0OtwO4GEAegL0AHgBwDqQQYB6AQ8ysPdMUJQZRC46iKOGmjKtTsMHLzPwtEQ0E8AURvQugCjN/4nq/HIAsZn6EiJ6DiJOBAEYAuIeZ1xJRewAfAujs+kxtABczcx4RDQFwlJnfAAAiWgagKzNvN0rCK4oSe6jAURQl3Hh1UTHzdCK6EWKVaeH2Vj6Ab11/jwLwk6tb88UAvpf2PwCAUm6f+Z6Z83xsfy5ESH0H4CcfyyiKEuWowFEUJSIgogQAjSHupioAtvlYlCEZoAf9xPIc87UdZr7HZfG5GsBCImrDzPuDHriiKBGJpokrihIpPALpGnwLgM+JKMn1egIKugjfAuBPZj4MYKPL4gMSWniu0MURABWMf4gomZnnM/NzkLicOtZ/FUVRnEYFjqIo4aYMEWW7PV4hovMB3AngMWaeA2A2gGddyx8D0I6IlkNibIa6Xu8L4A4iWgJgBYBrfWxvAoBerm1dAuB1IlrmWt88AEts+ZaKojiKZlEpihLReMu6UhRFKQ614CiKoiiKEnOoBUdRFEVRlJhDLTiKoiiKosQcKnAURVEURYk5VOAoiqIoihJzqMBRFEVRFCXmUIGjKIqiKErMoQJHURRFUZSYQwWOoiiKoigxhwocRVEURVFiDhU4iqIoiqLEHCpwFEVRFEWJOVTgKIoScxDRECIa5fQ4FEVxDhU4iqJYBhF1JKJ5RHSIiP4morlElOb0uIKBiBoR0UkVSooSnZRwegCKosQGRFQRwEQA9wL4DkBJAJcAOOXkuEJgOIAFTg9CUZTgUAuOoihWkQIAzDyGmfOY+QQzT2PmpQBARMlE9AcR7SeifUQ0mogqGx8mok1E9AQRLSWiY0Q0kojOJqLJRHSEiH4joiquZesTERPRXUS0g4h2EtHjvgZGRBe6LEsHiWgJEaX7+yJEdDOAgwB+D3WnKIriDCpwFEWxijUA8ojoSyLqZogRNwjAywDOBdAYQB0AQzyW+T8AV0DEUg8AkwE8A6AG5Hr1oMfynQA0AnAlgKeIqIvnoIioFoBfAfwXQFUAjwP4kYhqePsSLkvUUACPFv+VFUWJVFTgKIpiCcx8GEBHAAzgEwB7iWg8EZ3ten8dM09n5lPMvBfAWwAu81jN+8y8m5m3A5gDYD4zL2bmkwB+BtDKY/kXmPkYMy8D8DmAPl6GdiuAScw8iZnzmXk6gCwA3X18lRcBjGTmbYHuA0VRIgcVOIqiWAYzr2Lm25i5NoBmEGvNOwDgcjeNJaLtRHQYwCgA1T1Wsdvt7xNe/i/vsfxWt783u7bnST0AN7rcUweJ6CBEiNX0XJCIWgLoAuBtf99TUZTIR4OMFUWxBWbOIaIvANzteukliHUnlZn/JqLrAHwQ4mbqAMhx/V0XwA4vy2wF8DUzDzCxvnQA9QFsISJABFUiETVh5tYhjlVRlDCiFhxFUSyBiC4goseIqLbr/zoQl1Gma5EKAI4COOSKi3nCgs3+h4jKElFTALcD+NbLMqMA9CCirkSUSESliSjdGKcHIwAkA2jpevwPEr/T1YKxKooSRlTgKIpiFUcAtAcwn4iOQYTNcgCPud5/AUBrAIcgouEnC7Y5C8A6SLbTG8w8zXMBZt4K4FpIsPJeiEXnCXi5/jHzcWbeZTwgguykK2ZIUZQogpjZ6TEoiqIEBBHVB7ARQBIz5zo8HEVRIhC14CiKoiiKEnPYJnCI6DMi2kNEy328T0T0HhGtcxX20gA+RVEURVEswU4LzhcArvLzfjdIga5GAO4C8JGNY1EUJYZg5k3MTOqeUhTFF7YJHGaeDeBvP4tcC+ArFjIBVCaiInUpFEVRFEVRAsXJOji1ULhI1zbXazv9fah69epcv359G4elKIqiKEq0sHDhwn3MXKT1SlQU+iOiuyBuLNStWxdZWVkOj0hRFEVRlEiAiDZ7e93JLKrtkCqkBrVdrxWBmUcwc1tmblujhtf+eIqiKIqiKP/gpMAZD+BfrmyqCwEcYma/7ilFURRFURQz2JkmPgZABoDziWgbEd1BRPcQ0T2uRSYB2ACpQvoJgPvsGouiKIqiRBWjRwP16wMJCfI8erTTI4o6bIvBYeY+xbzPAO63YltnzpzBtm3bcPLkSStWF/OULl0atWvXRlJSktNDURRFUTwZPRq46y7g+HH5f/Nm+R8A+vZ1blxRRtS1amjbti17BhlvIfZBaQAAV5ZJREFU3LgRFSpUQLVq1eDqAKz4gJmxf/9+HDlyBA0aNHB6OIqiKIon9euLqPGkXj1g06ZwjybiIaKFzNzW8/WYaNVw8uRJFTcmISJUq1ZNrV2KoiiRypYt3l/fvBm4/Xbg3XeBWbOAgwf9ryfO3VwxIXAAqLgJAN1XiqJEPPF8c65b1/vrpUsDkycDDz8MpKcDVaoADRsC118PDB0KTJgAbN0KMBe4uTZvlv8NN1cc7ceoqIOjKIqixBHxHoMybFjh7w8AZcsCI0bI99+1C8jOBhYvLngeN06EDABUqwYcOwZ4WuqPHwcGD46PfYgYsuBEAsOGDUPTpk3RvHlztGzZEvPnz/e57Jw5c9C0aVO0bNkSGRkZmDRpUhhHqiiKEsEMHlz45g4U3Jzjgb59xQ1lUK9egbgBgHPOAa66Chg0CPj2W2DNGuDQIWDuXOCDD4BevYqKGwNf7q8YJC4Fjh2Wz4yMDEycOBGLFi3C0qVL8dtvv6FOnTo+lx89ejQGDRqE7OxsrF69WgWOoiiKga+bcBzdnGG0JJo6VQKLi7O6VKgAXHwxcP/9wCefiCjyhi/3VwwSdy4quyyfO3fuRPXq1VGqVCkAQPXq1QEAv//+Ox5//HHk5uYiLS0NH330Eb7++mt89913mDp1Kn799VfMnTsXJ06cwJ9//olBgwZh1apV2LhxIzZs2IAtW7bg7bffRmZmJiZPnoxatWphwoQJSEpKwtChQzFhwgScOHECF198MT7++GPk5eXhoosuwuuvv4709HQMGjQICQkJGDZsWCi7TVEUJXzUres9iyiObs7IzJTndu2C+7wvN1c83QuYOaoebdq0YU9Wrlz5z98PPcR82WW+H6VKMYujsvCjVCnfn3nooSKbLMKRI0e4RYsW3KhRI7733nt55syZfOLECa5duzavXr2amZn79evHb7/9NjMz9+/fn7///ntmZv7888/5/vvv/2ddzz//PHfo0IFPnz7N2dnZXKZMGZ40aRIzM1933XX8888/MzPz/v37//nMrbfeyuPHj2dm5uXLl/MFF1zA06dP55YtW/KpU6f87jNFUZSIYtQo5sTEwhfpsmXl9Xjh6quZGzcObR2jRjFXqiT7r06dmN1/ALLYi16IOxfVqVOBvW6W8uXLY+HChRgxYgRq1KiB3r174+OPP0aDBg2QkpICAOjfvz9mz55tan3dunVDUlISUlNTkZeXh6uuugoAkJqaik2uOggzZsxA+/btkZqaij/++AMrVqwAADRt2hT9+vXDNddcg88++wwlS5YM7cspiqKEk759C1tr6tYtHIMS6zCLBefCC0NbT9++wHffyd+ffx4/+89FzLmo3nnH//v+6ifNnBnathMTE5Geno709HSkpqZi+PDhQa/LcHUlJCQgKSnpn9TuhIQE5Obm4uTJk7jvvvuQlZWFOnXqYMiQIYVq2yxbtgyVK1fGnj17QvtSiqIo4YYZ2LsXOOssYM8eYOJEIDXV6VGFj/Xrgf37Qxc4ANDWVf/ur7+Ayy8PfX1RRNxZcIYNEzekO1a4JVevXo21a9f+8392djaSk5OxadMmrFu3DgDw9ddf47LLLivy2QoVKuDIkSMBbc8QM9WrV8fRo0fxww8//PPeTz/9hL///huzZ8/GAw88gIPFFYMC4rvmhKIokcWOHcDRo0Dv3vL/okXOjifcGBm4VgicqlWBRo1E4MQZcSdw+vYVS2e9egBR0ey7YDl69Cj69++PJk2aoHnz5li5ciVeeeUVfP7557jxxhuRmpqKhIQE3HPPPUU+26lTJ6xcuRItW7bEt99+a2p7lStXxoABA9CsWTN07doVaWlpAIB9+/bh6aefxqeffoqUlBQMHDgQDz30kP+VaUEoRVEiiZwcee7ZEyhXDli40NnxhJvMTPneTZtas7727UU0RVlrplCJiV5Uq1atQuPGjR0aUXRSaJ9p3xNFUSKJ4cOBgQOB7duBm26SG/PcuU6PKnykpQHlywMzZlizvvffBx58UKoc165tzTojiJjuRaWEiNacUBQlksjJkbouNWsCbdpItd68PKdHFR5OnJDva4V7yqB9e3n2U3w2FlGBo/iuLRFPNScURYkcVq8GLrhA4gjatJFaLqtXOz2q8LBoEZCba63AadECKFky7uJwVOAoEmFdunTh1+KtIJSiKJFDTo4IHEAEDhA/cTiGlcWwulhBqVJAy5ZqwVHikL59geuuK/i/VKn4qjmhKO5oRqGzHD0qsSKGwDn/fKBMmfgROJmZctydc461623XDsjKih9XH1TgKAabNwOtWgH9+gFnn63iRolPNKPQedaskWdD4JQoIdaHeBI4VlpvDNq1kw7jq1ZZv+4IRQWOAuzcKSdVr15Aw4Yyewq1tLOiRCPx3sU6EjBSxA2BA4ibavHi2Lc+bN8u118r428M4jDQWAWOhQwbNgxNmzZF8+bN0bJlS8yfPx933nknVq5caet2u3fv7rWY35AhQ/DGG28Uv4Lx42W22qsXkJwsf2t6uBKPaEah8+TkAImJci0yaNNGrA9uxVRjEisL/Hly3nlA5cpxFWgcc60aTDF6tMzItmyRTKFhw0J2yWRkZGDixIlYtGgRSpUqhX379uH06dP49NNPza1g/35R76dPS7R7rVpAtWqmPjpp0qQQRg7g55/l4G/aFDh8WF7bsEF834oST2gXa+fJyQEaNJBYQAP3QGN3y06skZkp1/9Wraxfd0KCuKnUgmMNRHQVEa0monVE9LSX9+sS0QwiWkxES4mou53jAWCbj33nzp2oXr36Pz2kqlevjnPPPRfp6ekwChOOHDkSKSkpaNeuHQYMGICBAwcCAG7r0wf33n03LuzbFw2vvRYzMzLw73//G41TUnDbbbf9s40xY8YgNTUVzZo1w1NPPfXP6/Xr18e+ffsAiBUpJSUFHTt2xGozaZWHDgF//CFBxkTiogKkF4qixBvPPFP0Nc0oDC/uGVQGjRtLpmesx+HMny/ixl3cWUm7dsDy5WINiwNss+AQUSKA4QCuALANwAIiGs/M7v6aZwF8x8wfEVETAJMA1A9pww8/LEWSfJGZWTS+5Phx4I47gE8+8f6Zli2L7eJ55ZVXYujQoUhJSUGXLl3Qu3fvQn2nduzYgRdffBGLFi1ChQoV0LlzZ7Ro0eKf7R84cQIZn32G8bNno+djj2Hup5/i0wsuQNoddyA7OxtnnXUWnnrqKSxcuBBVqlTBlVdeiXHjxuE6t+ynhQsXYuzYscjOzkZubi5at26NNsbMxxe//gqcOSPuKUACjMuVU4GjxCeLF8vzOecAu3ZJsbmPPtKg+3CRlyduqK5dC79eooTUcollgZObCyxYIBNuu2jfXvbxokXAJZfYt50IwU4LTjsA65h5AzOfBjAWwLUeyzCAiq6/KwHYYeN4BF/BsyEG1ZYvXx4LFy7EiBEjUKNGDfTu3RtffPHFP+//9ddfuOyyy1C1alUkJSXhxhtvLPhwXh56XHIJiAipyck4u2pVpJ53HhJyc9G0aVNs2rQJCxYsQHp6OmrUqIESJUqgb9++mD17dqExzJkzB7169ULZsmVRsWJF9OzZs/iBjxsnF3PD52tYcVTgKPHGX38BH38MPPSQBN63ayeuERU34WPLFuDkSe9uKCPQOD8//OMKB8uWSRVjO+JvDNq1k+c4icOxMwanFoCtbv9vA+CZ+zYEwDQiegBAOQBdvK2IiO4CcBcA1C3OF16MpcVv36WZM/1/thgSExORnp6O9PR0pKam4ssvvzT7QZQqWRIAkJCQgFJJSfJ6yZJISEhAbm4ukozXrOTkSWDyZLmAJ7hp3YYNAVcHdEWJC/LygHvukdYAQ4fKa23bAqNGyQ01QfMxwoK3DCqDNm2ADz+Ua1NKSnjHFQ4yM+XZjhRxg7POkntgnAgcp8/aPgC+YObaALoD+JqIioyJmUcwc1tmblujRo3QtjhsmPjU3bHAx7569WqsdYvwz87ORr169f75Py0tDbNmzcKBAweQm5uLH3/8sfD2PUlIkEBjF+3atcOsWbOwb98+5OXlYcyYMYVcYABw6aWXYty4cThx4gSOHDmCCRMm+B/0b79JUS3DPWWQnCxBxlHWiFVRgubDD8U68PbbQEWXUblNGwm6V7EfPooTOEDsuqkyMwsEiJ3EUaCxnQJnO4A6bv/Xdr3mzh0AvgMAZs4AUBpAdRvHJNaKESPEYkMkzxZU7T169Cj69++PJk2aoHnz5li5ciWGDBnyz/u1atXCM888g3bt2qFDhw6oX78+KlWqJG+WKlVwUS34QKEsqpo1a+KVV15Bp06d0KJFC7Rp0wbXXlvY49e6dWv07t0bLVq0QLdu3ZCWluZ/0D//LNvt1Knw68nJYirduTPQ3aAo0cfOncCzzwJXXAG4u45j/YYaieTkANWre88gbdJErpWx+ntkZop7isje7bRrJ16M3bvt3U4kwMy2PCDurw0AGgAoCWAJgKYey0wGcJvr78aQGBzyt942bdqwJytXrizyWiRy5MgRZmY+c+YMX3PNNfzTTz8VvLl1K3NWFvPJk8wLFjBv22brWFauWMFcvTpznz5F35w8mRlgnjPH1jEoSkRw883MpUoxr1lT+PXTp+X1xx5zZlzxyKWXMnfo4Pv9tDTm9PTwjSdc7N8v19yXXrJ/W3PmyLbGj7d/W2ECQBZ70Qu2WXCYORfAQABTAayCZEutIKKhRGREvz4GYAARLQEwxiV2YtYvMmTIELRs2RLNmjVDgwYNCmVA4fhx6bdSqhRQqRKwb5+9LqJTp2Qbnu4poKDAlgYaRx/aRykwpk8Hxo4Fnn4aaNSo8HtJSbGfuRNpeEsRd6dNG8kAirVAYyMmxs4AY4PWraWQYhzE4dha6I+ZJ0FSv91fe87t75UAOtg5hkjCZ1VhZhE4lSvL/zVqiN//0KGC16zm+HERU1ddVfS9evXkBqkCJ7owajwZrQaMGk+AZgJ549Qp4P77pcjl00XKdAlt2sh+1UBj+zlwANizp3iB87//SYzgeeeFb2x2k5kpx1fbtvZvq2xZIDU1LuJwYuaMjWrDz5kzUgPBCDauVElmj3v32rI5zs+XQk9dukidD09KlgTq1JGLiBI9aB+lwHjtNam5Mny4FJHzhhForGLffozCpP4ETuvW8hxrVrXMTKkk7+16bAft2knNnVizhHkQEwKndOnS2L9/f/SKHOOmZAgcIgm0O3TI8qaXzIz9O3eidE6Od/eUQXKyXtSjDe2jZJ716yVz8sYbgSuv9L2cMaOOtRtqJOIvg8qgWTOZgMXS75GfL9aUcLinDNq3Bw4ejPkMwZjoRVW7dm1s27YNe22yeNjOwYMiZjZvLjCD5+ZKjMyiRZa7qUpv3ozaQ4cCq1b5Xig5WYoAKtGD9lEyBzMwcKBYSd9+2/+yRuZOVhZw883hGV+8kpMj4sVfmnTJkuJeiSWBs3at3APCKXCMgn/z58dmTSEXMSFwkpKS0KBBA6eHETzXXScntzGDMXjiCWDlSmDjRgkKs4revcUc6q+mUHKyuMiOHAmf2VQJjWHDpOWIu9VP+ygV5ccfgSlTRNy41ZryigYah4+cHAn0LlHMbalNG+C770So2p1SHQ6MAn/hFDiNGwPly0ugcb9+4dtumIkJF1XUs2hRgW/ZnQEDgK1bgalTrdvW+vVSEtyfewooaLqpcTjRQ9++QMeOBf9Xq2ZJjaeY4sgR6VfXsqVYccwQq5k7kUZxGVQGbdqIxWPjRtuHFBYyM6UeWTi7pCcmivs1xgONVeA4zf79ImJatSr6Xs+e0vxyxAjrtvfzz/LsnqLuDU0Vjz6Yxafeo4eUHOjXT8WNJ0OGADt2SAPN4iwFBhpobD9nzsj+Pf/84peNtUDjzEyJiQl3ll67dtKY2uI4z0hCBY7TGN2LvQmcpCTgttuAiRPlomwFP/8ss9fiyoEbAkctONFDTo7E4Fx9tcQpLFni9Igii6VLgXffFctoIO4ArWhsP+vXS9yhGStGaqpcG2Ph9zh2TI7LcLqnDNq3F2GZnR3+bYcJFThOs2iRPHsTOABw553SCPDzz0Pf1q5dQEZG8e4pQFLVq1bVWWs0MXmyPHfrJnEjS5ZoPzGD/HxpplmlCvDyy4F9tmnT2G4REAmYyaAyKFVKsqli4ffIypJj084Gm76Ig87iKnCcZvFiyXLx1nsFkGJWnTsDn34aegzA+PFywzMjcABNFY82Jk+WrJ+6dUXg/P03sN2z/Vuc8vnnIu5ff12EeyAYgcZZWfaMTSmogWPGRQUUxEVFu4A3YmCcEDi1awPnnqsCR7GRRYt8W28MBgwANm2S7t+h8PPPEjzcrJm55Y2u4krkc/QoMHu2WG8AuSED6qYCpNzCk08Cl1wC9O8f3Do00NhecnLkZuvZdNgXbdqIgPdWFiGayMyUSWx1e3tM+yTGO4urwHGSo0elBoK3DCp3evUSC88nnwS/rUOHgN9/l3WZTa1s2FAuILm5wW9XCQ8zZgCnTxcInObN5VkFDvDUUxIk/NFHwacVa6CxvZjNoDKIhUBjZrEqOhF/Y9C+vdyD/v7buTHYiAocJzFiJIqz4JQqJTPPceOCb3E/ebIElJl1TwFiwcnN1Uq40cDkyUC5cgVp4hUrAg0aqMCZOxf47DPg0UclliZYNNDYPpgDFzjNm0sWXDT/Hlu3SlykkwLHiMNZsMC5MdiIChwnKS7A2J077xSx8eWXwW3r558l5TyQk0lTxQPHiW7ezCJwOncWMWxgBBrHK2fOSGBxnTrAc88Vv7w/NNDYPvbskbo2gQic0qXlNzGuodGIEwX+PGnbVqyaMRqHowLHSRYvlmrCxVVTBaTy5CWXiJsq0MC6kyeBSZOkrk4gFZG12F9gGN28N2+W38jo5m23yFmzRmK0DPeUQYsWYn72bMAZ6xgis2RJYPly4P/+T6xboZCUJFYDFTjWY2RQmQ0wNmjTRn6PaA00zswUoWa4k52gYkW5t8RoHI4KHCdZvFisN2bjAgYMkEJuM2cGtp3ff5d4n0DcU4AIr1Kl1IJjFqe6ebunh7vTooUExS5fbu/2Iwl3kWkwYoQ1IlMDje0hkBRxd9q0kQDyrVutH1M4mD9fvkNSkrPjaNdOLDjRKhT9oALHKU6dkhtPcQHG7txwgzTeDDTY+OefpZ9U586BfS4hQeI4VOCYw6lu3pMny83Bs3hjPGZS2Sky27aVYH09H6wlJ0d6ptWuHdjnojnQ+PRpGbeT7imD9u2l72C0Z6R5QQWOQbhjJ1askJgaM/E3Bkb5/R9/lBYPZsjLk/o3V19dOD7DLA0bqovKLL66dtvZzfv4cWDWrKLWG0CO4woV4kvg2CkyNdDYHlavFvdUoK0KWrQQl3s0/h5LlsgkNxIEjntn8RhDBQ7gTOyEvxYN/hgwQNT/V1+ZW37ePFHngbqnDIxifzFovrScYcOKXqTt7uY9Y4ZcKL0JnISE+GvZYKfI1EBjewg0g8qgTBkpbBmNgcaREGBskJoqsUAxGGisAgdwJnZi0SKZXRuZSmZJTZWTwmyw8c8/S7DlVVcFN87kZOnAvG9fcJ+PJ66/Xn4To1hZiRL2d/OePFlE1KWXen+/RQvpdRMvAnXYsKIxDVaJTA00tp4TJyRAPthO2tEaaJyZKTGOgbrl7CApSdx9asGJUZyInVi8WJpeBtNBdsAAYNUqqfHhD2YROF26mK8Q6olmUpln8WLZ519+CYwcKS7IQAVsIPhKD3enRQspUBeD/nWv9O0rWSElSkjwfr161orMWGkRECmsXSv7MhSBs2dP9LUkmT8/Mqw3Bu3ayXF95oy163WibIYbpu6uRFSLiC4mokuNh90DCyvhjp3IyxO3QaDuKYPevcX6U1yw8ZIlMjsK1j0FaC2cQDDMzu3bS0B4mTLmXYnBsHatCE9/1rl4CzTOzQU2bpRJQH6+HP9WWtDatNFAYysJNkXcIBoDjffulePHif5TvmjfXqxpVmZcOlU2w41iBQ4RvQpgLoBnATzhejxuZuVEdBURrSaidUT0tI9lbiKilUS0goi+CWDs1jFsmJix3bEzdmLNGnGBBZJB5U65cnLR/u474MAB38uNGyez2J49g9sOIFlUgF7QzZCZKRaDmjXFYtarFzB2rMTI2IGv9HB3UlPlGIgXgbN4sbhUL7vMnvVroLG15OTI8dmoUXCfN6zgdvwedlkfDFdQpFlwAGvjcJwqm+GGGQvOdQDOZ+buzNzD9Sj2jklEiQCGA+gGoAmAPkTUxGOZRgAGAejAzE0BPBzg+K2hb18xY59zjvxfvbq9sRPBBhi7M2CAFPDzd9L9/DPQoQNw1lnBb6dMGWmCpy6q4snMLHzR6t9fBOiECfZsb8oUICWlwI3ojXLlpJlfvAicWbPk2S6BYwQaa2dxa8jJkUmB5wTTLGXLikvS6kBjO60PmZmS/WWI5UigQQO571kZh+NU2Qw3zAicDQCCqUTUDsA6Zt7AzKcBjAVwrccyAwAMZ+YDAMDMe4LYjjX07Qts2wZUrQp0725vYOjixXKRbNw4+HW0bi0nyIgR3uMBNmyQ4NJQ3FMGRiaV4psdO6TgmLvAufxyEYfBttfwx4kTUvDRn/XGIJ5aNsyaJe4OY7JiNSVLaqCxlQSbQeWOEWhsJXZaHzIz5ZwMVtTZAVFBwT+rqFPH++t2ls3wwIzAOQ4gm4g+JqL3jIeJz9UC4F5icpvrNXdSAKQQ0VwiyiQir8EERHQXEWURUdbevXtNbDpIEhOBK64Apk2zN4hw0SJxHYRawXLAAGDZMu8H5c8/y7MKnPDgzeycmAjcequ4kvZYrN1nzhQLnlmBs369uG5imbw8YPZs+6w3BhpobA35+VIDJ1SB07q1NK3cscOacQH2WR/y8uR6HUnuKYP27YGVK627Tlx3XdHX7C6b4YEZgTMewIsA5gFY6PawghIAGgFIB9AHwCdEVNlzIWYewcxtmbltjRo1LNq0D7p2lZNl6VJ71s9c0KIhVPr0EReEt2DjcePkxmbE0IRCw4Zy8ThxIvR1xSqZmSJYW7Ys/Hr//nJR+8bi8LLJk8V9aOZmbgQaL1tm7RgijSVLJGMsPd3e7WigsTVs3y5WESssOIC1Vhy7Ek9yckRARKLAaddO7k9WuF9PnQJ+/VVS4evWtSej0QTFChxm/hLAGBQIm29crxXHdgDuNqrartfc2QZgPDOfYeaNANZABI9zdO0qz1On2rP+LVskLsMKgVOxInDzzcCYMXJhN9i9W1LIvSnoYDAyqTZutGZ9sUhmpvympUsXfr1JEynxb7WbavJkoFOnotvzRrxkUhk92sJhwQHsdVM5nF4bFoLtQeVJy5ZyA7UyDueFF4r2CExKCt364J5pGWlYGWg8fLhMAEaOlPglOzIaTWAmiyodwFpIwPCHANaYTBNfAKARETUgopIAboZYg9wZB7HegIiqQ1xWzkaznnuuuI/sEjjGSRhsBpUnAwbILGjMmILXxo8XJW6FewrQVPHiyM0FFizwPSvr3x/IzrbOKrhunTzMuKcA8YVXrhw+gePUzXnWLAmoPvdce7fTtKnE4tglcCIgvTYsWCVwypeXdVj5e5w+Lfu+Rg0ROmXKiCU21LpWmZlAlSrBZ43ZSdWqcv6EGmi8fz/w4otiLDAMBg5hxkX1JoArmfkyZr4UQFcAbxf3IWbOBTAQwFQAqwB8x8wriGgoERlZWFMB7CeilQBmAHiCmU02WbKRrl2BP/8Ejh2zft2LFxeU0LeCdu0k6NHdTfXzz+Kaat7cmm0YJ7VmUnln2TJx3/kSODffLLM/q2riGOnhZqtTE4Uv0Nipm3N+PjBnjv3uKUDETYsW9gmcCEivDQs5OUClSqFleRpYGWh8+jTw3/+KlWX3bjm2tm8XV8tNN5nvA+gNI9PS0zoUKVgRaPzii+JReOMNa8YUAmYEThIzrzb+YeY1MJlVxcyTmDmFmZOZeZjrteeYebzrb2bmR5m5CTOnMvPYYL6E5XTtKge5YfK2ksWLJXvKqgh6IrHiLFwo1qHDh4HffxfrjVUnUbVqUlhQLTjeKa6uRfXq0ux01Cix9oTK5Mky0zrvPPOfadFChFh+fujb94dTN+elS8X1a7d7ysDOFgERkF4bFowMKiuuU61bS5zgrl2hr+uLL2RfDxlSMLYqVYAffhDBc+utwZ1Hhw9Lk+VIjL8xaN9exFywlaHXrBH31J13As2aWTu2IDAjcLKI6FMiSnc9PgEQ20UgOnYUk6QdbqpFi6yJv3Hn1lslFuOTT+Tmd/q0de4pQE7yaMikcso1kpkps9D69X0v07+/XBynTQttWydOSINNs+4pgxYtxCJp92/o1M3Z7vo3ntgZaBwB6bVhwYoUcQOr4qJOn5Y4mwsvLOpeadMGePddqT/10kuBr3vBAhHEkSxwQo3DeeopuRcNHWrdmELAjMC5F8BKAA+6Hitdr8UupUuLqdtqgbNnj8wyrBY4lStLIOvHH4s7JCHB+oDgSBc4TsYtZGbKzMffTLR7d7GEheqmmj3bfHq4O+EKNA532xODWbMk28+XOLAaOwONb7216GthTq+1ncOH5VpolcBp1cqaQOPPPitqvXHn7ruBW24Bnn9eLOWBYFh6DRERibRsKe70YATOrFmSvfv008DZZ1s9sqAwk0V1ipnfYubrXY+3mdmm2vMRRNeuYm7btMm6dRoVjK0KMDYYPbpgdgCI+fSee6y9uTdsKKLJbhdHsDjlGvn7b6nlUdysrGRJSesfNw44eDD47U2eXCDAA6FpU6nLY7fAGTas6I3B7ptzfr5cXMNlvQHsDTTevFksyMZNolq1sKfX2s6aNfJslcCpUEGqeofye5w6JcfpRRcBV17pfRkimUief74InUBq72RmSnhC5crBj9FuSpeWyVCggcb5+cBjj8kE49FH7RlbEPgUOET0net5GREt9XyEb4gOYUe6uDG78KyVEiqDBxftd2T1zT05Wcy3kdq11ynXiDHTMWN27t9ffqfvvgt+e5Mni7gpUyawz5UuLRdluwVO+/YitI3x1ahh/815xQoRmuEUOHZVND54EPjxR+C22+Rcq1xZyj3EkrgBrMugcifUQOPPPpNq9t5SxN0pX17icY4elcbHZjpwMxdYeiOd9u2lFk5envnPjB4t+/6llwK/NtmIPwvOQ67nawD08PKIbc4/X8zqVgqcxYslu8lqBR+Om3ukp4o75RqZP18uhmlpxS/bpo3UxQm2Js6GDTLzDdQ9ZRCOTCojxuivv6ROU8+e9t+cwx1/Y2BHReNvvhEX5J13isUtPR344w/r1h8p5OQAJUqEnnbtTuvWIlCCqRp+6pTcnDt0ALp0KX75Jk0k5vHPP81NJDdulC7ikRx/Y9CunRQjNERocRw/DjzzjIRJ3HKLvWMLEJ8Ch5l3uv68j5k3uz8A3Bee4TkIkVhxfvvNnEI3w6JF1rungPDc3I2GjpGaKv7QQ0VfC0fcQmamZAtUqFD8skRixZk3T+rYBIqZ7uH+aNGioNCkXUyfLhVLmzaVzLFffrEmc8wfM2fKNv0FedtB27ZicbHynPj0U7HwGteJTp3k5milqzwSyMkRcRNquxp3QomL+vRTEUe+Ym+8ccstEgrw+utSe8wfRoG/aBE4gPk4nLffln335psS/xlBmBnNFV5eC/IKG2V07SpK1jg4Q8HIuLA6wBiQm7hn2rnVN/e6dWXGFakWnOxsGV8tt3Zn77xjr/UgP18sOIGYnfv2lYtAMMHGkyfLTSHYImFGoLFdbUjOnBFrw5VXyk2iVy9g3z6pqm0XzOGPvzEwbqhWdRZfvFged9xR8FrnzvI8Y4Y124gUcnLESm4lxrU10EDjkyfFetOxozTIDYS33xYx2r+/f6E7f7601WnaNLD1O0FKitQnMhOHs2sX8PLLcq5faqb+b3jxF4NzLxEtA3C+R/zNRgCxH4MDyMGemGiNm8pwDdghcPr2lTiHevXs6/lRooSsNxIFzurVUmPmoYdkJvHnn/J6lSr2bnftWrGGBDIrq1VLTOBffRVYwPbJkyIezBb384bdmVR//SXZMUaAZrduQKlSBY1f7WDlShFR4Sjw54nVgcYjR8r+cj9vmzaVOKZYclPl5sq5Y2X8DSA35UaNAv89Pv1UgoWLi73xRunSEo8DADfeKOepNzIzxY1dokRg63eChAQZqxkLznPPiXvv1VftH1cQ+LPgfAOJtRmPwrE3bZjZSx5jDFK5sszOrRA4Vrdo8KRvXzFj29nzo2HDyHRRvfCCBLY9+aT83769/HaTJtm73WDNzv37S6bM7NnmPzN7ttTACdY9BQDnnCM3S7sEzrRpcoMwrA7lywNXXCECx67O207F3wDWBhqfOCGBmtdfX1iYE4mb6o8/Yqd7+aZNkrBgtcABAg80PnlSLBCXXir7ORgaNJC4ukWLgEce8b6NxYujwz1l0L69WHr9NVhetkxE+f33R2brCfiPwTnEzJuYuY8r7uYEAAZQnohirOKUH7p2lRNm377Q1rN4sdxgzjnHmnE5QSTWwlmxAhg7FnjggYKS7yVKyO82ebK9ae2ZmRJI27hxYJ+77jqJ2QnETTV5sszug70IA/a3bJg+XWZ+VasWvHb99RL3Y2UjRHdmzZLU1AYN7Fl/cVgVaPzzzxLPc+edRd/r3FksDGvXhraNSGG1qzC+HQKndWs53sxer0eMkH0bSOyNN3r2lAnW//5XtDzH4sXivo2GDCqDdu0ki8rfefv442I1e+658I0rQMw02+xBRGsBbAQwC8AmAJNtHlfk0LWrXLymTw9tPYsX2+OeCifJyZKOG0odF6sZMkQsBY8/Xvj17t3FP2xn1tD8+XIhCDSwrmxZMWd//735fmeTJ4uVItQWHy1aAMuXWx/4e/Cg7A/P+iE9esj+scNNxSwBxpdd5lxvnzZtrAk0HjlSRJo3V5thEYsVN5WRnWN1DA5QEBdlRlCfOCHWm8suC23iYDBsGHDJJVJgdOXKgtcjuYO4L4oLNJ4yRSy2//lP4QlNhGHmyvxfABcCWMPMDQBcDsCCqNsooW1b+QFDcVOdOCEHvF3uqXARaZlUS5aI//vhh6UYmjtGHSO73FTHjokJN1izc//+UkfDzI1/40aZ9YbinjJo0UJ85kahNav44w+xlnkKnOrVxfxvh8BZvVpSgp1wTxlYUdF4/XrZf//+t3exfN55ErsVK4HGOTlibbXjxmhcY838HiNGyCTohRes2XaJEmJNLl8euOEGOb8BETj16gE1a1qznXBwzjmSWOIt0Dg3VyaUycninopgzAicM64O3wlElMDMMwC0tXlckUNiogSFTpsWvBl6+XIx98WCBQeIHDfV88+LidSb3/vss0Wc2iVwFi6U3zTYWVnHjgW+++KYMkWerRI4gPWWrenT5cLuTfD16iUC32pRZcTfOBFgbNCsWeiBxp9/LsLmttu8v2/ENc2YEbmVxAPByh5UnlSuLNep4n6PEyeAV14Ry42VAvncc6WW0erVYslhFpEQTfE3Bu3be7fgjBwpoQGvvSbHfgRjRuAcJKLyAGYDGE1E7wIwaVePEbp2BXbulKCqYDBaNES7wDEsOJEgcBYulBorjz3mO1uqe3eZPf39t/XbD9XsnJAA9Osn/Wy2bfO/7OTJIoZSUoLbljsXXCC1R6wWONOmyc3CW10To/Gr1VacmTPlhmJlsbhAMQKNg00Vz8uT7tVduwK1a/terlMnKRS3YkVw24kk7EgRd8dMoPH//ifWmyFDrN/+5ZeLVWjMGLk2bd4s50e4mv9aRbt2BQUKDQ4flpibSy6xtqGzTZgRONcCOA7gEQBTAKxHPFQydscwuwfrplq0SCwNTgVCWkWFCpKFEwkuqueeExO3twJ/Bt27y4w31A7e3sjMlBtrjRrBr+Nf/5IZ3qhRvpc5dUrcF926WRNnUrKkVGG1UuCsXy/HhK/+PXXqiDXtp5+s26Z7/Run4m8MQgk0njpVWjK4177xRqzUw9m3Tx52WXAAcVNt2gTs3+/9/ePHJa25c2f7arfUry+TmEOH5P8DB8LX/NcqvMXhvPqquIXffNP5884EZgTO3QBqMnMuM3/JzO+5XFbxQ+3aUo8iWIFjBBhHwQFRLJGQSZWRIa6nJ56QLCZftG0rsTlWu6mMvjKhmp2Tk8VV9eWXvm+Oc+ZIvE8o9W88sTqTyhCQvgQOILO9v/6yrpfZ2rViVXXSPWUQSqDxyJEiknsUM2esV08sqNEeaGxnBpWBERdlWM49+d//gN27rYu98cazzxZ1J4aj+a+VtGkjIs0QOFu2AG+9JRWczbSmiQDMCJwKAKYR0RwiGkhEkdEHPdx07VpwswmE3FwJRo32AGODSBA4zz8vN4WBA/0vl5gowmDKFGtjF7Ztk5urFX71/v3FZL9ggff3J08Wq4sxg7eCFi1k/O6m51CYPl0CEv3VwjDM2ePGWbNNJ+vfeBJsoPGePVLi/1//MhfL0KmTuOUCaYIYadjRZNMTf4HGx46JFaJLF5lc2IVTzX+tpFw5iTEzAo2feUaeX37ZuTEFSLECh5lfYOamAO4HUBPALCL6zfaRRRpdu0pxKuPCapacHCn0FO3xNwYNGwJbt8q+cII5c+SG+tRTEtRaHN27y43cyq7PVvaVufFGqYbqqybO5MliRi9XLvRtGVgZaJybK3FERnsGXzRuLDc1q+JwZs2SQHIr4pJCJdhA46++kv1XnHvKoHNncXlkZwc8xIhh9Wqp51Svnn3bqFpVwgG8/R4ffSTC0o7YG3ecav5rNUag8YIF4l575JGo+g6BFPDYA2AXgP0AzrJnOBHMJZfIjShQN1WsBBgbJCeLNWTzZme2/9xzksJ4773mljduvFa6qTIz5SLdvHno66pUSQr/jRkj8TbubN4MrFplTfaUO8a4rRA4nu0Z/NGrl1ggfMVGmMWof5OeHhlu35IlgdTUwAQOs7inLrrIfKFIo1ZLNLupcnJElCYm2rsdb4HGx45J5s8VV0jXcDsJR3/AcJCfL/FDRr0vI9EkSjBT6O8+IpoJ4HcA1QAMYGYLruxRRpkyYg4PRuCUKWNv1kA4cTJV/I8/5MY2aJD5gnfVq8ssxGqB06aNdSmS/ftLptevvxZ+PdTu4b6oUUNqcljRdNNoz2CmSWGvXuJemTgxtG1u2CCxPJHgnjJo21ZuqGYDjTMy5GZv1noDyG92wQXRL3DsdE8ZtG4tx8mBAwWvffihWHPtjL0xCEd/QLsZPbpwUHR+viR1RFGgtBkLTh0ADzNzU2Yewswri/2ECyK6iohWE9E6Inraz3L/R0RMRJFdX6drVzGxBmK9WLRIZszR0GTNDE6lijOL9aZWLclGCITu3cXEakXMyenT8ptaWdeiSxe5eXm6qaZMkQujHTcEqwKNp08vKIZZHG3bSsB+qG6qSKh/40mggcYjR4rb8aabAttO587ipj1zJuAhOs6pU7J/wiFwPAONjx4V603XrmI1Cwfh6A9oJ4MHF20eGmWB0v66iRvpKa8D2EJEVd0fxa2YiBIBDAfQDUATAH2IqImX5SoAeAiAid7sDmNUxzVrxWEWf3msuKcAuRGXKRP+VPFp04C5c+XkKl06sM926ya/hRVNU5culZPeSoFTooRc/H79tUCEnT4tsS1WpYd70qKFFN8LJZbKV3sGXxCJO27q1MCD9d2ZOVMq4YbjRmmWQAKNjxwBvv0WuPlmKb0QCJ07y77zFZQeyaxfLxa8cFizPX+P4cMlPd3u2JtYIgYCpYvrJg4ACwFkuZ4Xuv1fHO0ArGPmDcx8GsBYSE0dT14E8CoAH33mI4jGjWUGavZGuXGjBAXGSgYVIDephg3Da8ExrDd16wZm0jdo3VpuiFa4qawMMHanf38JOB0zRv7/80+ZdVrtnjJo0UKsAEZWSzDMmCE3LLMCB5DmmydPhiY2Z82SwOtIiL8xCCTQ+LvvRKQEcywbbrlodFOFI4PKoFo1sX4uXCjn0euvS0ZlNFYUdooYCJT21038GtdzA2Zu6Ho2HmYijWoB2Or2/zbXa/9ARK0B1GFmj+CDCIVIrDi//26uWaHR8C2WLDhA+AXOr79KMOt//hNc3EtCggiFqVNDT7HNzBQrlr+qs8HQrJkIMaN1w+TJUhXYyvRwd6zIpPLXnsEXl1wiN59gi/5t2iQzyEhyTwGBBRp/+qlMloK52VavLr9dNBb8s7PJpjdat5bf44MPJLA9HLE3sUQMBEqbCTL+3cxrgUJECQDeAvCYiWXvIqIsIsraa1XtjmDp2lWsMt6akHmyeLFkCzRrZv+4wklysriogu3NFQiG9aZhQ7FyBEv37hLI66s7rlmMAn92WA/69xdRvHy5CJxLLjGXCh8MKSmSCRaKwJk2TYRGIKKzRAkpajdxYnDusZkz5TmSAowNzFQ0XrlSjqE77gj+GOrcWdy1nvERkU5OjkwM7DqmPUlMBNatk6SE0qWlOKRinhgIlPYXg1PaFWtTnYiquMXf1IeHJcYH2yEByga1Xa8ZVADQDMBMItoE6Vg+3lugMTOPYOa2zNy2Riil8a2gSxexCJgxsS9eLBWQA40ZiXSSkyXYbPdu+7c1bpzsx+ee897nyCxXXCG/Wyhuqr17xXJll5m7Tx+5kLRvLz2HFi2yL2OhRAkR3sEKnPXr5RGIe8qgVy+ZJBhiJRBmzRILUJMi4XzO06aNZO1s3Oh7mZEjZd/36xf8djp1koBdw10aLaxeHb64qdGjgQkTCv4/eTL6WiVEAlEeKO3PgnM3JN7mAhSOv/kFwAcm1r0AQCMiakBEJQHcDGC88SYzH2Lm6sxcn5nrA8gE0JOZg+xaFyaqVJGaAGYEzqJFseeeAsKXKp6fL1WLU1JCP7GqVAEuvjg0gWNYf+wSONOmiQg7flz+P3jQ3ouykUkVjCVu+nR5DkbgXHGFZBAFk001c6ZYbxICKeEVJtq65ma+Gm+ePi2Zcj17SkxYsFx6qXz/aIrDYQ5fijggyQiedaWiLANICR1/MTjvMnMDAI97xOC0YOZiBQ4z5wIYCGAqgFUAvmPmFUQ0lIh6WvYNnOCqqySLYd8+38vs3CkWjlgUOEaquN2ZVD/8IB3cn3/emjT77t1FdO7aFdznMzPF7G1kaFjN4MFFY4TsvCi3aCFWqWD2h9GeIZhKwmXKSEzUuHGBtdDYskVmkZHongKKDzSeMEGuGXfeGdp2KlUSMRVNAmfXLikIGS6BEwMZQEromJkG5RNRZeMfl7vqPjMrZ+ZJzJzCzMnMPMz12nPMPN7LsukRb70x6NpVZiS/+elYYdRfiKUMKoP69cWVYqcFJy9PUjqbNAF697ZmnUZG0pQpwX0+M1NqGlnZNsGdcF+Ugw00NtozXHFF8HEkvXrJTS8QN0sk9Z/yRnGBxiNHSgxKMFYvTzp1kjjAUNLtw0k4M6iAmMgAUkLHjMAZwMwHjX+Y+QCAAbaNKBpISxOXhz83lZFBZdxEYolSpYA6dewVOGPHSpuCIUOsK+veooVkQAXjpsrLkxtK+/bWjMUb4b4oB9uyYcECiaEJ5UZ99dUSUxWIm2rmTDnvUlOD367d+Ao03rpVhPVtt1lzPHfuLELzzz9DX1c4CHcGVQxkACmhY0bgJBIVTNNcBfwsqlEfpSQmSrDxtGm+4xcWL5buyhUren8/2mnY0D4XVW6upHQ2bw783/9Zt14icVNNm2Yuzd+dnBwp0GZnHY1wX5SrVBHxFKjACaQ9gy8qVZKb9M8/m48BMurfRGL8jYGvQOMvvpDvefvt1mynQwcRiNHipsrJEctnLTP5KRYQAxlASuiYuVJMAfAtEV1ORJcDGANgsr3DigK6dgV27JCUXm/EaoCxQXKyfRacUaMkpfOFF6y/mXXvLtaHjIzAPmeUBbBT4DhxUQ6mZYPRnqFatdC2ff31cgz5Oofc2bZNlo20+jeeeKtonJ8PfPaZCDqrmhWWKyfWxGiph2MEGIezOGOUZwApoWPm7vEUgD8A3ON6LANQxs5BRQX+2jYcOCAnVKwLnN27pUqoVYweLTf122+X2akd8QVdukjAcqBuqsxMsXg0amT9mNwJ90W5RQtJ3zVbU+XQIdkXV1wR+ravvVZueGaK/kV6/I2Bt0DjGTPktwymcrE/OneW7Rw8aO167SCcGVSK4qJYgcPM+ZA+UZsg7Rc6Q7Ki4pvatSUA1pvAyc6W51gMMDYwZqL+an4EwujRkhJtBNSeOWNPinTFikDHjsEJnPbtI9s9EgwtWkh80YoV5pYPpj2DL84+W1L3zcThzJolbi0jbihSKVVKYoTcU8VHjhRxfP311m6rc2cRwrNnW7teqzl+XM5rFThKmPFX6C+FiJ4nohwA7wPYAgDM3MlMmnhc0LWrdPY16pYYxGqLBnesroUzeHDR/WhXinT37tI0c9s2c8sfOSJulFjsYxNoJtX06eIesaojc69esu3ihPLMmRJ/Y1XAuZ24BxofOCAWqr59rS/4eeGFss5Id1OtWSPPKnCUMONvOpoDsdZcw8wdmfl9ACE28okxunaVYlKG+dxg8WIJpnO66rKdWC1wwpkiHWi6+IIFcrOyM4PKKZKTRbCYFTjBtGfwR69e8uzPirNzp8RkRbp7ysA90Hj0aLlGWO2eAsRa1KFD5AcahztFXFFc+BM41wPYCWAGEX3iCjCOoPa9EcCll8oMytNNtXhxbLunADG5V65sXSZVOFOkmzaVNHezbiojwLhdO+vH4jQJCeJSMSNwNmyQ3j5WuKcMGjYUK5I/gRMt8TcG7oHGn34q14KWLe3ZVufOYo10ukefP3JyJNbqvPOcHokSZ/irZDyOmW+GtGqYAeBhAGcR0UdEZOEVLoopU0ZEjrvAOX5cTuhYdk8ZWJlJdf/9RV+zK0XaSBefPt1cw8fMTKnfUbWq9WOJBMy2bAilPYM/evWS5pG+epvNnCmxU3aJBKtp1kyC5EeMkP1qh/XGoFMnefa0IkcSOTlAgwax15NPiXjMBBkfY+ZvmLkHpGHmYkhmlQKImyonp8CVsnSpBP6pwAmMTZskvqJ27fCkSHfrJhlgc+f6X465oIN4rNKihWTibN3qf7np08XyZXWxtl69ZD//8ov392fNksBwK9p1hIMffpBno9J5KE1ii6NtW+nOHcluKs2gUhwioJQQZj7g6uwdQoWvGMMzXTyWWzR40rChCJNAi+Z5cugQ8OWXwK23yk02HCnSl18uN57i3FSbNgF79sS+wAH8u6msaM/gi9RUEcve3FS7d8sNMlrcU0Y24JkzBa89/LB9DVOTksSKHKmBxvn54e0irihuxFjOqwM0aSIBxYbAWbRIXBl16jg7rnCQnCw3PrPZSL744gupefPAA5YMyxTly8tNsziBY/RKisUAYwOj9YE/gZOVJVYeq91TgAimXr1EQB06VPg9w/US6QX+DMKZDWjQqZOIwB07rFvn6NHScy4hQZ6DFWhbtkiNJRU4igOowAkVIrHi/Pab3OwXLxb3VDgrdjqFFZlU+fnABx9I2rFdXbp90b07sHIlsHmz72Xmz5dYq0jufxQqFSrIb+lP4FjRnsEfvXqJ1ePXXwu/PmuWiNFosYg60cW6c2d5tsqKY1ihNm8W1+HmzcHXpFq9Wp5V4CgOoALHCrp2lZnn3LnAsmXRczEOFaPYXygCZ8oUycwJp/XGwEgXn+yn80hmpjRXjZb4j2AprmXD9OkiQKtXt2f7F14InHNOUTdVtMXfONHFukULyWq0SuBYaYXSFHHFQVTgWEGXLmLKfecdycqJhwBjQAKCk5JCSxV//325sVnZVNMs558v2R2+3FSnTolFLpbjbwxatBCh6a09xuHD0rvLivYMvkhIkNYNkycDJ07Ia3v3SoXlaIm/AZzpYp2YKPvIqkBjX9amzZslHi0QcnLEZW+XMFYUP6jAsYKqVeVGOW6c/P/44/YFFUYSiYnyvYO14KxZIxace+6xrnBcIBjp4r//7r0X0+LFIljjReAwiwXSEyvbM/jj+utFYBnZR0YLgmgSOE51se7cWQoLbtoU+rrOPtv3e7VqiRD9+WdzJRZycmQiEQ8ue4uxKgwqnlGBYwWjRxee9ezYYU8fpUikYcPgBc7w4WIBuvtua8cUCN26ifl9zpyi78VDgLGBv0yqadOsbc/gi/R06TdluKlmzhTrR9u29m7XapzoYm1VHM62bSL2PQVJ2bLAq68Cjz4qlb2vvx4491zgwQcL2lJ4Q1PEg8LKMKh4RgWOFQweXDgtFLA/cyJSMGrhFFckzpMjR4DPPwduvFFcVE7RqZOUvPfmpsrMlGy4c88N/7jCTb16Ii68CZzp00V8lCpl7xhKlgSuuQYYP14C9mfNklYEdtaRiRWaNAHOOis0N9WRI7L/8/KAl18uaoV68kkROVu2yPnSpYu83qaNCOS33ioo1jh6tJw7u3aJYNU7c0A4kYwXi6jAsQInMiciheRkidH4++/APvfVV3JBffBBe8ZllrJlReR4Ezjz58eHewqQG1nz5kUFzsaN0gfKzvgbd3r1AvbvF3fvsmXR5Z5yEiI5jv/4I/DJBiCC8uabpans998DTz3l2wpVooRYPseOlT5hH30k59Fjj4kLq1Ur4N//LigfcfCgmh8CJJ5vKVaiAscKnMiciBSCyaRiltTwtLTIcP906ybxQO7fYdcuubDHi8ABZBZuVOI2sKs9gy+uukpuoH36yP/vv683RrN07izu8bVrA//sI4+IyP/gg4LipWaoUkVi6DIzpeTC44+LMPWMz1HzQ0D4unWULKkiJxBU4FiBE5kTkYJRCyeQTKrffhPfvBOp4d7o3l2e3dPFjQab8SZwjh4tHKg6fbpky4UrjmLcOBHARnXs3bt19m8Woy9VoG6q994TYfPooyJWgqVxY+CVVwoLZHf0zmyaYcMkh8MdIw+jZUv/vWmVAmwVOER0FRGtJqJ1RPS0l/cfJaKVRLSUiH4nonp2jsc2nMqciASCseC8/77EC9x0kz1jCpTzzgMaNSrspsrMlNiPeEn5B4oGGufliRi98srwZcEMHizbdUdn/+Y47zwRo4EInIkTxXpz3XXAa69ZM454tmhbxEUXyWlQoULBLeWzz8SDmJwsMd733VdQUUHxjm0Ch4gSAQwH0A1AEwB9iKiJx2KLAbRl5uYAfgBg0RnmAE5kTkQCZcsCNWuaFzgbNshF9a677A9aDYTu3SUDxYjsy8yUG36ZMs6OK5w0bSo5qYbAMdozhCv+BtDgg1AgEjfVzJm+rSjuLF4scTetWgGjRhU1GQRLPFu0LeLNN8Vik5NT+JZy3nlST/bxxyX0qV07KRWleMdOC047AOuYeQMznwYwFsC17gsw8wxmNmLFMyHdypVoo2FD8y6qDz+UC2kopnA76NZN0mNnzpSp04IF8eWeAuQm1KhRgcAx2jN06RK+MejsPzQ6dSookOiPbdskY6pqVWDCBCkDYBXxbNG2gN27xVrzr395T+AsWRJ4/XUpIbZnj1RR+Pjj4GLLYx07BU4tAFvd/t/mes0XdwDwWjOfiO4ioiwiytq7d6+FQ1QswUgVL45jx4CRI8W+WsvfoeAAl10m1prJk+XmcOxY/AkcoHDLhunTpe1IOKvQ6uw/NMzE4Rw9CvToIVmMEyeKBdZq4tWibQHvvitF1J94wv9yXbvKqXrJJTJfvPFG4MCB8IwxWoiIIGMiuhVAWwCve3ufmUcwc1tmblujRo3wDk4pnuRkYPt279WA3Rk9WlwekRJc7E7p0tJIctIkaUsAxK/A2bhRZvgZGeHLnjLQ2X9o1Ksn56Ovgn95eZKhtnQp8O23UhpAiRgOHZL6p//3f0BKSvHLn3OOWHJeew345Rc5ff/80/5xRgt2CpztAOq4/V/b9VohiKgLgMEAejLzKRvHo9hFw4ZiH9240fcyzBJc3LKlFG+LRLp1E1fbV1+J1cIIoI4njEDj996TTKZwxt8Y6Ow/NDp1KnC1evLoo2K1+eCDgmazSsTw8cdSVuzpIik5vklIEGvPvHnivrrsMuDFF73//PGGnQJnAYBGRNSAiEoCuBnAePcFiKgVgI8h4ibALm5KxGAmVXzWLEkBeOCByO1LY9TumDdPXFTffOPseJzAEDgffyyuoYsvdnY8SuB07iymgOzswq+//74I10ceAe6915GhKb45eRJ4+22ZU7RpE/jn09Kka0afPsBzz4lB+v3347uflW0Ch5lzAQwEMBXAKgDfMfMKIhpKRD1di70OoDyA74kom4jG+1idEskYAsdfHM577wHVqhUUcIs0Ro8unIp84kR81l+ZNUuuhocPiwXlhx+cHpESKN7icH79FXj4YaBnT4lQjQLirdnkl19KfdFArDeeVKwIfP21rCsjQwrFx3M/K+IoC71u27YtZ2VlOT0MxR1mKdhwxx0SIefJ5s3i7nniCSkEFonUry/j9KRePWs6NEcDRoc/9yY4ZctqDEw00qSJHLuTJ4slp2NH6eo9e7a1GVM2EW+HYm6u/DzVqkmNUSuM3LVqSWFrT2LxkkZEC5m5SFfeiAgyVqIcIrHi+HJRffSRPEeyWVzrr2iHv1iidm1g6lQxf7RtK8EZVqeD20i8HYo//iiXz6efts6Dv3On99fj6ZKmAkexBl+p4idOAJ98Alx7rUwdIhWtv6IiL1YYPVosNczyyMuT89BXZlUEEk+HIrMYts8/XwpKW4Ve0lTgKFZhFPvzrKA6Zox0Go/E1HB3tP6KXhFjhcGDpZCKOydPRpX5o3Jl76/H4qE4dap4EZ96SgxuVuHtklaiRHxd0lTgKNaQnCwXVXe7qJEa3qwZkJ7u2NBMofVXVOTFClFu/li8WGLcPTtHlCoVm4fiK69IvIzVlxrPS1q5chLrE4si0RcqcBRr8JZJNXeuTE0iOTXcnXivv6IiLzaIYkvc0aNA795SwG748IJDsUQJqcXpRFkmO8nIkMTFxx4r6BZuJe6XtF275DLdr59UEYgHVOAo1uCtq/j774utWW+Q0UO8i7xYIIotcQ88AKxbJ2FEd99dcCguXCgG4ltvNddHNFp49VVpBzZggP3bKl9e9uu2bcD999u/vUhABY5iDfXqiU3ZyKTavl1SA+64I2oyNxQlJohSS9w33wBffAE8+6xU43WneXMppTV9OvDyy44Mz3JWrJD2Cg88IOIjHLRvDzz/vAideKhjqnVwFOto2FD6N33zDfCf/8iMcd26+Gx5oCiKadavB1q1kkLaM2aIS8oTZtFo334ry1x6afjHaSX9+0sdzc2bw9vPNjdXBOTy5dKSLJKTW82idXAU+2nYUK5Up07JjPGaa1TcKIril9OnpcB5YqJYFryJG0CMUR9/LHEkffoAe/eGd5xWsnmzzAMHDAivuAFk/44aJYKxX7/Y7lmlAkcBYFFZdKPY33ffAXv2RH5quKIojvPss8CCBcDIkcXHQVeoIJeX/fuBf/0reuNx3npLnh991JntN2gg/VbnzJFO5LGKChzln7LoIfcsSU4G9u2TvMcLLgC6dLFlvIqixAZTp0prrHvuAa6/3txnWrYE3nkHmDIlOm/O+/ZJ7dO+fZ1NbOvXTzLWnnsOiNWoDxU4inVl0bdvl+eVK4Hdu+Mjik1RlKDYvVusME2bFlg0zHL33cBNN4n1588/7RmfXbz3nhSWfuopZ8dBJF10atYUsXXsmLPjsQMVOHEOs0V1wUaPlrgbgwMH4q91rRITxFsXayfIzxdxc/iwBA2XKRPY54nEClK/vsTj7N9vyzAt58gRcQ1ddx3QuLHTowGqVAG++gpYu9Y5d5mdqMCJY7ZsAXr0EJHjDSJg4kSTKxs8WMrBuxPL3fGUmMQyd63il7feAqZNE1dT06bBraNixYJwv/79oyMe55NPZO7ntPXGnfR04MknZX76yy9Oj8ZaVOBECOGcNeblAe++CzRpIumWffsWrQtWurQ0JO7RQ6psnj5dzEqjvDy8ogDx18XaCRYsAAYNAv7v/0Q8hkLr1iKWfv0VePNNa8ZnF6dOyRjT06WaRiQxdKjsyzvu8N2FPCph5qh6tGnThmONUaOYy5Y1Wv/Ko2xZed1qFi9mbttWttGtG/OmTQVjqFePmUieR41iPnGC+f77Zdl27Zg3bPCz4nr1Cn8B41GvnvVfQlEsJi+P+c8/vR/CgJwXSugcOsTcsCFz3brMf/9tzTrz85lvuIE5MZF53jxr1mkHn34qx9KUKU6PxDurVjGXKcPctaucD9EEgCz2ohccFyyBPmJR4NSu7f2iWreudds4doz5ySflInDWWcxjx8qFwQw//MBcqZI8fvzRx0LhVGmKT7wJVcU7Z84w//478333Mdes6VvcAMwlSjB/9hnz6dNOjzp6yc9nvuUW5oQEEZNWcvAgc4MGcs3cv9/adVtBbi5zSgpzq1bmr7tO8OGHcry/+67TIwkMFTgRRn6+zDb+9S//F9bbb2eeMEGsKcEydaqc/ADznXcGdwFYv545LU3WMXAg88mTXhbSu6ujqMYsnlOnmCdPZr7jDubq1WUflSnDfP31zKNHM3/ySdF9WLJkgYGybl3mDz5gPn7c6W8SfXzxhezDoUPtWf+CBcxJScw9ekSeiPjhB/nu337r9Ej8k5/PfM01zKVKMS9d6vRozKMCpxjCdW8+dIh5+HDm1FTZ+xUqMJcv713clCsnVhNAlrnpJrG8HD5sblt79jDfeqt8PiWFeebM0MZ+6hTzI4/I+lq3Zl67NrT1xSJOaLz8fOaVK5mrVPF+HNWubf8YIgnP3+Czz5jHjWPu16/gfKpQgblPH7nxHD3q//OjRsk+njSJ+eKL5fNnn838+uvMR46E/etFJatXy/UsPV2sGXbx7rvy+7z5Zujrsupczs+XsIDkZHu/u1Xs3i1W/tTU0CbWzOG7HqrA8UM4Zr5ZWcwDBshJDoipcsQIuUD62/6pU+KzvesuOegAUdfXXCMX7r17C76DcSDVrct8993M1arJjOY//wn9QHXnl1/kZlqhggiuSMFpA9KoUWINsNuCkp8vFrVPPpGb9DnneBc27o9LL2V+7TXxs9s9u3Xyd/B2LhmPKlWYb7stNItofj7zjBnMXbrIOqtWZX7hBeviSQycPpat5ORJud5Vrcq8bZu928rPZ+7VS1yKmZnBr8eKe4L7bwgw//vfwY8n3Pz6q4z5kUeCX0c4LcoqcPzgKz72rLOYN282F3Dl7YJ09KgElhlBvWXKyEH+119FbzJmLmi5ucyzZ8tBZ4w5MZG5SRMRMp7jb9SIefnyUPeOdzZvLpjN3nWXNSb7UC7q4XbP5OdLgPa4cXKD69VLfgtvx1GZMsyPPy6CdsYMucj7Ehm+9sHWrcxffSU36Lp1C9Z9zjkS1/Dpp8znnut9+5UqMbdoUfB/cjLzQw8x//abCGgrsfrGUNxxcOyYTB6++IL5iSeYS5f2fS5bHT+Tmcncsyf/YxF6+mmZ/YYqTiLB1WjFdzA+X6GCfIdffrFhoF74+2/m+vVlgle7dmDf4cwZ5h07fMdk1azJvH178de7SPgNQ2XgwIJzx8w+PH1arm0LF4pAqlbN+z60I+/El8CxtZs4EV0F4F0AiQA+ZeZXPN4vBeArAG0A7AfQm5k3+VunHd3EExJk1/uidGmgUSMgJaXoo1o1Kdh7112F00tLlJDHyZNS5+Gee4BbbwUqV7ZmzMzA4sXATz8Br74qHWI9qVtX6njYxZkzUub7lVeA1FQp/T18uGSG160rzcT79jW3LqP+iPs+LFtWajP06CGFvP7+W569/f3DD0XL8ADSu+Y//wFq1QLOPbfguVw572MYPLjo+E+fluLM2dnyWLJEng8elM8RAeedJ8WyfFGyZOFU+7Jl5ZgyHikpwMaNUrb+xImC5UqUkGNs9275v2pVoFMneXTuLB0xiIrfh337Alu3Sl2jiROB33+XtNWKFYGuXaUvavfuUjrf2z5w59QpYNs2WZ/nY9o0OS48KVcOeOihor/D2WcXbq7o6zt8+KGksS5fDqxYIc/Ll0vrM+Pc9dzH7hDZVydl6VLgpZekJkuJEjIe9/PR+A1uuUWOmZ07gV27Cj+7/716tfexli0L3HuvdH82HnXryjXFOAYMfB3LZijuOArm8yVKAF98YX4MoTJ0KPD884VfK10aePBBKY+xe7c8du0q+Hv3bmmjYPaWWKqUnI9VqhR9/vxz4NChop+pVw/YtCnkrxcWPv9c0sbd90fJkkCvXnLuuu+/XbvMF1u041z01U3cNoFDRIkA1gC4AsA2AAsA9GHmlW7L3Af8f3v3HyNHWcdx/P29u5a7/uRom0K4a8GGogFaUIMpIDQQwR+JSNJiQQ0mKpKAARSiGFMqgUC0GkxUGogI/sBSpCgm/JAEEloxQIGe14Jc6a+UltJCe8BVaWv79Y/n2dze3c7e3s3uzu7280omOzs7O/vsd5+Z+e7MM88wx92vMrNFwCXu/uViy61EgnPCCYUTgenTw4rS09M/bNw4cOPV3h66uC60YR03Luwwzj576AaonJIStEpu1PM98QQsXAh9fQOnt7aGBOi880KMkoa+vrAyjaar8EmTQgKwefPI3jd58sAdbW8vPP74wJ1zc3N4befO/ultbTBnTrgfzty54fG002DChOR6NHNmqDfbtoUkaMOGUJdy45s2Fb+jb1sb3HprSGjmzAm/d5JSd2z79oUkJ5fw5Pq+aGoaWGfGjAn9dowb15/EFLqL85Qpod+krq7ksjU3D/2eTU1hPcv9Dk8/PbQeFVrO7Nlw6qlhOOWU8DhrVkg0k36DSu9YenpCElaoHjc3h538/v1DX2ttDd3lH3tseFy5MvkzWluHJvITJw5Met59Fx55ZOA26aij4IYb4NxzQ10+cKDw48GDYZuXS97zTZ4cltHUVHxYvDh0ZjdYNXfuSetivnHjQsynTw9D/vjNNxeu51OnhnVx797wx6rQ4969ocfiQqq1TS6HYjHMxS4Xs0JxXLAAduwY+t5K1IMsEpx5wBJ3vyg+vwnA3W/Pm+fJOM8/zawF2AlM8yKFqkSCM5J/LAcPhh+9pyf80+rpgWXLCi+3WpW52I61WhuUjo7+W1GNxNix4d99oQ1iztKl4Z/RlClhyI23t4cdMBSPQXd3KNuOHQMf88e3bSv82a2tcN11/QnNSSeFnVUho/3ne/Bg+J1OPjmbRPXw4XA08IILCv/rNAtJREcHdHYOHTo6+juKHC7J2727+G/R3Z1czgceCInM7Nlhh11I2qMPaRU7GnzjjQMTmdzjpEkD/wAVi+HmzaHn3q1bQxK7devQoVByUguquXMv9qdvw4awA54wIfn9aevRzJmF+zitpyM4af84V3NdTEpwKtZWBlhAOC2Ve/414JeD5lkHdOQ93whMLbCsK4E1wJoZ5ewcJk+ac85Z93FXC+d7cw3pBg9moZH0qlWhk8GennAOu7d3YJuItDFMG4Ni5R+Jeq5H5YhB2t+hHDHIsoFuucpfqbq8enVoA/jKK+7r14f1ccuWsE7u2hXWy87Owu+fMSO0Udm/PzTS3rcvXCTx3nvhfXv2uL/zTnK/XtXs8zPrelQL2+S0so7hSFDtRsblTHDyh1rsB6cWKnPWV11knaDkllGvyYV79vWoXDE4kncM5Sp/lnU57Xeohd+wVspQz1fC1UIMS5VFgjMPeDLv+U3ATYPmeRKYF8dbgHeIp82ShlpMcNzrvzKnlXWCklatrMyKQf2vS1mXvxbWxaxjUCtlqHf1EsMsEpwWYBNwIjAW6AJOGTTP1cCyOL4IWDHccms1wZH6WRmS1Hv5y0ExaAz6HeVIkpTgVPoy8c8DdxIuE7/X3W8zs1tiYR41s1bg98AZwB5gkbtvKrbMSjQyFhERkfqU1Mi4pdDM5eLujwGPDZq2OG/8Q2BhJcsgIiIiR54iPWqIiIiI1KeKnqKqBDPbDVSwf16mEho7y+gphukphukphukphukphukNF8OZ7j5t8MS6S3AqzczWFDqXJ6VTDNNTDNNTDNNTDNNTDNMbbQx1ikpEREQajhIcERERaThKcIa6O+sCNADFMD3FMD3FMD3FMD3FML1RxVBtcERERKTh6AiOiIiINBwlOCIiItJwlOBEZvZZM3vdzN4wsx9kXZ56ZGZbzKzbzNaame6nUSIzu9fMdpnZurxpx5jZU2a2IT62Z1nGWpcQwyVmtj3Wx7Xx1jGSwMw6zewZM3vVzNab2bVxuupiiYrEUHWxRGbWamYvmFlXjOGP4/QTzez5uI9+0MzGDrsstcEBM2sGeoDPAG8CLwKXufurmRaszpjZFuCT7q5OrUbAzM4F+oDfufupcdpPgD3ufkdMuNvd/ftZlrOWJcRwCdDn7kuzLFu9MLPjgOPc/WUzmwi8BHwJ+DqqiyUpEsNLUV0siZkZMN7d+8xsDLAauBb4LrDS3Zeb2TKgy93vKrYsHcEJzgTecPdN7n4AWA5cnHGZ5Ajh7s8Sbjab72Lg/jh+P2EjKQkSYigj4O5vufvLcfwD4DXgeFQXS1YkhlKieIPwvvh0TBwcOB/4c5xeUj1UghMcD2zLe/4mqpSj4cDfzewlM7sy68LUuenu/lYc3wlMz7IwdewaM/tXPIWlUyslMrMTgDOA51FdHJVBMQTVxZKZWbOZrQV2AU8BG4Fed/9fnKWkfbQSHCmnc9z948DngKvjaQNJycN5ZJ1LHrm7gFnA6cBbwM8yLU2dMLMJwMPAde7+fv5rqoulKRBD1cURcPdD7n460EE4w/LR0SxHCU6wHejMe94Rp8kIuPv2+LgLeIRQMWV03o7n83Pn9XdlXJ664+5vxw3lYeAeVB+HFds8PAz80d1XxsmqiyNQKIaqi6Pj7r3AM8A84Ggza4kvlbSPVoITvAicFFtpjwUWAY9mXKa6YmbjY6M6zGw8cCGwrvi7pIhHgSvi+BXAXzMsS13K7ZSjS1B9LCo27vwN8Jq7/zzvJdXFEiXFUHWxdGY2zcyOjuNthIt/XiMkOgvibCXVQ11FFcXL9u4EmoF73f22bEtUX8zsI4SjNgAtwAOKYWnM7E/AfGAq8DZwM/AXYAUwA9gKXOruakSbICGG8wmnBBzYAnw7ry2JDGJm5wCrgG7gcJz8Q0IbEtXFEhSJ4WWoLpbEzOYQGhE3Ew7CrHD3W+I+ZjlwDPAK8FV33190WUpwREREpNHoFJWIiIg0HCU4IiIi0nCU4IiIiEjDUYIjIiIiDUcJjoiIiDScluFnEREpHzM7RLiMNme5u99Rwc+bDxxw9+cq9RkiUnuU4IhItf03dsNecbHn0/mEO40rwRE5gqgfHBGpKjPrc/cJg6ZNBl4Avujur8eO+55293vMrI/Qvf2FhJs9LnL33WY2C/gVMA34D/Atd/+3md0HfEi40eF24CzgELAb+A5wLKEjwEPAe+6ue6aJNCAdwRGRamuLdwrOud3dHzSza4D7zOwXQLu73xNfHw+scffrzWwxITm5BrgbuMrdN5jZp4BfA+fH93QAZ7n7ITNbAvS5+1IAM+sGLnL37bku4UWk8SjBEZFqK3iKyt2fMrOFhKMyc/NeOgw8GMf/AKyMd2s+C3go3P4HgKPy3vOQux9K+Px/EBKpFcDKhHlEpM4pwRGRmmBmTcDHCKeb2oE3E2Z1whWgvUXa8uxL+hx3vyoe8fkC8JKZfcLd3x11wUWkJukycRGpFdcT7hp8OfBbMxsTpzfRfxfhy4HV7v4+sDke8cGCuYMXGH0ATMw9MbNZ7v68uy8mtMvpLP9XEZGsKcERkWprM7O1ecMdZnYy8E3ge+6+CngW+FGcfx9wppmtI7SxuSVO/wrwDTPrAtYDFyd83t+AS+JnfRr4qZl1x+U9B3RV5FuKSKZ0FZWI1LRCV12JiAxHR3BERESk4egIjoiIiDQcHcERERGRhqMER0RERBqOEhwRERFpOEpwREREpOEowREREZGG838i/NTDBSURoAAAAABJRU5ErkJggg==\n", "text/plain": ["<Figure size 576x864 with 4 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import torch\n", "import torch.nn.functional as F\n", "import matplotlib.pyplot as plt\n", "\n", "# 假设一个 4x5 的矩阵（4个样本，30个专家）\n", "scores = torch.randn(4, 30)  # 随机生成一个得分矩阵\n", "\n", "# 计算softmax和sigmoid的结果\n", "softmax_scores = F.softmax(scores, dim=-1)\n", "sigmoid_scores = torch.sigmoid(scores)\n", "\n", "# 创建图表：每个样本画一个图，4个样本对应4个图\n", "fig, axes = plt.subplots(4, 1, figsize=(8, 12))\n", "\n", "# 绘制每个样本的Softmax和Sigmoid折线图\n", "for i in range(4):\n", "    axes[i].plot(softmax_scores[i].detach().numpy(), 'b-', label='Softmax', marker='o')  # 蓝色：Softmax\n", "    axes[i].plot(sigmoid_scores[i].detach().numpy(), 'r-', label='Sigmoid', marker='o')  # 红色：Sigmoid\n", "    axes[i].set_title(f'Sample {i+1}')\n", "    axes[i].set_xlabel('Experts')\n", "    axes[i].set_ylabel('Activation')\n", "    axes[i].legend()\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "id": "ad25714f-5820-4d02-a576-8db8837158b2", "metadata": {}, "source": ["**当这些改变加在数学流程上时，DeepSeekMoE流程可以呈现为——**"]}, {"cell_type": "markdown", "id": "b0353262-45af-4880-87a6-5e8ed59cb95f", "metadata": {}, "source": ["假设：\n", "\n", "- 输入向量 `u_t` 为 token `t` 经过注意力机制后的隐藏状态。\n", "- 每个专家都是一个带有门控的前馈网络（SwiGLU FFN），也就是至少会含有3个线性层。\n", "- 门控网络（路由器）是一个线性层，其结构是(dim, n_experts)。\n", "- 只有 `K` 个专家会被选择（Top-K 机制）。"]}, {"cell_type": "markdown", "id": "ee0e3d81-9807-43b3-9179-8d8a2143eb12", "metadata": {}, "source": ["========="]}, {"cell_type": "markdown", "id": "511a3c73-b344-4b79-879d-f18a75643b44", "metadata": {}, "source": ["**1. 计算 `u_t` 经过共享专家（Shared Experts）的输出**\n", "\n", "- **普通 MoE 只有动态选择的专家routed_experts**，但 DeepSeekMoE **额外增加 `N_s` 个共享专家**（`FFN^{(s)}`）。\n", "- **所有 token 都必须经过这 `N_s` 个共享专家**，确保基本特征转换。\n", "\n", "在任意token输入所有共享专家后、无论有多少个共享专家，都对所有共享专家的结果进行加和，形成当前token在共享专家上的输出结果 ↓\n", "$$\n", "\\sum_{i=1}^{N_s} FFN_i^{(s)}(u_t)\n", "$$\n", "- 这里 `FFN_i^{(s)}` 表示第 `i` 个共享专家，它们是固定启用的，不依赖 token 选择。"]}, {"cell_type": "markdown", "id": "79952358-6fb0-4946-889f-04657bdc53bb", "metadata": {}, "source": ["========="]}, {"cell_type": "markdown", "id": "a0456f4f-1c0b-4863-b0f0-4a35a85947a0", "metadata": {}, "source": ["**2. 通过门控计算专家与输入向量的亲合度分数 `s_{i,t}`**\n", " \n", "   - 将$u_t$输入门控网络，通过门控网络（Gating Network）计算 token 对每个专家的亲合度分数。不同于普通 MoE 采用 **Softmax 计算专家分配权重**，DeepSeekMoE 采用 **Sigmoid 计算专家匹配度**——\n", "     $$\n", "     s_{i,t} = \\text{Sigmoid}(u_t^T e_i)\n", "     $$\n", "   - 其中 `e_i` 是 **专家 `i` 的中心向量（centroid vector）**，用于衡量 `u_t` 与专家$i$的匹配度。不难发现，事实上`e_i`的功能等同于门控网络的权重向量，实际我们在实现这一步骤计算时，也是使用线性层进行转换。"]}, {"cell_type": "markdown", "id": "bae8495e-9548-4ba7-b349-52386564240b", "metadata": {}, "source": ["========="]}, {"cell_type": "markdown", "id": "d9324f83-2c5a-4318-8066-b6a53c16c696", "metadata": {}, "source": ["**3. 选出 `Top-K_r` 个路由专家**\n", "$$\n", "g'_{i,t} =\n", "\\begin{cases}\n", "s_{i,t}, & s_{i,t} \\in \\text{Top-K\\_r}(s_{j,t}| 1 \\leq j \\leq N_r) \\\\\n", "0, & \\text{otherwise}\n", "\\end{cases}\n", "$$\n", "   - 选择分数最高的 `K_r` 个专家，其余专家的得分设为 `0`。\n", "   - 这确保每个 token **最多只经过 `K_r` 个专家**，减少计算量。"]}, {"cell_type": "markdown", "id": "e27d4662-b470-4cf6-b5ac-ac47ccb547b9", "metadata": {}, "source": ["========="]}, {"cell_type": "markdown", "id": "5cc54bfe-48db-4661-ba63-ac580abce03e", "metadata": {}, "source": ["**4. 归一化选中的专家权重**\n", "\n", "选中的 `K_r` 个专家需要 **进行归一化**，以确保不同 token 选择的专家权重总和为 1：\n", "$$\n", "g_{i,t} = \\frac{g'_{i,t}}{\\sum_{j=1}^{N_r} g'_{j,t}}\n", "$$\n", "- 归一化后的 `g_{i,t}` 作为最终的专家权重。"]}, {"cell_type": "markdown", "id": "aa720cb1-a08f-4d7d-8066-4fd1c456c331", "metadata": {}, "source": ["========="]}, {"cell_type": "markdown", "id": "a50385b9-f6ae-4381-8e1f-abcc3daabe61", "metadata": {}, "source": ["**5. 计算 `u_t` 经过路由专家（Routed Experts）的输出**\n", "只计算 **被选中的 `K_r` 个专家**：\n", "$$\n", "FFN_i^{(r)}(u_t)\n", "$$\n", "- 这里 `FFN_i^{(r)}` 是 **路由专家 `i`**，只有 `Top-K_r` 选出的专家会执行前馈计算。"]}, {"cell_type": "markdown", "id": "acbef5dd-be75-4c19-8018-94e74cebf033", "metadata": {}, "source": ["========="]}, {"cell_type": "markdown", "id": "82e26d6a-0a85-4548-b53b-f962672bb9ca", "metadata": {}, "source": ["**6. 路由专家（Routed Experts）结果与路由专家权重进行加权求和**\n", "只计算 **被选中的 `K_r` 个专家**：\n", "$$\n", "\\sum_{i=1}^{N_r} g_{i,t} FFN_i^{(r)}(u_t)\n", "$$\n", "- `g_{i,t}` 作为该专家的权重，与专家输出的结果进行加权求和。"]}, {"cell_type": "markdown", "id": "4345405c-fcb2-46c8-b490-5fa164ca6468", "metadata": {}, "source": ["========="]}, {"cell_type": "markdown", "id": "78b5c651-30ce-4f0b-aca1-6760ad2071dd", "metadata": {}, "source": ["**7. 计算最终输出 `h'_t`**\n", "综合共享专家和路由专家的计算结果：\n", "$$\n", "h'_t = u_t + \\sum_{i=1}^{N_s} FFN_i^{(s)}(u_t) + \\sum_{i=1}^{N_r} g_{i,t} FFN_i^{(r)}(u_t)\n", "$$\n", "- **第一项 $u_t$**：架构中的残差链接。\n", "- **第二项 $\\sum FFN_i^{(s)}(u_t)$**：所有 token 共享专家的计算结果。\n", "- **第三项 $\\sum g_{i,t} FFN_i^{(r)}(u_t)$**：根据 `Top-K_r` 选择的路由专家计算结果。"]}, {"cell_type": "markdown", "id": "12fdd3eb-c3de-47f3-9daf-f420807f5e04", "metadata": {}, "source": ["不记得残差连接？如下所示 ↓"]}, {"cell_type": "markdown", "id": "51dbb561-eb9c-4e6f-b304-6041e5f16b34", "metadata": {}, "source": ["<center><img src=\"https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/73-.png\" alt=\"image-20250107200502389\" style=\"zoom:30%;\" />"]}, {"cell_type": "markdown", "id": "ba917d54-8d64-4eb1-83a2-17a5f3cea5b7", "metadata": {}, "source": ["所以你不难发现，DeepSeekv3中的MoE实际的数学路程与论文中所呈现的顺序相反——"]}, {"cell_type": "markdown", "id": "28d34faf-c635-42fa-943b-a41e8dfa4be7", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/110.png)"]}, {"cell_type": "markdown", "id": "fced6b67-e4d3-46da-aa3d-567fbbdc3895", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "8643328e-f7c8-43ab-a718-553ddd145b86", "metadata": {}, "source": ["### 3.3 免负载均衡的DeepSeekMOE"]}, {"cell_type": "markdown", "id": "151f3cdb-afb3-4e32-9778-391481b1e31c", "metadata": {}, "source": ["DeepSeekMoE是DeepSeek公司一项重要的创新、但DeepSeekv3架构所使用的是**DeepSeekMoE基础上改进的架构，即免负载均衡的DeepSeekMoE**。\n", "\n", "负载不均衡是传统的混合专家（MoE）模型在处理专家负载时面临着一个重要问题，由于 MoE 模型根据输入的特征选择不同的专家进行计算，**通常会出现某些专家频繁被激活，而其他专家几乎不参与计算的情况**。这种不均衡的负载会导致部分专家的计算资源被过度消耗，而其他专家则未被充分利用，进而影响整体计算效率和模型性能。为了缓解这一问题，许多传统方法采用辅助损失（auxiliary loss）来强制平衡各个专家的负载，通过增加额外的损失项来惩罚负载不均衡。然而，过大的辅助损失往往会干扰模型的学习过程，导致模型性能下降。尽管这些方法可以在一定程度上减轻负载不均衡，但它们仍然面临着调整损失权重的难题，且可能影响模型的效果。"]}, {"cell_type": "markdown", "id": "6e925e4e-0fcc-4e29-8630-7e30b656b0c2", "metadata": {}, "source": ["为了在负载平衡和模型性能之间取得更好的折中，DeepSeekv3开创了一种无辅助损失的负载平衡策略。具体来说，他们为每个专家引入了一个偏置项 $ b_i $，并将其加到相应的亲和度得分（也就是我们之前总提到的每个专家的权重）$ s_{i,t} $ 上来帮助确定专家是否被激活。\n", "\n", "$$\n", "g'_{i,t} = \\begin{cases} \n", "s_{i,t} + b_i, & \\text{if } s_{i,t} \\in \\text{Topk}(\\{s_{j,t} + b_j | 1 \\leq j \\leq N_r \\}, K_r) \\\\\n", "0, & \\text{otherwise}.\n", "\\end{cases}\n", "$$\n", "\n", "经过上述计算后，如果亲合度得分在 **top-K** 所规定的范围内，那该专家被激活，而且具体的激活值为$ s_{i,t} + b_i $，否则激活值为 0。"]}, {"cell_type": "markdown", "id": "d7394865-816c-4d99-9f22-01f4ae6f0e83", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/109.png)"]}, {"attachments": {}, "cell_type": "markdown", "id": "3d7a4fa9-d073-4e03-b2a3-acc4c555a0d6", "metadata": {}, "source": ["除此之外，在论文中还特别提到，DeepSeekv3会在训练过程中动态调整每个专家的偏置项 $ b_i $，以保证负载平衡：\n", "- 如果某个专家的负载过重（即该专家被激活过多），偏置项会减小，这样该专家的亲和度得分会降低，减少其被激活的概率。<br><br>\n", "- 如果某个专家的负载过轻，偏置项会增加，增加该专家被激活的概率。<br><br>\n", "- 这个动态调整过程通过一个超参数 $ \\gamma $ 来控制偏置项的更新速度。\n", "\n", "**但事实上，在后来的架构实现中、DeepSeekv3采取的策略是：如果用户加载的是671B的DeepSeekv3模型、则直接让bias作为模型超参数、跟着损失函数一起更新、如果用户加载的是比671B更小的模型、则直接不设置bias选项**。但无论如何，在开源代码中都没有设置用于更新bias的参数$\\gamma$。在后续的开源的config中、也没有对$\\gamma$进行设置。"]}, {"cell_type": "markdown", "id": "6cc6385a-928f-48f6-ba53-4ae0123776ab", "metadata": {}, "source": ["<font color=\"red\">**【头脑风暴】DeepSeek为什么不对这部分进行开源？如果我们要自定义gamma相关的代码、最难的点在哪里？**</font>\n", "\n", "以下是头脑风暴的伪代码——"]}, {"cell_type": "markdown", "id": "b1dde03c-a16e-4a2c-8f4a-02a802389c15", "metadata": {}, "source": ["```python\n", "# 假设我们有一个专家激活次数的列表，记录每个专家在训练中的激活次数\n", "expert_loads = [5, 10, 2, 3, 8]  # 每个专家的激活次数\n", "total_load = sum(expert_loads)\n", "num_experts = len(expert_loads)\n", "mean_load = total_load / num_experts  # 计算负载均值\n", "\n", "# 假设负载的阈值是均值的 1.5 倍和 0.5 倍\n", "upper_threshold = mean_load * 1.5\n", "lower_threshold = mean_load * 0.5\n", "\n", "# 初始化gamma\n", "gamma = 0.1\n", "\n", "# 动态调整偏置项 b_i\n", "bias_updates = []\n", "for load in expert_loads:\n", "    if load > upper_threshold:\n", "        # 如果负载过重，减少偏置项\n", "        bias_update = -gamma\n", "    elif load < lower_threshold:\n", "        # 如果负载过轻，增加偏置项\n", "        bias_update = gamma\n", "    else:\n", "        # 如果负载正常，保持偏置项不变\n", "        bias_update = 0\n", "    \n", "    bias_updates.append(bias_update)\n", "\n", "# 输出偏置项的调整\n", "print(\"Bias updates:\", bias_updates)\n", "```"]}, {"cell_type": "markdown", "id": "ffe998da-c948-413b-bcd9-02242e6a3b27", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "c3d3294e-96f1-489c-8f71-d8c59f08302b", "metadata": {}, "source": ["### 3.4 补充辅助损失"]}, {"cell_type": "markdown", "id": "b5414073-b80d-4757-9b3d-7e7198016ef4", "metadata": {}, "source": ["尽管 DeepSeek-V3 主要依赖于无辅助损失的负载平衡策略，但为了防止在单个序列中出现极端的负载不平衡，模型还是引入了**序列级别的负载平衡损失**。这意味着，对于每一个输入序列，模型会对该序列中的每个专家的负载进行平衡、并且该流程会影响DeepSeekv3模型的预训练过程。"]}, {"cell_type": "markdown", "id": "22550fe9-03c0-40df-ba62-60458504a4dd", "metadata": {}, "source": ["任何负载均衡方向的辅助损失都会从两个不同的角度衡量专家$i$的负载情况，分别是——"]}, {"cell_type": "markdown", "id": "4f804814-e3d6-4428-b345-1f21b81c291a", "metadata": {}, "source": ["- **$ P_i $（论文中的第 20 个公式）：** 这是专家 $ i $ 在整个序列中的负载平衡指标，它是所有时间步 $ t $ 上归一化激活值的平均值。该指标用于衡量每个专家的负载是否平衡。"]}, {"cell_type": "markdown", "id": "17418df4-e983-4f2c-a9f1-e9b808d0e6ca", "metadata": {}, "source": ["$$\n", "s'_{i,t} = \\frac{s_{i,t}}{\\sum_{j=1}^{N_r} s_{j,t}} \\tag{19}\n", "$$"]}, {"cell_type": "markdown", "id": "a0448c3e-e991-4c42-b96e-b6433f431914", "metadata": {}, "source": ["$$\n", "P_i = \\frac{1}{T} \\sum_{t=1}^{T} s'_{i,t} \\tag{20}\n", "$$"]}, {"cell_type": "markdown", "id": "9e7d2839-6a3a-4b77-a2b5-efb81ef4307f", "metadata": {}, "source": ["其中，**$ s'_{i,t} $（第 19 式）** 是对专家负载的归一化处理，将每个专家在特定时间步 $ t $ 的激活值 $ s_{i,t} $ 除以该时间步所有专家的总激活值。这是为了避免某个专家的过高激活值影响到负载的判断。"]}, {"cell_type": "code", "execution_count": 85, "id": "ca811645-9e0b-429c-8106-b8cb0e3e1e18", "metadata": {}, "outputs": [], "source": ["# 假设一个 2x5 的矩阵（2个样本，5个专家）\n", "scores1 = torch.randn(1, 5)  # 随机生成一个得分矩阵\n", "scores2 = torch.randn(1, 5)\n", "sigmoid_scores_1 = torch.sigmoid(scores1)\n", "sigmoid_scores_2 = torch.sigmoid(scores2)"]}, {"cell_type": "code", "execution_count": 73, "id": "cd1970a7-2323-464f-9c80-109e7e043942", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.9218, 0.5966, 0.5226, 0.5555, 0.6477]])"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["sigmoid_scores_1"]}, {"cell_type": "code", "execution_count": 74, "id": "0adc2d60-9482-4b67-84fb-06a9afa80541", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.8026, 0.7039, 0.5104, 0.9361, 0.4413]])"]}, "execution_count": 74, "metadata": {}, "output_type": "execute_result"}], "source": ["sigmoid_scores_2"]}, {"cell_type": "code", "execution_count": 75, "id": "c84db1c3-3059-45a7-91cf-bcd1c4ecfebe", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.2841, 0.1839, 0.1611, 0.1712, 0.1996]])"]}, "execution_count": 75, "metadata": {}, "output_type": "execute_result"}], "source": ["# 对si_t进行归一化\n", "# 让一个token上所有专家的激活值除以所有专家的总激活值\n", "si_t_1 = sigmoid_scores_1/sigmoid_scores_1.sum()\n", "si_t_2 = sigmoid_scores_2/sigmoid_scores_2.sum()\n", "\n", "si_t_1"]}, {"cell_type": "code", "execution_count": 76, "id": "0a3196ad-7828-4395-bc66-52f17cff5775", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.2364, 0.2074, 0.1504, 0.2758, 0.1300]])"]}, "execution_count": 76, "metadata": {}, "output_type": "execute_result"}], "source": ["si_t_2"]}, {"cell_type": "code", "execution_count": 78, "id": "0eeb16b0-2c1b-4091-8ab0-7d1621cc9637", "metadata": {}, "outputs": [], "source": ["Pi = (si_t_1 + si_t_2)/2"]}, {"cell_type": "code", "execution_count": 79, "id": "14fa3f3d-6a54-4aad-9b08-cc22d9293087", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.2603, 0.1956, 0.1557, 0.2235, 0.1648]])"]}, "execution_count": 79, "metadata": {}, "output_type": "execute_result"}], "source": ["Pi"]}, {"cell_type": "code", "execution_count": null, "id": "c183c01a-cc70-4a7d-81dc-fb6010495067", "metadata": {}, "outputs": [], "source": ["        ex1 ex2  ex3 ... exn\n", "token1   0   1    1       0        \n", "token2   1   1    0       0\n", "         0.5 1    0.5   ..."]}, {"cell_type": "markdown", "id": "3e49131f-7436-4947-8530-0f976a4f5eaf", "metadata": {}, "source": ["- **$ f_i $（论文中的第 18 个公式）：** 对每个专家 $ i $，计算其负载平衡指标。具体来说，首先计算专家 $ i $ 在每个时间步中的激活情况（即0，1表示，使用 `Top-k` 路由选择，表示每个时间步选择激活最强的 $ k $ 个专家）。然后，根据这些激活情况，计算专家 $ i $ 在序列中的激活比例。"]}, {"cell_type": "markdown", "id": "42a3ec1b-6fd3-4d9c-93e0-9f7827fa3eb2", "metadata": {}, "source": ["$$\n", "s'_{i,t} = \\frac{s_{i,t}}{\\sum_{j=1}^{N_r} s_{j,t}} \\tag{19}\n", "$$"]}, {"cell_type": "markdown", "id": "7def4add-170f-483e-af62-c3df3faa233f", "metadata": {}, "source": ["$$\n", "f_i = \\frac{N_r}{K_r T} \\sum_{t=1}^{T} 1 \\left(s_{i,t} \\in \\text{Topk}(\\{s_{j,t} \\mid 1 \\leq j \\leq N_r\\}, K_r)\\right) \\tag{18}\n", "$$"]}, {"cell_type": "code", "execution_count": 86, "id": "ea2b410c-b54f-4549-81f6-89d40bc8fd48", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1., 0., 0., 1., 0.]])"]}, "execution_count": 86, "metadata": {}, "output_type": "execute_result"}], "source": ["# 假设一个 2x5 的矩阵（2个样本，5个专家）\n", "scores1 = torch.randn(1, 5)  # 随机生成一个得分矩阵\n", "scores2 = torch.randn(1, 5)\n", "sigmoid_scores_1 = torch.sigmoid(scores1)\n", "sigmoid_scores_2 = torch.sigmoid(scores2)\n", "\n", "# 对si_t进行归一化\n", "# 让一个token上所有专家的激活值除以所有专家的总激活值\n", "si_t_1 = sigmoid_scores_1/sigmoid_scores_1.sum()\n", "si_t_2 = sigmoid_scores_2/sigmoid_scores_2.sum()\n", "\n", "# 选择每一行的topk个值，其他部分为0\n", "k = 2  # 假设选择top2个值\n", "\n", "# 获取每行的topk值的索引\n", "topk_indices_1 = torch.topk(si_t_1, k, dim=-1).indices\n", "topk_indices_2 = torch.topk(si_t_2, k, dim=-1).indices\n", "\n", "# 创建一个与原矩阵同形状的全零矩阵\n", "topk_mask_1 = torch.zeros_like(sigmoid_scores_1)\n", "topk_mask_2 = torch.zeros_like(sigmoid_scores_2)\n", "\n", "# 将topk索引位置的值设为1\n", "topk_mask_1.scatter_(1, topk_indices_1, 1)\n", "topk_mask_2.scatter_(1, topk_indices_2, 1)\n", "\n", "# 输出结果\n", "topk_mask_1"]}, {"cell_type": "code", "execution_count": 81, "id": "6d689c27-5d18-4957-be05-5e03ccf93dff", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0., 1., 0., 0., 1.]])"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["topk_mask_2"]}, {"cell_type": "code", "execution_count": 82, "id": "0e1c6fdd-3d7d-41bd-982c-c7fb00e8422d", "metadata": {}, "outputs": [], "source": ["fi = (topk_mask_1 + topk_mask_2)/2"]}, {"cell_type": "code", "execution_count": 83, "id": "0c07febf-7608-4e36-a118-d9bd422c7f4d", "metadata": {"scrolled": true}, "outputs": [{"data": {"text/plain": ["tensor([[0.5000, 1.0000, 0.0000, 0.0000, 0.5000]])"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["fi"]}, {"cell_type": "markdown", "id": "469e685d-6f7c-4b34-9aa3-7aa3442f3d49", "metadata": {}, "source": ["最终的辅助损失为——"]}, {"cell_type": "markdown", "id": "e01688d9-f0d1-443f-a36e-646aab072675", "metadata": {}, "source": ["   - **$ L_{\\text{Bal}} $（第 17 式）：** 这是负载平衡的总损失，定义为所有专家负载平衡的加权和。这里的加权系数由 **平衡因子 $ \\alpha $** 控制，该因子通常是一个非常小的值，确保负载平衡不会主导训练过程。"]}, {"cell_type": "markdown", "id": "3f3fbb0b-1826-4d3b-a481-49ee15607d01", "metadata": {}, "source": ["$$\n", "L_{\\text{Bal}} = \\alpha \\sum_{i=1}^{N_r} f_i P_i \\tag{17}\n", "$$"]}, {"cell_type": "markdown", "id": "77916616-0242-4176-8b2e-f2d7ec26e320", "metadata": {}, "source": ["其中，平衡因子 $ \\alpha $ 是一个超参数，在 DeepSeek-V3 中将赋予一个非常小的值；$ 1(\\cdot) $ 表示指示函数；而 $ T $ 表示序列中的标记数量。序列级别的负载平衡损失鼓励每个序列中的专家负载保持平衡。**当负载不均衡时，这个辅助损失将变得更大。**"]}, {"cell_type": "markdown", "id": "ffa25fa0-1add-4dd2-b320-8bf27a469867", "metadata": {}, "source": ["<font color=\"red\">**【深度】为什么这里让两个因子相乘就能够代表负载的均衡？**</font>"]}, {"cell_type": "markdown", "id": "4b5ed716-a997-4591-a70b-149977c6cb2c", "metadata": {}, "source": ["---"]}, {"cell_type": "code", "execution_count": null, "id": "9ce8cde1-f4d3-4ba1-9eff-9ede223f90fd", "metadata": {}, "outputs": [], "source": ["tp(model para), dp, ep\n", "\n", "700 ep ==> 64\n", "\n", "ep == index ==> experts_list[0:63]\n", "                experts_list[64:127] \n", "\n", "gate ==> touch every gpus\n", "\n", "linear/embedding  ==> nn.Linear(512,32)  nn.Linear(32,1024)\n", "\n", "     row  ==> 512/ world_size  => 64\n", "     column  ==> 1024 / world_size  ==> troch.dsirtibuted all_reduce()"]}, {"cell_type": "markdown", "id": "edcd2b4e-6c34-4f22-8378-4980ed0f996e", "metadata": {}, "source": ["### 3.5 DeepSeekMoE的专家并行"]}, {"cell_type": "markdown", "id": "6e77ac22-8a86-470c-ad43-91f2c8151f5d", "metadata": {}, "source": ["专家并行机制是 **Mixture-of-Experts (MoE)** 模型在大规模分布式计算中的关键技术，旨在通过将模型的多个专家分布在不同的计算节点或设备上，来实现高效的计算和负载平衡。在大规模 **MoE** 模型中，模型的参数量非常庞大，且每个输入数据仅激活部分专家。如果每个设备都计算所有专家，计算量和内存消耗将急剧增加。专家并行机制的目标是将专家分配到多个设备上，只有特定的设备负责处理某些专家，从而达到：\n", "\n", "- **负载均衡**：不同的设备处理不同的专家，避免某个设备负载过重。\n", "- **计算效率**：通过并行计算，提高计算效率。\n", "- **内存管理**：每个设备只需要加载和处理自己负责的专家，降低内存消耗。"]}, {"cell_type": "markdown", "id": "95ceee84-30e0-4ae0-958b-a4c65a8ab9fa", "metadata": {}, "source": ["核心问题1——**如何分配专家到不同的设备呢**？"]}, {"cell_type": "markdown", "id": "77f8e059-17a8-4c7f-a4b1-bb88e4bc89cc", "metadata": {}, "source": ["- **本地专家**：在分布式设置下，每张GPU只负责一部分专家。这些专家被称为 **本地专家**。例如，假设有 1000 个专家，如果系统有 10 个进程（即 `world_size = 10`），每个节点将负责 100 个专家。这种方式通过分配专家来确保每个设备的计算负载均衡。\n", "  \n", "- **专家的起始和结束索引**：\n", "  - 每个设备负责一部分专家，这部分专家通过两个索引进行标识：`experts_start_idx` 和 `experts_end_idx`。\n", "  - `experts_start_idx` 表示当前设备上负责的第一位专家的索引，`experts_end_idx` 表示最后一位专家的索引。\n", "\n", "  例如，如果 `n_routed_experts = 1000` 且 `world_size = 10`，则每张GPU负责 100 个专家。假设GPU的 `rank = 0`，则该GPU负责的专家索引为 0 到 99（即从第 1 个专家到第 100 个专家）。\n", "\n", "- **局部专家的路由计算**：每个设备仅会参与计算它所负责的专家的输出。路由机制会确保每个输入数据仅由选定的 **top-k 专家** 来处理。因此，**专家并行机制** 基于数据路由而不需要每个设备计算所有专家，从而有效减少了计算量。"]}, {"cell_type": "markdown", "id": "9526f056-bb0e-4b0b-808b-e07b1c53ebbc", "metadata": {}, "source": ["从代码的角度来说，每张GPU只加载和计算它所负责的 **本地专家**。这通过以下代码实现：\n", "\n", "```python\n", "self.experts_start_idx = rank * self.n_local_experts\n", "self.experts_end_idx = self.experts_start_idx + self.n_local_experts\n", "```\n", "\n", "- **`rank`** 表示当前GPU的标识符（例如，在分布式训练中，`rank` 表示当前节点的编号）。\n", "- **`n_local_experts`** 是每张GPU上负责的专家数量。通过 `rank` 和 `n_local_experts` 计算当前节点负责的专家范围。"]}, {"cell_type": "markdown", "id": "182a274e-689c-429a-b8af-51cd7e543a99", "metadata": {}, "source": ["--- "]}, {"cell_type": "markdown", "id": "9a9ddd37-ca44-4eb3-9719-30660e1c8369", "metadata": {}, "source": ["核心问题2——**如何分配不同的token到不同的专家上呢**？"]}, {"cell_type": "markdown", "id": "d47fca84-7e89-4328-9f1c-703f751001db", "metadata": {}, "source": ["在前向传播过程中，每张GPU只负责计算它所负责的专家的输出，其中`self.experts`是专家的列表，x是输入的数据，weights是经过topk筛选出的权重。\n", "\n", "```python\n", "for i in range(self.experts_start_idx, self.experts_end_idx):\n", "    if counts[i] == 0:\n", "        continue\n", "    expert = self.experts[i]\n", "    #\n", "    idx, top = torch.where(indices == i)\n", "    y[idx] += expert(x[idx]) * weights[idx, top, None]\n", "```\n", "\n", "- `counts[i]` 记录了每个专家的激活次数，如果某个专家没有被激活，则跳过该专家。\n", "- 只计算当前GPU所负责的专家（由 `self.experts_start_idx` 和 `self.experts_end_idx` 确定的范围内）。\n", "- 我们会从所有的序列中索引出当前专家负责的token，然后将token输入到指定的专家中进行计算。"]}, {"cell_type": "markdown", "id": "603fbb2d-78fc-489a-bc81-4f0d37615b28", "metadata": {}, "source": ["`torch.where` 用于根据选定的专家索引来选择输入数据的相应部分。具体的代码片段是：\n", "\n", "```python\n", "idx, top = torch.where(indices == i)\n", "```\n", "\n", "- `indices` 是一个张量，表示每个输入样本对应的 **专家索引**。\n", "- `i` 是当前专家的索引。\n", "- `indices == i` 生成一个布尔条件张量，表示哪些样本被路由到了专家 `i`。\n", "- `torch.where(indices == i)` 返回两个张量：\n", "  - `idx`：表示满足条件 `indices == i` 的样本的索引。\n", "  - `top`：这些满足条件的样本的位置索引。\n", "\n", "在这段代码中，`torch.where` 用于 **筛选出** 路由到特定专家 `i` 的输入数据样本，并将它们的索引存储在 `idx` 和 `top` 变量中。"]}, {"cell_type": "markdown", "id": "a5c7cd94-60a5-4748-83bc-741a0e4d8ee4", "metadata": {}, "source": ["在分布式训练中，每个节点只计算它所负责的部分专家的输出。为了确保各个节点计算的结果一致，我们需要对结果进行 **同步**：\n", "\n", "```python\n", "if world_size > 1:\n", "    dist.all_reduce(y)\n", "```"]}, {"cell_type": "markdown", "id": "af5ab9af-4d8a-4012-acff-07f60c78f2c2", "metadata": {}, "source": ["### 3.6 DeepSeekMoE的全套实现代码"]}, {"cell_type": "markdown", "id": "ab985532-a7b9-42b5-bff2-2dd3157e3771", "metadata": {}, "source": ["在我们借助代码实现DeepSeekMOE的过程中，我们同样采取大模型脚本编写中惯例的**模块化代码编写方式**、先编写MoE所需的各个模块、再写一个串联起所有元素的类用来实现DeepSeekMoE的全局。"]}, {"cell_type": "markdown", "id": "8234d0c0-f436-4a6c-a5e3-d10b8244f60a", "metadata": {}, "source": ["```mathematica\n", "Input\n", "  │\n", "  ├──> Gate ───> Selects Top-k Experts\n", "  │             ├──> Expert 1\n", "  │             ├──> Expert 2\n", "  │             ├──> ...\n", "  │             ├──> Expert N\n", "  │             └──> Computes weighted sum of expert outputs\n", "  │\n", "  ├──> Shared <PERSON><PERSON> ───> Processes all inputs\n", "  │\n", "  └──> Sum (Experts Output + Shared MLP Output)\n", "  │\n", " Output\n", "```"]}, {"cell_type": "markdown", "id": "eeb00085-962f-4eae-8e30-8014a0fc6bd4", "metadata": {}, "source": ["- **分布式SwiGLU FFN作为共享专家层**"]}, {"cell_type": "markdown", "id": "c39a067b-76e5-4cbe-acfd-1c245c7f7c8a", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/111.png)"]}, {"cell_type": "markdown", "id": "5fab2207-c9f6-4247-9978-0a9fb021a6c0", "metadata": {}, "source": ["```python\n", "class MLP(nn.Mo<PERSON>le):\n", "    \"\"\"\n", "    支持张量并行和FP8量化的SwiGLU FFN前馈神经网络，并行方式为切分features，用于支持共享专家的构建。\n", "    Multi-Layer Perceptron (MLP) used as a feed-forward layer.\n", "\n", "    Attributes:\n", "        w1 (nn.<PERSON><PERSON><PERSON>): Linear layer for input-to-hidden transformation.\n", "        w2 (nn.<PERSON><PERSON><PERSON>): Linear layer for hidden-to-output transformation.\n", "        w3 (nn.<PERSON><PERSON><PERSON>): Additional linear layer for feature transformation.\n", "    \"\"\"\n", "    def __init__(self, dim: int, inter_dim: int):\n", "        \"\"\"\n", "        Initializes the MLP layer.\n", "\n", "        Args:\n", "            dim (int): Input and output dimensionality.\n", "            inter_dim (int): Hidden layer dimensionality.\n", "        \"\"\"\n", "        super().__init__()\n", "\n", "        # 门控\n", "        # 输入完整的X、W按列分割\n", "        self.w1 = ColumnParallelLinear(dim, inter_dim) \n", "\n", "        # 第二层线性层\n", "        # 输入分割状态的X、W按行分割\n", "        self.w2 = RowParallelLinear(inter_dim, dim) \n", "\n", "        # 输入完整的X、W按列分割\n", "        # 第一层线性层\n", "        self.w3 = ColumnParallelLinear(dim, inter_dim) \n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"\n", "        Forward pass for the MLP layer.\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor.\n", "\n", "        Returns:\n", "            torch.Tensor: Output tensor after MLP computation.\n", "        \"\"\"\n", "        return self.w2(<PERSON><PERSON>(self.w1(x)) * self.w3(x))\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "7e359064-031b-4b4a-a47f-ba480fb6a34f", "metadata": {}, "outputs": [], "source": ["一个nn.linear的流程是\n", "\n", "nn.Linear(in_features, out_features)\n", "\n", "x (seq_len, d)\n", "\n", "W (new_d, d)\n", "\n", "<PERSON>.T (d, new_d)\n", "\n", "x (seq_len, new_d)\n", "\n", "线性层的本质是 y = x * W.T\n", "\n", "将行进行分割，也就是在d上进行分割，将w.T分割成\n", "\n", "(d1, new_d) == gpu 0\n", "(d2, new_d)    1\n", "(d3, new_d)    2\n", "(d4, new_d)\n", "\n", "x (seq_len, d)   (d1, new_d)\n", "\n", "如果是将列进行分割，也就是在new_d上进行分割，将w.T分割成：\n", "\n", "(d, new_d1)  \n", "(d, new_d2)  \n", "(d, new_d3)\n", "(d, new_d4)\n", "\n", "X (seq_len, d)"]}, {"cell_type": "markdown", "id": "ba0b82d5-b41d-40e3-b126-0f5b1837814c", "metadata": {}, "source": ["- **经典的SwiGLU FFN作为路由专家层**"]}, {"cell_type": "markdown", "id": "fdea6331-6161-42e0-81a2-9eb6c76a9d88", "metadata": {}, "source": ["```python\n", "class Expert(nn.<PERSON><PERSON><PERSON>):\n", "    \"\"\"\n", "    支持FP8量化的SwiGLU FFN前馈神经网络，没有任何并行，用于支持路由专家的构建。\n", "    Expert layer for Mixture-of-Experts (MoE) models.\n", "\n", "    Attributes:\n", "        w1 (nn.<PERSON><PERSON><PERSON>): Linear layer for input-to-hidden transformation.\n", "        w2 (nn.<PERSON><PERSON><PERSON>): Linear layer for hidden-to-output transformation.\n", "        w3 (nn.<PERSON><PERSON><PERSON>): Additional linear layer for feature transformation.\n", "    \"\"\"\n", "    def __init__(self, dim: int, inter_dim: int):\n", "        \"\"\"\n", "        Initializes the Expert layer.\n", "\n", "        Args:\n", "            dim (int): Input and output dimensionality.\n", "            inter_dim (int): Hidden layer dimensionality.\n", "        \"\"\"\n", "        super().__init__()\n", "        self.w1 = Linear(dim, inter_dim)\n", "        self.w2 = Linear(inter_dim, dim)\n", "        self.w3 = Linear(dim, inter_dim)\n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"\n", "        Forward pass for the Expert layer.\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor.\n", "\n", "        Returns:\n", "            torch.Tensor: Output tensor after expert computation.\n", "        \"\"\"\n", "        return self.w2(<PERSON><PERSON>(self.w1(x)) * self.w3(x))\n", "```"]}, {"cell_type": "markdown", "id": "d84ef7f4-1123-471b-9cc3-13a0f3fd19ee", "metadata": {}, "source": ["- **门控层**"]}, {"cell_type": "markdown", "id": "9a77becd-d9ef-4467-b655-12e263433cd7", "metadata": {}, "source": ["```python\n", "class Gate(nn.Mo<PERSON>le):\n", "    \"\"\"\n", "    混合专家的路由门控层。\n", "    Gating mechanism for routing inputs in a mixture-of-experts (MoE) model.\n", "\n", "    Attributes:\n", "        dim (int): 路由层的输入维度\n", "        topk (int): 每个输入所激活的专家数量\n", "        n_groups (int): 路由的分组总量\n", "        topk_groups (int): 每个输入所激活的专家组的数量\n", "        score_func (str): 用来进行亲合度评分的函数('softmax' or 'sigmoid').\n", "        route_scale (float): 路由权重的归一化因子\n", "        weight (torch.nn.Parameter): 当前门控层的权重\n", "        bias (Optional[torch.nn.Parameter]): 可选、当前门控层的偏置。\n", "    \"\"\"\n", "    def __init__(self, args: ModelArgs):\n", "        \"\"\"\n", "        初始化门控层，核心功能为定义各个参数、并定义是否增加bias等规则。\n", "\n", "        Args:\n", "            args (ModelArgs): Model arguments containing gating parameters.\n", "        \"\"\"\n", "        super().__init__()\n", "        self.dim = args.dim\n", "        self.topk = args.n_activated_experts\n", "        self.n_groups = args.n_expert_groups\n", "        self.topk_groups = args.n_limited_groups\n", "        self.score_func = args.score_func\n", "        self.route_scale = args.route_scale\n", "\n", "        # 路由层权重的结构为 (n_routed_experts, dim)\n", "        # 接收原始数据X的维度dim，输出专家数量n_routed_experts，给每个专家打分\n", "        self.weight = nn.Parameter(torch.empty(args.n_routed_experts, args.dim))\n", "\n", "        # 偏置项、同样设置为参数。如果dim = 7168的话就允许该参数存在、且结构设置为n_routed_experts\n", "        # 如果dim不等于7168，则不允许该参数存在   \n", "        self.bias = nn.Parameter(torch.empty(args.n_routed_experts)) if self.dim == 7168 else None\n", "\n", "    def forward(self, x: torch.Tensor) -> <PERSON><PERSON>[torch.Tensor, torch.Tensor]:\n", "        \"\"\"\n", "        门控机制的向前传播。\n", "        Forward pass for the gating mechanism.\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor.\n", "\n", "        Returns:\n", "            Tuple[torch.Tensor, torch.Tensor]: Routing weights and selected expert indices.\n", "        \"\"\"\n", "        # 第一步：在门控层上输出初始的亲合度分数\n", "        scores = linear(x, self.weight)\n", "\n", "        # 第二步：依据参数选择，使用softmax或sigmoid进行归一化\n", "        if self.score_func == \"softmax\":\n", "            scores = scores.softmax(dim=-1, dtype=torch.float32)\n", "        else:\n", "            scores = scores.sigmoid()\n", "\n", "        # 将分数保存到riginal_scores中\n", "        # 为后续做准备\n", "        original_scores = scores\n", "\n", "        # 第三步：决策是否使用bias调节负载均衡\n", "        # 如果使用bias，则在亲合度分数上+bias\n", "        if self.bias is not None:\n", "            scores = scores + self.bias\n", "\n", "        # 第四步：决策是否使用分组方式进行scores打包\n", "        # 如果参数n_groups > 1，则将scores分组\n", "        if self.n_groups > 1:\n", "            scores = scores.view(x.size(0), self.n_groups, -1)\n", "\n", "            # 第4.1步：分组后是否使用bias会决定group_scores的合并状态\n", "            # 分组后，如果不使用bias，则按组进行聚合、聚合函数为max最大值\n", "            # 相当于、如果亲合度的专家在这组，那就取这组\n", "            if self.bias is None:\n", "                group_scores = scores.amax(dim=-1)\n", "            # 如果使用bias的话，亲合度是结合bias调整过的\n", "            # 为了有更大的容错空间、让更多的专家分数来决定哪个组被激活\n", "            # 这样专家不会过度集中\n", "            else:\n", "                group_scores = scores.topk(2, dim=-1)[0].sum(dim=-1)\n", "\n", "            # 第4.2步，获得分组后的每组亲合度后，对每组亲合度进行topk筛选\n", "            # 此时被选中的不再是一个专家、而是一组专家\n", "            \n", "            # 取出topk中的索引\n", "            indices = group_scores.topk(self.topk_groups, dim=-1)[1]\n", "            # 依据索引确定要掩码的部分\n", "            mask = scores.new_ones(x.size(0), self.n_groups, dtype=bool).scatter_(1, indices, False)\n", "            # 用-inf进行实际的掩码，并将结构转变为(sample, n_routed_experts)\n", "            scores = scores.masked_fill_(mask.unsqueeze(-1), float(\"-inf\")).flatten(1)\n", "\n", "        # 第五步：如果没有进行分组，则直接执行topk\n", "        indices = torch.topk(scores, self.topk, dim=-1)[1]\n", "        weights = original_scores.gather(1, indices)\n", "\n", "        # 如果是sigmoid，在topk之后还需要进行类似于softmax的归一化\n", "        if self.score_func == \"sigmoid\":\n", "            weights /= weights.sum(dim=-1, keepdim=True)\n", "\n", "        # 但归一化之后，还需要乘以一个额外的控制参数self.route_scale\n", "        weights *= self.route_scale\n", "\n", "        # 最终将返回weights权重与topk的索引\n", "        return weights.type_as(x), indices\n", "```"]}, {"cell_type": "markdown", "id": "5baef7e4-b3dc-498b-9ad3-07a87c882a8e", "metadata": {}, "source": ["> - **amax函数与topk函数的使用**"]}, {"cell_type": "code", "execution_count": null, "id": "5d337d55-978c-44da-af7b-f7050e77a54d", "metadata": {}, "outputs": [], "source": ["import torch"]}, {"cell_type": "markdown", "id": "3569d2a0-764d-491d-8caa-b97473d65bd6", "metadata": {}, "source": ["如果不加bias，则使用max对组内做聚合——"]}, {"cell_type": "code", "execution_count": 20, "id": "c6d6c8bc-8c07-4644-9694-5f5ea70df4f2", "metadata": {}, "outputs": [], "source": ["# 假设3个token，2个组，1组4个专家\n", "scores = torch.randn(size=(3,2,4))"]}, {"cell_type": "code", "execution_count": 21, "id": "a3d0b924-e068-4656-9066-2b62f87803dd", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[ 1.8303, -0.8667, -0.2941,  1.5419],\n", "         [-0.1703, -0.1111,  0.8191, -0.2934]],\n", "\n", "        [[ 0.7393, -0.7747,  0.5040, -1.9316],\n", "         [ 0.7520,  0.6761,  0.5019, -2.2859]],\n", "\n", "        [[-2.2322,  0.4963, -0.4418, -2.0716],\n", "         [ 3.1794, -0.6805, -0.2347, -0.2901]]])"]}, "execution_count": 21, "metadata": {}, "output_type": "execute_result"}], "source": ["scores"]}, {"cell_type": "code", "execution_count": 22, "id": "d464cfab-7a69-431d-a179-6ee429b0d83c", "metadata": {}, "outputs": [], "source": ["group_scores = scores.amax(dim=-1)"]}, {"cell_type": "code", "execution_count": 23, "id": "19f81ac2-9242-46c8-84e1-060ac24a9286", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([3, 2])"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["group_scores.shape #聚合之后，结构变成3个token，每个token2个分数"]}, {"cell_type": "code", "execution_count": 24, "id": "495d400c-4a98-46ca-ae5b-6dea219a8cc2", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1.8303, 0.8191],\n", "        [0.7393, 0.7520],\n", "        [0.4963, 3.1794]])"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["group_scores #取每组的最大值"]}, {"cell_type": "markdown", "id": "d49e85b4-f2e5-4e76-84c7-1c3f00766c5d", "metadata": {}, "source": ["如果加了bias，则使用topk进行筛选——"]}, {"cell_type": "code", "execution_count": 30, "id": "0ecad72a-4d2e-4654-99ba-457f246e2271", "metadata": {}, "outputs": [], "source": ["# 使用每组最高的两个值、并对这两个值求和\n", "group_scores = scores.topk(2, dim=-1)[0].sum(dim=-1)"]}, {"cell_type": "code", "execution_count": 31, "id": "38af55c4-fdbc-455a-942c-d71ab8787586", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[3.3722, 0.7080],\n", "        [1.2434, 1.4281],\n", "        [0.0544, 2.9447]])"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["group_scores"]}, {"cell_type": "code", "execution_count": 34, "id": "ad38bc68-8633-48dc-81b7-29f79ecbd431", "metadata": {}, "outputs": [{"data": {"text/plain": ["3.3722000000000003"]}, "execution_count": 34, "metadata": {}, "output_type": "execute_result"}], "source": ["1.8303 + 1.5419 #两组中的top2，进行加和就等于scores.topk(2, dim=-1)[0].sum(dim=-1)的结果"]}, {"cell_type": "code", "execution_count": 33, "id": "77b655ed-052e-48b3-be6a-2e66ed34770d", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.return_types.topk(\n", "values=tensor([[[ 1.8303,  1.5419],\n", "         [ 0.8191, -0.1111]],\n", "\n", "        [[ 0.7393,  0.5040],\n", "         [ 0.7520,  0.6761]],\n", "\n", "        [[ 0.4963, -0.4418],\n", "         [ 3.1794, -0.2347]]]),\n", "indices=tensor([[[0, 3],\n", "         [2, 1]],\n", "\n", "        [[0, 2],\n", "         [0, 1]],\n", "\n", "        [[1, 2],\n", "         [0, 2]]]))"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["scores.topk(2, dim=-1) #返回两个对象，一个是topk的值，一个是topk所在的索引"]}, {"cell_type": "markdown", "id": "487116b6-952a-4925-89b2-5cfd92a99a5f", "metadata": {}, "source": ["> - **掩码流程**"]}, {"cell_type": "code", "execution_count": 80, "id": "f6ca84c0-11a7-49aa-aa34-e1f834895e11", "metadata": {}, "outputs": [], "source": ["x = torch.randn(3,10) # 原本是10个维度\n", "scores = torch.randn(3,5,4) # 5个组，1组4个专家\n", "group_scores = torch.randn(3,5) # 假设5个组"]}, {"cell_type": "code", "execution_count": 81, "id": "078c3832-d304-41ac-856f-defede6fc703", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0.5855,  0.6637,  0.0572, -2.1959,  0.3793],\n", "        [-0.6886,  1.4708,  0.7450,  1.3572, -0.1413],\n", "        [ 1.3816,  0.8669, -0.4182,  0.4437,  0.0865]])"]}, "execution_count": 81, "metadata": {}, "output_type": "execute_result"}], "source": ["group_scores"]}, {"cell_type": "code", "execution_count": 82, "id": "f983a027-41a9-494e-90cd-c2180a37be2b", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1, 0],\n", "        [1, 3],\n", "        [0, 1]])"]}, "execution_count": 82, "metadata": {}, "output_type": "execute_result"}], "source": ["# indices = group_scores.topk(self.topk_groups, dim=-1)[1]\n", "topk_groups = 2\n", "\n", "indices = group_scores.topk(topk_groups, dim=-1)[1]\n", "indices"]}, {"cell_type": "code", "execution_count": 83, "id": "b99018ed-747b-40b3-8a62-6800b5af85c9", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1., 1., 1., 1., 1.],\n", "        [1., 1., 1., 1., 1.],\n", "        [1., 1., 1., 1., 1.]])"]}, "execution_count": 83, "metadata": {}, "output_type": "execute_result"}], "source": ["# mask = scores.new_ones(x.size(0), self.n_groups, dtype=bool).scatter_(1, indices, False)\n", "\n", "n_groups = 5\n", "\n", "# 生成 3个token、每个token 5个1的矩阵\n", "scores.new_ones(3,5)"]}, {"cell_type": "code", "execution_count": 84, "id": "9c27ae14-8e7d-4584-87f8-49918804f001", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[False, False,  True,  True,  True],\n", "        [ True, False,  True, False,  True],\n", "        [False, False,  True,  True,  True]])"]}, "execution_count": 84, "metadata": {}, "output_type": "execute_result"}], "source": ["# 按照topk输出的索引、将被选中的位置标记为false，这部分为掩码\n", "mask = scores.new_ones(3,5, dtype=bool).scatter_(1, indices, False)\n", "mask"]}, {"cell_type": "code", "execution_count": 85, "id": "92150096-2299-4083-a217-7d0b93693f17", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[False],\n", "         [False],\n", "         [ True],\n", "         [ True],\n", "         [ True]],\n", "\n", "        [[ True],\n", "         [False],\n", "         [ True],\n", "         [False],\n", "         [ True]],\n", "\n", "        [[False],\n", "         [False],\n", "         [ True],\n", "         [ True],\n", "         [ True]]])"]}, "execution_count": 85, "metadata": {}, "output_type": "execute_result"}], "source": ["#scores = scores.masked_fill_(mask.unsqueeze(-1), float(\"-inf\")).flatten(1)\n", "\n", "# 将掩码矩阵升维，增加一个维度\n", "mask.unsqueeze(-1)"]}, {"cell_type": "code", "execution_count": 87, "id": "3b516446-3db1-4b5e-9241-dc587fd11bc8", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[-1.1088,  1.8885, -2.9506, -1.6955],\n", "         [-0.2400,  1.5713,  0.0125,  0.2397],\n", "         [-0.5074,  0.8783,  1.0049, -0.9397],\n", "         [-0.2211, -0.8823, -1.4014,  1.1175],\n", "         [-1.8172, -0.4547, -0.4848, -1.2907]],\n", "\n", "        [[ 1.2011,  0.1775, -0.6158, -1.2251],\n", "         [-0.3468,  0.0886, -1.5812, -0.9783],\n", "         [ 0.1057, -0.1810,  0.1144,  0.6682],\n", "         [ 0.4399, -0.2450, -1.2659, -0.6371],\n", "         [ 0.2966, -0.7545,  0.3628,  0.4863]],\n", "\n", "        [[ 0.5299,  0.1263,  0.2177,  0.4757],\n", "         [ 1.6755,  2.5559,  0.5491, -0.2969],\n", "         [-1.3285, -2.2348,  0.2587,  1.9271],\n", "         [ 0.6726, -0.0908,  0.8956,  0.8794],\n", "         [-0.4439,  0.9272,  0.0913, -1.7637]]])"]}, "execution_count": 87, "metadata": {}, "output_type": "execute_result"}], "source": ["scores # 3个token。每个token5个组，每个组里4个专家"]}, {"cell_type": "code", "execution_count": 88, "id": "cd40a713-1dc3-42f9-8105-5b3f91591415", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[[-1.1088,  1.8885, -2.9506, -1.6955],\n", "         [-0.2400,  1.5713,  0.0125,  0.2397],\n", "         [   -inf,    -inf,    -inf,    -inf],\n", "         [   -inf,    -inf,    -inf,    -inf],\n", "         [   -inf,    -inf,    -inf,    -inf]],\n", "\n", "        [[   -inf,    -inf,    -inf,    -inf],\n", "         [-0.3468,  0.0886, -1.5812, -0.9783],\n", "         [   -inf,    -inf,    -inf,    -inf],\n", "         [ 0.4399, -0.2450, -1.2659, -0.6371],\n", "         [   -inf,    -inf,    -inf,    -inf]],\n", "\n", "        [[ 0.5299,  0.1263,  0.2177,  0.4757],\n", "         [ 1.6755,  2.5559,  0.5491, -0.2969],\n", "         [   -inf,    -inf,    -inf,    -inf],\n", "         [   -inf,    -inf,    -inf,    -inf],\n", "         [   -inf,    -inf,    -inf,    -inf]]])"]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["# 按照掩码、将没有被选中的组用inf填写\n", "scores.masked_fill_(mask.unsqueeze(-1), float(\"-inf\"))"]}, {"cell_type": "code", "execution_count": 90, "id": "a9a94b0f-dd84-47a3-849b-75c1bc135e56", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[-1.1088,  1.8885, -2.9506, -1.6955, -0.2400,  1.5713,  0.0125,  0.2397,\n", "            -inf,    -inf,    -inf,    -inf,    -inf,    -inf,    -inf,    -inf,\n", "            -inf,    -inf,    -inf,    -inf],\n", "        [   -inf,    -inf,    -inf,    -inf, -0.3468,  0.0886, -1.5812, -0.9783,\n", "            -inf,    -inf,    -inf,    -inf,  0.4399, -0.2450, -1.2659, -0.6371,\n", "            -inf,    -inf,    -inf,    -inf],\n", "        [ 0.5299,  0.1263,  0.2177,  0.4757,  1.6755,  2.5559,  0.5491, -0.2969,\n", "            -inf,    -inf,    -inf,    -inf,    -inf,    -inf,    -inf,    -inf,\n", "            -inf,    -inf,    -inf,    -inf]])"]}, "execution_count": 90, "metadata": {}, "output_type": "execute_result"}], "source": ["# 将掩码后的分数拉平、变成3个token、每个20个分数\n", "scores.flatten(1)"]}, {"cell_type": "markdown", "id": "540b1b2e-df3e-4070-b3a3-49f0f5fe35f2", "metadata": {}, "source": ["> - gather函数"]}, {"cell_type": "code", "execution_count": 97, "id": "8cef4d20-d2fc-4b38-8b5d-bc6caef9bb75", "metadata": {}, "outputs": [], "source": ["original_scores = torch.randn(3,20) # 3个token，20个专家\n", "scores = original_scores"]}, {"cell_type": "code", "execution_count": 98, "id": "273cef41-8814-4692-bce0-ce9087b355f7", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[ 0.2008,  0.2857, -0.3625,  0.7759, -0.9224, -1.8691, -1.3247,  0.0282,\n", "          0.8471,  1.1229, -0.1536,  0.6230, -1.3058,  0.3145, -0.3127, -0.2567,\n", "         -0.4445, -0.4580, -0.2824, -0.0919],\n", "        [ 0.0224,  0.2313, -0.6618, -0.2284, -0.1896,  0.4864, -1.1317, -0.9244,\n", "         -0.7046,  1.8219,  2.0350,  0.4856, -0.4754, -0.1353,  0.5474, -0.6283,\n", "         -1.8189,  0.0124,  1.7688,  1.4045],\n", "        [-0.5270,  0.9410,  1.3931, -1.4349,  1.2374, -0.1488, -1.3319, -1.4626,\n", "          0.5079,  0.4603,  0.0321,  0.8893, -0.7839, -0.0885, -1.0298,  0.6869,\n", "         -0.0148,  0.5373, -1.0058, -0.4704]])"]}, "execution_count": 98, "metadata": {}, "output_type": "execute_result"}], "source": ["scores"]}, {"cell_type": "code", "execution_count": 99, "id": "f1be880d-0cb8-4ce6-933f-a1bba6ebfd86", "metadata": {}, "outputs": [], "source": ["#执行topk，获得索引\n", "topk = 2\n", "indices = torch.topk(scores, topk, dim=-1)[1]\n", "\n", "#按照索引，取出相应的值\n", "weights = original_scores.gather(1, indices)"]}, {"cell_type": "code", "execution_count": 100, "id": "25e1a2c4-fdcb-40d2-9b05-cc0bad413af4", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[1.1229, 0.8471],\n", "        [2.0350, 1.8219],\n", "        [1.3931, 1.2374]])"]}, "execution_count": 100, "metadata": {}, "output_type": "execute_result"}], "source": ["weights"]}, {"cell_type": "markdown", "id": "3c4e624c-c335-4a67-9711-e1ac15dd2421", "metadata": {}, "source": ["- **MoE融合层**"]}, {"cell_type": "code", "execution_count": null, "id": "32e37dad-b4f2-40b3-91f6-b915fad601b1", "metadata": {}, "outputs": [], "source": ["experts (1000)\n", "\n", "0~99 gpu0 experts （0...99）\n", "100~199 gpu1       （100...199）\n", "\n", "gpu0, gpu1, 2, 3 （进程process，独立的环境）\n", "\n", "rank、local_rank、某些数据的索引、结构  ==> 以环境变量方式定义到特定gpu上\n", "\n", "model.py, pretrain.py"]}, {"cell_type": "code", "execution_count": null, "id": "ea9db34e-cd39-4126-85fc-be397bc6de91", "metadata": {}, "outputs": [], "source": ["64\n", "\n", "rank 0  start 0 * 64 = 0\n", "        end 0 + 64 = 64\n", "\n", "[0,64)\n", "\n", "rank 1  start 1 * 64 = 64\n", "        end 64 + 64 = 128\n", "\n", "[64,128)"]}, {"cell_type": "markdown", "id": "60d607b6-6b9c-4931-ba34-cfba2962f76d", "metadata": {}, "source": ["```python\n", "class MoE(nn.Module):\n", "    \"\"\"\n", "    Mixture-of-Experts (MoE) 模块\n", "\n", "    该模块实现了混合专家模型（MoE），用于动态路由输入到不同的专家网络。\n", "    \n", "    属性:\n", "        dim (int): 输入特征的维度。\n", "        n_routed_experts (int): 总共的可路由专家数量。\n", "        n_local_experts (int): 本地处理的专家数量（用于分布式训练）。\n", "        n_activated_experts (int): 每个输入样本激活的专家数量。\n", "        gate (nn.Mo<PERSON><PERSON>): 门控机制，用于决定输入应路由到哪些专家。\n", "        experts (nn.ModuleList): 专家网络的列表，其中部分由本地设备负责计算。\n", "        shared_experts (nn.<PERSON><PERSON><PERSON>): 共享专家，每个输入都会经过它进行处理。\n", "    \"\"\"\n", "    \n", "    def __init__(self, args: ModelArgs):\n", "        \"\"\"\n", "        初始化 MoE 模块\n", "\n", "        参数:\n", "            args (ModelArgs): 包含 MoE 参数的模型参数对象。\n", "        \"\"\"\n", "        super().__init__()\n", "        \n", "        # 设置输入特征维度\n", "        self.dim = args.dim\n", "        \n", "        # 确保专家总数可以被设备数量整除，以保证分布式训练的专家均匀分配\n", "        assert args.n_routed_experts % world_size == 0, f\"专家数量必须能被 world_size 整除 (world_size={world_size})\"\n", "        \n", "        # 总共可路由的专家数量\n", "        self.n_routed_experts = args.n_routed_experts\n", "        \n", "        # 计算本地设备应处理的专家数量\n", "        self.n_local_experts = args.n_routed_experts // world_size\n", "        \n", "        # 每个输入激活的专家数量\n", "        self.n_activated_experts = args.n_activated_experts\n", "\n", "        # 确定本地设备的专家索引范围（用于分布式环境）\n", "        self.experts_start_idx = rank * self.n_local_experts\n", "        self.experts_end_idx = self.experts_start_idx + self.n_local_experts\n", "\n", "        # 初始化门控机制，用于决定输入数据应路由到哪些专家\n", "        self.gate = Gate(args)\n", "\n", "        # 创建专家网络，每个专家是一个 `Expert` 模块\n", "        # 只有索引范围在本地设备负责的范围内的专家会被创建，其余位置填充 `None`\n", "        self.experts = nn.ModuleList([\n", "            Expert(args.dim, args.moe_inter_dim) if self.experts_start_idx <= i < self.experts_end_idx else None\n", "            for i in range(self.n_routed_experts)\n", "        ])\n", "        \n", "        # 初始化共享专家（全局专家），所有输入数据都会经过它处理\n", "        self.shared_experts = MLP(args.dim, args.n_shared_experts * args.moe_inter_dim)\n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"\n", "        MoE 的前向传播过程\n", "\n", "        参数:\n", "            x (torch.Tensor): 输入张量，形状 (batch_size, dim)。\n", "\n", "        返回:\n", "            torch.Tensor: 经过专家网络处理后的输出张量。\n", "        \"\"\"\n", "        \n", "        # 获取输入数据的形状，以便在计算结束后恢复形状\n", "        shape = x.size()\n", "        \n", "        # 将输入数据展平，使其形状变为 (batch_size, dim)\n", "        x = x.view(-1, self.dim)\n", "        \n", "        # 通过门控机制计算每个输入应路由到哪些专家，并获得专家的索引 `indices` 和相应的权重 `weights`\n", "        weights, indices = self.gate(x)\n", "        \n", "        # 初始化存储专家计算结果的张量，与 x 形状一致，初始值为 0\n", "        y = torch.zeros_like(x)\n", "        \n", "        # 统计每个专家被选中的次数，生成一个计数列表 `counts`\n", "        counts = torch.bincount(indices.flatten(), minlength=self.n_routed_experts).tolist()\n", "        \n", "        # 遍历本设备负责的专家（索引范围在 `experts_start_idx` 到 `experts_end_idx` 之间）\n", "        for i in range(self.experts_start_idx, self.experts_end_idx):\n", "            \n", "            # 如果该专家未被任何输入选中，则跳过计算\n", "            if counts[i] == 0:\n", "                continue\n", "            \n", "            # 取出当前专家\n", "            expert = self.experts[i]\n", "            \n", "            # 找出哪些输入样本被路由到了该专家\n", "            idx, top = torch.where(indices == i)\n", "            \n", "            # 计算选中的输入数据在该专家上的输出，并乘以相应的权重\n", "            y[idx] += expert(x[idx]) * weights[idx, top, None]\n", "\n", "        # 计算共享专家的输出\n", "        z = self.shared_experts(x)\n", "\n", "        # 如果采用分布式训练，则对 `y` 进行 all-reduce 操作，以确保不同设备上的专家计算结果可以合并\n", "        if world_size > 1:\n", "            dist.all_reduce(y)\n", "\n", "        # 返回最终的计算结果，并恢复原始输入形状\n", "        return (y + z).view(shape)\n", "```"]}, {"cell_type": "code", "execution_count": 105, "id": "f2731e4b-4dfe-4602-9530-fca93525da14", "metadata": {}, "outputs": [], "source": ["import torch"]}, {"cell_type": "code", "execution_count": 111, "id": "f7e57232-712c-44ed-b6ac-73ac4a1dfb64", "metadata": {}, "outputs": [], "source": ["#(token, topk)\n", "\n", "indices = torch.randint(low=0,high=5,size=(3,2))"]}, {"cell_type": "code", "execution_count": 112, "id": "07f7f262-d545-47cb-878f-f6867de35b45", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[2, 1],\n", "        [4, 0],\n", "        [3, 2]])"]}, "execution_count": 112, "metadata": {}, "output_type": "execute_result"}], "source": ["indices"]}, {"cell_type": "code", "execution_count": 130, "id": "bb579df9-5dfe-46ae-986f-e4fb96ea0591", "metadata": {}, "outputs": [], "source": ["idx, top = torch.where(indices == 1)"]}, {"cell_type": "code", "execution_count": 131, "id": "da1e456b-2291-48f6-a9b9-dbd6ee51d703", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0])"]}, "execution_count": 131, "metadata": {}, "output_type": "execute_result"}], "source": ["idx"]}, {"cell_type": "code", "execution_count": 132, "id": "df6530cd-e750-4692-84c7-4fe453ff51e2", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([1])"]}, "execution_count": 132, "metadata": {}, "output_type": "execute_result"}], "source": ["top"]}, {"cell_type": "code", "execution_count": 114, "id": "e555d1d3-ac7a-48ed-a378-63fa78415262", "metadata": {}, "outputs": [], "source": ["#(token, topk)\n", "\n", "weights = torch.randn((3,2))"]}, {"cell_type": "code", "execution_count": 123, "id": "98b62c8b-96ac-4216-84a3-5591e72d6841", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([1.1267, 0.7266])"]}, "execution_count": 123, "metadata": {}, "output_type": "execute_result"}], "source": ["weights[(0,1),(1,0)]"]}, {"cell_type": "code", "execution_count": 117, "id": "2c53652e-1b9e-4183-9a42-00ec1b60817b", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0.1151],\n", "        [0.1447]])"]}, "execution_count": 117, "metadata": {}, "output_type": "execute_result"}], "source": ["weights[idx, top, None]"]}, {"cell_type": "code", "execution_count": null, "id": "b0bfaa6e-8b0f-4d71-8d32-55988c0bbf35", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 1, "id": "4360edd2-1531-40c6-995b-11546b464962", "metadata": {}, "outputs": [], "source": ["import torch"]}, {"cell_type": "code", "execution_count": 41, "id": "b52e6de3-ce5e-4fd3-8b5c-a0da339273e4", "metadata": {}, "outputs": [], "source": ["x = torch.randn((3,6)) # 3个token，d_model=6"]}, {"cell_type": "code", "execution_count": 42, "id": "dc6e9d3f-781c-4cfb-94e7-31b89819d053", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[-0.7137,  0.4374,  1.1560,  0.6840,  0.1837, -0.7231],\n", "        [ 0.4997,  1.6743,  0.2551,  0.8975, -0.9503, -1.4675],\n", "        [-0.7641, -1.3975, -0.4332, -0.4213, -1.7334,  1.0643]])"]}, "execution_count": 42, "metadata": {}, "output_type": "execute_result"}], "source": ["x"]}, {"cell_type": "code", "execution_count": 18, "id": "eae31abd-06b1-410b-957c-179938090c02", "metadata": {}, "outputs": [], "source": ["#假设3个token，20个专家，选择了topk5个专家出来\n", "topk_ids = torch.randint(low=0,high=20, size=(3,5))"]}, {"cell_type": "code", "execution_count": 19, "id": "c01fa01e-03e1-4a91-ba7d-d669b98a741d", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[16,  3,  8, 16, 18],\n", "        [11,  3, 14,  3, 11],\n", "        [13, 19,  5, 16,  9]])"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["topk_ids"]}, {"cell_type": "code", "execution_count": 22, "id": "e0de0069-ace2-4113-b56c-e17e292665e6", "metadata": {}, "outputs": [], "source": ["cnts = topk_ids.new_zeros((topk_ids.shape[0], 20)) # 20个专家"]}, {"cell_type": "code", "execution_count": 23, "id": "383c63bc-e098-45ea-b292-9490fa5d3c98", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n", "        [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0],\n", "        [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0]])"]}, "execution_count": 23, "metadata": {}, "output_type": "execute_result"}], "source": ["cnts"]}, {"cell_type": "code", "execution_count": 24, "id": "4177ae32-44ee-47de-a550-d95c4d38b65c", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[0, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 1, 0],\n", "        [0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 0],\n", "        [0, 0, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 1, 0, 0, 1]])"]}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["cnts.scatter_(1, topk_ids, 1)"]}, {"cell_type": "code", "execution_count": 25, "id": "ff298670-9012-492d-82b7-90cf224a88ee", "metadata": {}, "outputs": [], "source": ["tokens_per_expert = cnts.sum(dim=0)"]}, {"cell_type": "code", "execution_count": 26, "id": "b73e47f8-aa8c-439f-bee4-7655d4600c0c", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0, 0, 0, 2, 0, 1, 0, 0, 1, 1, 0, 1, 0, 1, 1, 0, 2, 0, 1, 1])"]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["tokens_per_expert #每个专家被多少个token给选中"]}, {"cell_type": "code", "execution_count": 27, "id": "548d3a1b-aea1-4dea-91c7-c78a4495db7a", "metadata": {}, "outputs": [], "source": ["ep_size = 4\n", "\n", "tokens_per_ep_rank = tokens_per_expert.view(ep_size, -1).sum(dim=1)"]}, {"cell_type": "code", "execution_count": 28, "id": "4e11f0bd-ef42-4178-a5a0-4e86557c03c5", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([2, 3, 3, 4])"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["tokens_per_ep_rank"]}, {"cell_type": "code", "execution_count": 29, "id": "8030d5bf-64f5-4ad5-972c-50e1f3bd5e7c", "metadata": {}, "outputs": [], "source": ["tokens_per_expert_group = tokens_per_expert.new_empty(\n", "                tokens_per_expert.shape[0]\n", "            )"]}, {"cell_type": "code", "execution_count": 32, "id": "e9cd1192-d9f8-433c-b7ec-06b6c8cdcffa", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0, 0, 0, 2, 0, 1, 0, 0, 1, 1, 0, 1, 0, 1, 1, 0, 2, 0, 1, 1])"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["tokens_per_expert #20个专家，代表每个专家被多少个token给激活"]}, {"cell_type": "code", "execution_count": 30, "id": "20a150c4-bf4a-403a-bd56-f87643ee2c7f", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0])"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["tokens_per_expert_group # 全0，代表一共有多少个专家被激活"]}, {"cell_type": "markdown", "id": "708dee61-2c5d-4a80-948a-552603156637", "metadata": {}, "source": ["===="]}, {"cell_type": "code", "execution_count": 36, "id": "7d16e08c-c060-4a2f-b35e-abc225c07dbb", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[16,  3,  8, 16, 18],\n", "        [11,  3, 14,  3, 11],\n", "        [13, 19,  5, 16,  9]])"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["topk_ids"]}, {"cell_type": "code", "execution_count": 39, "id": "efa1ab67-1c3c-4852-8f4d-dd3814503371", "metadata": {}, "outputs": [], "source": ["idxs = topk_ids.view(-1).argsort() #拉平，对拉平后的序列进行排序、并返回排序后的元素在原始序列中的索引"]}, {"cell_type": "code", "execution_count": 40, "id": "18352ff0-3171-40ee-9b7e-54fff77dfaac", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([ 1,  6,  8, 12,  2, 14,  5,  9, 10,  7,  0,  3, 13,  4, 11])"]}, "execution_count": 40, "metadata": {}, "output_type": "execute_result"}], "source": ["idxs"]}, {"cell_type": "code", "execution_count": 43, "id": "c194803c-a177-46a5-aa3a-db45ee7aea5c", "metadata": {}, "outputs": [], "source": ["sorted_tokens = x[idxs // topk_ids.shape[1]]"]}, {"cell_type": "code", "execution_count": 48, "id": "745f81fb-643c-4b0b-9a44-997df621e6c2", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([[-0.7137,  0.4374,  1.1560,  0.6840,  0.1837, -0.7231],\n", "        [ 0.4997,  1.6743,  0.2551,  0.8975, -0.9503, -1.4675],\n", "        [ 0.4997,  1.6743,  0.2551,  0.8975, -0.9503, -1.4675],\n", "        [-0.7641, -1.3975, -0.4332, -0.4213, -1.7334,  1.0643],\n", "        [-0.7137,  0.4374,  1.1560,  0.6840,  0.1837, -0.7231],\n", "        [-0.7641, -1.3975, -0.4332, -0.4213, -1.7334,  1.0643],\n", "        [ 0.4997,  1.6743,  0.2551,  0.8975, -0.9503, -1.4675],\n", "        [ 0.4997,  1.6743,  0.2551,  0.8975, -0.9503, -1.4675],\n", "        [-0.7641, -1.3975, -0.4332, -0.4213, -1.7334,  1.0643],\n", "        [ 0.4997,  1.6743,  0.2551,  0.8975, -0.9503, -1.4675],\n", "        [-0.7137,  0.4374,  1.1560,  0.6840,  0.1837, -0.7231],\n", "        [-0.7137,  0.4374,  1.1560,  0.6840,  0.1837, -0.7231],\n", "        [-0.7641, -1.3975, -0.4332, -0.4213, -1.7334,  1.0643],\n", "        [-0.7137,  0.4374,  1.1560,  0.6840,  0.1837, -0.7231],\n", "        [-0.7641, -1.3975, -0.4332, -0.4213, -1.7334,  1.0643]])"]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["sorted_tokens # 取出每个专家所对应的token的值（每个token选了5个专家，一共15个）"]}, {"cell_type": "code", "execution_count": 49, "id": "83ab0d49-6ba0-41fa-b355-540510a78dec", "metadata": {}, "outputs": [], "source": ["sorted_tokens_shape = sorted_tokens.shape"]}, {"cell_type": "code", "execution_count": 50, "id": "fe78b28f-0a6b-4d72-8d4b-818dff8d32c7", "metadata": {}, "outputs": [], "source": ["gathered_tokens = sorted_tokens.new_empty(\n", "                tokens_per_expert_group.sum(dim=0).item(), sorted_tokens.shape[1]\n", "            ) # 每个专家所对应的token的值"]}, {"cell_type": "code", "execution_count": 51, "id": "f434ee24-8e50-4ee5-b77d-13579593e87a", "metadata": {}, "outputs": [{"data": {"text/plain": ["tensor([], size=(0, 6))"]}, "execution_count": 51, "metadata": {}, "output_type": "execute_result"}], "source": ["gathered_tokens # "]}, {"cell_type": "markdown", "id": "8826623d-4523-4f2b-b68f-a6754cd28523", "metadata": {}, "source": ["## 4 DeepSeek的量化技巧"]}, {"cell_type": "markdown", "id": "075c1469-34fc-4a9e-9f27-68b50875beca", "metadata": {}, "source": ["### 4.1 经典量化入门"]}, {"cell_type": "markdown", "id": "ce2b1211-148d-41c4-8192-f257560ced25", "metadata": {}, "source": ["大模型量化（Quantization）是指在神经网络推理或训练过程中，**将模型中的权重、激活值、计算精度从高精度（如FP32或BF16）转换为低精度（如INT8或INT4）**，以降低计算和存储需求，同时尽可能减少精度损失。这种技术能够显著减少模型的存储占用，提高计算效率，特别是在推理部署中尤为重要。在大模型训练或推理过程中、从高精度下调到低精度的做法就是**量化Quantization**，而从低精度上升到高精度的做法就是**解量化 Dequantization**。"]}, {"cell_type": "markdown", "id": "dd450f28-c19b-4823-8e77-900d7480b6a6", "metadata": {}, "source": ["> - **认识计算机系统中的各类精度**"]}, {"cell_type": "markdown", "id": "dcd20c5a-89c2-4099-bf51-90cc1976e5a6", "metadata": {}, "source": ["<font color=\"red\">**<center>精度是一种数值表示，它由数据可覆盖的范围大小、以及该范围内每个数据点之间的间隔大小共同决定。**"]}, {"cell_type": "markdown", "id": "bfff65b4-e347-4b87-a382-0cdb9746843c", "metadata": {}, "source": ["例如——"]}, {"cell_type": "markdown", "id": "e6911417-04be-4afa-a3d8-1c9ceeb28c36", "metadata": {}, "source": ["<center>[1,2,3,4,5] 的范围 小于 [1,2,3,4,5,6,7,8,9,10] 的范围\n", "    <br><br>\n", "<center>[1,2,3,4,5] 的间隔 大于 [1.1,1.2,1.3,1.4,1.5] 的间隔"]}, {"cell_type": "markdown", "id": "358e54ac-3061-4603-a5bf-aadc67f41476", "metadata": {}, "source": ["而在计算机数值表示中，范围由“指数位”控制、间隔由“尾数位”控制，常见的精度的位数如下 ↓"]}, {"cell_type": "markdown", "id": "e7722e0d-550c-4c12-9fb9-9c9fdbec19b6", "metadata": {}, "source": ["| 格式   | 位宽  | 指数位<br>控制范围 | 尾数位<br>控制间隔 | 范围（约）           | 最小间隔（ε）      | 占用内存（字节） |\n", "|--------|------|------|------|------------------|------------------|--------------|\n", "| FP32   | 32   | 8    | 23   | ±3.4 × 10⁻³⁸ ~ ±3.4 × 10³⁸ | ~1.2 × 10⁻⁷ | 4            |\n", "| BF16   | 16   | 8    | 7    | ±3.9 × 10⁻³⁸ ~ ±3.9 × 10³⁸ | ~3.9 × 10⁻³ | 2            |\n", "| FP16   | 16   | 5    | 10   | ±6.1 × 10⁻⁵ ~ ±6.5 × 10⁴ | ~5.96 × 10⁻⁸ | 2            |\n", "| FP8 (E5M2) | 8  | 5    | 2    | ±9.8 × 10⁻² ~ ±57344 | ~6.25 × 10⁻² | 1            |\n", "| FP8 (E4M3) | 8  | 4    | 3    | ±9.8 × 10⁻² ~ ±448 | ~1.25 × 10⁻² | 1            |\n", "| INT8   | 8    | -    | -    | -128 ~ 127       | 1                | 1            |\n", "| INT4   | 4    | -    | -    | -8 ~ 7           | 1                | 0.5          |\n", "\n", "- **指数位**（Exponent Bits）：用于表示指数大小，决定数据的动态范围。\n", "- **尾数位**（Mantissa Bits）：用于存储精度信息，尾数位越多，数值表示越精确。\n", "- **范围**：表示该精度的数值可表示的最小值到最大值。\n", "- **最小间隔（ε）**：即最小的可区分数值，通常由 `2^(-尾数位)` 决定。\n", "- **字节**：计算机通常以字节为基本存储单位，比如内存、硬盘大小都是用字节（Byte）来衡量。相对的，一个很重要的概念叫做比特（bit），比特是计算机存储的最小单位，可以表示0或者1，一般1个字节等于8个比特。一般我们认为字节代表存储空间大小、比特代表信息位数大小。"]}, {"cell_type": "markdown", "id": "400bcdef-5243-4bff-9908-c79bcd0841ec", "metadata": {}, "source": ["不难发现，一个精度可表示的范围越大，数值的动态范围就越广，数值之间的最小间隔越小（即分辨率越高），精度就越高，**如果一种精度范围又大、间隔又小，那这种精度所表示的数据就更稳定**。然而，范围大和间隔小往往难以同时实现，因为在固定的存储位宽下，控制范围和间隔精度就需要权衡。**如果一种数据格式既有很大的表示范围，又能保持极小的间隔（高精度），那么它通常需要更多的存储位数（占用更大的内存）**。"]}, {"cell_type": "markdown", "id": "0a7e3bab-1364-4ff4-a4b7-f356e70f90f1", "metadata": {}, "source": ["> - **间隔可以理解、为什么范围对精度如此重要？**"]}, {"cell_type": "markdown", "id": "05afa860-3c09-4541-966d-944293ba9bc1", "metadata": {}, "source": ["数值范围的重要性体现在避免溢出（Overflow）、欠流（Underflow）、动态范围适配等多个方面。如果数据的范围不够大、则可能在神经网络计算过程中出现众多正无穷负无穷值（+inf 或 -inf），在实际计算中，数值范围影响模型的稳定性和可表达性，而不仅仅是我们理解的“精确度”问题。\n", "\n", "虽然在神经网络迭代过程中、激活和权重一般都是较小的数值、**但是神经网络中的某些计算可能会极大程度放大数值范围**，比如softmax函数和sigmoid函数都涉及指数运算exp(x)，指数运算可能会导致数值溢出。同时，深层网络中梯度可能在层层传播中不断被放大或被缩小、最终可能超出当前精度的表示范围、引发梯度爆炸或者梯度消失现象。由于神经网络不断更新参数、某些情况下、浮点误差可能会累积、最终突破上限或者下限，导致具体数字变成nan、从而导致神经网络的迭代陷入混乱，**因此在神经网络训练过程中，精度的范围十分重要，范围越大往往神经网络越稳定**，和范围比起来，间隔相对可以牺牲。"]}, {"cell_type": "markdown", "id": "1445bfc0-c77a-4bd7-bcd5-7be157428143", "metadata": {}, "source": ["> - **理解精度与精确度之间的关系**"]}, {"cell_type": "markdown", "id": "24d6ccea-e1ae-478e-9b92-7d9134c91249", "metadata": {}, "source": ["<font color=\"red\">**<center>“精度” ≠ “精确度”，高精度不等同于更准确、或携带更多信息**"]}, {"cell_type": "markdown", "id": "2eaff657-05a5-4126-b472-15927e8fadec", "metadata": {}, "source": ["在计算机的数据表示中、更高的精度意味着更稳定、更可靠的数据表示、而不是仅仅是更准确或者携带更多信息。表面上看，FP32 拥有 23 位尾数，而 BF16 只有 7 位尾数，因此 FP32 似乎总是比 BF16 “携带更多信息”。但实际上，信息量是否更多取决于具体的计算任务，并不是绝对的。\n", "\n", "在大多数深度学习任务中，BF16 的信息已经足够，神经网络的权重和激活值往往不需要非常高的精度，只要保持一定的分辨率，计算结果不会有明显变化。**由于深度学习的训练本身就是一种近似优化（尽量求解最小值）、因此FP32 的高尾数精度很多时候并不会带来更好的训练效果，在深度学习中从FP32到BF16几乎没有信息损失**。不过，在在科学计算、金融计算、高精度仿真等场景，数值计算要求非常高，甚至 FP32 也可能不够，需要 FP64。如果计算任务对“极小变化的累积”非常敏感（如长时间积分运算），那么 FP32 确实比 BF16 携带更多的信息。\n", "\n", "在此前提下，**量化的核心是牺牲部分精度换区更快的计算速度和耕地的显存占用。** 现代 GPU/TPU 在 BF16/FP16 上有 专门优化的硬件加速单元（Tensor Cores, Matrix Cores），使得 BF16/FP16 计算比 FP32 快 2-4 倍，在现在的趋势下，H系列显卡增加了对 FP8 精度的支持、计算可以变得更快。"]}, {"cell_type": "markdown", "id": "d358a10c-78d9-4431-8b8b-007382c9ab29", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "467a7741-e9b7-4f3a-a973-4706452f7194", "metadata": {}, "source": ["> - **什么精度应该用于什么场合？**"]}, {"cell_type": "markdown", "id": "a1d29bb4-e5ed-40f3-87ec-d72c1f032e39", "metadata": {}, "source": ["- **FP32 是最占空间的，适用于最高精度的计算**。<br><br>\n", "- **FP8** 和 **INT8/INT4** 适用于推理优化，推理优化过程中权重是固定的、也无需反向传播、因此对于数据范围的要求没有那么高，尤其是大模型量化时**FP8、INT8 或 INT4 都有助于减少显存使用，提高推理速度**。<br><br>\n", "- **BF16 和 FP16 用 16 位表示，适用于混合精度计算**，尤其是在深度学习训练中广泛使用（如 **BF16 在 TPU 上常见**）。在不同的神经网络计算环节里使用恰当的精度、并且支持精度之间相互转换、这就是混合精度计算的本质。\n", "\n", "**<center>神经网络经典计算流程精度需求表** \n", "\n", "🟢 **低精度**（float16 / bfloat16 ）：一般来说、计算量很大的部分我们愿意牺牲精度换取效率\n", "\n", "🔴 **高精度**（float32）：对**稳定性**要求很高的部分、我们则会保持高精度、牺牲效率\n", "\n", "| **计算流程**                      | **精度需求**          | **原因** |\n", "|----------------------------------|---------------------|--------|\n", "| **前向传播（Forward Pass）**      | 🟢 **低精度（FP16或BF16）** | 计算量大，FP16 可以提高吞吐量并减少显存使用 |\n", "| **矩阵乘法（GEMM，线性层）**     | 🟢 **低精度（FP16或BF16）** | 计算密集型操作，FP16 提升计算效率 |\n", "| **激活函数（ReLU, GELU）**      | 🟢 **低精度（FP16或BF16）** | 计算量大，对数值精度不敏感 |\n", "| **反向传播（Backward Pass）** | 🟢 **低精度（FP16或BF16）** | 包括计算图中所有的张量（中间变量、激活值、权重等）的梯度，都可以容忍较低的精度。 |\n", "| **权重存储（Model Weights）**    | 🟢 **低精度（FP16或BF16）** | 低精度存储权重，减少显存占用 |\n", "| **权重梯度（Weight Gradient）** | 🟢 **低精度（FP16或BF16）** | 最后用于迭代的梯度，可以使用低精度 |\n", "| **损失计算（Loss Computation）** | 🔴 **高精度（FP32）** | 需要较高数值稳定性，使用 FP32 进行累积 |\n", "| **MoE 门控（MoE Gating）**        | 🔴 **高精度（FP32）** | MoE 门控机制对精度要求高 |\n", "| **注意力计算（Attention Mechanism）** | 🔴 **高精度（FP32 / BF16）** | 注意力机制对精度要求高，低精度可能不稳定 |\n", "| **Softmax / LogSoftmax**        | 🔴 **高精度（FP32）** | 低精度可能导致 underflow，使用 FP32 计算 |\n", "| **指数运算（Exp）**              | 🔴 **高精度（FP32）** | 低精度可能导致溢出或 underflow |\n", "| **归一化（BatchNorm / LayerNorm）** | 🔴 **高精度（FP32 / BF16）** | 低精度导致不稳定，保持 FP32 或 BF16 |\n"]}, {"cell_type": "markdown", "id": "9e585c9c-66b7-4793-94bd-5183cd4f81cf", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "11059bb7-8927-451c-bd68-94a14a7d98de", "metadata": {}, "source": ["> - **量化的本质是什么？**"]}, {"cell_type": "markdown", "id": "1c6aa8af-83b2-4037-8ffb-85196346e8f5", "metadata": {}, "source": ["**量化（Quantization）操作的核心就是通过一个缩放因子（Scale Factor）对原始数据进行线性变换，从而改变数据的表示范围和数值间隔，并转换成特定的数值格式**。🚀\n", "\n", "大多数时候，量化可以表示为一个**线性变换**：\n", "$$\n", "Q(x) = \\text{round} \\left( \\frac{x}{S} \\right)\n", "$$\n", "其中：\n", "- $ x $ 是原始数据（FP32 等高精度格式）。\n", "- $ S $ 是**缩放因子（Scale Factor）**，用于调整数值范围。\n", "- `round()` 是四舍五入操作，将结果转换为整数表示（如 INT8、INT4）。\n", "- $ Q(x) $ 是量化后的数据（整数格式）。\n", "\n", "然后，在使用该数据时，需要**解量化（Dequantization）**，将数据转换回近似的浮点数：\n", "$$\n", "x' = Q(x) \\times S\n", "$$\n", "其中：\n", "- $ x' $ 是近似的原始数据（由于量化误差，可能与 $ x $ 不完全相同）。\n", "\n", "在这套操作中——\n", "- **通过缩放因子 $ S $**，可以**调整数据的表示范围**，从而适配不同的数值格式。\n", "- **通过四舍五入**，可以**减少数据的比特位数**，降低存储和计算开销。\n", "- **通过整数格式计算**（如 INT8、INT4），可以加速推理并降低功耗。"]}, {"cell_type": "markdown", "id": "b534c6e8-0aad-4a2f-af82-fe7f72c9e784", "metadata": {}, "source": ["举例说明，假设我们有一个 FP32 数值：\n", "```python\n", "x = 0.15625  # 原始数据 (FP32)\n", "```\n", "我们希望将其量化为 **INT8（范围 -128 ~ 127）**：\n", "```python\n", "S = 0.01  # 设定缩放因子\n", "Q_x = round(x / S)  # 量化\n", "```\n", "计算得到：\n", "$$\n", "Q_x = \\text{round} \\left( \\frac{0.15625}{0.01} \\right) = 16\n", "$$\n", "然后，我们可以通过**解量化**恢复数据：\n", "$$\n", "x' = Q_x \\times S = 16 \\times 0.01 = 0.16\n", "$$\n", "这里，**原始数据是 0.15625，但解量化后变成 0.16，存在量化误差**。"]}, {"attachments": {}, "cell_type": "markdown", "id": "d73341a2-108b-4082-89b0-a35f1c83ae74", "metadata": {}, "source": ["---\n", "\n", "> - **量化只需要一个缩放因子就能完成吗？**"]}, {"cell_type": "markdown", "id": "92c541ab-bf09-43fb-a3af-4b47b135b948", "metadata": {}, "source": ["缩放因子是量化的核心，但只是量化中的其中一个环节。要完成一套完整的量化，至少得有如下流程——\n", "\n", "==================\n", "\n", "**（1）确定每个缩放因子所属的量化范围**\n", "\n", "与归一化一样，量化也可以被确定在“某个范围内”进行，我们可以对数据中不同的范围采取不同的量化因子。常见的范围有——\n", "\n", "- **全局范围**（<PERSON>, <PERSON>）：在整个张量上计算一个缩放因子（计算数据范围内的最小值和最大值），然后对数据进行全局量化。<br><br>\n", "- **每通道范围**（Per-Channel Quantization）：为不同通道（如 CNN 的卷积核）分别计算范围。<br><br>\n", "- **块处理**（Block-wise Processing）：是指将数据拆分成多个小块（block），然后在每个块内单独计算数值范围（Min, Max），并分别量化。\n", "\n", "==================\n", "\n", "**（2）计算或确定缩放因子 $ S $**\n", "\n", "缩放因子的确认方式有多重，其中非常核心的一种是极值确认法。假设原始数据范围是 $[x_{\\min}, x_{\\max}]$，目标是量化到整数范围 $[-128, 127]$（INT8），缩放因子计算如下：\n", "$$\n", "S = \\frac{x_{\\max} - x_{\\min}}{2^b - 1}\n", "$$\n", "其中 $ b $ 是目标精度的比特数（如 INT8 和 FP8 的 $ b=8 $）。\n", "\n", "还有更常见的最大值归一化法——\n", "\n", "$$\n", "S = \\frac{x_{\\max}}{目标精度的最大范围}\n", "$$\n", "\n", "例如，对于FP8 (E4M3) ，其表示精度的最大范围是448，所以我们可以使用$S = \\frac{X_{max}}{448}$的方式来对缩放因子进行计算。\n", "\n", "在块量化中，x_max 和 x_min 是每个块的范围，因此 每个块会有一个不同的 scale factor。\n", "\n", "==================\n", "\n", "**（3）执行量化**\n", "$$\n", "Q(x) = \\text{round} \\left( \\frac{x - Z}{S} \\right)\n", "$$\n", "\n", "其中 $ Z $ 是**零点（Zero Point）**，用于非对称量化，确保 0 可以精确表示。$Z$也可以不存在。\n", "\n", "如果是块量化，不同的块会有不同的 S 和 Z，因此 每个块的数值分布更加紧凑，量化精度更高。\n", "\n", "==================\n", "\n", "**（4）执行解量化**\n", "$$\n", "x' = Q(x) \\times S + Z\n", "$$\n", "这样，在推理时，我们可以恢复近似的原始数值。\n", "\n", "因此，基于数据的范围、缩放因子的具体计算方式，量化可以有各种不同的策略：\n", "\n", "==================\n", "\n", "| **量化类型** | **特点** | **适用场景** |\n", "|-------------|--------|------------|\n", "| **对称量化（Symmetric Quantization）** | 量化范围关于 0 对称（即 $ Z=0 $），计算简单 | 大多数权重量化 |\n", "| **非对称量化（Asymmetric Quantization）** | 允许偏移量（$ Z\\neq 0 $），适用于非对称数据 | 激活值量化 |\n", "| **动态量化（Dynamic Quantization）** | 运行时动态计算 $ S $，适用于 RNN、LSTM | NLP 模型推理 |\n", "| **逐通道量化（Per-Channel Quantization）** | 每个通道有不同的 $ S $ | CNN 权重量化 |\n", "| **逐层量化（Per-Tensor Quantization）** | 整个张量使用相同的 $ S $ | 计算简单，适用于大模型 |"]}, {"cell_type": "markdown", "id": "df43541d-a00d-4b25-b004-c9f0d612ef52", "metadata": {}, "source": ["**非常重要的块量化**——"]}, {"cell_type": "markdown", "id": "80cc890a-964f-4386-8ed2-2adddbc764c6", "metadata": {}, "source": ["如果数据分布很不均匀（比如部分数据特别大，部分数据特别小），全局 min/max 可能导致小范围数据的量化误差特别大，比如说——"]}, {"cell_type": "markdown", "id": "b1996291-184b-4603-949d-2451886cc43f", "metadata": {}, "source": ["```python\n", "[0.01, 0.02, 0.03, 1000, 1001, 1002]\n", "```"]}, {"cell_type": "markdown", "id": "d28622a4-265e-4512-a5f2-21a579a87c7a", "metadata": {}, "source": ["全局 min=0.01, max=1002，缩放因子 S = $(1002 - 0.01) / (2^8-1)$，结果就是前几个小数值都会被量化为接近为0，精度的损失非常大。块量化可以避免这个问题，因为每个 block 处理的范围更小，scale factor 更合适。\n", "\n", "并且，块量化可以并行计算，现代大部分GPU都支持 块级计算（Block-wise Computation），块量化可以利用 GPU 并行加速处理。"]}, {"cell_type": "markdown", "id": "b0bf96bf-b576-4ff8-9cc6-196b2977026e", "metadata": {}, "source": ["### 4.2 DeepSeek训练中的FP8量化处理"]}, {"cell_type": "markdown", "id": "c220e172-6867-446e-a4c6-7c7e6f2398fe", "metadata": {}, "source": ["相比起一般的量化，DeepSeek做出了如下的改动——\n", "\n", "1. **大胆并充分地使用了FP8(E4M3)精度**，具体地来说 ↓\n", "\n", "DeepSeek将传统量化方法中需要**FP32 或 BF16**计算的环节，例如**前向传播（Fprop）、反向传播中的常规梯度计算（Dgrad）、权重梯度计算（Wgrad）以及优化器状态存储**都使用了**FP8**进行计算，仅在求和操作、权重更新、以及保存 Master Weight 存储时恢复 FP32。\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/112.png)"]}, {"cell_type": "markdown", "id": "0b7bb6d4-d0d3-400b-ae02-db83612b22e8", "metadata": {}, "source": ["假设有一个两层的神经网络：\n", "$$\n", "\\begin{aligned}\n", "Y_1 = XW_1 \\\\ \\\\\n", "Y_2 = Y_1W_2 \\\\ \\\\\n", "L = \\text{Loss}(Y_2, Y_{\\text{true}})\n", "\\end{aligned}$$\n", "\n", "那在反向传播过程中，我们需要求解的是——\n", "\n", "$$\n", "\\begin{aligned}\n", "\\textcolor{blue}{\\frac{dL}{dW_2}} &= \\textcolor{red}{\\frac{dL}{dY_2}} \\cdot \\frac{dY_2}{dW_2} \\\\ \\\\\n", "\\textcolor{blue}{\\frac{dL}{dW_1}} &= \\textcolor{green}{\\frac{dL}{dY_2} \\cdot \\frac{dY_2}{dY_1}} \\cdot \\frac{dY_1}{dW_1}\n", "\\end{aligned}\n", "$$\n", "\n", "在这个过程里——\n", "\n", "- `Weight Gradient`是 <font color=\"blue\">$\\frac{dL}{dW_1}$和$\\frac{dL}{dW_2}$</font>，他们用于迭代权重，是权重上的梯度。\n", "\n", "- `Output Gradient`是<font color=\"red\">$\\frac{dL}{dY_2}$</font>，也就是损失函数直接向最终输出的$y$求导所得的梯度。\n", "\n", "- `Input Gradient`是 $\\frac{dL}{dY_1}$，它等于<font color=\"green\">$\\frac{dL}{dY_2} \\cdot \\frac{dY_2}{dY_1}$</font>，它是损失函数向前一层的$y$求解所得的梯度。\n", "\n", "在神经网络反向传播过程中，这些都是属于要计算保存的中间变量，而他们全都是求导和矩阵计算得来。其中`Weight Gradient`要用于权重更新、并且还要被保存为Master Weights。而`Output Gradient`和`Input Gradient`是为求解最终的`Weight Gradient`的中间变量。"]}, {"cell_type": "markdown", "id": "808706b3-e2bf-413a-97ef-aa0701f8df3d", "metadata": {}, "source": ["在计算input gradient时，由于<font color=\"green\">$\\frac{dY_2}{dY_1} = W2$</font>，所以有——\n", "\n", "$$\n", "\\begin{aligned}\n", "\\textcolor{blue}{\\frac{dL}{dW_1}} &= \\textcolor{green}{\\frac{dL}{dY_2} \\cdot \\frac{dY_2}{dY_1}} \\cdot \\frac{dY_1}{dW_1} \\\\ \\\\\n", "& = \\textcolor{red}{\\frac{dL}{dY_2}} \\cdot W_2 \\cdot \\frac{dY_1}{dW_1}\n", "\\end{aligned}\n", "$$\n", "\n", "同理，由于$\\frac{dL}{dW_2} = X$，所以有——\n", "\n", "$$\n", "\\begin{aligned}\n", "\\textcolor{blue}{\\frac{dL}{dW_2}} &= \\textcolor{red}{\\frac{dL}{dY_2}} \\cdot \\frac{dY_2}{dW_2} \\\\ \\\\\n", "& = \\textcolor{red}{\\frac{dL}{dY_2}} \\cdot X\n", "\\end{aligned}\n", "$$"]}, {"cell_type": "markdown", "id": "da1987b0-5491-40e0-bfab-3dda97263415", "metadata": {}, "source": ["因此你会看到`Output Gradient`经过与权重相乘得到了`Input Gradient`，而`Output Gradient`经过与`Input`相乘得到`Weight Gradient`。\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/112.png)"]}, {"attachments": {}, "cell_type": "markdown", "id": "43f1e6e5-76a7-4e77-9217-1437f18f751e", "metadata": {}, "source": ["如果对应到神经网络训练循环的各个环节，你会看到 ↓\n", "\n", "| **环节** | **常规精度** | **DeepSeek 精度** |\n", "|----------|-------------|-------------------|\n", "| **输入数据** | BF16 | <font color=\"red\">**BF16 → FP8** | \n", "| **前向传播<br><font color=\"green\">（Fprop，输出output）**| FP32/BF16 | <font color=\"red\">**FP16 → FP8 矩阵乘法 → FP32求和 → 输出BF16**|\n", "| **损失函数计算<br><font color=\"green\">（图上未展示）** | FP32 | output是BF16，一般label也是BF16<br>现代GPU计算中，BF16 x BF16的计算一般会输出FP32<br>这是一个硬件内自动的解量化过程 |\n", "| **Output Gradient计算** | FP32/BF16 | BF16 |\n", "| **Input Gradient计算<br><font color=\"green\">（Dgrad）**|FP32/BF16 | <font color=\"red\">**FP16 → FP8 矩阵乘法 → FP32求和 → 输出BF16**|\n", "| **weight Gradient计算<br><font color=\"green\">（Wgrad）**| FP32/BF16 | <font color=\"red\">**FP16 → FP8 矩阵乘法 → FP32求和 → 输出FP32**|\n", "| **梯度更新<br><font color=\"green\">(Optimizer states)** | FP32/BF16 | BF16 |\n", "| **Master Weight 存储** | FP32 | <font color=\"red\">**FP32/BF16 → FP8** |\n", "| **优化器状态存储** | FP32/BF16 | <font color=\"red\">**论文中提到可以 FP8 存储，图上未明确** |\n", "\n", "**在进行向前传播时**，权重原始是FP16或FP32，input原始是BF16，都压缩到PF8进行矩阵计算，在对不同矩阵结果进行加和时，升级到FP32。**在进行各类梯度计算时**，同样面临原始输入是BF16，Master weights甚至是FP32，但也都需要压缩至FP8进行矩阵计算，对不同矩阵结果进行加和时，升级到FP32。这样的流程最大程度实现了 ↓ \n", "\n", "✅ **更快的计算速度（FP8 计算）**  \n", "✅ **更少的显存占用（BF16 存储，FP8 计算）**  \n", "✅ **更稳定的训练精度（FP32 累加，Master Weight 保持 FP32）**\n", "\n", "可谓一举多得。"]}, {"cell_type": "markdown", "id": "d2a73431-ccbb-4113-8846-57a6909273b3", "metadata": {}, "source": ["---"]}, {"cell_type": "code", "execution_count": null, "id": "4f06158f-80f8-4818-b2a5-1dc49b8c472d", "metadata": {}, "outputs": [], "source": ["[0.01,0.5,0.9, 10**10]\n", "                488"]}, {"cell_type": "markdown", "id": "147e10c6-a2b3-41fb-8611-eebc72d5f269", "metadata": {}, "source": ["除了使用FP8之外、DeepSeek还实现了——\n", "\n", "2. **使用了更加细粒度的量化策略、用以降低FP8下的精度损失**。\n", "\n", "具体地来说，在低精度训练框架中，由于 FP8 格式的指数位数较少，其动态范围受限，溢出（overflow）和欠流（underflow）问题 是常见挑战。这会导致数据超出了 FP8 的可表示范围，变成 inf 或 NaN，或者数据太小，会被截断为0。为了应对这一问题，一种标准的做法是**将输入数据的分布对齐到 FP8 格式的可表示范围，即通过缩放输入张量的最大绝对值，使其等于 FP8 所能表示的最大值**，但这样的话就会导致**低精度训练对激活值中的异常值（outliers）高度敏感，而异常值会严重影响量化精度，导致训练不稳定**。\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/113.png)\n", "\n", "为了解决这个问题，DeepSeek实现了更细粒度的量化（FGQ），对对激活值/input数据进行1 x Nc个元素量化、对权重Weights进行Nc x Nc个元素的块量化、使得量化更合理更细腻。\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/114.png)"]}, {"cell_type": "markdown", "id": "ac4bc5b6-54a6-4e3e-a941-939500771758", "metadata": {}, "source": ["3. **累积精度策略、用以降低FP8下的精度损失**。"]}, {"cell_type": "markdown", "id": "a80f2283-ea16-474e-b89e-c2d41ec00848", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/115.png)"]}, {"cell_type": "markdown", "id": "4cf5e728-114e-43ce-bcc7-cd889389b3c4", "metadata": {}, "source": ["### 4.3 DeepSeek预测中的量化处理"]}, {"cell_type": "markdown", "id": "5b23ceed-374d-4d37-9c9b-ef76458adc0f", "metadata": {}, "source": ["令人吃惊的是，**DeepSeek推理过程使用了与训练相反的量化处理、在存储的时候采取小精度、计算的时候允许大精度，并且将默认精度调节到了bf16**。"]}, {"cell_type": "markdown", "id": "a78e0ce1-2636-4087-9d62-8c6678913336", "metadata": {}, "source": ["```python\n", "#===================\n", "# 2. 支持混合精度 + 细粒度量化操作的线性函数与线性层\n", "#===================\n", "\n", "def linear(x: torch.Tensor, weight: torch.Tensor, bias: Optional[torch.Tensor] = None) -> torch.Tensor:\n", "    \"\"\"\n", "    自定义的、能够实现FP8、BF16、FP16、FP32各种精度之间的转化和GEMM的线性运算函数\n", "    \n", "    补充：BF16和FP16的区别在于、都是16位数，但BF16是8个指数7个尾数（8E7M），FP16是5E10M，指数越大可表示的数值范围越大、尾数越大越精确。因此，BF16范围更大适用于梯度计算、激活值存储等场景、可以稳定训练；FP16更加精确、适用于权重存储等存储场景，更适合保存信息。\n", "\n", "    Args:\n", "        x (torch.Tensor): The input tensor.\n", "        weight (torch.Tensor): The weight tensor. It may be quantized and \n", "            requires dequantization for certain cases.\n", "        bias (Optional[torch.Tensor]): The bias tensor to be added. <PERSON><PERSON><PERSON> is None.\n", "\n", "    Returns:\n", "        torch.Tensor: The result of the linear transformation, which may involve \n", "        quantization-aware computations depending on the input parameters.\n", "\n", "    Notes:\n", "        - If `weight` is quantized (e.g., `element_size() > 1`), a dequantized version \n", "          is used for computation.\n", "        - If `gemm_impl == \"bf16\"`, dequantization and a `bf16` GEMM operation are applied.\n", "        - For other cases, the function applies quantization to `x` and uses `fp8_gemm` for computation.\n", "    \"\"\"\n", "    # 如果字节大于1（精度大于fp8），则应该是fp16或者fp32、直接使用非量化计算\n", "    if weight.element_size() > 1:\n", "        return F.linear(x, weight, bias)\n", "    # 如果GEMM精度是bf16，那要将权重解量化为bf16\n", "    elif gemm_impl == \"bf16\":\n", "        weight = weight_dequant(weight, weight.scale)\n", "        return F.linear(x, weight, bias)\n", "    # 如果element_size不大于1，同时gemm_impl不是bf16（也就是fp8）\n", "    # 就说明是支持fp8计算的，则无论什么数据输入都量化成fp8\n", "    # 使用fp8精度进行fp8的GEMM\n", "    else:\n", "        x, scale = act_quant(x, block_size)\n", "        y = fp8_gemm(x, scale, weight, weight.scale)\n", "        if bias is not None:\n", "            y += bias\n", "        return y\n", "\n", "class Linear(nn.Module):\n", "    \"\"\"\n", "    自定义的、能够支持FP8精度的细粒度量化（主要是创建缩放因子进行缩放）的线性层。\n", "\n", "    Args:\n", "        in_features (int): Number of input features.\n", "        out_features (int): Number of output features.\n", "        bias (bool): Whether to include a bias term. Defaults to False.\n", "        dtype (optional): Data type for the layer. Defaults to `torch.bfloat16`.\n", "    \"\"\"\n", "    dtype = torch.bfloat16\n", "\n", "    def __init__(self, in_features: int, out_features: int, bias: bool = False, dtype = None):\n", "        super().__init__()\n", "        self.in_features = in_features\n", "        self.out_features = out_features\n", "        self.weight = nn.Parameter(torch.empty(out_features, in_features, dtype=dtype or Linear.dtype))\n", "        # 如果字节 == 1，即weight精度是FP8，则执行归一化\n", "        # 将FP8权重缩放到FP8的动态范围内、则会需要对每一个“块”构建缩放因子\n", "        # 其中block_size是我们规定的“块”的大小，用下面的公式来确认缩放因子的具体数量\n", "        if self.weight.element_size() == 1:\n", "            scale_out_features = (out_features + block_size - 1) // block_size\n", "            scale_in_features = (in_features + block_size - 1) // block_size\n", "            self.weight.scale = self.scale = nn.Parameter(torch.empty(scale_out_features, scale_in_features, dtype=torch.float32))\n", "        else:\n", "            #如果不是fp8，则直接将scale设置为None来表示该参数不存在\n", "            self.register_parameter(\"scale\", None)\n", "        if bias:\n", "            self.bias = nn.Parameter(torch.empty(self.part_out_features))\n", "        else:\n", "            self.register_parameter(\"bias\", None)\n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"\n", "        Forward pass for the custom linear layer.\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor.\n", "\n", "        Returns:\n", "            torch.Tensor: Transformed tensor after linear computation.\n", "        \"\"\"\n", "        return linear(x, self.weight, self.bias)\n", "```"]}, {"cell_type": "markdown", "id": "f535c2e0-5141-487b-b7fd-e686d6c20f03", "metadata": {}, "source": ["在开源的DeepSeek量化kernels脚本中，使用 **Triton** 进行高效的 **FP8 量化和 GEMM（矩阵乘法）**。Triton 是由 OpenAI 开发的一种高效的 GPU 编程语言和编译器，专为深度学习计算优化设计。它允许研究人员和工程师使用 Python 编写自定义 GPU 内核，而无需深入了解 CUDA 或手写低级 GPU 代码。现在 Triton 常常被用于加速矩阵计算、量化、Transformer 计算加速等深度学习任务，并且已被 OpenAI 和多个研究机构用于优化大规模 AI 训练和推理。遗憾的是，Triton只能在linux系统中运行，对windows和Mac的Cen，专为深度学习计算优化设计。它允许研究人员和工程师使用 Python 编写自定义 GPU 内核，而无需深入了解 CUDA 或手写低级 GPU 代码。现在 Triton 常常被用于加速矩阵计算、量化、Transformer 计算加速等深度学习任务，并且已被 OpenAI 和多个研究机构用于优化大规模 AI 训练和推理。**遗憾的是，Triton只能在linux系统中运行，对windows和centos系统不兼容**。\n", "\n", "在这次脚本中、主要包括以下 **三类量化相关的函数**：\n", "\n", "1. **`act_quant` (激活量化)**：针对激活值进行的量化、但不一定是量化到fp8本身\n", "2. **`weight_dequant` (权重解量化)**：针对权重进行的解量化、可能解量化到bf16或者fp32\n", "3. **`fp8_gemm` (使用量化 FP8 进行矩阵乘法)**"]}, {"cell_type": "markdown", "id": "5d9285b9-54fb-4bab-9e89-d7d30cfda492", "metadata": {}, "source": ["- **激活值量化**"]}, {"cell_type": "markdown", "id": "36363502-bfc9-40b7-ae88-2e91da4131ac", "metadata": {}, "source": ["```python\n", "import triton\n", "import triton.language as tl\n", "\n", "def act_quant_kernel(x_ptr, y_ptr, s_ptr, BLOCK_SIZE: tl.constexpr):\n", "    \"\"\"\n", "    该函数实现了 **激活值的量化（Activation Quantization）**，用于将输入张量 `x_ptr` 进行量化，\n", "    并将量化后的结果存储到 `y_ptr`，同时计算并存储缩放因子 `s_ptr`。\n", "\n", "    **量化原理**：\n", "    1. 计算当前块（block）内数据的最大绝对值 `max(abs(x))`\n", "    2. 计算缩放因子 `s = max(abs(x)) / 448`，使得 FP8 数据范围对齐 FP8 格式可表示的最大值\n", "    3. 使用缩放因子 `s` 对输入 `x` 进行归一化，转换为 FP8 格式\n", "    4. 将量化后的值存储到 `y_ptr`，缩放因子存储到 `s_ptr`\n", "\n", "    **参数说明**：\n", "    - `x_ptr (triton.Pointer)`: 指向输入张量的指针，包含需要量化的数据\n", "    - `y_ptr (triton.Pointer)`: 指向量化后数据的存储位置\n", "    - `s_ptr (triton.Pointer)`: 指向存储缩放因子的内存地址\n", "    - `BLOCK_SIZE (tl.constexpr)`: 每个 block 处理的数据大小\n", "\n", "    **返回值**：\n", "    - 该函数无返回值，量化后的数据和缩放因子直接存储在 `y_ptr` 和 `s_ptr` 中\n", "    \"\"\"\n", "\n", "    # 获取当前 block（程序实例）的 ID\n", "    pid = tl.program_id(axis=0)\n", "\n", "    # 计算该 block 负责的数据偏移量\n", "    offs = pid * BLOCK_SIZE + tl.arange(0, BLOCK_SIZE)\n", "\n", "    # 从输入指针 `x_ptr` 读取数据，并转换为 float32 进行计算\n", "    x = tl.load(x_ptr + offs).to(tl.float32)\n", "\n", "    # 计算该 block 内的最大绝对值（max(|x|)）\n", "    s = tl.max(tl.abs(x)) / 448.  # 448 代表 FP8 最大可表示值，用于缩放数据到 FP8 范围\n", "\n", "    # 归一化数据（x / s），将数据缩放到 FP8 量化范围\n", "    y = x / s\n", "\n", "    # 将数据转换为目标存储格式（FP8 或其他格式）\n", "    y = y.to(y_ptr.dtype.element_ty)\n", "\n", "    # 存储量化后的数据到 `y_ptr`\n", "    tl.store(y_ptr + offs, y)\n", "\n", "    # 存储缩放因子到 `s_ptr`\n", "    tl.store(s_ptr + pid, s)\n", "```"]}, {"cell_type": "markdown", "id": "ef3201e7-c57d-4ec6-970f-2eec2bf658d6", "metadata": {}, "source": ["- **权重解量化**"]}, {"cell_type": "markdown", "id": "aed03f0c-cca4-4dd8-ba1f-f53c6029b019", "metadata": {}, "source": ["```python\n", "import triton\n", "import triton.language as tl\n", "import torch\n", "\n", "@triton.jit\n", "def weight_dequant_kernel(x_ptr, s_ptr, y_ptr, M, N, BLOCK_SIZE: tl.constexpr):\n", "    \"\"\"\n", "    该函数用于 **对量化后的权重进行解量化（Dequantization）**，根据提供的缩放因子 `s_ptr` 还原 FP8 存储的权重，并将解量化后的权重存入 `y_ptr`。\n", "\n", "    **解量化原理**：\n", "    - 量化的权重通常采用 **FP8 格式存储**，存储时会使用一个缩放因子 `s` 来对数据进行缩放：\n", "      $$\n", "      x_{\\text{quantized}} = \\frac{x}{s}\n", "      $$\n", "    - **解量化** 的过程就是将 FP8 恢复到更高精度格式（如 FP16 或 FP32）：\n", "      $$\n", "      x_{\\text{dequantized}} = x_{\\text{quantized}} \\times s\n", "      $$\n", "\n", "    **参数说明**：\n", "    - `x_ptr (tl.pointer)`: 指向量化权重张量的指针（FP8 格式存储的权重）。\n", "    - `s_ptr (tl.pointer)`: 指向缩放因子的指针（每个 block 计算时会有对应的 scale factor）。\n", "    - `y_ptr (tl.pointer)`: 指向存储解量化权重的缓冲区（最终存为 FP16 或 FP32）。\n", "    - `M (int)`: 权重矩阵的行数。\n", "    - `N (int)`: 权重矩阵的列数。\n", "    - `BLOCK_SIZE (tl.constexpr)`: 计算块的大小（用于优化计算）。\n", "\n", "    **返回值**：\n", "    - 该函数无返回值，解量化后的数据存储在 `y_ptr` 中。\n", "    \"\"\"\n", "\n", "    # 获取当前计算 block 在 M 方向（行）和 N 方向（列）的 ID\n", "    pid_m = tl.program_id(axis=0)\n", "    pid_n = tl.program_id(axis=1)\n", "\n", "    # 计算需要多少个 block 来覆盖整个 N 维度\n", "    n = tl.cdiv(N, BLOCK_SIZE)\n", "\n", "    # 计算当前 block 在 M 和 N 方向上的数据偏移量\n", "    offs_m = pid_m * BLOCK_SIZE + tl.arange(0, BLOCK_SIZE)\n", "    offs_n = pid_n * BLOCK_SIZE + tl.arange(0, BLOCK_SIZE)\n", "\n", "    # 计算全局索引（即在整个矩阵中的位置）\n", "    offs = offs_m[:, None] * N + offs_n[None, :]\n", "\n", "    # 创建掩码，确保不会读取超出矩阵边界的无效数据\n", "    mask = (offs_m[:, None] < M) & (offs_n[None, :] < N)\n", "\n", "    # 从量化权重张量 `x_ptr` 读取数据，并转换为 float32 进行计算\n", "    x = tl.load(x_ptr + offs, mask=mask).to(tl.float32)\n", "\n", "    # 从 `s_ptr` 读取当前 block 对应的 scale factor\n", "    s = tl.load(s_ptr + pid_m * n + pid_n)\n", "\n", "    # 执行解量化计算：FP8 还原为 FP32\n", "    y = x * s\n", "\n", "    # 将解量化后的数据存储到 `y_ptr`\n", "    tl.store(y_ptr + offs, y, mask=mask)\n", "\n", "\n", "def weight_dequant(x: torch.Tensor, s: torch.Tensor, block_size: int = 128) -> torch.Tensor:\n", "    \"\"\"\n", "    该函数用于 **对量化后的权重进行解量化**，从 FP8 还原到更高精度格式（FP16/FP32）。\n", "\n", "    **工作流程**：\n", "    - 量化权重 `x` 通过 `s` 进行解量化（FP8 还原为 FP16/FP32）。\n", "    - 采用 Triton 并行计算，实现高效的解量化运算。\n", "\n", "    **参数说明**：\n", "    - `x (torch.Tensor)`: 量化后的权重张量，形状 `(M, N)`，数据类型通常为 FP8。\n", "    - `s (torch.Tensor)`: 缩放因子张量，形状 `(M, N)`，用于恢复原始权重。\n", "    - `block_size (int, optional)`: 计算块大小，默认值为 `128`。\n", "\n", "    **返回值**：\n", "    - `torch.Tensor`: 解量化后的权重张量，与 `x` 形状相同，但存储精度更高（FP16/FP32）。\n", "\n", "    **可能的错误**：\n", "    - 如果 `x` 或 `s` 不是连续存储的张量，会触发 `AssertionError`。\n", "    - 如果 `x` 或 `s` 不是 2 维矩阵，也会触发 `AssertionError`。\n", "    \"\"\"\n", "\n", "    # 确保 `x` 和 `s` 是连续存储的\n", "    assert x.is_contiguous() and s.is_contiguous()\n", "    # 确保 `x` 和 `s` 都是二维张量\n", "    assert x.dim() == 2 and s.dim() == 2\n", "\n", "    # 获取权重矩阵的行数和列数\n", "    M, N = x.size()\n", "\n", "    # 创建一个空的 tensor，用于存储解量化后的权重\n", "    y = torch.empty_like(x, dtype=torch.get_default_dtype())\n", "\n", "    # 计算网格（grid）大小，确定有多少个 block 需要计算\n", "    grid = lambda meta: (triton.cdiv(M, meta['BLOCK_SIZE']), triton.cdiv(N, meta['BLOCK_SIZE']))\n", "\n", "    # 调用 Triton 核函数进行解量化计算\n", "    weight_dequant_kernel[grid](x, s, y, M, N, BLOCK_SIZE=block_size)\n", "\n", "    return y\n", "```"]}, {"cell_type": "markdown", "id": "2ac2b78a-88d0-44d7-b7bb-3041f5e5f97b", "metadata": {}, "source": ["- **FP8矩阵的实现**"]}, {"cell_type": "markdown", "id": "84106034-c2e7-4fc1-b33f-019924aa652b", "metadata": {}, "source": ["```python\n", "import triton\n", "import triton.language as tl\n", "import torch\n", "\n", "# =============================\n", "# FP8 量化矩阵乘法 (FP8 GEMM)\n", "# =============================\n", "\n", "# 预定义多个 `block_size` 配置，以支持自动调优（auto-tuning）\n", "# Triton 可以在这些配置中选择最优的计算块大小，提高计算性能\n", "fp8_gemm_configs = [\n", "    Config({'BLOCK_SIZE_M': block_m, 'BLOCK_SIZE_N': block_n, 'BLOCK_SIZE_K': 128}, num_stages=num_stages, num_warps=8)\n", "    for block_m in [16, 32, 64]  # 行块大小\n", "    for block_n in [32, 64, 128]  # 列块大小\n", "    for num_stages in [3, 4, 5, 6]  # 计算流水线的阶段数\n", "]\n", "\n", "@triton.autotune(configs=fp8_gemm_configs, key=['N', 'K'])\n", "@triton.jit\n", "def fp8_gemm_kernel(a_ptr, b_ptr, c_ptr,\n", "                    a_s_ptr, b_s_ptr,\n", "                    M, N: tl.constexpr, K: tl.constexpr,\n", "                    BLOCK_SIZE_M: tl.constexpr,\n", "                    BLOCK_SIZE_N: tl.constexpr,\n", "                    BLOCK_SIZE_K: tl.constexpr):\n", "    \"\"\"\n", "    该函数实现 **FP8 量化矩阵乘法（GEMM）**，同时应用了缩放因子（scaling factors）。\n", "    \n", "    **计算流程**：\n", "    1. 量化数据 `a` 和 `b` 以 FP8 形式存储，并且具有各自的缩放因子 `a_s` 和 `b_s`。\n", "    2. 进行 **块级矩阵乘法**（block-wise matrix multiplication），在 FP8 计算后，累加时转换为 FP32。\n", "    3. 使用 `a_s` 和 `b_s` 进行 **缩放因子恢复**，确保计算稳定性。\n", "    4. 计算结果 `c` 存储到 FP32，以保证后续计算精度。\n", "\n", "    **参数说明**：\n", "    - `a_ptr (tl.tensor)`: 指向第一个输入矩阵 `A` 的指针（FP8）。\n", "    - `b_ptr (tl.tensor)`: 指向第二个输入矩阵 `B` 的指针（FP8）。\n", "    - `c_ptr (tl.tensor)`: 指向输出矩阵 `C` 的指针（存储 FP32）。\n", "    - `a_s_ptr (tl.tensor)`: `A` 的缩放因子（FP32）。\n", "    - `b_s_ptr (tl.tensor)`: `B` 的缩放因子（FP32）。\n", "    - `M (int)`: `A` 和 `C` 的行数。\n", "    - `N (tl.constexpr)`: `B` 和 `C` 的列数。\n", "    - `K (tl.constexpr)`: `A` 的列数，同时也是 `B` 的行数。\n", "    - `BLOCK_SIZE_M (tl.constexpr)`: M 方向计算的 block 大小。\n", "    - `BLOCK_SIZE_N (tl.constexpr)`: N 方向计算的 block 大小。\n", "    - `BLOCK_SIZE_K (tl.constexpr)`: K 方向计算的 block 大小（默认 128）。\n", "\n", "    **返回值**：\n", "    - 计算结果存储在 `c_ptr`，数据格式为 FP32。\n", "    \"\"\"\n", "\n", "    # 获取当前块（block）在 M 方向和 N 方向的 ID\n", "    pid_m = tl.program_id(axis=0)\n", "    pid_n = tl.program_id(axis=1)\n", "\n", "    # 计算 K 方向的 block 数量\n", "    k = tl.cdiv(K, BLOCK_SIZE_K)\n", "\n", "    # 计算当前 block 在 M 和 N 方向上的索引偏移\n", "    offs_m = (pid_m * BLOCK_SIZE_M + tl.arange(0, BLOCK_SIZE_M)) % M\n", "    offs_n = (pid_n * BLOCK_SIZE_N + tl.arange(0, BLOCK_SIZE_N)) % N\n", "    offs_k = tl.arange(0, BLOCK_SIZE_K)\n", "\n", "    # 计算 `A` 和 `B` 在 FP8 存储中的指针索引\n", "    a_ptrs = a_ptr + offs_m[:, None] * K + offs_k[None, :]\n", "    b_ptrs = b_ptr + offs_n[None, :] * K + offs_k[:, None]\n", "\n", "    # 计算缩放因子的索引\n", "    a_s_ptrs = a_s_ptr + offs_m * k\n", "    b_s_ptrs = b_s_ptr + (offs_n // BLOCK_SIZE_K) * k\n", "\n", "    # 初始化 FP32 累加器 (accumulator) 用于存储计算结果\n", "    accumulator = tl.zeros((BLOCK_SIZE_M, BLOCK_SIZE_N), dtype=tl.float32)\n", "\n", "    # 使用 FP8 数据计算矩阵乘法（逐步累加 FP8 计算结果）\n", "    for i in range(k):\n", "        # 读取 FP8 量化数据，并转换为 FP32 计算\n", "        a = tl.load(a_ptrs, mask=offs_k[None, :] < K - i * BLOCK_SIZE_K, other=0.0)\n", "        b = tl.load(b_ptrs, mask=offs_k[:, None] < K - i * BLOCK_SIZE_K, other=0.0)\n", "\n", "        # 读取 FP8 缩放因子\n", "        a_s = tl.load(a_s_ptrs)\n", "        b_s = tl.load(b_s_ptrs)\n", "\n", "        # 进行 FP8 矩阵乘法计算，累加到 FP32\n", "        accumulator += tl.dot(a, b) * a_s[:, None] * b_s[None, :]\n", "\n", "        # 更新指针，读取下一个 block\n", "        a_ptrs += BLOCK_SIZE_K\n", "        b_ptrs += BLOCK_SIZE_K\n", "        a_s_ptrs += 1\n", "        b_s_ptrs += 1\n", "\n", "    # 计算结果转换为 FP32\n", "    c = accumulator.to(c_ptr.dtype.element_ty)\n", "\n", "    # 计算存储索引\n", "    offs_m = pid_m * BLOCK_SIZE_M + tl.arange(0, BLOCK_SIZE_M)\n", "    offs_n = pid_n * BLOCK_SIZE_N + tl.arange(0, BLOCK_SIZE_N)\n", "    c_ptrs = c_ptr + offs_m[:, None] * N + offs_n[None, :]\n", "\n", "    # 存储计算结果到 `c_ptr`，确保不会越界\n", "    mask = (offs_m[:, None] < M) & (offs_n[None, :] < N)\n", "    tl.store(c_ptrs, c, mask=mask)\n", "\n", "\n", "def fp8_gemm(a: torch.Tensor, a_s: torch.Tensor, b: torch.Tensor, b_s: torch.Tensor):\n", "    \"\"\"\n", "    使用 FP8 进行矩阵乘法计算（GEMM），并自动转换为 FP32 以减少数值误差。\n", "\n", "    **计算流程**：\n", "    1. `a` 和 `b` 是 **已量化的 FP8 数据**，其缩放因子分别为 `a_s` 和 `b_s`。\n", "    2. 在 **Triton 核函数（fp8_gemm_kernel）** 内执行 FP8 矩阵乘法计算，并在累加时转换为 FP32。\n", "    3. 计算结果返回一个 **FP32 矩阵**，用于后续计算。\n", "\n", "    **参数说明**：\n", "    - `a (torch.Tensor)`: 已量化的 FP8 矩阵 `A`。\n", "    - `a_s (torch.Tensor)`: `A` 的缩放因子（FP32）。\n", "    - `b (torch.Tensor)`: 已量化的 FP8 矩阵 `B`。\n", "    - `b_s (torch.Tensor)`: `B` 的缩放因子（FP32）。\n", "\n", "    **返回值**：\n", "    - `torch.Tensor`: 计算结果 `C`，格式为 FP32。\n", "    \"\"\"\n", "\n", "    # 确保数据是连续存储的，以优化计算性能\n", "    assert a.is_contiguous() and b.is_contiguous()\n", "    assert a_s.is_contiguous() and b_s.is_contiguous()\n", "\n", "    # 获取矩阵的形状\n", "    K = a.size(-1)\n", "    M = a.numel() // K\n", "    N = b.size(0)\n", "\n", "    # 创建 FP32 输出矩阵 `c`\n", "    c = a.new_empty(*a.size()[:-1], N, dtype=torch.get_default_dtype())\n", "\n", "    # 计算网格（grid）大小，确定多少个 block 进行计算\n", "    grid = lambda META: (triton.cdiv(M, META['BLOCK_SIZE_M']), triton.cdiv(N, META['BLOCK_SIZE_N']))\n", "\n", "    # 调用 Triton 核函数进行 FP8 矩阵乘法计算\n", "    fp8_gemm_kernel[grid](a, b, c, a_s, b_s, M, N, K)\n", "\n", "    return c\n", "```"]}, {"cell_type": "markdown", "id": "156a602a-5e8f-4428-9ace-0ee2b38b6224", "metadata": {}, "source": ["## 5 DeepSeek中的张量并行"]}, {"cell_type": "code", "execution_count": null, "id": "732eb26e-9ac6-491d-a2ab-55e622f7c5a4", "metadata": {}, "outputs": [], "source": ["DP --> data 大, model 小\n", "TP --> data 一致、model不一致（weights）  ==> sum\n", "       data 不一样，model不一致  ==> concat\n", "EP --> 64个专家并行、一致的数据不同的模型、测试/推理的时候会使用不一致的数据\n", "\n", "embedding层实现张量并行\n", "\n", "Pipeline"]}, {"cell_type": "markdown", "id": "0620eb56-4180-4ef4-a3a0-859437dd0254", "metadata": {}, "source": ["### 5.1 遵循张量并行的Embedding层"]}, {"cell_type": "markdown", "id": "00cb96dc-a3f2-4eb1-96d1-480751f051af", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/85.png)\n", "\n", "而DeepSeek实现的是——\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/86.png)"]}, {"cell_type": "markdown", "id": "b04ee146-d2ba-4585-a707-7daab27b52d0", "metadata": {}, "source": ["```python\n", "class ParallelEmbedding(nn.Module):\n", "    \"\"\"\n", "    Embedding layer with parallelism support across distributed processes.\n", "\n", "    Args:\n", "        vocab_size (int): Vocabulary size.\n", "        dim (int): Embedding dimension.\n", "    \"\"\"\n", "    def __init__(self, vocab_size: int, dim: int):\n", "        super().__init__()\n", "        self.vocab_size = vocab_size\n", "        self.dim = dim\n", "\n", "        #将需要向量化的token分到不同GPU上\n", "        assert vocab_size % world_size == 0, f\"Vocabulary size must be divisible by world size (world_size={world_size})\"\n", "        self.part_vocab_size = (vocab_size // world_size)\n", "\n", "        #划分为 [0,500],[500,1000],[1000,1500]这样的区间\n", "        self.vocab_start_idx = rank * self.part_vocab_size\n", "        self.vocab_end_idx = self.vocab_start_idx + self.part_vocab_size\n", "\n", "        #embedding层的权重等于一个GPU上的vocab_size乘以相应的权重\n", "        self.weight = nn.Parameter(torch.empty(self.part_vocab_size, self.dim))\n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"\n", "        接收段落/句子中的所有token、并依赖索引进行token分割和调控。\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor containing token indices.\n", "\n", "        Returns:\n", "            torch.Tensor: Embedded representations.\n", "\n", "        Raises:\n", "            ValueError: If `world_size` is not defined.\n", "        \"\"\"\n", "\n", "        #设置特殊的掩码、将不属于当前GPU的token给掩盖住\n", "        #竖线表示 or \n", "        if world_size > 1:\n", "            # 求解掩码的索引\n", "            mask = (x < self.vocab_start_idx) | (x >= self.vocab_end_idx)\n", "            #化全局索引为局部索引\n", "            x = x - self.vocab_start_idx\n", "            # 对x施加掩码\n", "            x[mask] = 0\n", "        y = F.embedding(x, self.weight)\n", "        if world_size > 1:\n", "            # 对向量再次进行掩码\n", "            y[mask] = 0\n", "            #聚合所有GPU上的embedding、默认加和同步\n", "            dist.all_reduce(y)\n", "        return y\n", "```"]}, {"cell_type": "markdown", "id": "6b647621-2e47-4c32-88fb-02106f54635f", "metadata": {}, "source": ["### 5.2 支持分布式并行的线性层"]}, {"cell_type": "markdown", "id": "2c5cc795-ae9a-4fe5-b86c-08a578a7a594", "metadata": {}, "source": ["```python\n", "#===================\n", "# 3. 支持分布式并行（张量并行）的线性层\n", "#===================\n", "\n", "class ColumnParallelLinear(Linear):\n", "    \"\"\"\n", "    具有列并行功能的线性层，将输出的features分布到不同的进程（GPU）上完成。\n", "\n", "    Args:\n", "        in_features (int): Number of input features.\n", "        out_features (int): Total number of output features.\n", "        bias (bool): Whether to include a bias term. Defaults to False.\n", "        dtype (optional): Data type for the layer. Defaults to `torch.bfloat16`.\n", "    \"\"\"\n", "    def __init__(self, in_features: int, out_features: int, bias: bool = False, dtype = None):\n", "        assert out_features % world_size == 0, f\"Output features must be divisible by world size (world_size={world_size})\"\n", "\n", "        #将输出的features按照world_size整除\n", "        self.part_out_features = out_features // world_size\n", "        #在定义类的时候，将整除完的part_out_features作为输出的特征数\n", "        super().__init__(in_features, self.part_out_features, bias, dtype)\n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"\n", "        Forward pass for column parallel linear layer.\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor.\n", "\n", "        Returns:\n", "            torch.Tensor: Transformed tensor with column-parallel computation.\n", "        \"\"\"\n", "        y = linear(x, self.weight, self.bias)\n", "        return y\n", "\n", "\n", "class RowParallelLinear(Linear):\n", "    \"\"\"\n", "    具有行并行功能的线性层，将输入的features分布到不同的进程（GPU）上完成。\n", "\n", "    Args:\n", "        in_features (int): Total number of input features.\n", "        out_features (int): Number of output features.\n", "        bias (bool): Whether to include a bias term. Defaults to False.\n", "        dtype (optional): Data type for the layer. Defaults to `torch.bfloat16`.\n", "    \"\"\"\n", "    def __init__(self, in_features: int, out_features: int, bias: bool = False, dtype = None):\n", "        assert in_features % world_size == 0, f\"Input features must be divisible by world size (world_size={world_size})\"\n", "        self.part_in_features = in_features // world_size\n", "        super().__init__(self.part_in_features, out_features, bias, dtype)\n", "\n", "    def forward(self, x: torch.Tensor) -> torch.Tensor:\n", "        \"\"\"\n", "        Forward pass for row parallel linear layer.\n", "\n", "        Args:\n", "            x (torch.Tensor): Input tensor.\n", "\n", "        Returns:\n", "            torch.Tensor: Transformed tensor with row-parallel computation.\n", "        \"\"\"\n", "        y = linear(x, self.weight)\n", "\n", "        #如果是多线程运行，还需要进行all_reduce聚合，并加上偏置\n", "        if world_size > 1:\n", "            dist.all_reduce(y)\n", "        if self.bias is not None:\n", "            y += self.bias\n", "        return y\n", "```"]}, {"cell_type": "markdown", "id": "f61b3948-1410-4721-bed1-cd247305a5ce", "metadata": {}, "source": ["### 5.3 DualPipe管道并行技术"]}, {"cell_type": "markdown", "id": "b9aaeb83-154b-4dc9-b33a-b99af78a22eb", "metadata": {}, "source": ["在 深度学习 的 分布式训练 中，管道并行（Pipeline Parallelism） 是一种将**神经网络不同层**划分到**多个 GPU 设备**，以加速训练的技术。每个 GPU 负责一部分网络层的计算、其中数据像“流水线”一样流动，一个批次完成前向传播（Forward）后，下一批次才能开始计算。但是，传统的 Pipeline 并行 方式有个大问题：就是前向传播完成后才能进行反向传播，导致 GPU 在前向和反向之间会有大量空闲时间（低 GPU 利用率）。"]}, {"cell_type": "markdown", "id": "426af301-e5d0-44a3-a614-43354899938a", "metadata": {}, "source": ["| **并行方式** | **适用场景** | **显存占用** | **计算通信比** | **计算任务拆分** | **适合大模型训练** |\n", "|------------|-----------|------------|------------|-------------|---------------|\n", "| **数据并行（DP）** | 适用于小模型、大数据 | 高 | 低 | **按数据拆分** | ❌ 受限于单卡显存 |\n", "| **张量并行（TP）** | 适用于 Transformer | 中 | 高 | **按矩阵拆分** | ✅ 可用于大模型 |\n", "| **专家并行（EP）** | 适用于 Mixture of Experts | 低 | 高 | **按专家层拆分** | ✅ 适用于 MoE 训练 |\n", "| **管道并行（PP）** | 适用于极大模型 | 低 | 低 | **按层拆分** | ✅ 适用于 GPT-4, DeepSeek |\n", "\n", "- **为什么管道并行比张量并行、数据并行更适合超大模型？**\n", "\n", "1. **数据并行（DP）受限于单卡显存**<br><br>\n", "   - 每个 GPU 都需要存完整的模型，无法训练 GPT-4 这样的大模型。\n", "<br><br>\n", "2. **张量并行（TP）通信开销较大**<br><br>\n", "   - 在 **多机（multi-node）情况下**，大量的 **All-Gather、Reduce-Scatter** 可能成为瓶颈。\n", "<br><br>\n", "3. **管道并行（PP）适用于超大模型**<br><br>\n", "   - **每个 GPU 只存一部分模型层**，可以训练 **超过单卡显存的超大模型**。\n", "   - **只需要传递激活值，而不是整个梯度矩阵，通信开销低**。\n", "   - **结合 1F1B、ZB1P、DualPipe，可以优化计算效率**。"]}, {"cell_type": "markdown", "id": "f7a1f966-89ec-471f-a902-929b05fccbcb", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/116.png)"]}, {"cell_type": "markdown", "id": "f5c6bf35-72a1-48d3-a021-d35147155dc0", "metadata": {}, "source": ["- **1F1B（One Forward One Backward）**：1F1B 是最常见的 pipeline 训练方式，交替执行 Forward（前向传播）和 Backward（反向传播），保证计算流程合理。\n", "\n", "> - **第一步**：设备 1 计算 **第 1 层的 Forward**，然后传给设备 2，继续计算 **第 2 层的 Forward**，依次类推。\n", ">   \n", "> - **第二步**：当设备 4 计算完所有 Forward 后，开始进行 **反向传播（Backward）**，梯度从设备 4 传播回设备 1。\n", "\n", "在这种并行方式中，设备 1 先计算 Forward **然后等待**，直到设备 4 开始反向传播，才有计算任务。前向计算（蓝色）和反向计算（橙色）互相交替，但是 **设备会有空闲时间**（灰色）。优化器（Optimizer Step）必须等所有梯度都传回后才能执行。\n", "\n", "这样比传统 pipeline 并行更高效，因为前向和反向交错执行，但是设备利用率仍然较低，因为大部分时间设备都在等待前后计算同步。  "]}, {"cell_type": "markdown", "id": "e7caf72d-d458-40f4-a04c-800dca03838d", "metadata": {}, "source": ["- **ZB1P（Zero Bubble 1F1B）**：1F1B 的改进版，减少 **Idle 时间（空闲时间）**，其中Forward、Backward 和权重梯度计算（Weight Gradient）可以部分重叠。设备利用率提高，避免 GPU 过长时间等待。\n", "\n", "> - **第 1 设备（Device 1）开始计算 Forward**，然后传输到 **Device 2、Device 3...**。\n", "> \n", "> - **当最后一个设备（Device 4）计算 Forward 时，前面的设备已经开始计算 Backward**。\n", "> \n", "> - 由于 Backward **稍微滞后于 Forward 计算**，可以在计算 **权重梯度（W）** 时减少 GPU 空闲。\n", "\n", "这样可以减少1F1B 里的 GPU 空闲时间**，提高计算利用率，同时前向传播和反向传播部分重叠，可以提高吞吐量，但是还是有大量的“气泡”。"]}, {"cell_type": "markdown", "id": "713f6b23-9cc3-45ec-9955-d5478bf00b06", "metadata": {}, "source": ["- **DualPipe（双管道并行）**：进一步提高 GPU 计算利用率，减少空闲时间（Idle）。在设备之间重叠 Forward、Backward、Gradient 计算**，形成高度并行的流水线。结合 **Overlapped Forward & Backward（黄色）** 的方式，优化计算效率。\n", "\n", "> - **设备 0 计算第 0 层的 Forward，同时设备 1 计算第 1 层 Forward**。\n", "> \n", "> - **当 Forward 进行到一半时，设备 0 就已经开始 Backward**，其他设备的 Forward 和 Backward 交错进行。\n", "> \n", "> - **设备可以同时计算多个批次**，这就是 DualPipe **重叠计算** 的特点。\n", "\n", "这个方案几乎消除了所有 GPU 空闲时间**，计算资源利用率达到极限。数据流动连续不断，Forward 和 Backward 高度重叠，非常适用于大规模分布式训练（如 GPT-4、DeepSeek LLM 训练），但是该方案的**实现复杂度极高**，需要精确调度 CUDA 计算流。"]}, {"cell_type": "markdown", "id": "8895bd38-1e41-466c-bf99-1b1b623db936", "metadata": {}, "source": ["上述这些并行方式主要通过 CUDA 计算流（CUDA Streams）和通信优化（NCCL）实现，而不是 Python 代码直接控制。  \n", "在 DeepSpeed / Megatron-LM 这样的框架中：\n", "1. **Pipeline 任务调度** 由 **CUDA 计算流（CUDA Streams）** 进行控制，而不是 Python 代码。\n", "2. **设备间通信（Device Communication）** 通过 **NCCL（NVIDIA Collective Communications Library）** 实现高效数据传输。\n", "3. **Tensor 并行（Tensor Parallelism）和 数据并行（Data Parallelism）可以结合 pipeline 并行**，优化计算效率。"]}, {"cell_type": "markdown", "id": "1c632a57-11e8-4f50-9411-dd3c3368793a", "metadata": {}, "source": ["## 6 完整的deepseekv3代码梳理"]}, {"cell_type": "markdown", "id": "1b4e7f8e-b95c-4800-b004-3ffded286886", "metadata": {}, "source": ["（见py脚本文件）"]}, {"cell_type": "code", "execution_count": null, "id": "0d078430-f8f3-4350-b0af-e0007bdc70b1", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}