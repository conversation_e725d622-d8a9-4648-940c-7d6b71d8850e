{"cells": [{"cell_type": "code", "execution_count": 1, "id": "fc8cf0e6-da62-42a7-ab60-bf638cc347fd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["problem                    ## Task B-1.3.\\n\\nA ship traveling along a riv...\n", "solution                   ## Solution.\\n\\nLet $t$ be the time required f...\n", "answer                     v_{R}=4\\mathrm{~}/\\mathrm{},v_{B}=10\\mathrm{~}...\n", "problem_type                                                         Algebra\n", "question_type                                              math-word-problem\n", "source                                                             olympiads\n", "uuid                                    586fd646-76d6-5070-8c81-9993ab9d8559\n", "is_reasoning_complete                                           [True, True]\n", "generations                [<think>\\nOkay, so I need to find the speed of...\n", "correctness_math_verify                                        [True, False]\n", "correctness_llama                                                       None\n", "finish_reasons                                                          None\n", "correctness_count                                                          1\n", "messages                   [{'content': '## Task B-1.3.\n", "\n", "A ship traveling...\n", "Name: 0, dtype: object\n"]}], "source": ["import pandas as pd\n", "\n", "file_path = r\"D:\\pythonwork\\2025LLMtraining\\minideepseek\\data\\openr1\\default-00000-of-00010.parquet\"\n", "\n", "# 读取 Parquet 文件\n", "df = pd.read_parquet(file_path, engine=\"pyarrow\")\n", "\n", "# 查看第一行\n", "print(df.iloc[0])  # 只打印第一行数据"]}, {"cell_type": "code", "execution_count": 2, "id": "bbb0d443-c8f1-476f-86c4-59c435214f02", "metadata": {}, "outputs": [{"data": {"text/plain": ["problem                    ## Task B-1.3.\\n\\nA ship traveling along a riv...\n", "solution                   ## Solution.\\n\\nLet $t$ be the time required f...\n", "answer                     v_{R}=4\\mathrm{~}/\\mathrm{},v_{B}=10\\mathrm{~}...\n", "problem_type                                                         Algebra\n", "question_type                                              math-word-problem\n", "source                                                             olympiads\n", "uuid                                    586fd646-76d6-5070-8c81-9993ab9d8559\n", "is_reasoning_complete                                           [True, True]\n", "generations                [<think>\\nOkay, so I need to find the speed of...\n", "correctness_math_verify                                        [True, False]\n", "correctness_llama                                                       None\n", "finish_reasons                                                          None\n", "correctness_count                                                          1\n", "messages                   [{'content': '## Task B-1.3.\n", "\n", "A ship traveling...\n", "Name: 0, dtype: object"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df.il<PERSON>[0]"]}, {"cell_type": "code", "execution_count": 3, "id": "2baba78e-ae35-4271-9001-f2134033cc70", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["problem                    ## Task B-1.3.\\n\\nA ship traveling along a riv...\n", "solution                   ## Solution.\\n\\nLet $t$ be the time required f...\n", "answer                     v_{R}=4\\mathrm{~}/\\mathrm{},v_{B}=10\\mathrm{~}...\n", "problem_type                                                         Algebra\n", "question_type                                              math-word-problem\n", "source                                                             olympiads\n", "uuid                                    586fd646-76d6-5070-8c81-9993ab9d8559\n", "is_reasoning_complete                                           [True, True]\n", "generations                [<think>\\nOkay, so I need to find the speed of...\n", "correctness_math_verify                                        [True, False]\n", "correctness_llama                                                       None\n", "finish_reasons                                                          None\n", "correctness_count                                                          1\n", "messages                   [{'content': '## Task B-1.3.\n", "\n", "A ship traveling...\n", "Name: 0, dtype: object\n"]}], "source": ["import pyarrow.parquet as pq\n", "\n", "file_path = r\"D:\\pythonwork\\2025LLMtraining\\minideepseek\\data\\openr1\\default-00000-of-00010.parquet\"\n", "\n", "# 读取 Parquet 文件的第一行\n", "table = pq.read_table(file_path, columns=None)  # 读取整个表\n", "first_row = table.to_pandas().iloc[0]  # 转换为 Pandas 取第一行\n", "\n", "print(first_row)"]}, {"cell_type": "code", "execution_count": 4, "id": "382499b7-47b0-406a-936b-4e6216ca0543", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'problem': '## Task B-1.3.\\n\\nA ship traveling along a river has covered $24 \\\\mathrm{~km}$ upstream and $28 \\\\mathrm{~km}$ downstream. For this journey, it took half an hour less than for traveling $30 \\\\mathrm{~km}$ upstream and $21 \\\\mathrm{~km}$ downstream, or half an hour more than for traveling $15 \\\\mathrm{~km}$ upstream and $42 \\\\mathrm{~km}$ downstream, assuming that both the ship and the river move uniformly.\\n\\nDetermine the speed of the ship in still water and the speed of the river.', 'solution': '## Solution.\\n\\nLet $t$ be the time required for the boat to travel $24 \\\\mathrm{~km}$ upstream and $28 \\\\mathrm{~km}$ downstream, $v_{R}$ the speed of the river, and $v_{B}$ the speed of the boat. When the boat is traveling upstream, its speed is $v_{B}-v_{R}$, and when it is traveling downstream, its speed is $v_{B}+v_{R}$.\\n\\nSince $t=\\\\frac{s}{v}$, from the given data, we obtain the following system of equations:\\n\\n$\\\\left\\\\{\\\\begin{array}{l}t=\\\\frac{24}{v_{B}-v_{R}}+\\\\frac{28}{v_{B}+v_{R}} \\\\\\\\ t+0.5=\\\\frac{30}{v_{B}-v_{R}}+\\\\frac{21}{v_{B}+v_{R}} \\\\\\\\ t-0.5=\\\\frac{15}{v_{B}-v_{R}}+\\\\frac{42}{v_{B}+v_{R}}\\\\end{array}\\\\right.$\\n\\nBy introducing new variables $x=\\\\frac{3}{v_{B}-v_{R}}, y=\\\\frac{7}{v_{B}+v_{R}}$, the system transforms into:\\n\\n$\\\\left\\\\{\\\\begin{array}{l}t=8 x+4 y \\\\\\\\ t+0.5=10 x+3 y \\\\\\\\ t-0.5=5 x+6 y\\\\end{array}\\\\right.$\\n\\nSubstituting $t$ from the first equation into the remaining two, we get:\\n\\n$\\\\left\\\\{\\\\begin{array}{l}8 x+4 y+0.5=10 x+3 y \\\\\\\\ 8 x+4 y-0.5=5 x+6 y\\\\end{array}\\\\right.$\\n\\n$\\\\left\\\\{\\\\begin{array}{l}2 x-y=0.5 \\\\\\\\ 3 x-2 y=0.5\\\\end{array}\\\\right.$\\n\\nThe solution to the last system is (0.5, 0.5). Then we have:\\n\\n$\\\\frac{3}{v_{B}-v_{R}}=0.5$, hence, $v_{B}-v_{R}=6 \\\\mathrm{~and}$\\n\\n$\\\\frac{7}{v_{B}+v_{R}}=0.5$, hence, $v_{B}+v_{R}=14$.\\n\\nThe speed of the river is $v_{R}=4 \\\\mathrm{~km} / \\\\mathrm{h}$, and the speed of the boat is $v_{B}=10 \\\\mathrm{~km} / \\\\mathrm{h}$.\\n\\n## Note:\\n\\nBy substituting $x=\\\\frac{1}{v_{B}-v_{R}}, y=\\\\frac{1}{v_{B}+v_{R}} \\\\mathrm{~and}$ following the same procedure, the initial system transforms into the system $\\\\left\\\\{\\\\begin{array}{l}6 x-7 y=0.5 \\\\\\\\ 9 x-14 y=0.5\\\\end{array}\\\\right.$\\n\\nThe solution to this system is $\\\\left(\\\\frac{1}{6}, \\\\frac{1}{14}\\\\right)$.', 'answer': 'v_{R}=4\\\\mathrm{~}/\\\\mathrm{},v_{B}=10\\\\mathrm{~}/\\\\mathrm{}', 'problem_type': 'Algebra', 'question_type': 'math-word-problem', 'source': 'olympiads', 'uuid': '586fd646-76d6-5070-8c81-9993ab9d8559', 'is_reasoning_complete': array([ True,  True]), 'generations': array(['<think>\\nOkay, so I need to find the speed of the ship in still water and the speed of the river. Let me start by recalling that when a ship is moving upstream, its effective speed is the speed of the ship minus the speed of the river. Conversely, when moving downstream, its effective speed is the ship\\'s speed plus the river\\'s speed. \\n\\nLet me denote the speed of the ship in still water as \\\\( v \\\\) (in km/h) and the speed of the river as \\\\( r \\\\) (also in km/h). Then, the upstream speed would be \\\\( v - r \\\\), and the downstream speed would be \\\\( v + r \\\\).\\n\\nThe problem mentions three different journeys:\\n\\n1. 24 km upstream and 28 km downstream, taking half an hour less than the second journey.\\n2. 30 km upstream and 21 km downstream, which takes half an hour more than the first journey.\\n3. 15 km upstream and 42 km downstream, which takes half an hour less than the first journey.\\n\\nWait, actually, the problem states: \"For this journey, it took half an hour less than for traveling 30 km upstream and 21 km downstream, or half an hour more than for traveling 15 km upstream and 42 km downstream...\"\\n\\nHmm, let me parse that again. The first journey (24 km upstream, 28 km downstream) took half an hour less than the journey with 30 km upstream and 21 km downstream. Alternatively, it took half an hour more than the journey with 15 km upstream and 42 km downstream. So, there are two comparisons here:\\n\\n- Time for 24 up + 28 down = Time for 30 up + 21 down - 0.5 hours\\n- Time for 24 up + 28 down = Time for 15 up + 42 down + 0.5 hours\\n\\nTherefore, we can set up two equations based on these time differences.\\n\\nFirst, let me express the time taken for each journey. Time is equal to distance divided by speed. So, for the first journey (24 km up, 28 km down):\\n\\nTime1 = \\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} \\\\)\\n\\nFor the second journey (30 km up, 21 km down):\\n\\nTime2 = \\\\( \\\\frac{30}{v - r} + \\\\frac{21}{v + r} \\\\)\\n\\nAccording to the problem, Time1 = Time2 - 0.5\\n\\nSimilarly, for the third journey (15 km up, 42 km down):\\n\\nTime3 = \\\\( \\\\frac{15}{v - r} + \\\\frac{42}{v + r} \\\\)\\n\\nAnd here, Time1 = Time3 + 0.5\\n\\nSo, we have two equations:\\n\\n1. \\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} = \\\\frac{30}{v - r} + \\\\frac{21}{v + r} - 0.5 \\\\)\\n2. \\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} = \\\\frac{15}{v - r} + \\\\frac{42}{v + r} + 0.5 \\\\)\\n\\nOkay, so now I need to solve these two equations for \\\\( v \\\\) and \\\\( r \\\\).\\n\\nLet me first work on the first equation:\\n\\n\\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} = \\\\frac{30}{v - r} + \\\\frac{21}{v + r} - 0.5 \\\\)\\n\\nLet me subtract \\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} \\\\) from both sides, but maybe it\\'s better to bring all terms to the left side:\\n\\n\\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} - \\\\frac{30}{v - r} - \\\\frac{21}{v + r} + 0.5 = 0 \\\\)\\n\\nSimplify the terms:\\n\\nFor the upstream terms: \\\\( 24 - 30 = -6 \\\\), so \\\\( \\\\frac{-6}{v - r} \\\\)\\n\\nFor the downstream terms: \\\\( 28 - 21 = 7 \\\\), so \\\\( \\\\frac{7}{v + r} \\\\)\\n\\nSo, the equation becomes:\\n\\n\\\\( \\\\frac{-6}{v - r} + \\\\frac{7}{v + r} + 0.5 = 0 \\\\)\\n\\nSimilarly, for the second equation:\\n\\n\\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} - \\\\frac{15}{v - r} - \\\\frac{42}{v + r} - 0.5 = 0 \\\\)\\n\\nSimplify:\\n\\nUpstream terms: \\\\( 24 - 15 = 9 \\\\), so \\\\( \\\\frac{9}{v - r} \\\\)\\n\\nDownstream terms: \\\\( 28 - 42 = -14 \\\\), so \\\\( \\\\frac{-14}{v + r} \\\\)\\n\\nThus:\\n\\n\\\\( \\\\frac{9}{v - r} - \\\\frac{14}{v + r} - 0.5 = 0 \\\\)\\n\\nNow, we have two equations:\\n\\n1. \\\\( -\\\\frac{6}{v - r} + \\\\frac{7}{v + r} + 0.5 = 0 \\\\)\\n2. \\\\( \\\\frac{9}{v - r} - \\\\frac{14}{v + r} - 0.5 = 0 \\\\)\\n\\nLet me denote \\\\( x = \\\\frac{1}{v - r} \\\\) and \\\\( y = \\\\frac{1}{v + r} \\\\). Then, the equations become:\\n\\n1. \\\\( -6x + 7y + 0.5 = 0 \\\\) ⇒ \\\\( -6x + 7y = -0.5 \\\\)\\n2. \\\\( 9x - 14y - 0.5 = 0 \\\\) ⇒ \\\\( 9x - 14y = 0.5 \\\\)\\n\\nNow, we have a system of linear equations:\\n\\nEquation (1): \\\\( -6x + 7y = -0.5 \\\\)\\n\\nEquation (2): \\\\( 9x - 14y = 0.5 \\\\)\\n\\nLet me solve this system using the elimination method. Let\\'s multiply Equation (1) by 2 to make the coefficients of y terms opposites:\\n\\nEquation (1) multiplied by 2: \\\\( -12x + 14y = -1 \\\\)\\n\\nNow, add this to Equation (2):\\n\\n\\\\( (-12x + 14y) + (9x -14y) = -1 + 0.5 \\\\)\\n\\nSimplify:\\n\\n\\\\( -3x = -0.5 \\\\)\\n\\nThus, \\\\( x = (-0.5)/(-3) = 0.5/3 ≈ 0.1667 \\\\)\\n\\nBut exact value is \\\\( x = 1/6 \\\\)\\n\\nNow, substitute \\\\( x = 1/6 \\\\) back into Equation (1):\\n\\n\\\\( -6*(1/6) + 7y = -0.5 \\\\)\\n\\nSimplify:\\n\\n\\\\( -1 + 7y = -0.5 \\\\)\\n\\nAdd 1 to both sides:\\n\\n7y = 0.5\\n\\nThus, \\\\( y = 0.5/7 = 1/14 ≈ 0.0714 \\\\)\\n\\nSo, \\\\( x = 1/6 \\\\) and \\\\( y = 1/14 \\\\)\\n\\nRecall that \\\\( x = \\\\frac{1}{v - r} = 1/6 \\\\) ⇒ \\\\( v - r = 6 \\\\)\\n\\nSimilarly, \\\\( y = \\\\frac{1}{v + r} = 1/14 \\\\) ⇒ \\\\( v + r = 14 \\\\)\\n\\nTherefore, we have two equations:\\n\\n1. \\\\( v - r = 6 \\\\)\\n2. \\\\( v + r = 14 \\\\)\\n\\nAdding these equations:\\n\\n\\\\( (v - r) + (v + r) = 6 + 14 \\\\)\\n\\nSimplify:\\n\\n\\\\( 2v = 20 \\\\) ⇒ \\\\( v = 10 \\\\)\\n\\nSubstituting back into \\\\( v + r = 14 \\\\):\\n\\n\\\\( 10 + r = 14 \\\\) ⇒ \\\\( r = 4 \\\\)\\n\\nSo, the speed of the ship in still water is 10 km/h, and the speed of the river is 4 km/h.\\n\\nLet me verify these results by plugging them back into the original time equations.\\n\\nFirst journey: 24/(10-4) + 28/(10+4) = 24/6 + 28/14 = 4 + 2 = 6 hours.\\n\\nSecond journey: 30/(10-4) + 21/(10+4) = 30/6 + 21/14 = 5 + 1.5 = 6.5 hours. So, 6.5 - 0.5 = 6 hours, which matches the first journey. Good.\\n\\nThird journey: 15/(10-4) + 42/(10+4) = 15/6 + 42/14 = 2.5 + 3 = 5.5 hours. Then, 5.5 + 0.5 = 6 hours, which also matches the first journey. Perfect.\\n\\nTherefore, the solution is correct.\\n\\n**Final Answer**\\nThe speed of the ship in still water is \\\\boxed{10} km/h and the speed of the river is \\\\boxed{4} km/h.\\n</think>\\n\\nLet \\\\( v \\\\) be the speed of the ship in still water (in km/h) and \\\\( r \\\\) be the speed of the river (in km/h). The effective speeds of the ship upstream and downstream are \\\\( v - r \\\\) and \\\\( v + r \\\\), respectively.\\n\\nWe are given three journeys with the following time relationships:\\n1. The journey of 24 km upstream and 28 km downstream takes half an hour less than the journey of 30 km upstream and 21 km downstream.\\n2. The journey of 24 km upstream and 28 km downstream takes half an hour more than the journey of 15 km upstream and 42 km downstream.\\n\\nWe set up the equations for the times taken for each journey:\\n1. For the first journey (24 km upstream, 28 km downstream):\\n   \\\\[\\n   \\\\frac{24}{v - r} + \\\\frac{28}{v + r}\\n   \\\\]\\n2. For the second journey (30 km upstream, 21 km downstream):\\n   \\\\[\\n   \\\\frac{30}{v - r} + \\\\frac{21}{v + r}\\n   \\\\]\\n3. For the third journey (15 km upstream, 42 km downstream):\\n   \\\\[\\n   \\\\frac{15}{v - r} + \\\\frac{42}{v + r}\\n   \\\\]\\n\\nThe time relationships give us the following equations:\\n1. \\\\[\\n   \\\\frac{24}{v - r} + \\\\frac{28}{v + r} = \\\\frac{30}{v - r} + \\\\frac{21}{v + r} - 0.5\\n   \\\\]\\n2. \\\\[\\n   \\\\frac{24}{v - r} + \\\\frac{28}{v + r} = \\\\frac{15}{v - r} + \\\\frac{42}{v + r} + 0.5\\n   \\\\]\\n\\nIntroducing variables \\\\( x = \\\\frac{1}{v - r} \\\\) and \\\\( y = \\\\frac{1}{v + r} \\\\), we transform the equations into:\\n1. \\\\[\\n   -6x + 7y = -0.5\\n   \\\\]\\n2. \\\\[\\n   9x - 14y = 0.5\\n   \\\\]\\n\\nSolving this system of linear equations:\\n1. Multiply the first equation by 2:\\n   \\\\[\\n   -12x + 14y = -1\\n   \\\\]\\n2. Add the modified first equation to the second equation:\\n   \\\\[\\n   -3x = -0.5 \\\\implies x = \\\\frac{1}{6}\\n   \\\\]\\n3. Substitute \\\\( x = \\\\frac{1}{6} \\\\) into the first equation:\\n   \\\\[\\n   -6 \\\\left( \\\\frac{1}{6} \\\\right) + 7y = -0.5 \\\\implies -1 + 7y = -0.5 \\\\implies 7y = 0.5 \\\\implies y = \\\\frac{1}{14}\\n   \\\\]\\n\\nConverting back to \\\\( v \\\\) and \\\\( r \\\\):\\n1. \\\\( v - r = 6 \\\\)\\n2. \\\\( v + r = 14 \\\\)\\n\\nSolving these equations:\\n1. Adding the two equations:\\n   \\\\[\\n   2v = 20 \\\\implies v = 10\\n   \\\\]\\n2. Substituting \\\\( v = 10 \\\\) into \\\\( v + r = 14 \\\\):\\n   \\\\[\\n   10 + r = 14 \\\\implies r = 4\\n   \\\\]\\n\\nThe speed of the ship in still water is \\\\(\\\\boxed{10}\\\\) km/h and the speed of the river is \\\\(\\\\boxed{4}\\\\) km/h.',\n", "       \"<think>\\nOkay, so I need to solve this problem about a ship traveling upstream and downstream. The question is asking for the speed of the ship in still water and the speed of the river. Let me think through this step by step.\\n\\nFirst, let me parse the problem again. The ship covers different distances upstream and downstream, and the time taken for these journeys is compared. Specifically, it says that traveling 24 km upstream and 28 km downstream took half an hour less than traveling 30 km upstream and 21 km downstream. Additionally, that same journey (24 km up, 28 km down) took half an hour more than traveling 15 km upstream and 42 km downstream. \\n\\nAll right, so the key here is that the time differences are half an hour in each case. Since the ship and river are moving uniformly, their speeds are constant. Let me denote the speed of the ship in still water as \\\\( v \\\\) (km/h) and the speed of the river as \\\\( r \\\\) (km/h). Then, when the ship is going upstream, its effective speed is \\\\( v - r \\\\), and downstream it's \\\\( v + r \\\\).\\n\\nTime is distance divided by speed, so the time taken for each part of the journey (upstream and downstream) can be calculated by dividing the distance by the respective effective speed. The total time for a journey would be the sum of the time upstream and the time downstream.\\n\\nLet me write down the equations based on the problem statements. Let's consider the first comparison:\\n\\n1. The journey of 24 km upstream and 28 km downstream took half an hour less than the journey of 30 km upstream and 21 km downstream.\\n\\nLet me denote the time for 24 km up and 28 km down as \\\\( T_1 \\\\), and the time for 30 km up and 21 km down as \\\\( T_2 \\\\). According to the problem, \\\\( T_1 = T_2 - 0.5 \\\\) hours.\\n\\nSimilarly, the second comparison:\\n\\n2. The same journey (24 km up, 28 km down) took half an hour more than the journey of 15 km upstream and 42 km downstream.\\n\\nLet me denote the time for 15 km up and 42 km down as \\\\( T_3 \\\\). Then, \\\\( T_1 = T_3 + 0.5 \\\\) hours.\\n\\nTherefore, we have two equations:\\n\\n\\\\[\\n\\\\frac{24}{v - r} + \\\\frac{28}{v + r} = \\\\frac{30}{v - r} + \\\\frac{21}{v + r} - 0.5\\n\\\\]\\n\\nand\\n\\n\\\\[\\n\\\\frac{24}{v - r} + \\\\frac{28}{v + r} = \\\\frac{15}{v - r} + \\\\frac{42}{v + r} + 0.5\\n\\\\]\\n\\nHmm, these are the two equations we need to solve. Let me simplify these equations.\\n\\nStarting with the first equation:\\n\\nLeft side: \\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} \\\\)\\n\\nRight side: \\\\( \\\\frac{30}{v - r} + \\\\frac{21}{v + r} - 0.5 \\\\)\\n\\nLet me subtract the left side from the right side to set the equation to zero:\\n\\n\\\\( \\\\frac{30}{v - r} + \\\\frac{21}{v + r} - 0.5 - \\\\left( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} \\\\right) = 0 \\\\)\\n\\nSimplify term by term:\\n\\n\\\\( \\\\frac{30 - 24}{v - r} + \\\\frac{21 - 28}{v + r} - 0.5 = 0 \\\\)\\n\\nWhich simplifies to:\\n\\n\\\\( \\\\frac{6}{v - r} - \\\\frac{7}{v + r} - 0.5 = 0 \\\\)\\n\\nSimilarly, for the second equation:\\n\\nLeft side: \\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} \\\\)\\n\\nRight side: \\\\( \\\\frac{15}{v - r} + \\\\frac{42}{v + r} + 0.5 \\\\)\\n\\nSubtract left side from right side:\\n\\n\\\\( \\\\frac{15}{v - r} + \\\\frac{42}{v + r} + 0.5 - \\\\left( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} \\\\right) = 0 \\\\)\\n\\nSimplify term by term:\\n\\n\\\\( \\\\frac{15 - 24}{v - r} + \\\\frac{42 - 28}{v + r} + 0.5 = 0 \\\\)\\n\\nWhich simplifies to:\\n\\n\\\\( -\\\\frac{9}{v - r} + \\\\frac{14}{v + r} + 0.5 = 0 \\\\)\\n\\nSo now we have two equations:\\n\\n1. \\\\( \\\\frac{6}{v - r} - \\\\frac{7}{v + r} = 0.5 \\\\)\\n2. \\\\( -\\\\frac{9}{v - r} + \\\\frac{14}{v + r} = -0.5 \\\\)\\n\\nLet me write them as:\\n\\nEquation (1): \\\\( \\\\frac{6}{v - r} - \\\\frac{7}{v + r} = 0.5 \\\\)\\n\\nEquation (2): \\\\( -\\\\frac{9}{v - r} + \\\\frac{14}{v + r} = -0.5 \\\\)\\n\\nHmm, these are two equations with two variables, \\\\( \\\\frac{1}{v - r} \\\\) and \\\\( \\\\frac{1}{v + r} \\\\). Let me denote \\\\( x = \\\\frac{1}{v - r} \\\\) and \\\\( y = \\\\frac{1}{v + r} \\\\). Then, the equations become:\\n\\nEquation (1): \\\\( 6x - 7y = 0.5 \\\\)\\n\\nEquation (2): \\\\( -9x + 14y = -0.5 \\\\)\\n\\nNow, I can solve this system of linear equations for x and y. Let me write them again:\\n\\n1. \\\\( 6x - 7y = 0.5 \\\\)\\n2. \\\\( -9x + 14y = -0.5 \\\\)\\n\\nLet me use the elimination method. If I multiply equation (1) by 2, I get:\\n\\nEquation (1a): \\\\( 12x - 14y = 1 \\\\)\\n\\nNow, equation (2) is \\\\( -9x + 14y = -0.5 \\\\)\\n\\nIf I add equation (1a) and equation (2), the y terms will cancel out:\\n\\n\\\\( (12x - 14y) + (-9x + 14y) = 1 + (-0.5) \\\\)\\n\\nSimplify:\\n\\n\\\\( 3x = 0.5 \\\\)\\n\\nTherefore, \\\\( x = 0.5 / 3 = 1/6 \\\\)\\n\\nNow, substitute x = 1/6 back into equation (1):\\n\\n\\\\( 6*(1/6) -7y = 0.5 \\\\)\\n\\nSimplify:\\n\\n\\\\( 1 - 7y = 0.5 \\\\)\\n\\nSubtract 1:\\n\\n\\\\( -7y = -0.5 \\\\)\\n\\nDivide:\\n\\n\\\\( y = (-0.5)/(-7) = 0.5/7 = 1/14 \\\\)\\n\\nSo, we have x = 1/6 and y = 1/14. Recall that:\\n\\n\\\\( x = \\\\frac{1}{v - r} = 1/6 \\\\)\\n\\n\\\\( y = \\\\frac{1}{v + r} = 1/14 \\\\)\\n\\nTherefore, we can write:\\n\\n\\\\( v - r = 6 \\\\)\\n\\n\\\\( v + r = 14 \\\\)\\n\\nNow, solving these two equations:\\n\\nAdd the two equations:\\n\\n\\\\( (v - r) + (v + r) = 6 + 14 \\\\)\\n\\nWhich simplifies to:\\n\\n\\\\( 2v = 20 \\\\)\\n\\nTherefore, \\\\( v = 10 \\\\) km/h\\n\\nSubstituting back into \\\\( v - r = 6 \\\\):\\n\\n\\\\( 10 - r = 6 \\\\)\\n\\nThus, \\\\( r = 10 - 6 = 4 \\\\) km/h\\n\\nSo, the speed of the ship in still water is 10 km/h, and the speed of the river is 4 km/h.\\n\\nLet me verify these results to ensure there are no mistakes.\\n\\nFirst, check the first journey: 24 km upstream and 28 km downstream.\\n\\nUpstream speed: 10 - 4 = 6 km/h. Time upstream: 24 / 6 = 4 hours.\\n\\nDownstream speed: 10 + 4 = 14 km/h. Time downstream: 28 / 14 = 2 hours.\\n\\nTotal time: 4 + 2 = 6 hours.\\n\\nSecond journey: 30 km upstream and 21 km downstream.\\n\\nTime upstream: 30 / 6 = 5 hours.\\n\\nTime downstream: 21 / 14 = 1.5 hours.\\n\\nTotal time: 5 + 1.5 = 6.5 hours.\\n\\nDifference between the two journeys: 6.5 - 6 = 0.5 hours, which is half an hour. That's correct.\\n\\nThird journey: 15 km upstream and 42 km downstream.\\n\\nTime upstream: 15 / 6 = 2.5 hours.\\n\\nTime downstream: 42 / 14 = 3 hours.\\n\\nTotal time: 2.5 + 3 = 5.5 hours.\\n\\nDifference between the first journey (6 hours) and this one: 6 - 5.5 = 0.5 hours, which is half an hour more. That also checks out.\\n\\nTherefore, the solution seems correct. The speed of the ship in still water is 10 km/h and the speed of the river is 4 km/h.\\n\\n**Final Answer**\\nThe speed of the ship in still water is \\\\boxed{10} km/h and the speed of the river is \\\\boxed{4} km/h.\\n</think>\\n\\nLet \\\\( v \\\\) be the speed of the ship in still water (in km/h) and \\\\( r \\\\) be the speed of the river (in km/h). The effective speeds of the ship upstream and downstream are \\\\( v - r \\\\) and \\\\( v + r \\\\) respectively.\\n\\nWe are given two comparisons involving the time taken for different journeys. The first comparison states that traveling 24 km upstream and 28 km downstream took half an hour less than traveling 30 km upstream and 21 km downstream. The second comparison states that the same journey (24 km upstream and 28 km downstream) took half an hour more than traveling 15 km upstream and 42 km downstream.\\n\\nWe set up the following equations based on the time differences:\\n\\n1. For the first comparison:\\n   \\\\[\\n   \\\\frac{24}{v - r} + \\\\frac{28}{v + r} = \\\\frac{30}{v - r} + \\\\frac{21}{v + r} - 0.5\\n   \\\\]\\n   Simplifying this, we get:\\n   \\\\[\\n   \\\\frac{6}{v - r} - \\\\frac{7}{v + r} = 0.5\\n   \\\\]\\n\\n2. For the second comparison:\\n   \\\\[\\n   \\\\frac{24}{v - r} + \\\\frac{28}{v + r} = \\\\frac{15}{v - r} + \\\\frac{42}{v + r} + 0.5\\n   \\\\]\\n   Simplifying this, we get:\\n   \\\\[\\n   -\\\\frac{9}{v - r} + \\\\frac{14}{v + r} = -0.5\\n   \\\\]\\n\\nIntroducing \\\\( x = \\\\frac{1}{v - r} \\\\) and \\\\( y = \\\\frac{1}{v + r} \\\\), we convert these equations into a system of linear equations:\\n1. \\\\( 6x - 7y = 0.5 \\\\)\\n2. \\\\( -9x + 14y = -0.5 \\\\)\\n\\nSolving this system, we multiply the first equation by 2:\\n1. \\\\( 12x - 14y = 1 \\\\)\\n\\nAdding this to the second equation:\\n\\\\[\\n3x = 0.5 \\\\implies x = \\\\frac{1}{6}\\n\\\\]\\n\\nSubstituting \\\\( x = \\\\frac{1}{6} \\\\) back into the first equation:\\n\\\\[\\n6 \\\\left( \\\\frac{1}{6} \\\\right) - 7y = 0.5 \\\\implies 1 - 7y = 0.5 \\\\implies y = \\\\frac{1}{14}\\n\\\\]\\n\\nThus, we have:\\n\\\\[\\nv - r = 6 \\\\quad \\\\text{and} \\\\quad v + r = 14\\n\\\\]\\n\\nSolving these equations:\\n\\\\[\\n2v = 20 \\\\implies v = 10 \\\\quad \\\\text{and} \\\\quad r = 10 - 6 = 4\\n\\\\]\\n\\nThe speed of the ship in still water is \\\\boxed{10} km/h and the speed of the river is \\\\boxed{4} km/h.\"],\n", "      dtype=object), 'correctness_math_verify': array([ True, False]), 'correctness_llama': None, 'finish_reasons': None, 'correctness_count': 1, 'messages': array([{'content': '## Task B-1.3.\\n\\nA ship traveling along a river has covered $24 \\\\mathrm{~km}$ upstream and $28 \\\\mathrm{~km}$ downstream. For this journey, it took half an hour less than for traveling $30 \\\\mathrm{~km}$ upstream and $21 \\\\mathrm{~km}$ downstream, or half an hour more than for traveling $15 \\\\mathrm{~km}$ upstream and $42 \\\\mathrm{~km}$ downstream, assuming that both the ship and the river move uniformly.\\n\\nDetermine the speed of the ship in still water and the speed of the river.', 'role': 'user'},\n", "       {'content': '<think>\\nOkay, so I need to find the speed of the ship in still water and the speed of the river. Let me start by recalling that when a ship is moving upstream, its effective speed is the speed of the ship minus the speed of the river. Conversely, when moving downstream, its effective speed is the ship\\'s speed plus the river\\'s speed. \\n\\nLet me denote the speed of the ship in still water as \\\\( v \\\\) (in km/h) and the speed of the river as \\\\( r \\\\) (also in km/h). Then, the upstream speed would be \\\\( v - r \\\\), and the downstream speed would be \\\\( v + r \\\\).\\n\\nThe problem mentions three different journeys:\\n\\n1. 24 km upstream and 28 km downstream, taking half an hour less than the second journey.\\n2. 30 km upstream and 21 km downstream, which takes half an hour more than the first journey.\\n3. 15 km upstream and 42 km downstream, which takes half an hour less than the first journey.\\n\\nWait, actually, the problem states: \"For this journey, it took half an hour less than for traveling 30 km upstream and 21 km downstream, or half an hour more than for traveling 15 km upstream and 42 km downstream...\"\\n\\nHmm, let me parse that again. The first journey (24 km upstream, 28 km downstream) took half an hour less than the journey with 30 km upstream and 21 km downstream. Alternatively, it took half an hour more than the journey with 15 km upstream and 42 km downstream. So, there are two comparisons here:\\n\\n- Time for 24 up + 28 down = Time for 30 up + 21 down - 0.5 hours\\n- Time for 24 up + 28 down = Time for 15 up + 42 down + 0.5 hours\\n\\nTherefore, we can set up two equations based on these time differences.\\n\\nFirst, let me express the time taken for each journey. Time is equal to distance divided by speed. So, for the first journey (24 km up, 28 km down):\\n\\nTime1 = \\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} \\\\)\\n\\nFor the second journey (30 km up, 21 km down):\\n\\nTime2 = \\\\( \\\\frac{30}{v - r} + \\\\frac{21}{v + r} \\\\)\\n\\nAccording to the problem, Time1 = Time2 - 0.5\\n\\nSimilarly, for the third journey (15 km up, 42 km down):\\n\\nTime3 = \\\\( \\\\frac{15}{v - r} + \\\\frac{42}{v + r} \\\\)\\n\\nAnd here, Time1 = Time3 + 0.5\\n\\nSo, we have two equations:\\n\\n1. \\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} = \\\\frac{30}{v - r} + \\\\frac{21}{v + r} - 0.5 \\\\)\\n2. \\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} = \\\\frac{15}{v - r} + \\\\frac{42}{v + r} + 0.5 \\\\)\\n\\nOkay, so now I need to solve these two equations for \\\\( v \\\\) and \\\\( r \\\\).\\n\\nLet me first work on the first equation:\\n\\n\\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} = \\\\frac{30}{v - r} + \\\\frac{21}{v + r} - 0.5 \\\\)\\n\\nLet me subtract \\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} \\\\) from both sides, but maybe it\\'s better to bring all terms to the left side:\\n\\n\\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} - \\\\frac{30}{v - r} - \\\\frac{21}{v + r} + 0.5 = 0 \\\\)\\n\\nSimplify the terms:\\n\\nFor the upstream terms: \\\\( 24 - 30 = -6 \\\\), so \\\\( \\\\frac{-6}{v - r} \\\\)\\n\\nFor the downstream terms: \\\\( 28 - 21 = 7 \\\\), so \\\\( \\\\frac{7}{v + r} \\\\)\\n\\nSo, the equation becomes:\\n\\n\\\\( \\\\frac{-6}{v - r} + \\\\frac{7}{v + r} + 0.5 = 0 \\\\)\\n\\nSimilarly, for the second equation:\\n\\n\\\\( \\\\frac{24}{v - r} + \\\\frac{28}{v + r} - \\\\frac{15}{v - r} - \\\\frac{42}{v + r} - 0.5 = 0 \\\\)\\n\\nSimplify:\\n\\nUpstream terms: \\\\( 24 - 15 = 9 \\\\), so \\\\( \\\\frac{9}{v - r} \\\\)\\n\\nDownstream terms: \\\\( 28 - 42 = -14 \\\\), so \\\\( \\\\frac{-14}{v + r} \\\\)\\n\\nThus:\\n\\n\\\\( \\\\frac{9}{v - r} - \\\\frac{14}{v + r} - 0.5 = 0 \\\\)\\n\\nNow, we have two equations:\\n\\n1. \\\\( -\\\\frac{6}{v - r} + \\\\frac{7}{v + r} + 0.5 = 0 \\\\)\\n2. \\\\( \\\\frac{9}{v - r} - \\\\frac{14}{v + r} - 0.5 = 0 \\\\)\\n\\nLet me denote \\\\( x = \\\\frac{1}{v - r} \\\\) and \\\\( y = \\\\frac{1}{v + r} \\\\). Then, the equations become:\\n\\n1. \\\\( -6x + 7y + 0.5 = 0 \\\\) ⇒ \\\\( -6x + 7y = -0.5 \\\\)\\n2. \\\\( 9x - 14y - 0.5 = 0 \\\\) ⇒ \\\\( 9x - 14y = 0.5 \\\\)\\n\\nNow, we have a system of linear equations:\\n\\nEquation (1): \\\\( -6x + 7y = -0.5 \\\\)\\n\\nEquation (2): \\\\( 9x - 14y = 0.5 \\\\)\\n\\nLet me solve this system using the elimination method. Let\\'s multiply Equation (1) by 2 to make the coefficients of y terms opposites:\\n\\nEquation (1) multiplied by 2: \\\\( -12x + 14y = -1 \\\\)\\n\\nNow, add this to Equation (2):\\n\\n\\\\( (-12x + 14y) + (9x -14y) = -1 + 0.5 \\\\)\\n\\nSimplify:\\n\\n\\\\( -3x = -0.5 \\\\)\\n\\nThus, \\\\( x = (-0.5)/(-3) = 0.5/3 ≈ 0.1667 \\\\)\\n\\nBut exact value is \\\\( x = 1/6 \\\\)\\n\\nNow, substitute \\\\( x = 1/6 \\\\) back into Equation (1):\\n\\n\\\\( -6*(1/6) + 7y = -0.5 \\\\)\\n\\nSimplify:\\n\\n\\\\( -1 + 7y = -0.5 \\\\)\\n\\nAdd 1 to both sides:\\n\\n7y = 0.5\\n\\nThus, \\\\( y = 0.5/7 = 1/14 ≈ 0.0714 \\\\)\\n\\nSo, \\\\( x = 1/6 \\\\) and \\\\( y = 1/14 \\\\)\\n\\nRecall that \\\\( x = \\\\frac{1}{v - r} = 1/6 \\\\) ⇒ \\\\( v - r = 6 \\\\)\\n\\nSimilarly, \\\\( y = \\\\frac{1}{v + r} = 1/14 \\\\) ⇒ \\\\( v + r = 14 \\\\)\\n\\nTherefore, we have two equations:\\n\\n1. \\\\( v - r = 6 \\\\)\\n2. \\\\( v + r = 14 \\\\)\\n\\nAdding these equations:\\n\\n\\\\( (v - r) + (v + r) = 6 + 14 \\\\)\\n\\nSimplify:\\n\\n\\\\( 2v = 20 \\\\) ⇒ \\\\( v = 10 \\\\)\\n\\nSubstituting back into \\\\( v + r = 14 \\\\):\\n\\n\\\\( 10 + r = 14 \\\\) ⇒ \\\\( r = 4 \\\\)\\n\\nSo, the speed of the ship in still water is 10 km/h, and the speed of the river is 4 km/h.\\n\\nLet me verify these results by plugging them back into the original time equations.\\n\\nFirst journey: 24/(10-4) + 28/(10+4) = 24/6 + 28/14 = 4 + 2 = 6 hours.\\n\\nSecond journey: 30/(10-4) + 21/(10+4) = 30/6 + 21/14 = 5 + 1.5 = 6.5 hours. So, 6.5 - 0.5 = 6 hours, which matches the first journey. Good.\\n\\nThird journey: 15/(10-4) + 42/(10+4) = 15/6 + 42/14 = 2.5 + 3 = 5.5 hours. Then, 5.5 + 0.5 = 6 hours, which also matches the first journey. Perfect.\\n\\nTherefore, the solution is correct.\\n\\n**Final Answer**\\nThe speed of the ship in still water is \\\\boxed{10} km/h and the speed of the river is \\\\boxed{4} km/h.\\n</think>\\n\\nLet \\\\( v \\\\) be the speed of the ship in still water (in km/h) and \\\\( r \\\\) be the speed of the river (in km/h). The effective speeds of the ship upstream and downstream are \\\\( v - r \\\\) and \\\\( v + r \\\\), respectively.\\n\\nWe are given three journeys with the following time relationships:\\n1. The journey of 24 km upstream and 28 km downstream takes half an hour less than the journey of 30 km upstream and 21 km downstream.\\n2. The journey of 24 km upstream and 28 km downstream takes half an hour more than the journey of 15 km upstream and 42 km downstream.\\n\\nWe set up the equations for the times taken for each journey:\\n1. For the first journey (24 km upstream, 28 km downstream):\\n   \\\\[\\n   \\\\frac{24}{v - r} + \\\\frac{28}{v + r}\\n   \\\\]\\n2. For the second journey (30 km upstream, 21 km downstream):\\n   \\\\[\\n   \\\\frac{30}{v - r} + \\\\frac{21}{v + r}\\n   \\\\]\\n3. For the third journey (15 km upstream, 42 km downstream):\\n   \\\\[\\n   \\\\frac{15}{v - r} + \\\\frac{42}{v + r}\\n   \\\\]\\n\\nThe time relationships give us the following equations:\\n1. \\\\[\\n   \\\\frac{24}{v - r} + \\\\frac{28}{v + r} = \\\\frac{30}{v - r} + \\\\frac{21}{v + r} - 0.5\\n   \\\\]\\n2. \\\\[\\n   \\\\frac{24}{v - r} + \\\\frac{28}{v + r} = \\\\frac{15}{v - r} + \\\\frac{42}{v + r} + 0.5\\n   \\\\]\\n\\nIntroducing variables \\\\( x = \\\\frac{1}{v - r} \\\\) and \\\\( y = \\\\frac{1}{v + r} \\\\), we transform the equations into:\\n1. \\\\[\\n   -6x + 7y = -0.5\\n   \\\\]\\n2. \\\\[\\n   9x - 14y = 0.5\\n   \\\\]\\n\\nSolving this system of linear equations:\\n1. Multiply the first equation by 2:\\n   \\\\[\\n   -12x + 14y = -1\\n   \\\\]\\n2. Add the modified first equation to the second equation:\\n   \\\\[\\n   -3x = -0.5 \\\\implies x = \\\\frac{1}{6}\\n   \\\\]\\n3. Substitute \\\\( x = \\\\frac{1}{6} \\\\) into the first equation:\\n   \\\\[\\n   -6 \\\\left( \\\\frac{1}{6} \\\\right) + 7y = -0.5 \\\\implies -1 + 7y = -0.5 \\\\implies 7y = 0.5 \\\\implies y = \\\\frac{1}{14}\\n   \\\\]\\n\\nConverting back to \\\\( v \\\\) and \\\\( r \\\\):\\n1. \\\\( v - r = 6 \\\\)\\n2. \\\\( v + r = 14 \\\\)\\n\\nSolving these equations:\\n1. Adding the two equations:\\n   \\\\[\\n   2v = 20 \\\\implies v = 10\\n   \\\\]\\n2. Substituting \\\\( v = 10 \\\\) into \\\\( v + r = 14 \\\\):\\n   \\\\[\\n   10 + r = 14 \\\\implies r = 4\\n   \\\\]\\n\\nThe speed of the ship in still water is \\\\(\\\\boxed{10}\\\\) km/h and the speed of the river is \\\\(\\\\boxed{4}\\\\) km/h.', 'role': 'assistant'}],\n", "      dtype=object)}\n"]}], "source": ["import pyarrow.parquet as pq\n", "\n", "file_path = r\"D:\\pythonwork\\2025LLMtraining\\minideepseek\\data\\openr1\\default-00000-of-00010.parquet\"\n", "\n", "# 读取 Parquet 文件的第一行\n", "table = pq.read_table(file_path)  # 读取整个表\n", "df = table.to_pandas()  # 转换为 Pandas DataFrame\n", "\n", "# 显示完整结构和内容\n", "print(df.iloc[0].to_dict())  # 直接打印完整的第一行数据"]}, {"cell_type": "code", "execution_count": 6, "id": "4c76b4c9-8273-4208-9dcb-103303510eee", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 'ape210k__00971711', 'question': \"<PERSON>'s family bought a washing machine and a refrigerator, and they spent a total of 6,000 yuan. The price of the refrigerator is 3/5 of that of the washing machine. Ask for the price of the washing machine.\", 'question_chinese': '王艳家买了一台洗衣机和一台电冰箱，一共花了6000元，电冰箱的价钱是洗衣机的(3/5)，求洗衣机的价钱．', 'chain': '<gadget id=\"calculator\">3 / 5</gadget>\\n<output>3/5 = around 0.6</output>\\n\\n<gadget id=\"calculator\">1 + (3/5)</gadget>\\n<output>8/5 = around 1.6</output>\\n\\n<gadget id=\"calculator\">6_000 / (8/5)</gadget>\\n<output>3_750</output>\\n\\n<result>3_750</result>', 'result': '3_750', 'result_float': 3750.0, 'equation': 'x=6000/(1+(3/5))'}\n"]}], "source": ["import pyarrow.parquet as pq\n", "\n", "file_path = r\"D:\\pythonwork\\2025LLMtraining\\minideepseek\\data\\ape\\test-00000-of-00001-3ffa1fbd65291d25.parquet\"\n", "\n", "# 读取 Parquet 文件的第一行\n", "table = pq.read_table(file_path)  # 读取整个表\n", "df = table.to_pandas()  # 转换为 Pandas DataFrame\n", "\n", "# 显示完整结构和内容\n", "print(df.iloc[0].to_dict())  # 直接打印完整的第一行数据"]}, {"cell_type": "code", "execution_count": 7, "id": "c5c4c979-275b-454e-b110-1f94a0a2d741", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 'ape210k__01099539', 'question': 'The fifth grade students participated in the voluntary book donation activity. Class Five 1 donated 500 books, Class Five 2 donated 80% of Class Five 1, Class Five 3 donated 120% of Class Five 2, Class Five 1 and Class Five 3 Donate more books than who? (Please compare the two methods).', 'question_chinese': '五年级同学参加义务捐书活动，五1班捐了500本，五2班捐的本数是五1班80%，五3班捐的本数是五2班120%，五1班和五3班比谁捐书多？(请用两种方法比较一下)．', 'chain': '<result>1</result>', 'result': '1', 'result_float': 1.0, 'equation': 'x=1'}\n"]}], "source": ["import pyarrow.parquet as pq\n", "\n", "file_path = r\"D:\\pythonwork\\2025LLMtraining\\minideepseek\\data\\ape\\train-00000-of-00001-b9f022a8492442e4.parquet\"\n", "\n", "# 读取 Parquet 文件的第一行\n", "table = pq.read_table(file_path)  # 读取整个表\n", "df = table.to_pandas()  # 转换为 Pandas DataFrame\n", "\n", "# 显示完整结构和内容\n", "print(df.iloc[0].to_dict())  # 直接打印完整的第一行数据"]}, {"cell_type": "code", "execution_count": 8, "id": "e9df52f6-eaf8-4fdf-920f-15ceebcb911d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'id': 'ape210k__00300852', 'question': 'In order to help the victims of the earthquake in Sichuan, the factory rushed to make a batch of disaster relief tents. The first workshop completed (1/5) of this batch of tents, the second workshop completed (1/4) of this batch of tents, and the remaining batch of tents What percentage of it is not completed?', 'question_chinese': '为了帮助四川地震灾民，工厂赶制一批救灾帐篷，第一车间完成了这批帐篷的(1/5)，第二车间完成了这批帐篷的(1/4)，还剩下这批帐篷的几分之几没完成？', 'chain': '<gadget id=\"calculator\">1 / 5</gadget>\\n<output>1/5 = around 0.2</output>\\n\\n<gadget id=\"calculator\">1 / 4</gadget>\\n<output>1/4 = around 0.25</output>\\n\\n<gadget id=\"calculator\">1 - (1/5) - (1/4)</gadget>\\n<output>11/20 = around 0.55</output>\\n\\n<result>11/20 = around 0.55</result>', 'result': '11/20', 'result_float': 0.55, 'equation': 'x=1-(1/5)-(1/4)'}\n"]}], "source": ["import pyarrow.parquet as pq\n", "\n", "file_path = r\"D:\\pythonwork\\2025LLMtraining\\minideepseek\\data\\ape\\validation-00000-of-00001-b9c45633c2837e3b.parquet\"\n", "\n", "# 读取 Parquet 文件的第一行\n", "table = pq.read_table(file_path)  # 读取整个表\n", "df = table.to_pandas()  # 转换为 Pandas DataFrame\n", "\n", "# 显示完整结构和内容\n", "print(df.iloc[0].to_dict())  # 直接打印完整的第一行数据"]}, {"cell_type": "code", "execution_count": null, "id": "7a0ced47-4b17-4b1f-a646-a193404786f5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}