{"cells": [{"cell_type": "markdown", "id": "c5de6aba-f81e-4a72-bee1-df46a91904c4", "metadata": {}, "source": ["# 多模态大模型（1）CLIP"]}, {"cell_type": "code", "execution_count": 1, "id": "c3b0f5d0-e781-4df9-b8a9-2b6238dd9132", "metadata": {}, "outputs": [], "source": ["from collections import OrderedDict\n", "from typing import Tuple, Union\n", "\n", "import numpy as np\n", "import torch\n", "import torch.nn.functional as F\n", "from torch import nn"]}, {"cell_type": "markdown", "id": "7f21819c-7aeb-462f-82d6-f9bd8b8396bc", "metadata": {}, "source": ["## 0 开篇：多模态大模型与CLIP"]}, {"cell_type": "markdown", "id": "bbd2ce57-54ab-4f1c-bb0c-4a6916544891", "metadata": {}, "source": ["多模态大模型（Multimodal Large Model）是一种能够处理和理解多种模态数据的人工智能模型。模态指的是数据的表现形式，例如文字、图像、音频、视频等。多模态大模型通过结合不同模态数据的特性，利用深度学习技术和大规模的训练数据，构建一个统一的框架来实现跨模态的感知、理解和生成能力。例如，一个多模态大模型可以同时处理文本描述和对应的图片，完成图像生成、描述生成、跨模态检索等任务。这种模型的核心是利用不同模态之间的互补性，通过信息的融合和关联增强模型的表现能力，克服单一模态信息不足的问题。\n", "\n", "学习多模态大模型的意义在于其广泛的应用潜力和巨大的社会价值。首先，多模态大模型可以在真实世界中广泛应用于多场景任务，如自动驾驶中的视觉和语言指令结合、医疗影像分析中的图像和文本报告结合、智能客服中的语音与文字结合等。这种模型能更好地模拟人类在复杂情境中处理多种信息的能力，从而大幅提升人工智能系统的实用性和智能水平。其次，多模态大模型的研究促进了人工智能跨学科的融合，推动了计算机视觉、自然语言处理、语音识别等领域的协同发展。此外，多模态学习也对人机交互的自然性和效率有重要提升作用，帮助构建更直观、更人性化的交互系统。总之，学习和发展多模态大模型是实现通用人工智能的重要一步，能够推动技术的前沿进展，为各行各业带来深远的影响。"]}, {"cell_type": "markdown", "id": "a3674d19-6187-478d-a220-db1b583a01ec", "metadata": {}, "source": ["关于多模态模型，我们许多疑惑之处，在正式学习之前，我们要先辨析部分关于多模态大模型的概念。"]}, {"cell_type": "markdown", "id": "597ed7bc-4d74-47b2-b898-ff6a5b82e65e", "metadata": {}, "source": ["- **Q1：多模态为什么有价值？从工程的角度来说，为什么多模态模型难度高？**"]}, {"cell_type": "markdown", "id": "45019b80-d2a0-4544-9471-41dff6f8aa33", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["多模态模型能够处理更丰富的信息表达、现实又大量跨模态任务需求，且多模态很有可能是同往AGI的道路，这是多模态模型最有价值的点之一。然而，多模态数据的最大挑战在于 **它们不在一个空间中**，每种模态的结构和分布差异巨大，难以直接结合：\n", "\n", "- **图像数据**：\n", "    - 结构：4维 (N, C, H, W)。\n", "      - N：批量大小（batch size）。\n", "      - C：通道数（通常为 3，RGB）。\n", "      - H 和 W：图像高度和宽度。\n", "    - 特性：数据通常是局部相关（如相邻像素相关），需要卷积操作提取局部特征。\n", "<br><br>\n", "- **时间序列数据**：\n", "    - 结构：3维 (N, T, F)。\n", "      - N：批量大小。\n", "      - T：时间步长。\n", "      - F：特征维度（如传感器数据的特征数）。\n", "    - 特性：时间相关性强，依赖递归网络（如 RNN）或 Transformer 提取时间序列特征。\n", "<br><br>\n", "- **文本数据**：\n", "    - 结构：2维 (N, L) 或 3维 (N, L, D)\n", "      - N：批量大小。\n", "      - L：句子长度（不同样本可能长度不同）。\n", "    - 特性：离散数据，依赖序列建模（如 Transformer）理解上下文。\n", "<br><br>\n", "- **视频数据**：\n", "    - 结构：5维 (N, T, C, H, W)。\n", "      - N：批量大小。\n", "      - T：时间帧数。\n", "      - C：通道数。\n", "      - H 和 W：帧的高度和宽度。\n", "    - 特性：视频数据包含时间和空间信息，既需要卷积提取空间特征，又需要时序建模处理时间相关性。\n", "<br><br>\n", "- **音频数据**：\n", "    - 结构：3维 (N, T, F)。\n", "      - N：批量大小。\n", "      - T：时间步长。\n", "      - F：频谱特征（如 Mel 频谱的频率维度）。\n", "    - 特性：通常是时间相关数据，需要结合卷积和序列建模。"]}, {"cell_type": "markdown", "id": "66a49608-84c9-446e-9e7a-aa92265e524e", "metadata": {}, "source": ["- **Q2：都是多模态大模型、SORA/Veo2和LLava/Flamingo有什么不同？**\n", "\n", "多模态模型是能够处理多种模态（如文本、图像、视频等）数据的人工智能模型，但在细分领域中，我们通常把多模态模型分为两大类：**多模态理解模型**和**多模态生成模型**。 \n", "\n", "1. **多模态理解模型**：这类模型的输入是多模态数据（如图像和文本），但输出不一定是多模态的，通常是一个预测结果或单一模态的数据（如分类标签或文字）。\n", "   - **例子**：\n", "     - **CLIP**：输入图像和文本，用于在共享语义空间中匹配图像和文本，输出相似度或分类结果。\n", "     - **LLaVA**：输入图像和问题文本，输出自然语言回答。\n", "     - **Flamingo**：能够将视觉和文本信息结合，用于多模态对话或问答。\n", "     - **ActionCLIP**：视频理解模型，输入视频和文本描述，输出动作分类结果。\n", "<br><br>\n", "2. **多模态生成模型**：这类模型不仅可以理解多模态信息，其输出也可以是多模态的（如图像生成、视频生成或音频生成）。  \n", "   - **例子**：\n", "     - **DALL·E**：输入文本描述，生成对应的图像。\n", "     - **Stable Diffusion**：输入文本描述，生成高质量图像。\n", "     - **SORA**：输入视频和文本，生成符合语义的视频片段。\n", "     - **VideoGPT**：输入语义信息，生成动态的视频内容。\n", "     - **GATO**：支持输入和输出都是多模态的任务，例如从图像输入到动作输出。\n", "\n", "这些模型在多模态处理上展现了强大的能力，多模态理解模型通常专注于**模态间语义的对齐和推理**，多模态生成模型**则进一步实现了从语义到多模态内容的生成**。两类模型在任务和应用中互为补充，推动了多模态 AI 的快速发展。<font color =\"Red\">**特别的是，从结构上看，多模态生成模型可以理解为多模态理解模型 + 一个生成解码器**</font>，因为生成模型不仅需要理解输入，还需要在解码阶段根据输入模态生成输出模态。下面是两种多模态架构的对比——"]}, {"cell_type": "markdown", "id": "ffc596a0-f9c2-475a-90fc-f543d1cc6dbf", "metadata": {}, "source": ["| 特点                  | 多模态理解模型                                                                                                                                                  | 多模态生成模型                                                                                                                                               |\n", "|-----------------------|--------------------------------------------------------------------------------------------------------------------------------------------------|-------------------------------------------------------------------------------------------------------------------------------------------------|\n", "| **输入**           | 通常是多模态数据，例如图像、文本、视频等形式的数据，提供给模型进行理解任务。                                                                                     | 可以是多模态数据，也可以是单一模态数据，作为生成过程的条件输入，例如文本或图像。                                                                                       |\n", "| **输出**           | 输出单一模态的预测结果，例如分类标签、相似度分数、或自然语言形式的回答等。                                                                                          | 输出为生成的多模态数据，包括图像、视频、文本等内容，具有更广泛的表现形式。                                                                                           |\n", "| **架构核心**       | 1. **模态编码器（Encoders）**：每种模态独立的编码器处理输入数据，例如 CNN 或 ViT（图像），Transformer 或预训练语言模型（文本）。编码器将不同模态的数据转化为高维特征向量。<br><br>2. **模态对齐模块（Alignment Module）**：通过对比学习或跨模态注意力机制，将不同模态的特征对齐到共享的语义空间。例如，CLIP 利用对比学习对齐图像和文本的语义表示。<br><br>3. **输出头（Prediction Head）**：根据具体任务输出结果，例如分类标签、相似度分数或文本回答等内容。<br><br>- **例子**：<br>- **CLIP**：图像编码器 + 文本编码器 + 对比学习目标。<br>- **Flamingo**：视觉编码器结合文本编码器，通过注意力机制实现模态对齐。 | 1. **模态编码器（Encoders）**：和多模态理解模型类似，生成模型也有独立的模态编码器，例如文本编码器提取语义嵌入，作为生成条件输入。<br><br>2. **生成解码器（Generative Decoder）**：生成模型增加了解码器，将编码的特征转化为目标模态。<br><br>- *图像生成*：解码器通过扩散模型（如 Stable Diffusion）或 GAN 生成图像。<br>- *视频生成*：通过时间序列扩展（如 VideoGPT）生成动态视频内容。<br>- *文本生成*：使用标准 Transformer 解码器生成文本描述。<br><br>3. **跨模态交互模块（Cross-Modal Interaction Module）**：生成过程中，可能需要模态之间反复交互，例如跨模态注意力机制。例如，DALL·E 使用文本特征指导生成图像，确保生成结果符合输入语义。<br><br>- **例子**：<br>- **DALL·E**：文本编码器结合扩散解码器生成图像。<br>- **Stable Diffusion**：文本编码器结合图像生成解码器生成精细图像。<br>- **VideoGPT**：多模态编码器与视频解码器结合生成动态视频。 |\n"]}, {"cell_type": "markdown", "id": "80a3f387-1f40-4443-a7c1-135b8aa01070", "metadata": {}, "source": ["总结一下，我们可以认为理解模型和生成模型有的差异就在于——\n", "\n", "- **理解模型**：输入数据 → 编码器 → 共享语义空间 → 输出单一模态预测。\n", "- **生成模型**：输入数据 → 编码器 → 跨模态交互模块 → 解码器 → 输出多模态内容。"]}, {"cell_type": "markdown", "id": "324846ee-484d-40ef-a961-b9b86f4863fc", "metadata": {}, "source": ["- **Q3：多模态模型与多模态大模型有什么区别？多大才算是多模态大模型？**"]}, {"cell_type": "markdown", "id": "9e5ba764-3a14-4328-88cd-6eb64d25b8bf", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/05.png)"]}, {"cell_type": "markdown", "id": "27e48825-b8fe-4b73-be3e-c96095d6665c", "metadata": {}, "source": ["补充：DALLE2约有3.5B参数左右、而llava则分为8B、13B、34B三种尺寸。"]}, {"cell_type": "markdown", "id": "ba739a26-4644-4c65-bace-aa5a15a3d309", "metadata": {}, "source": ["- **Q4：多模态大模型的训练和推理需要多少运存？什么样的设备才能跑？**"]}, {"cell_type": "markdown", "id": "95df4362-e182-4d6b-9604-8f68924db590", "metadata": {}, "source": ["训练和推理多模态大模型（如参数规模为 0.4B 的 CLIP 模型）对硬件资源有一定要求。以下是对所需内存和设备的详细说明：\n", "\n", "**训练阶段：**\n", "\n", "- **显存需求：** 训练 CLIP 模型时，显存需求取决于模型大小、批次大小（batch size）和优化器状态等因素。以 0.4B 参数的 CLIP 模型为例，使用较小的批次大小（如 32）进行训练，可能需要约 16GB 或更多的显存。增大批次大小或使用更复杂的优化器将增加显存需求。\n", "\n", "- **设备要求：** 高端 GPU，如 NVIDIA RTX 3090（24GB 显存）或更强的 GPU（如 A100），适合训练此类模型。此外，分布式训练可以利用多块 GPU 或多台机器来分担计算和内存负担。\n", "\n", "**推理阶段：**\n", "\n", "- **显存需求：** 推理时，显存需求相对较低，但仍需考虑模型大小和输入数据的尺寸。对于 0.4B 参数的 CLIP 模型，8GB 至 12GB 显存的 GPU 通常足够进行推理。\n", "\n", "- **设备要求：** 中高端 GPU，如 NVIDIA GTX 1080 Ti、RTX 2080 Ti 或更高型号，能够满足推理需求。在某些情况下，使用 CPU 也可以进行推理，但速度会明显慢于 GPU。\n", "\n", "**注意事项：**\n", "\n", "- **内存需求：** 除了显存，主机的 RAM 也需要足够大，以处理数据加载和预处理等任务。建议至少具备 16GB RAM，较大的内存（如 32GB 或更多）有助于提升性能。\n", "\n", "- **存储空间：** 模型文件和数据集可能占用数十 GB 的存储空间。确保有足够的磁盘空间来存储模型权重和相关数据。\n", "\n", "- **软件环境：** 确保安装了兼容的深度学习框架（如 PyTorch）和必要的库。根据 OpenAI 的 CLIP 项目，所需的 Python 库包括 `torch`、`torchvision`、`ftfy`、`regex`、`tqdm` 和 `packaging`。 \n", "\n", "请注意，实际的硬件需求可能因具体的模型配置、数据集大小和应用场景而有所不同。在部署前，建议根据自身的任务需求进行测试和评估，以确定所需的硬件资源。 "]}, {"cell_type": "markdown", "id": "3419ac2d-6532-4162-8360-f9334497f49f", "metadata": {}, "source": ["- **Q5：为什么是CLIP？为什么从CLIP开始？**"]}, {"cell_type": "markdown", "id": "492404a8-b6d7-45dd-a919-065c18641643", "metadata": {}, "source": ["CLIP（Contrastive Language-Image Pre-training）是OpenAI于2021年发布的一种**多模态预训练模型**，它能够将图像和文本嵌入到同一向量空间中，实现同一模型系统下的跨模态理解。其基本结构包括图像编码器和文本编码器，分别处理图像和文本数据。图像编码器通常采用卷积神经网络（如ResNet）或Vision Transformer（ViT），而文本编码器则使用Transformer模型。这些编码器分别处理图像和文本数据，并将它们投影到一个共同的嵌入空间中、从而实现图文分类、文本-图像检索等基础的多模态任务。\n", "\n", "对于CLIP，我们今天要学习和掌握它的关键原因在于——\n", "\n", "1. **独创性的架构**：CLIP是一个无监督的、端到端的多模态系统，它是图文同向量空间对齐思路下效果最好的模型之一，它采用了经典的Transformer架构，是我们理解深度学习世界星罗棋布的多模态算法的根基。<br><br>\n", "2. **独创性的预训练流程**：CLIP创新了精彩的预训练方式对比学习预训练（contrastive learning）、这种训练方式不仅不需要对数据进行标注、还使得多模态模型的训练样本从“特定任务数据”拓展到了“”使得CLIP能够在下游任务中实现零样本学习，即在未见过的类别上进行分类或检索、使其在文本生成图像描述、跨模态搜索、图文匹配等任务中展现了前所未有的灵活性和效率、大大拓展了多模态模型的应用场景。<br><br>\n", "3. **Llava必备基础**：多模态大模型LLava借鉴了CLIP的思想与板块，因此理解CLIP可以帮我们更好地学习Llava模型<br><br>\n", "4. **非常好的顶会文章基础**：光是2024年一年内，牛津大学、复旦大学、上海佳通大学、以及微软研究院、Google研究院都分别以CLIP为核心主题发表了CVPR论文，CLIP可以说是非常好的链接深度学习与大语言模型的关键研究点。"]}, {"cell_type": "markdown", "id": "1a0cad0c-861e-41e5-aa2d-d19623086826", "metadata": {}, "source": ["-----\n", "\n", "截至2024年，关于CLIP（Contrastive Language-Image Pre-training）的研究在多个顶级会议上发表。以下是一些具有代表性的论文及其链接："]}, {"cell_type": "markdown", "id": "f1d596ee-3403-4eba-b350-ac072b865d1f", "metadata": {"jupyter": {"source_hidden": true}}, "source": ["1. **Alpha-CLIP: A CLIP Model Focusing on Wherever You Want**  \n", "   该论文提出了Alpha-CLIP模型，旨在增强CLIP在特定图像区域的关注能力，使其能够更精确地处理用户指定的图像区域。  \n", "   *发表会议：2024年IEEE计算机视觉与模式识别会议（CVPR 2024）*  \n", "   [论文链接](https://openaccess.thecvf.com/content/CVPR2024/html/Sun_Alpha-CLIP_A_CLIP_Model_Focusing_on_Wherever_You_Want_CVPR_2024_paper.html)\n", "\n", "2. **MoDE: CLIP Data Experts via Clustering**  \n", "   该研究提出了MoDE方法，通过对数据进行聚类，训练一组CLIP数据专家，以提升模型在多样化数据上的表现。  \n", "   *发表会议：2024年IEEE计算机视觉与模式识别会议（CVPR 2024）*\n", "   [论文链接](https://openaccess.thecvf.com/content/CVPR2024/html/Ma_MoDE_CLIP_Data_Experts_via_Clustering_CVPR_2024_paper.html)\n", "\n", "3. **CLIP-ProbCR: CLIP-based Probability Embedding Combination Retrieval**\n", "    该论文提出了一个使用文本和图像组合进行检索的图像检索模型，利用 CLIP 编码文本与图像，获得对应的特征，通过对特征添加概率分布获得特征的高斯概率密度函数，最后使用概率密度函数之间的乘法法则获得组合特征的表示。\n", "    发表会议：2024 年多媒体检索国际会议（ICMR 2024）\n", "    [论文链接](https://dl.acm.org/doi/10.1145/3652583.3657611)\n", "\n", "4. **CLIP as RNN: Segment Countless Visual Concepts without Training Endeavor**\n", "    该论文基于CLIP提出了全新的零样本图像分割方法，通过循环框架将 CLIP 的开放词汇能力与分割任务相结合，逐步过滤无关文本并提高掩码质量、这样既拓展了 CLIP 的应用范围，也突破了现有图像分割方法的局限性。\n", "    *发表会议：2024年IEEE计算机视觉与模式识别会议（CVPR 2024）*\n", "    [论文链接](https://arxiv.org/abs/2312.07661)\n", "\n", "5. **Highlight-CLIP: Unleash the Potential of CLIP for Video Highlight Detection**  \n", "   该论文提出了Highlight-CLIP方法，利用CLIP的预训练知识，提升视频高光检测任务的性能。  \n", "   *发表会议：2024年IEEE计算机视觉与模式识别会议工作坊（CVPRW 2024）*  \n", "   [论文链接](https://openaccess.thecvf.com/content/CVPR2024W/ELVM/html/<PERSON>_Unleash_the_Potential_of_CLIP_for_Video_Highlight_Detection_CVPRW_2024_paper.html)\n", "\n", "6. **MobileCLIP: Fast Image-Text Models through Multi-Modal Reinforced Training**\n", "    论文提出了一个名为 MobileCLIP 的高效图文模型家族。MobileCLIP 针对 CLIP 模型的特性和性能瓶颈（如计算资源需求和延迟问题），进行了优化，以便能够更好地适应移动设备等资源受限的环境，同时，论文设计了一种多模态强化训练（multi-modal reinforced training）方法，充分利用了 CLIP 模型的能力，这些工作属于对 CLIP 模型的优化和工程改进，并充分体现了 CLIP 模型在多模态领域的强大潜力。\n", "   *发表会议：2024年IEEE计算机视觉与模式识别会议（CVPR 2024）*  \n", "    [论文链接](https://arxiv.org/abs/2311.17049)"]}, {"cell_type": "markdown", "id": "84bbf425-16a3-4b16-92fc-d46bb79516b3", "metadata": {}, "source": ["还有的大量我没有去记录期刊的论文——"]}, {"cell_type": "markdown", "id": "d0a5b5e3-c048-4c17-969c-8d904e39580a", "metadata": {"jupyter": {"source_hidden": true}}, "source": ["1. **FLEX-CLIP: Feature-Level Generation Network Enhanced CLIP for X-shot Learning**  \n", "   该论文介绍了FLEX-CLIP模型，通过特征级生成网络增强CLIP的能力，旨在解决小样本学习中的数据不平衡和特征退化问题。  \n", "   *发表会议/期刊：信息待补充*  \n", "   [论文链接](https://arxiv.org/abs/2411.17454)\n", "\n", "2. **TripletCLIP: Improving Compositional Reasoning of CLIP via Triplet Loss**  \n", "   该研究提出了TripletCLIP方法，通过引入三元组损失，增强CLIP的组合推理能力，在复杂概念理解上取得了显著提升。  \n", "   *发表会议/期刊：信息待补充*  \n", "   [论文链接](https://arxiv.org/abs/2411.02545)\n", "\n", "3. **FastCLIP: A Suite of Optimization Techniques to Accelerate CLIP Training with Limited Resources**  \n", "   该论文提出了FastCLIP框架，旨在通过一系列优化技术，在有限资源下加速CLIP的训练过程。  \n", "   *发表会议/期刊：信息待补充*  \n", "   [论文链接](https://arxiv.org/abs/2407.01445)\n", "\n", "4. **<PERSON><PERSON> (Down) CLIP: A Comprehensive Analysis of Data, Architecture, and Training Strategies**  \n", "   该研究对CLIP的缩放进行了全面分析，探讨了数据、架构和训练策略对模型性能的影响，为资源受限情况下的模型训练提供了指导。  \n", "   *发表会议/期刊：信息待补充*  \n", "   [论文链接](https://arxiv.org/abs/2404.08197)\n", "\n", "5. **CLIP in Medical Imaging: A Comprehensive Survey**  \n", "    该综述论文系统地总结了CLIP在医学影像领域的应用，展示了其在多种临床任务中的潜力和挑战。  \n", "    *发表会议/期刊：信息待补充*  \n", "    [论文链接](https://arxiv.org/abs/2312.07353)"]}, {"cell_type": "markdown", "id": "c8076d27-aea9-48e2-9188-7a0166249a4c", "metadata": {}, "source": ["总而言之CLIP是具备跨模态检索、零样本分类能力的跨时代突破性架构，是多模态学习领域的根基，今天就让我们一起来看看这个经典的多模态模型。"]}, {"cell_type": "markdown", "id": "613f0c6b-137a-4708-8a54-ba5b40cb1d04", "metadata": {}, "source": ["## 1. CLIP模型架构"]}, {"cell_type": "markdown", "id": "974acf33-a847-4a60-85f2-633be97a14f1", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/01.png)"]}, {"cell_type": "markdown", "id": "e7afed30-4a9d-476f-a7c3-76a575513a6d", "metadata": {}, "source": ["从这张图可以看出，CLIP的模型架构包含三个主要部分：**图像编码器**、**文本编码器** 和 **余弦相似度计算模块**。CLIP 是一个强大的多模态对比学习模型，通过图像编码器和文本编码器分别对图像和文本进行特征提取，并在共享语义空间中对齐两种模态，最后通过余弦相似度实现跨模态任务的统一处理。"]}, {"cell_type": "markdown", "id": "a3e3e13a-f596-4b7a-a4fc-a3892da1f331", "metadata": {}, "source": ["### 1.1 图像编码器"]}, {"cell_type": "markdown", "id": "179b5705-49e9-4b73-a342-b7968df440d5", "metadata": {}, "source": ["在 **CLIP**（Contrastive Language-Image Pretraining）中，选择使用 **残差网络（ResNet）** 或 **视觉Transformer（ViT）** 取决于任务需求、计算资源、模型复杂度以及性能目标。两者各有优劣——\n", "\n", "ResNet作为CLIP的图像编码器，适用于以下情况：\n", "\n", "- **轻量化场景**：  \n", "  ResNet的计算效率较高，尤其在参数量和计算复杂度受限时，其优化的卷积设计和残差连接使其在嵌入计算效率和性能之间取得较好平衡。\n", "\n", "- **小规模数据和有限资源**：  \n", "  如果计算资源有限或需要快速部署，ResNet的高效架构更适合，因为它训练速度较快，并且对小数据集的泛化能力较强。\n", "\n", "- **对局部特征依赖较强的任务**：  \n", "  ResNet通过卷积操作擅长提取图像的局部特征，因此在局部信息对模型性能至关重要的任务中表现较好。\n", "\n", "- **经典的卷积架构迁移**：  \n", "  ResNet是传统卷积神经网络的代表，许多现成的优化方法和技术（如权重初始化、批归一化、迁移学习）都能直接套用，便于快速调整和扩展。\n", "\n", "---\n", "\n", "ViT作为CLIP的图像编码器，适用于以下情况：\n", "\n", "- **大规模数据集（如LAION-400M等）**：  \n", "  ViT对大规模训练数据表现更佳，因为其自注意力机制能够捕获更丰富的全局信息，并且在数据充足的情况下展现出极强的特征学习能力。\n", "\n", "- **对全局特征建模需求高的任务**：  \n", "  ViT通过Transformer架构的自注意力机制，可以更好地捕获图像的全局关系，因此在需要语义对齐或全局信息理解的任务中效果更强。\n", "\n", "- **追求更高性能和模型可扩展性**：  \n", "  ViT在模型扩展性和性能上有优势，尤其是在高计算资源场景中，ViT能够利用更深层次的注意力机制提升图像表征能力。\n", "\n", "- **跨领域迁移能力强**：  \n", "  ViT具备较好的跨领域迁移能力，在图像特征需要进一步跨模态对齐或迁移到其他任务时表现出色。\n", "\n", "---\n", "\n", "- **数据规模和计算资源**：  \n", "  如果数据规模较小，或者计算资源受限，优先选择ResNet。如果有足够的数据和计算资源，则ViT表现更优。\n", "\n", "- **特征建模需求**：  \n", "  ResNet更适合依赖局部特征的任务，而ViT更适合全局语义建模。\n", "\n", "- **部署需求**：  \n", "  ResNet因其较低的计算需求和较高的部署成熟度，适合需要快速、轻量部署的场景；而ViT适合在追求性能极限的离线或高性能计算环境中使用。\n", "\n", "在CLIP中，OpenAI已经实现了**基于ResNet和ViT的两种模型版本**，用户可以根据任务需求选择最适合的编码器架构。"]}, {"cell_type": "markdown", "id": "60acddc4-a061-4748-a997-5ebe421c8e3f", "metadata": {}, "source": ["#### 1.1.1 残差网络"]}, {"cell_type": "markdown", "id": "13428821-e850-462e-8563-2c6e20e6dd5d", "metadata": {}, "source": ["- **stem**"]}, {"cell_type": "markdown", "id": "babb3447-e940-48c0-bbba-149f3e031e1c", "metadata": {}, "source": ["在深度学习中，Stem 是指网络的起始部分（通常是前几层），用于处理原始输入数据（如图像）并将其转换为更适合后续处理的特征表示。Stem通常由一些简单的卷积操作或其他特征提取模块组成，旨在快速压缩输入数据的维度，并提取初步特征。\n", "\n", "残差网络（ResNet）中的Stem\n", "在 ResNet 中，Stem模块是模型的最前端部分，通常负责对原始图像进行基本处理。这部分的设计决定了输入图像的特征提取效率以及后续网络的性能和计算复杂度。"]}, {"cell_type": "markdown", "id": "1196a3dc-567d-45c2-95d9-906e947773b0", "metadata": {}, "source": ["- **基础的残差网络结构与残差层**"]}, {"cell_type": "markdown", "id": "d920cacc-6912-4d20-ab44-799461bc904e", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/02.png)"]}, {"cell_type": "code", "execution_count": 2, "id": "3dbad94a-1cf0-4911-a3ad-4edcaa8e3e86", "metadata": {}, "outputs": [], "source": ["class Bottleneck(nn.Module):\n", "    # 定义扩展倍数，用于决定输出通道数的扩展比例\n", "    expansion = 4\n", "\n", "    def __init__(self, inplanes, planes, stride=1):\n", "        \"\"\"\n", "        Bottleneck 模块的初始化方法。\n", "        参数：\n", "        - inplanes: 输入通道数。\n", "        - planes: 每一层卷积的基础通道数，最终会扩展为 planes * expansion。\n", "        - stride: 步幅，控制空间分辨率的缩小（默认值为1）。\n", "        \"\"\"\n", "        super().__init__()\n", "\n", "        # 第1个卷积层，使用 1x1 卷积，用于减少计算量\n", "        self.conv1 = nn.Conv2d(inplanes, planes, kernel_size=1, bias=False)\n", "        self.bn1 = nn.BatchNorm2d(planes)  # 批归一化\n", "        self.relu1 = nn.ReLU(inplace=True)  # 激活函数，inplace=True 节省内存\n", "\n", "        # 第2个卷积层，使用 3x3 卷积，用于提取特征\n", "        self.conv2 = nn.Conv2d(planes, planes, kernel_size=3, padding=1, bias=False)\n", "        self.bn2 = nn.BatchNorm2d(planes)  # 批归一化\n", "        self.relu2 = nn.ReLU(inplace=True)  # 激活函数\n", "\n", "        # 如果 stride > 1，使用 AvgPool2d 进行空间降采样，否则使用 Identity（保持不变）\n", "        self.avgpool = nn.AvgPool2d(stride) if stride > 1 else nn.Identity()\n", "\n", "        # 第3个卷积层，使用 1x1 卷积，用于扩展通道数至 planes * expansion\n", "        self.conv3 = nn.Conv2d(planes, planes * self.expansion, kernel_size=1, bias=False)\n", "        self.bn3 = nn.BatchNorm2d(planes * self.expansion)  # 批归一化\n", "        self.relu3 = nn.ReLU(inplace=True)  # 激活函数\n", "\n", "        # 如果输入和输出的通道数或分辨率不同，则需要通过 downsample 进行调整\n", "        self.downsample = None\n", "        self.stride = stride\n", "\n", "        if stride > 1 or inplanes != planes * Bottleneck.expansion:\n", "            # downsample 模块，用于调整 shortcut 的形状，使其与主路径的输出形状匹配\n", "            self.downsample = nn.Sequential(OrderedDict([\n", "                # 使用 AvgPool2d 进行空间降采样（仅在 stride > 1 时）\n", "                (\"-1\", nn.AvgPool2d(stride)),\n", "                # 1x1 卷积调整通道数\n", "                (\"0\", nn.Conv2d(inplanes, planes * self.expansion, kernel_size=1, stride=1, bias=False)),\n", "                # 批归一化\n", "                (\"1\", nn.<PERSON>ch<PERSON>orm2d(planes * self.expansion))\n", "            ]))\n", "\n", "    def forward(self, x: torch.Tensor):\n", "        \"\"\"\n", "        前向传播函数。\n", "        参数：\n", "        - x: 输入张量，形状为 (batch_size, inplanes, H, W)\n", "        返回值：\n", "        - 输出张量，形状为 (batch_size, planes * expansion, H / stride, W / stride)\n", "        \"\"\"\n", "        identity = x  # 保存输入作为 shortcut（残差连接的输入）\n", "\n", "        # 主路径：第1个卷积 + BN + ReLU\n", "        out = self.relu1(self.bn1(self.conv1(x)))\n", "        # 主路径：第2个卷积 + BN + ReLU\n", "        out = self.relu2(self.bn2(self.conv2(out)))\n", "        # 如果 stride > 1，进行空间降采样\n", "        out = self.avgpool(out)\n", "        # 主路径：第3个卷积 + BN\n", "        out = self.bn3(self.conv3(out))\n", "\n", "        # 如果 downsample 不为 None，则对 identity 进行调整\n", "        if self.downsample is not None:\n", "            identity = self.downsample(x)\n", "\n", "        # 残差连接：将主路径的输出和 shortcut 相加\n", "        out += identity\n", "        # 激活函数\n", "        out = self.relu3(out)\n", "        return out"]}, {"cell_type": "markdown", "id": "c7d0b679-39f4-40a0-8732-146a20f445ae", "metadata": {}, "source": ["- **AttentionPool 注意力池化**"]}, {"cell_type": "markdown", "id": "cff83e5b-778f-48c4-aad6-f68411022079", "metadata": {}, "source": ["Attention Pooling 是 CLIP 的 **Modified ResNet** 架构中用来替代传统全局平均池化（Global Average Pooling, GAP）的方法、同时对整个CLIP来说也是重要的**向量压缩层**。在经过了 `stem` 和多个 `Residual Layers`（`layer1-layer4`）的特征提取后，网络会输出一个形状为 `(batch_size, channels, height, width)` 的特征图，Attention Pooling 会将这个特征图整合为一个全局特征向量，表示整幅图像的特征、从而方便后续的投影。因此Attention Pooling 的主要任务是将二维的特征图 (batch_size, channels, height, width) 转换为一个全局特征向量 (batch_size, embed_dim)。\n", "\n", "和传统的全局平均池化相比，注意力池化可以**对特征图上的每个位置（像素点）的重要性**进行加权、这样生成的全局特征向量能够更好地捕获空间上的信息以及全局上下文关系。我们来看一下Attention Pooling 的具体实现代码（同时纯享版代码在py文件中哦）——"]}, {"cell_type": "code", "execution_count": 3, "id": "888e3ec3-c8d7-4412-a481-87f3867b356f", "metadata": {}, "outputs": [], "source": ["class AttentionPool2d(nn.Module):\n", "    def __init__(self, spacial_dim: int, embed_dim: int, num_heads: int, output_dim: int = None):\n", "        \"\"\"\n", "        初始化 Attention Pooling 模块。\n", "\n", "        参数：\n", "        - spacial_dim (int): 输入特征图的空间维度（即特征图宽高的大小，假设特征图是正方形）。\n", "        - embed_dim (int): 输入特征的嵌入维度（通道数）。\n", "        - num_heads (int): 多头注意力的头数。\n", "        - output_dim (int, optional): 输出特征的目标维度。如果未指定，则默认为 embed_dim。\n", "        \"\"\"\n", "        super().__init__()\n", "        # 位置嵌入 (Positional Embedding)，初始化为随机值，大小为 (spacial_dim^2 + 1, embed_dim)\n", "        # 额外的 \"+1\" 是为全局 token 预留的位置。\n", "        self.positional_embedding = nn.Parameter(torch.randn(spacial_dim ** 2 + 1, embed_dim) / embed_dim ** 0.5)\n", "\n", "        # Query、Key、Value 的线性投影\n", "        self.k_proj = nn.Linear(embed_dim, embed_dim)\n", "        self.q_proj = nn.Linear(embed_dim, embed_dim)\n", "        self.v_proj = nn.Linear(embed_dim, embed_dim)\n", "\n", "        # 输出投影，将嵌入维度从 embed_dim 调整为 output_dim\n", "        self.c_proj = nn.Linear(embed_dim, output_dim or embed_dim)\n", "\n", "        # 多头注意力的头数\n", "        self.num_heads = num_heads\n", "\n", "    def forward(self, x):\n", "        \"\"\"\n", "        前向传播函数，执行 Attention Pooling 操作。\n", "\n", "        参数：\n", "        - x (Tensor): 输入特征图，形状为 (batch_size, channels, height, width)。\n", "\n", "        返回：\n", "        - Tensor: 输出的全局特征向量，形状为 (batch_size, output_dim)。\n", "        \"\"\"\n", "        # 1. 展平特征图并转置\n", "        # 原始形状：x -> (batch_size, channels, height, width)\n", "        # 转换后：x -> (height * width, batch_size, channels)\n", "        x = x.flatten(start_dim=2).permute(2, 0, 1)\n", "\n", "        # 2. 添加全局 token\n", "        # 全局 token 是整幅特征图的均值，用于捕获全局信息。\n", "        # `x.mean(dim=0, keepdim=True)` 的形状为 (1, batch_size, channels)\n", "        # 添加到特征图的前面，新的 x 形状为 (height * width + 1, batch_size, channels)\n", "        x = torch.cat([x.mean(dim=0, keepdim=True), x], dim=0)\n", "\n", "        # 3. 添加位置嵌入\n", "        # self.positional_embedding 的形状为 (height * width + 1, embed_dim)\n", "        # 将位置嵌入添加到特征图上，每个位置都会加上对应的嵌入值。\n", "        x = x + self.positional_embedding[:, None, :].to(x.dtype)\n", "\n", "        # 4. 执行多头注意力\n", "        # 注意力操作的参数：\n", "        # - query=x[:1]: 使用全局 token 作为 Query，形状为 (1, batch_size, embed_dim)。\n", "        # - key=x: 使用整个特征图（包括全局 token）作为 Key，形状为 (height * width + 1, batch_size, embed_dim)。\n", "        # - value=x: 同上，使用整个特征图作为 Value。\n", "        # 注意力机制的输出形状为 (1, batch_size, embed_dim)，即仅保留全局 token 的注意力输出。\n", "        x, _ = F.multi_head_attention_forward(\n", "            query=x[:1],  # 全局 token 作为 Query\n", "            key=x,  # 整个特征图作为 Key\n", "            value=x,  # 整个特征图作为 Value\n", "            embed_dim_to_check=x.shape[-1],  # 嵌入维度\n", "            num_heads=self.num_heads,  # 注意力头的数量\n", "            q_proj_weight=self.q_proj.weight,  # Query 的投影权重\n", "            k_proj_weight=self.k_proj.weight,  # Key 的投影权重\n", "            v_proj_weight=self.v_proj.weight,  # Value 的投影权重\n", "            in_proj_weight=None,  # 不使用合并的投影权重\n", "            in_proj_bias=torch.cat([self.q_proj.bias, self.k_proj.bias, self.v_proj.bias]),  # Query, Key, Value 的偏置\n", "            bias_k=None,  # Key 的额外偏置（未使用）\n", "            bias_v=None,  # Value 的额外偏置（未使用）\n", "            add_zero_attn=False,  # 不添加额外的全零注意力头\n", "            dropout_p=0,  # 不使用 Dropout\n", "            out_proj_weight=self.c_proj.weight,  # 输出投影的权重\n", "            out_proj_bias=self.c_proj.bias,  # 输出投影的偏置\n", "            use_separate_proj_weight=True,  # 使用单独的 Query, Key, Value 投影权重\n", "            training=self.training,  # 根据当前模式确定是否训练\n", "            need_weights=False  # 不需要返回注意力权重\n", "        )\n", "\n", "        # 5. 移除第一个维度\n", "        # 注意力输出的形状为 (1, batch_size, embed_dim)，去掉第一个维度后，变为 (batch_size, embed_dim)。\n", "        return x.squeeze(0)"]}, {"cell_type": "markdown", "id": "0a3d5ae6-4e03-4142-baa3-34c5273e8589", "metadata": {}, "source": ["- **输入：**\n", "  - 特征图 `(batch_size, channels, height, width)`。\n", "  - 特征图被展平到 `(spatial_dim, batch_size, embed_dim)` 以适配注意力机制。\n", "<br><br>\n", "- **处理步骤：**<br><br>\n", "  1. **Flatten 特征图**:\n", "     - 将特征图展平（flatten），从 `(N, C, H, W)` 变为 `(HW, N, C)`，即空间位置作为序列。\n", "  2. **添加全局 token**:\n", "     - 为每个图像添加一个全局 token，代表全局的上下文信息。\n", "  3. **位置嵌入 (Positional Embedding)**:\n", "     - 为每个位置加上位置信息，保留空间结构。\n", "  4. **多头注意力机制**:\n", "     - 使用多头注意力，计算每个位置的重要性权重，并整合为全局特征向量。\n", "  5. **输出变换**:\n", "     - 通过线性投影层调整最终输出的维度（`output_dim`）。\n", "<br><br>\n", "- **输出：**\n", "  - 一个全局特征向量，表示整幅图像的特征。"]}, {"cell_type": "code", "execution_count": 4, "id": "bc529eac-d092-4008-be44-cf90ce08fb62", "metadata": {}, "outputs": [], "source": ["# 假设输入数据\n", "batch_size = 4  # 假设有 4 个样本\n", "channels = 512  # 从 Bottleneck 输出的特征通道\n", "height = 7      # 特征图的高度\n", "width = 7       # 特征图的宽度\n", "spacial_dim = height  # 假设输入特征图是正方形\n", "embed_dim = channels  # 嵌入维度与输入通道数一致\n", "num_heads = 8  # 多头注意力头数\n", "output_dim = 256  # 输出特征维度\n", "\n", "# 生成模拟输入数据（模拟从 Bottleneck 输出的特征图）\n", "input_data = torch.randn(batch_size, channels, height, width)"]}, {"cell_type": "code", "execution_count": 5, "id": "a49b7d0a-fa6b-4d32-a58f-246d4da30406", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.Si<PERSON>([4, 512, 7, 7])"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["input_data.shape"]}, {"cell_type": "code", "execution_count": 6, "id": "0a449a00-9f50-4b54-a8ab-7a45d4297d0e", "metadata": {}, "outputs": [], "source": ["# 创建 AttentionPool2d 实例\n", "attention_pool = AttentionPool2d(spacial_dim, embed_dim, num_heads, output_dim)\n", "\n", "# 前向传播\n", "output = attention_pool(input_data)"]}, {"cell_type": "code", "execution_count": 7, "id": "1190cb0a-2fa0-4890-8fc2-86c7e19c5c7e", "metadata": {}, "outputs": [{"data": {"text/plain": ["(<PERSON><PERSON><PERSON><PERSON>([4, 256]),\n", " tensor([[ 0.0342,  0.0482,  0.0279,  ..., -0.0458,  0.0388,  0.1614],\n", "         [-0.0745,  0.0592,  0.0461,  ..., -0.0438,  0.0673,  0.0063],\n", "         [ 0.0225,  0.0971,  0.0892,  ..., -0.0572,  0.0491,  0.0798],\n", "         [-0.0118,  0.1063,  0.0430,  ...,  0.0047, -0.0379, -0.0116]],\n", "        grad_fn=<SqueezeBackward1>))"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["output.shape, output"]}, {"cell_type": "markdown", "id": "c552d961-b9cc-4f21-a882-3b047c4c5eab", "metadata": {}, "source": ["- **完整的残差网络实现**"]}, {"cell_type": "code", "execution_count": 8, "id": "79f9c7f4-228e-460e-b131-4610d76f3677", "metadata": {}, "outputs": [], "source": ["class ModifiedResNet(nn.Module):\n", "    \"\"\"\n", "    一个基于 ResNet 的改进版本，包含以下变化：\n", "    - 起始部分使用了 3 层卷积（stem），而不是原始 ResNet 的单层卷积，同时将 max pool 替换为 avg pool。\n", "    - 对于 stride > 1 的卷积层，使用了反锯齿（anti-aliasing）的卷积方式，即在卷积前添加了 avg pool。\n", "    - 最终的全局池化层替换为 QKV 注意力池化层，而不是传统的 average pool。\n", "    \"\"\"\n", "\n", "    def __init__(self, layers, output_dim, heads, input_resolution=224, width=64):\n", "        \"\"\"\n", "        初始化函数。\n", "        参数：\n", "        - layers: 每个阶段的残差块数量（如 [3, 4, 6, 3] 表示每个阶段的残差块数）。\n", "        - output_dim: 输出特征的维度。\n", "        - heads: 注意力池化的头数。\n", "        - input_resolution: 输入图像的分辨率（默认 224）。\n", "        - width: 初始通道宽度（默认 64）。\n", "        \"\"\"\n", "        super().__init__()\n", "        self.output_dim = output_dim\n", "        self.input_resolution = input_resolution\n", "\n", "        # 初始化阶段（stem）：3 层卷积代替原来的单层卷积\n", "        self.conv1 = nn.Conv2d(3, width // 2, kernel_size=3, stride=2, padding=1, bias=False)  # 输入通道为3，输出为 width//2\n", "        self.bn1 = nn.BatchNorm2d(width // 2)\n", "        self.relu1 = nn.ReLU(inplace=True)\n", "        self.conv2 = nn.Conv2d(width // 2, width // 2, kernel_size=3, padding=1, bias=False)\n", "        self.bn2 = nn.BatchNorm2d(width // 2)\n", "        self.relu2 = nn.ReLU(inplace=True)\n", "        self.conv3 = nn.Conv2d(width // 2, width, kernel_size=3, padding=1, bias=False)  # 输出为 width\n", "        self.bn3 = nn.BatchNorm2d(width)\n", "        self.relu3 = nn.ReLU(inplace=True)\n", "        self.avgpool = nn.AvgPool2d(2)  # 平均池化，用于下采样\n", "\n", "        # 残差层\n", "        self._inplanes = width  # 初始输入通道数，用于动态调整残差块的输入\n", "        self.layer1 = self._make_layer(width, layers[0])  # 第一阶段残差块\n", "        self.layer2 = self._make_layer(width * 2, layers[1], stride=2)  # 第二阶段残差块，输出通道翻倍\n", "        self.layer3 = self._make_layer(width * 4, layers[2], stride=2)  # 第三阶段残差块\n", "        self.layer4 = self._make_layer(width * 8, layers[3], stride=2)  # 第四阶段残差块\n", "\n", "        # 注意力池化层\n", "        embed_dim = width * 32  # ResNet 的特征维度，等于最后一个阶段的输出通道数\n", "        self.attnpool = AttentionPool2d(input_resolution // 32, embed_dim, heads, output_dim)  # QKV 注意力池化\n", "\n", "    def _make_layer(self, planes, blocks, stride=1):\n", "        \"\"\"\n", "        创建一个残差模块（由多个 Bottleneck 组成）。\n", "        参数：\n", "        - planes: 当前阶段的通道数。\n", "        - blocks: 残差块数量。\n", "        - stride: 当前阶段的步幅。\n", "        返回值：\n", "        - 一个由多个残差块组成的序列模块。\n", "        \"\"\"\n", "        layers = [Bottleneck(self._inplanes, planes, stride)]  # 第一个残差块，可能需要调整步幅\n", "        self._inplanes = planes * Bottleneck.expansion  # 更新通道数\n", "        for _ in range(1, blocks):\n", "            layers.append(Bottleneck(self._inplanes, planes))  # 后续残差块保持步幅为 1\n", "\n", "        return nn.Sequential(*layers)  # 返回残差块的序列\n", "\n", "    def forward(self, x):\n", "        \"\"\"\n", "        前向传播函数。\n", "        参数：\n", "        - x: 输入张量，形状为 (batch_size, channels, height, width)。\n", "        返回值：\n", "        - x: 输出张量，经过特征提取和注意力池化后。\n", "        \"\"\"\n", "        def stem(x):\n", "            \"\"\"\n", "            起始阶段（stem）：连续 3 层卷积 + 平均池化。\n", "            参数：\n", "            - x: 输入张量。\n", "            返回值：\n", "            - x: 经过 stem 阶段后的特征张量。\n", "            \"\"\"\n", "            x = self.relu1(self.bn1(self.conv1(x)))  # 第1层卷积\n", "            x = self.relu2(self.bn2(self.conv2(x)))  # 第2层卷积\n", "            x = self.relu3(self.bn3(self.conv3(x)))  # 第3层卷积\n", "            x = self.avgpool(x)  # 平均池化\n", "            return x\n", "\n", "        x = x.type(self.conv1.weight.dtype)  # 将输入类型调整为卷积权重的类型（支持混合精度训练）\n", "        x = stem(x)  # 经过起始阶段\n", "        x = self.layer1(x)  # 第一阶段残差层\n", "        x = self.layer2(x)  # 第二阶段残差层\n", "        x = self.layer3(x)  # 第三阶段残差层\n", "        x = self.layer4(x)  # 第四阶段残差层\n", "        x = self.attnpool(x)  # 最后通过注意力池化\n", "        return x"]}, {"cell_type": "code", "execution_count": 10, "id": "cf2a3bf8-cafd-46c5-a597-c1de644d4627", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["输出形状: torch.<PERSON><PERSON>([10, 512])\n"]}], "source": ["# 实例化模型\n", "model = ModifiedResNet(layers=[3, 4, 6, 3], output_dim=512, heads=8)\n", "\n", "# 输入示例数据\n", "input_data = torch.randn(10, 3, 224, 224)  # (batch_size, channels, height, width)\n", "output = model(input_data)\n", "\n", "print(\"输出形状:\", output.shape)"]}, {"cell_type": "markdown", "id": "64e06df3-b511-4e99-9e42-2f811173b42a", "metadata": {}, "source": ["如你所见，残差网络已经将数据结构转变为了一个二维向量。"]}, {"cell_type": "markdown", "id": "7ae93a9f-83f6-4c34-b110-53f78788d208", "metadata": {}, "source": ["#### 1.1.2 Vision Transformer"]}, {"cell_type": "markdown", "id": "eab749c1-1678-40e4-ba51-43e45935db75", "metadata": {}, "source": ["**Vision Transformer (ViT)** 是一种将 Transformer 架构应用于计算机视觉任务的深度学习模型，旨在利用 Transformer 的全局建模能力处理图像数据。与传统的卷积神经网络（CNN）不同，ViT 不依赖卷积操作，而是通过将图像分割为固定大小的子块（Patch）并将这些子块视为序列输入，结合自注意力机制对全局特征进行建模。ViT 的主要特点在于，它能够直接处理图像的全局上下文关系，尤其在大规模数据集上（如 ImageNet 或 LAION）展现了极高的性能。\n", "\n", "ViT的特性就是能够处理图像信息、并将图像信息转变为Transformer能够读取的信息。在理解Transformer和卷积层的基础上，ViT并不难，让我们来解答关于ViT的几个最为核心的问题——\n", "\n", "- <font color=\"red\">**Q1：ViT如何将图像信息转变为Transformer能够读取的序列？**\n", "\n", "在 ViT 中，负责转变信息的结构叫做**Patch Embedding**。传统图像输入是三维的张量（如形状为 $ H \\times W \\times C $，分别表示图像高度、宽度和通道数），而 Transformer 的输入要求是一维的序列。因此，**Patch Embedding 的任务是将图像转化为适合 Transformer 输入的嵌入序列**。具体来说，ViT 会将输入图像按照固定大小 $ P \\times P $ 分割成若干个不重叠的子块（Patch），每个 Patch 被展平为一维向量，形状为 $ P^2 \\cdot C $，其中 $ C $ 是图像的通道数。随后，使用一个线性投影层（通常是一个全连接层）将每个 Patch 的一维向量投影到 $ D $ 维的特征空间中（$ D $ 是 Transformer 的隐藏层维度），生成对应的 Patch Embedding。我们可以来看一下具体的流程——"]}, {"cell_type": "markdown", "id": "d2877893-6878-483d-95bb-6ae2ca243408", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/06_1.png)"]}, {"cell_type": "markdown", "id": "73857f0d-8560-4f9b-a032-fc9a246ddb9b", "metadata": {}, "source": ["如你所见，ViT是将每个分开的patch中所有的像素整合成了一个向量，通过这种方式就可以让图像信息被转变为Transformer能够接纳的三维向量。这个过程用代码实现是 ↓ "]}, {"cell_type": "code", "execution_count": 42, "id": "606ea020-5f15-4a64-85f6-8ff2ffb34936", "metadata": {}, "outputs": [], "source": ["import torch\n", "import torch.nn as nn\n", "\n", "class PatchEmbedding(nn.Module):\n", "    def __init__(self, img_size=224, patch_size=16, in_channels=3, embed_dim=768):\n", "        \"\"\"\n", "        初始化 PatchEmbedding 类，用于将输入图像划分为若干 Patch，并投影到固定的嵌入维度。\n", "        \n", "        参数：\n", "        - img_size: 输入图像的分辨率（默认为 224）。\n", "        - patch_size: 每个 Patch 的尺寸（默认为 16x16）。\n", "        - in_channels: 输入图像的通道数（默认为 3，即 RGB 图像）。\n", "        - embed_dim: 嵌入维度（Transformer 的输入特征维度，默认为 768）。\n", "        \"\"\"\n", "        super().__init__()\n", "        self.img_size = img_size  # 保存输入图像的尺寸\n", "        self.patch_size = patch_size  # 保存每个 Patch 的尺寸\n", "        self.num_patches = (img_size // patch_size) * (img_size // patch_size)  \n", "        # 计算图像被划分的总 Patch 数量 = (图像宽度上的 Patch 数) * (图像高度上的 Patch 数)\n", "        self.patch_dim = patch_size * patch_size * in_channels  \n", "        # 每个 Patch 的维度 = patch_size * patch_size * 通道数（即展平后的特征数）\n", "\n", "        # 定义一个线性层，将每个 Patch 的原始特征（patch_dim）投影到 Transformer 的嵌入维度（embed_dim）\n", "        self.projection = nn.Linear(self.patch_dim, embed_dim)\n", "        # 初始化一个可学习的参数，用于位置编码，形状为 (1, num_patches, embed_dim)\n", "        self.position_embedding = nn.Parameter(torch.randn(1, self.num_patches, embed_dim))\n", "\n", "    def forward(self, x):\n", "        \"\"\"\n", "        前向传播函数，将输入图像转换为 Patch 嵌入，并添加位置编码。\n", "\n", "        参数：\n", "        - x: 输入张量，形状为 (batch_size, in_channels, height, width)。\n", "\n", "        返回值：\n", "        - embeddings: 包含位置编码的 Patch 嵌入，形状为 (batch_size, num_patches, embed_dim)。\n", "        \"\"\"\n", "        # 获取输入张量的批量大小、通道数、高度和宽度\n", "        B, C, H, W = x.shape\n", "        # 确保输入图像的分辨率与定义的 img_size 一致\n", "        assert H == self.img_size and W == self.img_size, \"Input size must match img_size\"\n", "        \n", "        # 将输入图像划分为 Patch，使用 unfold 函数：\n", "        # unfold(2, patch_size, patch_size) 在高度维度上以 patch_size 的窗口滑动并提取小块；\n", "        # unfold(3, patch_size, patch_size) 在宽度维度上以 patch_size 的窗口滑动并提取小块。\n", "        patches = x.unfold(2, self.patch_size, self.patch_size).unfold(3, self.patch_size, self.patch_size)\n", "        \n", "        # contiguous() 确保数据在内存中是连续的；\n", "        # view 将张量重塑为 (B, num_patches, patch_dim)，\n", "        # 其中每个 Patch 被展平为长度为 patch_dim 的向量。\n", "        patches = patches.contiguous().view(B, -1, self.patch_dim)\n", "\n", "        # 使用线性层将每个 Patch 的特征投影到嵌入维度（embed_dim），\n", "        # 输出的形状为 (B, num_patches, embed_dim)。\n", "        embeddings = self.projection(patches)\n", "\n", "        # 添加位置编码，位置编码的形状为 (1, num_patches, embed_dim)，\n", "        # 会广播到 batch 的每个样本。\n", "        embeddings += self.position_embedding\n", "\n", "        # 返回添加了位置编码的 Patch 嵌入\n", "        return embeddings"]}, {"cell_type": "markdown", "id": "615ab14d-ae82-4e26-81e4-e6796ba33c81", "metadata": {}, "source": ["然而在CLIP中，存在更高效的做法——**即利用卷积层来完成上述流程**。"]}, {"cell_type": "markdown", "id": "68cb1c73-34bd-41e1-9015-1a0c85a22001", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/06_2.png)"]}, {"cell_type": "markdown", "id": "da65de4e-a8bc-4803-8dfe-64eb82fc4ee9", "metadata": {}, "source": ["这个过程由代码实现起来非常简单，只需要将out_channels设置为d_model，并且将卷积核kernel_size和步长stride设置为patch_size即可 ↓ "]}, {"cell_type": "code", "execution_count": 27, "id": "a355ac25-21c6-4de5-af20-d410002ea0da", "metadata": {}, "outputs": [], "source": ["d_model = 512\n", "patch_size = 16\n", "stride = 16\n", "\n", "conv1 = nn.Conv2d(in_channels=3, out_channels=d_model, kernel_size=patch_size, stride=patch_size, bias=False)"]}, {"cell_type": "code", "execution_count": 37, "id": "030448f7-99e7-414a-956a-860e081a43e5", "metadata": {}, "outputs": [], "source": ["input_data = torch.randn(10, 3, 224, 224)  # (batch_size, channels, height, width)"]}, {"cell_type": "code", "execution_count": 38, "id": "3f438857-1195-4a81-9629-625bd2a8446f", "metadata": {"scrolled": true}, "outputs": [], "source": ["x = conv1(input_data)"]}, {"cell_type": "code", "execution_count": 41, "id": "7e0f78dd-2813-46e8-9f99-41f76a8011a8", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([10, 512, 14, 14])"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["x.shape #最终输出的是 224/16 = 14"]}, {"cell_type": "code", "execution_count": 35, "id": "3da41c18-fe97-4f6f-8374-1514eb771dde", "metadata": {}, "outputs": [], "source": ["x = x.reshape(x.shape[0], x.shape[1], -1)  # 形状 = [batch_size, width, grid ** 2]\n", "x = x.permute(0, 2, 1)  # 调整为 [batch_size, num_patches, d_model]"]}, {"cell_type": "code", "execution_count": 36, "id": "a3281f69-111d-4931-8c5f-3ac4a4dcb1a2", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([10, 196, 512])"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["x.shape"]}, {"cell_type": "markdown", "id": "e936da7c-d436-4b90-92f1-e518f0feb72b", "metadata": {}, "source": ["总结一下 ↓ "]}, {"cell_type": "markdown", "id": "dcdd6e36-a29b-4614-9de7-34a2e9856597", "metadata": {}, "source": ["| Patch Embedding的环节                 | ViT常规实现方法 | CLIP中的实现方法|\n", "|-----------------------|-----|------|\n", "| **分块**           | 设置好patch_size<br>使用unfold等方式手动划分 | 使用卷积层进行划分 |\n", "| **投影前展平**           | 在划分好的patch上继续unford<br>view等方式进行手动展平 | -- |\n", "| **投影**           | 设置单独的投影线性层<br>接收in_channels * patch_size * patch_size<br>输出d_model | 在卷积层中<br>通过将out_channels设置成d_model<br>从而实现投影 |\n", "| **投影后展平**       | -- | 使用 reshape和permute进行展平|\n"]}, {"cell_type": "markdown", "id": "f602e7ce-eeac-4b32-8c62-4c02266f7f3d", "metadata": {}, "source": ["这么做的优势在于——\n", "\n", "**1. 更高的计算效率**\n", "使用卷积操作可以有效利用硬件加速：\n", "- **并行计算**：卷积操作在现代深度学习框架（如 PyTorch、TensorFlow）中得到了高度优化，可以充分利用 GPU 的并行计算能力，而展平每个 Patch 的传统方式需要额外的显式循环操作，效率较低。\n", "- **少数据搬移**：传统方式需要将图像数据重新组织成 $ [num\\_patches, patch\\_size \\cdot patch\\_size \\cdot channels] $，这涉及到大量的数据搬移。而卷积操作直接在输入图像上执行，无需显式展平，减少了数据搬移的开销。\n", "\n", "---\n", "\n", "**2. 自然的空间感知**\n", "卷积操作天生具有局部感知能力：\n", "- 卷积核会在每个 Patch 内进行加权求和，相当于提取 Patch 的局部特征，而不是简单的展平数据。\n", "- 如果需要，卷积还可以在 Patch 中实现更高级的特征提取（比如使用非线性激活函数或更深的卷积层），相比于简单的 `Linear` 投影更加灵活。\n", "\n", "---\n", "\n", "**3. 内存消耗更少**\n", "卷积操作直接作用于原始的高维图像输入，而传统方法中展平操作会生成一个高维度的中间结果：\n", "- 在传统实现中，每个 $16 \\times 16$ 的 Patch 会被展平成长度为 $16 \\times 16 \\times 3 = 768$ 的向量。如果图像分辨率较高，Patch 较多，会导致内存占用增加。\n", "- 使用 `Conv2d` 的实现直接输出 $ [batch\\_size, width, grid\\_height, grid\\_width] $，避免了显式展平产生的中间结果，从而节省内存。\n", "\n", "---\n", "\n", "**4. 代码简洁性与可扩展性**\n", "卷积的实现直接将 Patch 的划分和投影合并成一个操作：\n", "- **简洁性**：`Conv2d` 的 `kernel_size` 和 `stride` 参数天然对应 Patch 的大小和步幅，代码更加直观、简洁，省去了展平和逐个 Patch 投影的步骤。\n", "- **可扩展性**：如果需要更复杂的处理，可以直接在卷积层中添加更多的非线性操作（如激活函数）或深层网络结构，而不是依赖额外的模块。\n", "\n", "---\n", "\n", "**5. 支持任意大小的输入图像**\n", "- **传统方法**：要求图像分辨率是 Patch 大小的整数倍（否则划分出的 Patch 数量不一致），而且需要显式处理 Padding。\n", "- **卷积方法**：`Conv2d` 中的 `padding` 参数能够自动处理边界情况，使得网络支持任意大小的输入图像。\n", "\n", "---\n", "\n", "**6. 与预训练 CNN 特征结合的潜力**\n", "在实际应用中，我们可能会用预训练的 CNN 特征作为 Transformer 的输入：\n", "- 卷积操作本质上是 CNN 的核心，因此可以方便地与预训练模型（如 ResNet、EfficientNet）的特征对接。\n", "- 这种方式可以充分利用卷积的特性提取更高质量的 Patch 特征。\n", "\n", "---\n", "\n", "**7. 灵活的 Patch 特征生成**\n", "- **传统方法**：展平的 Patch 中，每个像素值直接作为输入，无法动态调整 Patch 内部的特征。\n", "- **卷积方法**：通过设计卷积核（如不同大小、深度或非线性处理），可以灵活生成更复杂的 Patch 特征。例如：\n", "  - 使用更大的卷积核捕获跨越多个像素的上下文关系；\n", "  - 使用多层卷积构造更丰富的层次化 Patch 表示。\n", "\n", "---\n", "\n", "**8. 提前降维，减少计算量**\n", "如果输入图像分辨率较高（如 4K 图像），直接展平后的序列长度会非常长，导致 Transformer 的计算量急剧增加。而卷积方法可以在切片的同时降低通道数，从而有效减少计算量。"]}, {"cell_type": "markdown", "id": "82573ef7-9045-4c91-94fe-96701d305ac4", "metadata": {}, "source": ["- <font color=\"red\">**Q2：除了Patch Embedding之外，ViT和普通的Transformer还有什么不同？**"]}, {"cell_type": "markdown", "id": "2d807ec1-41b5-4bbf-9ef8-916437e16bb0", "metadata": {}, "source": ["为了保留图像中 Patch 的位置信息，ViT 会在 Patch Embedding 中加入**二维位置编码（Positional Encoding）**，以向模型提供顺序信息。这种位置编码可以是可学习的参数，或者是基于正弦和余弦的固定位置编码。最终，所有 Patch Embedding 和位置编码的加和结果被组成一个序列，输入 Transformer 模型进行后续的全局特征建模和分类任务。通过这种方式，ViT 将原始图像处理成了一个序列结构，充分利用了 Transformer 在自然语言处理任务中擅长的序列建模能力，从而在图像分类、目标检测等视觉任务中实现了优异的性能。\n", "\n", "除此之外，**ViT支持类别嵌入**。类别嵌入是一个 learnable 参数，直接添加到序列的开头，用于提取全局图像特征，就类似于我们在Bert当中所插入的[CLS]、这个token/patch用于收集全局信息。\n", "\n", "同时，ViT所需要的**数据量会比普通Transformer大很多**、ViT被认为是一个图像的架构而不是文本的架构，因此ViT需要的输入是Patch、而普通Transformer需要的输入是Token。"]}, {"cell_type": "markdown", "id": "8d47ed62-b3c8-431a-8170-6622ca62895d", "metadata": {}, "source": ["- <font color=\"red\">**Q3：常规的ViT和CLIP中的ViT有什么区别？**"]}, {"cell_type": "markdown", "id": "f03ca195-b47b-4107-8bc4-091667ff6793", "metadata": {}, "source": ["| 特性                       | 普通 ViT                | CLIP中的ViT            |\n", "|----------------------------|-------------------------|------------------------------|\n", "| LayerNorm                  | 标准 LayerNorm          | 在前向传播中，先将输入数据转换为 `float32` 类型<br>计算 LayerNorm，再恢复为原始数据类型（如 `fp16`）<br>支持一定程度的混合精度训练。<br><br>通过这种方式，避免了使用 `fp16` 时因数值精度不足<br>导致的归一化误差，提升训练稳定性。 |\n", "| 前馈网络                   | 标准 GELU 前馈网络              | 使用了自定义的 **QuickGELU** 激活函数，其公式为：<br><br>$\\text{QuickGELU}(x) = x \\cdot \\text{sigmoid}(1.702 \\cdot x)$<br><br>QuickGELU比标准 GELU 计算效率更高<br>且在实际性能上接近或优于标准 GELU。                   |\n", "| 类别嵌入初始化             | 无特殊处理             | 使用 `scale = width ** -0.5` 对初始值进行缩放<br>缩放初始化，增强稳定性        |\n", "| 位置编码                   | 无缩放处理             | 用 learnable 的 2D 位置编码，形状为 `[num_patches, d_model]`<br>直接与 Patch 嵌入相加，同时还带有缩放初始化|\n", "| 输出特征                  | 全序列输入到分类头  | 仅取类别嵌入作为全局特征、传输给输出层      |\n", "| 多模态任务支持             | 无                     | 具备投影层、通过对比学习统一表示空间       |"]}, {"cell_type": "markdown", "id": "c023bf47-2502-474a-88a8-c8c84c13ac54", "metadata": {}, "source": ["基于这些关键的总结信息，我们可以开始梳理CLIP中的ViT的全套代码了 ↓"]}, {"cell_type": "markdown", "id": "6e222d04-65c3-44b6-a030-faaa09395b94", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/01.png)"]}, {"cell_type": "code", "execution_count": 53, "id": "df76f252-02d4-434f-a9d1-570f2e6e47c5", "metadata": {}, "outputs": [], "source": ["class LayerNorm(nn.LayerNorm):\n", "    \"\"\"\n", "    LayerNorm 的子类，用于处理 fp16（半精度浮点数）。\n", "    \"\"\"\n", "\n", "    def forward(self, x: torch.Tensor):\n", "        # 保存原始数据类型\n", "        orig_type = x.dtype\n", "        # 将输入转换为 float32 类型，执行 LayerNorm 操作\n", "        ret = super().forward(x.type(torch.float32))\n", "        # 恢复原始数据类型\n", "        return ret.type(orig_type)\n", "\n", "\n", "class QuickGELU(nn.Module):\n", "    \"\"\"\n", "    实现 QuickGELU 激活函数，比标准 GELU 更简单且计算效率更高。\n", "    \"\"\"\n", "\n", "    def forward(self, x: torch.Tensor):\n", "        # QuickGELU 激活函数公式：x * sigmoid(1.702 * x)\n", "        return x * torch.sigmoid(1.702 * x)\n", "\n", "\n", "class ResidualAttentionBlock(nn.Module):\n", "    \"\"\"\n", "    实现一个残差注意力模块，包括多头注意力机制和前馈网络（MLP）。\n", "    \"\"\"\n", "\n", "    def __init__(self, d_model: int, n_head: int, attn_mask: torch.Tensor = None):\n", "        \"\"\"\n", "        初始化函数。\n", "        参数：\n", "        - d_model: 模型的隐藏层维度（即特征向量的维度）。\n", "        - n_head: 多头注意力的头数。\n", "        - attn_mask: 注意力掩码，可选。\n", "        \"\"\"\n", "        super().__init__()\n", "\n", "        # 多头注意力模块\n", "        self.attn = nn.MultiheadAttention(d_model, n_head)\n", "        # 第一个 LayerNorm\n", "        self.ln_1 = LayerNorm(d_model)\n", "        # 前馈网络（MLP），包括线性层、QuickGELU 激活和另一层线性变换\n", "        self.mlp = nn.Sequential(OrderedDict([\n", "            (\"c_fc\", nn.<PERSON>ar(d_model, d_model * 4)),  # 扩展特征维度 4 倍\n", "            (\"gelu\", QuickGELU()),  # QuickGELU 激活\n", "            (\"c_proj\", nn.<PERSON>ar(d_model * 4, d_model))  # 恢复到原始特征维度\n", "        ]))\n", "        # 第二![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/01.png)个 LayerNorm\n", "        self.ln_2 = LayerNorm(d_model)\n", "        # 注意力掩码\n", "        self.attn_mask = attn_mask\n", "\n", "    def attention(self, x: torch.Tensor):\n", "        \"\"\"\n", "        计算多头注意力。\n", "        \"\"\"\n", "        # 如果存在注意力掩码，将其转换为与输入相同的数据类型和设备\n", "        self.attn_mask = self.attn_mask.to(dtype=x.dtype, device=x.device) if self.attn_mask is not None else None\n", "        # 调用多头注意力，返回注意力结果（不需要权重）\n", "        return self.attn(x, x, x, need_weights=False, attn_mask=self.attn_mask)[0]\n", "\n", "    def forward(self, x: torch.Tensor):\n", "        \"\"\"\n", "        前向传播。\n", "        \"\"\"\n", "        # 1. 多头注意力：先进行 LayerNorm，再计算注意力，并与输入 x 相加（残差连接）\n", "        x = x + self.attention(self.ln_1(x))\n", "        # 2. 前馈网络：先进行 LayerNorm，再通过 MLP，并与输入 x 相加（残差连接）\n", "        x = x + self.mlp(self.ln_2(x))\n", "        return x\n", "\n", "\n", "class Transformer(nn.Mo<PERSON>le):\n", "    \"\"\"\n", "    基于 ResidualAttentionBlock 的 Transformer 模块。\n", "    \"\"\"\n", "\n", "    def __init__(self, width: int, layers: int, heads: int, attn_mask: torch.Tensor = None):\n", "        \"\"\"\n", "        初始化函数。\n", "        参数：\n", "        - width: 模型的隐藏层维度。\n", "        - layers: Transformer 层数。\n", "        - heads: 多头注意力的头数。\n", "        - attn_mask: 注意力掩码。\n", "        \"\"\"\n", "        super().__init__()\n", "        self.width = width\n", "        self.layers = layers\n", "        # 堆叠多个 ResidualAttentionBlock，构成完整的 Transformer\n", "        self.resblocks = nn.Sequential(*[ResidualAttentionBlock(width, heads, attn_mask) for _ in range(layers)])\n", "\n", "    def forward(self, x: torch.Tensor):\n", "        \"\"\"\n", "        前向传播。\n", "        \"\"\"\n", "        return self.resblocks(x)\n", "\n", "\n", "class VisionTransformer(nn.Module):\n", "    \"\"\"\n", "    实现 Vision Transformer，用于处理图像输入。\n", "    \"\"\"\n", "\n", "    def __init__(self, input_resolution: int, patch_size: int, width: int, layers: int, heads: int, output_dim: int):\n", "        \"\"\"\n", "        初始化函数。\n", "        参数：\n", "        - input_resolution: 输入图像的分辨率（如 224）。\n", "        - patch_size: 图像切片的尺寸（如 16x16）。\n", "        - width: Transformer 的隐藏层维度。\n", "        - layers: Transformer 层数。\n", "        - heads: 多头注意力的头数。\n", "        - output_dim: 输出特征维度。\n", "        \"\"\"\n", "        super().__init__()\n", "        self.input_resolution = input_resolution\n", "        self.output_dim = output_dim\n", "        # 图像切片卷积，将输入图像分割为若干 patch，并映射到 width 维度\n", "        self.conv1 = nn.Conv2d(in_channels=3, out_channels=width, kernel_size=patch_size, stride=patch_size, bias=False)\n", "\n", "        # 类别嵌入，用于标识全局图像特征\n", "        scale = width ** -0.5\n", "        self.class_embedding = nn.Parameter(scale * torch.randn(width))\n", "        # 位置嵌入，用于编码每个 patch 的位置\n", "        self.positional_embedding = nn.Parameter(scale * torch.randn((input_resolution // patch_size) ** 2 + 1, width))\n", "        # 输入的 LayerNorm\n", "        self.ln_pre = LayerNorm(width)\n", "\n", "        # Transformer 编码器\n", "        self.transformer = Transformer(width, layers, heads)\n", "\n", "        # 输出的 LayerNorm 和投影层\n", "        self.ln_post = LayerNorm(width)\n", "        self.proj = nn.Parameter(scale * torch.randn(width, output_dim))\n", "\n", "    def forward(self, x: torch.Tensor):\n", "        \"\"\"\n", "        前向传播。\n", "        参数：\n", "        - x: 输入图像张量，形状为 (batch_size, 3, H, W)。\n", "        返回值：\n", "        - x: 输出特征，形状为 (batch_size, output_dim)。\n", "        \"\"\"\n", "        # 图像切片，将输入分割为 patch 并映射到特征维度\n", "        x = self.conv1(x)  # 形状 = [batch_size, width, grid, grid]\n", "        # 将每个 patch 展平，并调整为 [batch_size, num_patches, width]\n", "        x = x.reshape(x.shape[0], x.shape[1], -1)  # 形状 = [batch_size, width, grid ** 2]\n", "        x = x.permute(0, 2, 1)  # 调整为 [batch_size, num_patches, width]\n", "\n", "        # 添加类别嵌入\n", "        x = torch.cat([self.class_embedding.to(x.dtype) + torch.zeros(x.shape[0], 1, x.shape[-1], dtype=x.dtype, device=x.device), x], dim=1)  # 形状 = [batch_size, num_patches + 1, width]\n", "        # 加上位置嵌入\n", "        x = x + self.positional_embedding.to(x.dtype)\n", "        # 输入的 LayerNorm\n", "        x = self.ln_pre(x)\n", "\n", "        # Transformer 编码器\n", "        x = x.permute(1, 0, 2)  # 调整为 [num_patches + 1, batch_size, width]\n", "        x = self.transformer(x)\n", "        x = x.permute(1, 0, 2)  # 调整回 [batch_size, num_patches + 1, width]\n", "\n", "        # 输出特征仅取第一个（全局图像特征）\n", "        x = self.ln_post(x[:, 0, :])\n", "\n", "        # 如果存在投影层，将其映射到输出维度\n", "        if self.proj is not None:\n", "            x = x @ self.proj\n", "\n", "        return x"]}, {"cell_type": "code", "execution_count": 54, "id": "343b3d04-2194-4a63-8462-996f4947f85f", "metadata": {}, "outputs": [], "source": ["# 调整参数以减少内存占用\n", "input_resolution = 224  # 分辨率\n", "width = 512  # 隐藏层维度\n", "layers = 6  #  Transformer 层数\n", "heads = 8  # 注意力头数\n", "output_dim = 512  # 输出特征维度\n", "batch_size = 10  # batch大小\n", "\n", "# 重新定义 VisionTransformer 模型\n", "vit = VisionTransformer(\n", "    input_resolution=input_resolution,\n", "    patch_size=16,\n", "    width=width,\n", "    layers=layers,\n", "    heads=heads,\n", "    output_dim=output_dim\n", ")"]}, {"cell_type": "code", "execution_count": 55, "id": "0c0dc31d-d24f-4ed6-9300-050735517332", "metadata": {}, "outputs": [{"data": {"text/plain": ["torch.<PERSON><PERSON>([10, 512])"]}, "execution_count": 55, "metadata": {}, "output_type": "execute_result"}], "source": ["# 假设输入图像数据 (10,3,224,224)\n", "input_data = torch.randn(batch_size, 3, input_resolution, input_resolution)\n", "\n", "# 通过模型前向传播\n", "output = vit(input_data)\n", "\n", "# 打印输出的形状\n", "output.shape"]}, {"cell_type": "markdown", "id": "3f30f933-7810-44ec-97a2-f19b2bee4840", "metadata": {}, "source": ["如你缩减，我们将结构为(10,3,224,224)的图像数据压缩为了二维数据(10,128)，这和ResNet的输出结果一致。"]}, {"cell_type": "markdown", "id": "bba7db46-e781-4ef8-bd86-146171ba9090", "metadata": {}, "source": ["### 1.2 文本编码器速览"]}, {"cell_type": "markdown", "id": "168b2e5e-a66f-4c46-a633-07d039d68ecc", "metadata": {}, "source": ["在 **CLIP** 中，Transformer 作为文本编码器，其结构与标准的 Transformer 中的编码器结构高度相似，由嵌入层（Embedding Layer）、多层的自注意力模块（Self-Attention）、前馈网络（Feed-Forward Network, FFN）和归一化（LayerNorm）组成。\n", "\n", "首先，输入文本通过嵌入层，将每个词映射为固定维度的向量，同时添加可学习的 **位置编码**，以保留序列的位置信息。随后，这些嵌入通过多层 Transformer 模块，每层由多头自注意力（Multi-Head Attention）和 FFN 组成，并在每个子模块后加上残差连接（Residual Connection）和 LayerNorm。Transformer 的作用是逐层捕获文本序列中的上下文关系和全局语义特征。最终，文本的全局语义特征通过特定的标记（例如 [EOS] token）的表示提取，并投影到与图像特征一致的嵌入空间中，用于多模态匹配。这种结构使 CLIP 能够高效地对齐图像和文本的语义信息，实现跨模态理解。"]}, {"cell_type": "markdown", "id": "6d698335-edc2-4fd2-abb2-4128e2d16f0a", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/01.png)"]}, {"cell_type": "markdown", "id": "37f24f62-5487-4484-bff6-b259aa8d1aee", "metadata": {}, "source": ["## 2 CLIP模型的预训练流程"]}, {"cell_type": "markdown", "id": "2974bf9a-1559-445e-8627-d48ae7b4329b", "metadata": {}, "source": ["现在我们有了图像信息和文本信息，但一个多模态模型如何将这两类信息整合起来呢？就像文字架构预训练目标是创造出“能够理解基础文字信息”、多模态架构的预训练目标就是“能够理解基础文字和基础图像信息”。因此，CLIP的预训练流程是让架构本身能够理解图像和文本的流程。**CLIP 通过学习图像和文字的匹配关系，成功地将两种模态的信息（图像和文本）映射到同一个语义空间，这使得它能够理解并关联图像和文本**，从而实现强大的多模态任务能力，比如跨模态检索、分类、生成等。\n", "\n", "在 CLIP 中，计算余弦相似度和匹配图像与文本特征的过程可以概括为：\n", "1. 输入**图-文对数据集**作为初始数据、各自提取图像和文本特征。<br><br>\n", "2. 通过归一化、点积等流程，计算图像和文本的**配对余弦相似度**。<br><br>\n", "3. 实现**对比学习**（Contrastive Learning）、使用对比损失作为损失函数，对比损失越高说明匹配越不精准——\n", "> 基于损失函数来调整**相似度矩阵**，调整的方向是让配对样本的余弦相似度尽可能接近 1，而非配对样本尽可能接近 0。<br><br>\n", "> 由于要推动相似度矩阵变得更精准、因此损失函数也会**优化图像编码结果和文字编码结果**（也就是优化图像编码器和文字编码器的权重），如果样本是配对的、那拉进两种编码结果在语义空间中更接近，如果样本不是配对的、则将样本的编码结果在语义空间中分离。<br>\n", "\n", "4. 最终，将图像和文本的语义表示统一到一个多模态的共享空间中、使得图像和文字能够被精准理解，例如通过输入文本检索相关图像，或者通过图像查找相关文本描述，实现高效的跨模态搜索和匹配。"]}, {"cell_type": "markdown", "id": "90d10286-6e0c-44f6-9fe9-cdb35ed9f7c7", "metadata": {}, "source": ["### 2.1 经典图文对数据集"]}, {"cell_type": "markdown", "id": "b613195a-8751-4654-9eae-4dc4db26236e", "metadata": {}, "source": ["在多模态研究中，有许多经典的图文对数据集可用于预训练，涵盖英文和中文的多模态场景——\n", "\n", "**英文图文对数据集**\n", "<br><br>\n", "1. **COCO Captions**  \n", "   - **简介**：MS COCO 数据集的扩展版本，包含图像和其多种文本描述。每张图像对应 5 条人工撰写的文本。\n", "   - **规模**：约 330k 图像-文本对。\n", "   - **应用**：图像描述生成（Captioning）、多模态检索、图文匹配。\n", "   - **链接**：[COCO Captions](https://cocodataset.org/#captions-2015)\n", "<br><br>\n", "2. **Flickr30k**  \n", "   - **简介**：包含日常场景的图片及其文本描述，每张图像有 5 条人工标注。\n", "   - **规模**：31k 图像-文本对。\n", "   - **应用**：图文匹配、跨模态检索。\n", "   - **链接**：[Flickr30k](http://shannon.cs.illinois.edu/DenotationGraph/)\n", "<br><br>\n", "3. **Visual Genome**  \n", "   - **简介**：包含详细的图像标注和关系描述，每张图像对应多个区域和其文字说明。\n", "   - **规模**：约 108k 图像，超过 500 万区域描述。\n", "   - **应用**：图像场景理解、关系推理、多模态检索。\n", "   - **链接**：[Visual Genome](https://visualgenome.org/)\n", "<br><br>\n", "4. **Conceptual Captions (CC3M & CC12M)**  \n", "   - **简介**：从网页爬取的大规模图文对数据集，使用自动化方法生成文本描述。\n", "   - **规模**：CC3M 包含 3M 图文对，CC12M 包含 12M 图文对。\n", "   - **应用**：多模态预训练、图文生成、检索。\n", "   - **链接**：[Conceptual Captions](https://ai.google.com/research/ConceptualCaptions/)\n", "<br><br>\n", "5. **LAION-400M / LAION-5B**  \n", "   - **简介**：一个超大规模的开放图文对数据集，包含从互联网中爬取的图文对，经过一定程度的清理。\n", "   - **规模**：LAION-400M（4 亿对），LAION-5B（50 亿对）。\n", "   - **应用**：多模态预训练、检索、生成任务。\n", "   - **链接**：[LAION](https://laion.ai/)\n", "\n", "---\n", "\n", "**中文图文对数据集**\n", "<br><br>\n", "1. **<PERSON><PERSON><PERSON> (文澜)**  \n", "   - **简介**：中文多模态预训练数据集，由多个来源的大规模图文对组成，涵盖自然图片、新闻图片等场景。\n", "   - **规模**：约 30M 图文对。\n", "   - **应用**：多模态预训练、中文图文检索。\n", "   - **论文**：[<PERSON><PERSON><PERSON>](https://arxiv.org/abs/2103.06561)\n", "<br><br>\n", "2. **CLUEVIT**  \n", "   - **简介**：中文领域的 Vision Transformer 数据集，适用于多模态任务。\n", "   - **规模**：超过 10M 图文对。\n", "   - **应用**：多模态检索、图文生成。\n", "   - **链接**：[CLUE](https://www.cluebenchmarks.com/)\n", "<br><br>\n", "3. **CCF Open Dataset**  \n", "   - **简介**：由中文社区提供的多模态开放数据集，部分数据集包含图文对。\n", "   - **规模**：根据具体子集而定。\n", "   - **应用**：多模态任务研究。\n", "   - **链接**：[CCF](https://www.ccf.org.cn/)\n", "<br><br>\n", "4. **AiM Dataset**  \n", "   - **简介**：一个中文图文对数据集，包含社交媒体图片及其描述。\n", "   - **规模**：待公开具体规模。\n", "   - **应用**：多模态生成、检索。\n", "<br><br>\n", "---\n", "\n", "**如何选择合适的数据集**\n", "<br><br>\n", "1. **任务需求**：<br><br>\n", "   - 图文匹配或检索：COCO Captions、Flickr30k、<PERSON><PERSON><PERSON>。\n", "   - 多模态生成：CC12M、Visual Genome。\n", "   - 多模态预训练：LAION、Conceptual Captions、<PERSON><PERSON><PERSON>。\n", "<br><br>\n", "2. **语言和领域**：<br><br>\n", "   - 英文场景：使用 LAION 或 Conceptual Captions。\n", "   - 中文场景：推荐使用 WenLan 或 CLUEVIT。\n", "<br><br>\n", "3. **数据规模**：<br><br>\n", "   - 小规模：COCO Captions（约 330k 对）。\n", "   - 大规模：LAION-5B、<PERSON><PERSON><PERSON>（适合预训练大模型）。"]}, {"cell_type": "markdown", "id": "6009f54d-ca93-486b-8de2-2c8b7b939075", "metadata": {}, "source": ["### 2.2 余弦相似度"]}, {"cell_type": "markdown", "id": "740312e1-c107-40b4-8645-cc27ac7c9d95", "metadata": {}, "source": ["在 **CLIP（Contrastive Language–Image Pretraining）** 模型中，图像和文本特征的匹配是核心任务，而 **余弦相似度** 是实现这一匹配的关键工具。CLIP 的目标是通过对比学习，将配对的图像和文本映射到同一语义空间，使其具有相似的特征表示。在CLIP中，我们计算余弦相似度是为了——\n", "\n", "1. **衡量图像和文本的相似性**：\n", "   - 图像和文本的特征表示是高维向量，余弦相似度衡量它们之间的语义相关性。\n", "   - 使用余弦相似度可以忽略向量大小的影响，只关注它们的方向，即语义一致性。\n", "\n", "2. **训练中的对比学习**：\n", "   - 通过余弦相似度，CLIP 区分语义匹配的图像-文本对和不匹配的对。\n", "   - 配对的图像和文本应该具有高相似度（相似度接近 1），而不匹配的对应该具有低相似度（接近 0 或更低）。\n", "\n", "---\n", "\n", "**CLIP 的学习过程**\n", "\n", "CLIP 的核心思想是通过 **对比损失（contrastive loss）** 来优化图像和文本的特征表示，使配对的样本相似，不配对的样本尽可能分离开。\n", "\n", "**1. 特征提取**\n", "1. **图像特征**：\n", "   - 图像通过 **视觉编码器（Vision Encoder）** 提取特征。\n", "   - 视觉编码器可以是 ResNet 或 Vision Transformer（ViT），将输入图像处理为固定大小的特征向量 $ v \\in \\mathbb{R}^{d} $。\n", "   \n", "2. **文本特征**：\n", "   - 文本通过 **语言编码器（Text Encoder）** 提取特征。\n", "   - 语言编码器是一个 Transformer，将输入文本的每个 token 转换为特征向量，并最终通过一个线性投影层得到全局表示 $ t \\in \\mathbb{R}^{d} $。\n", "\n", "**2. 归一化**\n", "- 在计算余弦相似度之前，图像和文本特征都进行 **L2 归一化**：\n", "  $$\n", "  v' = \\frac{v}{\\|v\\|}, \\quad t' = \\frac{t}{\\|t\\|}\n", "  $$\n", "- 归一化后的特征向量模为 1，余弦相似度可以用点积表示。\n", "\n", "**3. 余弦相似度计算**\n", "对于一批 $ N $ 个图像和文本，计算它们之间的余弦相似度矩阵：\n", "$$\n", "S_{ij} = v_i' \\cdot t_j'\n", "$$\n", "其中：\n", "- $ S_{ij} $ 表示第 $ i $ 个图像和第 $ j $ 个文本的余弦相似度。\n", "- 结果是一个 $ N \\times N $ 的矩阵。\n", "\n", "**4. 对比损失**\n", "CLIP 使用一种基于余弦相似度的 **对比损失（Contrastive Loss）**，包括以下两部分：\n", "\n", "1. **图像到文本的匹配**：\n", "   - 将每个图像的特征 $ v_i' $ 与所有文本特征 $ t_j' $ 的余弦相似度作为 logits，使用交叉熵损失优化配对的文本具有最高的相似度：\n", "     $$\n", "     \\mathcal{L}_{\\text{image}} = - \\frac{1}{N} \\sum_{i=1}^{N} \\log \\frac{\\exp(S_{ii} / \\tau)}{\\sum_{j=1}^{N} \\exp(S_{ij} / \\tau)}\n", "     $$\n", "\n", "2. **文本到图像的匹配**：\n", "   - 类似地，将每个文本的特征 $ t_j' $ 与所有图像特征 $ v_i' $ 的余弦相似度作为 logits：\n", "     $$\n", "     \\mathcal{L}_{\\text{text}} = - \\frac{1}{N} \\sum_{j=1}^{N} \\log \\frac{\\exp(S_{jj} / \\tau)}{\\sum_{i=1}^{N} \\exp(S_{ij} / \\tau)}\n", "     $$\n", "\n", "3. **总损失**：\n", "   - 最终对比损失是两者的平均：\n", "     $$\n", "     \\mathcal{L} = \\frac{1}{2} (\\mathcal{L}_{\\text{image}} + \\mathcal{L}_{\\text{text}})\n", "     $$\n", "   - 其中，$\\tau$ 是一个可学习的缩放参数（在代码中用 `logit_scale` 表示），控制相似度的数值范围。\n", "\n", "---\n", "\n", "**CLIP 如何通过对比学习优化特征**\n", "\n", "1. **正样本的拉近**：\n", "   - 配对的图像和文本通过对比损失被拉近，使它们的余弦相似度更高。\n", "   - 在损失函数中，这体现在配对样本的分子部分（$\\exp(S_{ii} / \\tau)$）的优化。\n", "\n", "2. **负样本的分离**：\n", "   - 非配对的图像和文本被推远，使它们的余弦相似度更低。\n", "   - 在损失函数中，这体现在分母部分（对所有非配对样本的 $\\exp(S_{ij} / \\tau)$ 的求和）的优化。\n", "\n", "3. **多模态统一表示空间**：\n", "   - CLIP 的最终目标是将图像和文本的特征投影到同一个嵌入空间中，使它们的语义相关性可以直接通过向量的余弦相似度来衡量。"]}, {"cell_type": "markdown", "id": "d4fdfa24-af36-48bf-a5c6-9b76bb2b902a", "metadata": {}, "source": ["**完整的CLIP实现代码如下——**"]}, {"cell_type": "code", "execution_count": 56, "id": "94706113-31af-4ba2-b1e9-c299f1f3dec2", "metadata": {}, "outputs": [], "source": ["class CLIP(nn.Module):\n", "    def __init__(self,\n", "                 embed_dim: int,\n", "                 # vision\n", "                 image_resolution: int,\n", "                 vision_layers: Union[Tuple[int, int, int, int], int],\n", "                 vision_width: int,\n", "                 vision_patch_size: int,\n", "                 # text\n", "                 context_length: int,\n", "                 vocab_size: int,\n", "                 transformer_width: int,\n", "                 transformer_heads: int,\n", "                 transformer_layers: int\n", "                 ):\n", "        super().__init__()\n", "\n", "        self.context_length = context_length\n", "\n", "        if isinstance(vision_layers, (tuple, list)):\n", "            vision_heads = vision_width * 32 // 64\n", "            self.visual = ModifiedResNet(\n", "                layers=vision_layers,\n", "                output_dim=embed_dim,\n", "                heads=vision_heads,\n", "                input_resolution=image_resolution,\n", "                width=vision_width\n", "            )\n", "        else:\n", "            vision_heads = vision_width // 64\n", "            self.visual = VisionTransformer(\n", "                input_resolution=image_resolution,\n", "                patch_size=vision_patch_size,\n", "                width=vision_width,\n", "                layers=vision_layers,\n", "                heads=vision_heads,\n", "                output_dim=embed_dim\n", "            )\n", "\n", "        self.transformer = Transformer(\n", "            width=transformer_width,\n", "            layers=transformer_layers,\n", "            heads=transformer_heads,\n", "            attn_mask=self.build_attention_mask()\n", "        )\n", "\n", "        self.vocab_size = vocab_size\n", "        self.token_embedding = nn.Embedding(vocab_size, transformer_width)\n", "        self.positional_embedding = nn.Parameter(torch.empty(self.context_length, transformer_width))\n", "        self.ln_final = LayerNorm(transformer_width)\n", "\n", "        self.text_projection = nn.Parameter(torch.empty(transformer_width, embed_dim))\n", "        self.logit_scale = nn.Parameter(torch.ones([]) * np.log(1 / 0.07))\n", "\n", "        self.initialize_parameters()\n", "\n", "    def initialize_parameters(self):\n", "        nn.init.normal_(self.token_embedding.weight, std=0.02)\n", "        nn.init.normal_(self.positional_embedding, std=0.01)\n", "\n", "        if isinstance(self.visual, ModifiedResNet):\n", "            if self.visual.attnpool is not None:\n", "                std = self.visual.attnpool.c_proj.in_features ** -0.5\n", "                nn.init.normal_(self.visual.attnpool.q_proj.weight, std=std)\n", "                nn.init.normal_(self.visual.attnpool.k_proj.weight, std=std)\n", "                nn.init.normal_(self.visual.attnpool.v_proj.weight, std=std)\n", "                nn.init.normal_(self.visual.attnpool.c_proj.weight, std=std)\n", "\n", "            for resnet_block in [self.visual.layer1, self.visual.layer2, self.visual.layer3, self.visual.layer4]:\n", "                for name, param in resnet_block.named_parameters():\n", "                    if name.endswith(\"bn3.weight\"):\n", "                        nn.init.zeros_(param)\n", "\n", "        proj_std = (self.transformer.width ** -0.5) * ((2 * self.transformer.layers) ** -0.5)\n", "        attn_std = self.transformer.width ** -0.5\n", "        fc_std = (2 * self.transformer.width) ** -0.5\n", "        for block in self.transformer.resblocks:\n", "            nn.init.normal_(block.attn.in_proj_weight, std=attn_std)\n", "            nn.init.normal_(block.attn.out_proj.weight, std=proj_std)\n", "            nn.init.normal_(block.mlp.c_fc.weight, std=fc_std)\n", "            nn.init.normal_(block.mlp.c_proj.weight, std=proj_std)\n", "\n", "        if self.text_projection is not None:\n", "            nn.init.normal_(self.text_projection, std=self.transformer.width ** -0.5)\n", "\n", "    def build_attention_mask(self):\n", "        # lazily create causal attention mask, with full attention between the vision tokens\n", "        # pytorch uses additive attention mask; fill with -inf\n", "        mask = torch.empty(self.context_length, self.context_length)\n", "        mask.fill_(float(\"-inf\"))\n", "        mask.triu_(1)  # zero out the lower diagonal\n", "        return mask\n", "\n", "    @property\n", "    def dtype(self):\n", "        return self.visual.conv1.weight.dtype\n", "\n", "    def encode_image(self, image):\n", "        return self.visual(image.type(self.dtype))\n", "\n", "    def encode_text(self, text):\n", "        x = self.token_embedding(text).type(self.dtype)  # [batch_size, n_ctx, d_model]\n", "\n", "        x = x + self.positional_embedding.type(self.dtype)\n", "        x = x.permute(1, 0, 2)  # NLD -> LND\n", "        x = self.transformer(x)\n", "        x = x.permute(1, 0, 2)  # LND -> NLD\n", "        x = self.ln_final(x).type(self.dtype)\n", "\n", "        # x.shape = [batch_size, n_ctx, transformer.width]\n", "        # take features from the eot embedding (eot_token is the highest number in each sequence)\n", "        x = x[torch.arange(x.shape[0]), text.argmax(dim=-1)] @ self.text_projection\n", "\n", "        return x\n", "\n", "    def forward(self, image, text):\n", "        image_features = self.encode_image(image)\n", "        text_features = self.encode_text(text)\n", "\n", "        # normalized features\n", "        image_features = image_features / image_features.norm(dim=1, keepdim=True)\n", "        text_features = text_features / text_features.norm(dim=1, keepdim=True)\n", "\n", "        # cosine similarity as logits\n", "        logit_scale = self.logit_scale.exp()\n", "        logits_per_image = logit_scale * image_features @ text_features.t()\n", "        logits_per_text = logits_per_image.t()\n", "\n", "        # shape = [global_batch_size, global_batch_size]\n", "        return logits_per_image, logits_per_text\n", "\n", "\n", "def convert_weights(model: nn.Module):\n", "    \"\"\"Convert applicable model parameters to fp16\"\"\"\n", "\n", "    def _convert_weights_to_fp16(l):\n", "        if isinstance(l, (nn.Conv1d, nn.Conv2d, nn.Linear)):\n", "            l.weight.data = l.weight.data.half()\n", "            if l.bias is not None:\n", "                l.bias.data = l.bias.data.half()\n", "\n", "        if isinstance(l, nn.MultiheadAttention):\n", "            for attr in [*[f\"{s}_proj_weight\" for s in [\"in\", \"q\", \"k\", \"v\"]], \"in_proj_bias\", \"bias_k\", \"bias_v\"]:\n", "                tensor = getattr(l, attr)\n", "                if tensor is not None:\n", "                    tensor.data = tensor.data.half()\n", "\n", "        for name in [\"text_projection\", \"proj\"]:\n", "            if hasattr(l, name):\n", "                attr = getattr(l, name)\n", "                if attr is not None:\n", "                    attr.data = attr.data.half()\n", "\n", "    model.apply(_convert_weights_to_fp16)\n", "\n", "\n", "def build_model(state_dict: dict):\n", "    vit = \"visual.proj\" in state_dict\n", "\n", "    if vit:\n", "        vision_width = state_dict[\"visual.conv1.weight\"].shape[0]\n", "        vision_layers = len([k for k in state_dict.keys() if k.startswith(\"visual.\") and k.endswith(\".attn.in_proj_weight\")])\n", "        vision_patch_size = state_dict[\"visual.conv1.weight\"].shape[-1]\n", "        grid_size = round((state_dict[\"visual.positional_embedding\"].shape[0] - 1) ** 0.5)\n", "        image_resolution = vision_patch_size * grid_size\n", "    else:\n", "        counts: list = [len(set(k.split(\".\")[2] for k in state_dict if k.startswith(f\"visual.layer{b}\"))) for b in [1, 2, 3, 4]]\n", "        vision_layers = tuple(counts)\n", "        vision_width = state_dict[\"visual.layer1.0.conv1.weight\"].shape[0]\n", "        output_width = round((state_dict[\"visual.attnpool.positional_embedding\"].shape[0] - 1) ** 0.5)\n", "        vision_patch_size = None\n", "        assert output_width ** 2 + 1 == state_dict[\"visual.attnpool.positional_embedding\"].shape[0]\n", "        image_resolution = output_width * 32\n", "\n", "    embed_dim = state_dict[\"text_projection\"].shape[1]\n", "    context_length = state_dict[\"positional_embedding\"].shape[0]\n", "    vocab_size = state_dict[\"token_embedding.weight\"].shape[0]\n", "    transformer_width = state_dict[\"ln_final.weight\"].shape[0]\n", "    transformer_heads = transformer_width // 64\n", "    transformer_layers = len(set(k.split(\".\")[2] for k in state_dict if k.startswith(\"transformer.resblocks\")))\n", "\n", "    model = CLIP(\n", "        embed_dim,\n", "        image_resolution, vision_layers, vision_width, vision_patch_size,\n", "        context_length, vocab_size, transformer_width, transformer_heads, transformer_layers\n", "    )\n", "\n", "    for key in [\"input_resolution\", \"context_length\", \"vocab_size\"]:\n", "        if key in state_dict:\n", "            del state_dict[key]\n", "\n", "    convert_weights(model)\n", "    model.load_state_dict(state_dict)\n", "    return model.eval()"]}, {"cell_type": "code", "execution_count": null, "id": "91768fce-889a-4873-866a-0f3e0faf19de", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}