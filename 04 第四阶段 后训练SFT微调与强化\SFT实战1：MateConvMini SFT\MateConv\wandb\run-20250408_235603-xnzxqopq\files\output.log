LLM总参数量：26.878 百万
Loading checkpoint from out/full_sft_512.pth
Traceback (most recent call last):
  File "/root/autodl-tmp/MateConv/full_sft.py", line 258, in <module>
    train_epoch(epoch, wandb, start_step)
  File "/root/autodl-tmp/MateConv/full_sft.py", line 44, in train_epoch
    for step, (X, Y, loss_mask) in enumerate(train_loader, start=start_step):  # 从 start_step 开始
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 630, in __next__
    data = self._next_data()
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 1345, in _next_data
    return self._process_data(data)
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 1371, in _process_data
    data.reraise()
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/torch/_utils.py", line 694, in reraise
    raise exception
RecursionError: Caught RecursionError in DataLoader worker process 0.
Original Traceback (most recent call last):
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/torch/utils/data/_utils/worker.py", line 308, in _worker_loop
    data = fetcher.fetch(index)
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 51, in fetch
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 51, in <listcomp>
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/root/autodl-tmp/MateConv/model/dataset.py", line 115, in __getitem__
    return self.__getitem__((index + 1) % len(self.df))
  File "/root/autodl-tmp/MateConv/model/dataset.py", line 115, in __getitem__
    return self.__getitem__((index + 1) % len(self.df))
  File "/root/autodl-tmp/MateConv/model/dataset.py", line 115, in __getitem__
    return self.__getitem__((index + 1) % len(self.df))
  [Previous line repeated 963 more times]
  File "/root/autodl-tmp/MateConv/model/dataset.py", line 92, in __getitem__
    prompt_ids = self.tokenizer(prompt_text, add_special_tokens=False).input_ids
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 3055, in __call__
    encodings = self._call_one(text=text, text_pair=text_pair, **all_kwargs)
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 3163, in _call_one
    return self.encode_plus(
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 3237, in encode_plus
    return self._encode_plus(
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/transformers/tokenization_utils_fast.py", line 601, in _encode_plus
    batched_output = self._batch_encode_plus(
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/transformers/tokenization_utils_fast.py", line 576, in _batch_encode_plus
    return BatchEncoding(sanitized_tokens, sanitized_encodings, tensor_type=return_tensors)
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 215, in __init__
    super().__init__(data)
  File "/root/miniconda3/envs/MateConv/lib/python3.10/collections/__init__.py", line 1094, in __init__
    self.update(dict)
  File "/root/miniconda3/envs/MateConv/lib/python3.10/_collections_abc.py", line 997, in update
    if isinstance(other, Mapping):
  File "/root/miniconda3/envs/MateConv/lib/python3.10/abc.py", line 119, in __instancecheck__
    return _abc_instancecheck(cls, instance)
RecursionError: maximum recursion depth exceeded in comparison

Traceback (most recent call last):
  File "/root/autodl-tmp/MateConv/full_sft.py", line 258, in <module>
    train_epoch(epoch, wandb, start_step)
  File "/root/autodl-tmp/MateConv/full_sft.py", line 44, in train_epoch
    for step, (X, Y, loss_mask) in enumerate(train_loader, start=start_step):  # 从 start_step 开始
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 630, in __next__
    data = self._next_data()
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 1345, in _next_data
    return self._process_data(data)
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/torch/utils/data/dataloader.py", line 1371, in _process_data
    data.reraise()
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/torch/_utils.py", line 694, in reraise
    raise exception
RecursionError: Caught RecursionError in DataLoader worker process 0.
Original Traceback (most recent call last):
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/torch/utils/data/_utils/worker.py", line 308, in _worker_loop
    data = fetcher.fetch(index)
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 51, in fetch
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/torch/utils/data/_utils/fetch.py", line 51, in <listcomp>
    data = [self.dataset[idx] for idx in possibly_batched_index]
  File "/root/autodl-tmp/MateConv/model/dataset.py", line 115, in __getitem__
    return self.__getitem__((index + 1) % len(self.df))
  File "/root/autodl-tmp/MateConv/model/dataset.py", line 115, in __getitem__
    return self.__getitem__((index + 1) % len(self.df))
  File "/root/autodl-tmp/MateConv/model/dataset.py", line 115, in __getitem__
    return self.__getitem__((index + 1) % len(self.df))
  [Previous line repeated 963 more times]
  File "/root/autodl-tmp/MateConv/model/dataset.py", line 92, in __getitem__
    prompt_ids = self.tokenizer(prompt_text, add_special_tokens=False).input_ids
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 3055, in __call__
    encodings = self._call_one(text=text, text_pair=text_pair, **all_kwargs)
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 3163, in _call_one
    return self.encode_plus(
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 3237, in encode_plus
    return self._encode_plus(
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/transformers/tokenization_utils_fast.py", line 601, in _encode_plus
    batched_output = self._batch_encode_plus(
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/transformers/tokenization_utils_fast.py", line 576, in _batch_encode_plus
    return BatchEncoding(sanitized_tokens, sanitized_encodings, tensor_type=return_tensors)
  File "/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/transformers/tokenization_utils_base.py", line 215, in __init__
    super().__init__(data)
  File "/root/miniconda3/envs/MateConv/lib/python3.10/collections/__init__.py", line 1094, in __init__
    self.update(dict)
  File "/root/miniconda3/envs/MateConv/lib/python3.10/_collections_abc.py", line 997, in update
    if isinstance(other, Mapping):
  File "/root/miniconda3/envs/MateConv/lib/python3.10/abc.py", line 119, in __instancecheck__
    return _abc_instancecheck(cls, instance)
RecursionError: maximum recursion depth exceeded in comparison
