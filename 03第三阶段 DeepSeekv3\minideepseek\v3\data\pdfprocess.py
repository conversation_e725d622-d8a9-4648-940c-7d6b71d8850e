import os
import json
import pdfplumber
import fitz  # PyMuPDF
import pytesseract
from multiprocessing import Pool
from PIL import Image
from io import BytesIO
from tqdm import tqdm  # 进度条库
import pdfminer.pdfparser

# 统一的 JSONL 输出文件夹
JSONL_OUTPUT_DIR = "/root/autodl-tmp/minideepseek/v3/data/finPDF/output_jsonl"
os.makedirs(JSONL_OUTPUT_DIR, exist_ok=True)  # 确保目录存在

def extract_text_from_pdf(pdf_path):
    """提取 PDF 里的文本、表格、OCR 解析的图像文本"""
    extracted_data = {"file": pdf_path, "text": ""}

    try:
        # 用 pdfplumber 解析文本和表格
        with pdfplumber.open(pdf_path) as pdf:
            # 提取文本
            text_content = "\n".join(page.extract_text() for page in pdf.pages if page.extract_text())

            # 提取表格
            table_text = []
            for page in pdf.pages:
                tables = page.extract_tables()
                for table in tables:
                    if table:
                        # 处理表格中的 None 值
                        table_text.append("\n".join(
                            ["\t".join(str(cell) if cell is not None else "" for cell in row) for row in table]
                        ))

            # 合并文本
            extracted_data["text"] = text_content + "\n\n" + "\n".join(table_text)

        # 用 PyMuPDF (fitz) 解析图片
        doc = fitz.open(pdf_path)
        image_texts = []
        for page_index in range(len(doc)):
            images = doc[page_index].get_images(full=True)  # 获取当前页的所有图片
            for img_index, img in enumerate(images):
                xref = img[0]
                base_image = doc.extract_image(xref)
                image_bytes = base_image["image"]
                image = Image.open(BytesIO(image_bytes))

                # 用 OCR 识别图像中的文本
                ocr_text = pytesseract.image_to_string(image)
                if ocr_text.strip():
                    image_texts.append(f"Page {page_index + 1} Image {img_index + 1}: {ocr_text}")

        # 合并 OCR 文本
        extracted_data["text"] += "\n\n" + "\n".join(image_texts)

    except (pdfminer.pdfparser.PDFSyntaxError, ValueError, TypeError, Exception) as e:
        extracted_data["text"] = f"ERROR: Unable to read PDF ({str(e)})"
        print(f"⚠️ 无法解析 PDF: {pdf_path}，跳过。错误信息: {e}")

    return extracted_data

def process_folder(pdf_folder):
    """处理单个文件夹中的所有 PDF 并保存为 JSONL"""
    pdf_files = [os.path.join(pdf_folder, f) for f in os.listdir(pdf_folder) if f.endswith(".pdf")]
    if not pdf_files:
        return  # 如果当前文件夹没有 PDF，跳过处理

    folder_name = os.path.basename(pdf_folder)  # 获取文件夹名称
    output_file = os.path.join(JSONL_OUTPUT_DIR, f"{folder_name}.jsonl")  # JSONL 统一存放到 JSONL_OUTPUT_DIR

    print(f"📂 正在处理文件夹: {pdf_folder}，发现 {len(pdf_files)} 个 PDF 文件...")

    with Pool(processes=32) as pool:  # 32 个进程并行处理 PDF
        results = list(tqdm(pool.imap(extract_text_from_pdf, pdf_files), total=len(pdf_files), desc=f"🔄 处理中: {folder_name}"))

    # 统计失败的 PDF 文件
    failed_pdfs = [r["file"] for r in results if "ERROR" in r["text"]]

    # 保存 JSONL 格式文件
    with open(output_file, "w", encoding="utf-8") as f:
        for item in results:
            f.write(json.dumps(item, ensure_ascii=False) + "\n")  # 确保支持中文

    print(f"✅ 文件夹 {folder_name} 处理完成，结果已保存至 {output_file}")

    # 记录失败的 PDF
    if failed_pdfs:
        error_log = os.path.join(JSONL_OUTPUT_DIR, f"{folder_name}_failed_pdfs.txt")
        with open(error_log, "w", encoding="utf-8") as f:
            for pdf in failed_pdfs:
                f.write(pdf + "\n")
        print(f"⚠️ 部分 PDF 解析失败，失败文件列表已保存到: {error_log}")

def main():
    root_folder = "/root/autodl-tmp/minideepseek/v3/data/finPDF/"
    
    # 遍历根目录下的所有子文件夹
    for subdir, _, _ in os.walk(root_folder):
        if subdir == root_folder:
            continue  # 跳过根目录自身，只处理子目录
        process_folder(subdir)  # 处理每个子文件夹

if __name__ == "__main__":
    main()