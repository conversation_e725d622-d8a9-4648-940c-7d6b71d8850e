body {
  background-color: #1a1a2e;
  color: #e0e0e0;
}

.App {
  text-align: center;
  margin: 20px;
}

.container {
  margin-top: 20px;
}

button {
  margin-right: 10px;
}

.card {
  margin-top: 20px;
  background-color: #28293d;
  color: #e0e0e0;
}

.card-body {
  background-color: #28293d;
  color: #e0e0e0;
}

.mt-2 {
  margin-top: 20px;
}

.mt-4 {
  margin-top: 40px;
}

.btn-primary {
  background-color: #6a0dad;
  border-color: #6a0dad;
}

.btn-success {
  background-color: #28a745;
  border-color: #28a745;
}

.btn-info {
  background-color: #17a2b8;
  border-color: #17a2b8;
}

.btn-danger {
  background-color: #dc3545;
  border-color: #dc3545;
}

.pre {
  background-color: #1e1e2e;
  border: 1px solid #444;
  padding: 10px;
  color: #e0e0e0;
}

h1, h2 {
  color: #ffffff;
}

select.form-control {
  background-color: #2e2e4d;
  color: #ffffff;
  border: none;
}

select.form-control:focus {
  background-color: #2e2e4d;
  color: #ffffff;
  border: none;
}

.accordion-item {
  background-color: #2e2e31;
  border: none;
}

.accordion-button {
  background-color: #28293d;
  color: #b3b3b3;
}

.accordion-button:not(.collapsed) {
  background-color: #4a90e2; /* Muted blue for active item */
  color: #ffffff;
}

.accordion-body {
  background-color: #34396d;
  color: #b3b3b3;
  border: none;
}

.accordion-button:focus {
  box-shadow: none;
}