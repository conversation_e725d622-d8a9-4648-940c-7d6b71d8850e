{"cells": [{"cell_type": "markdown", "id": "3964d13d-26c2-4836-af62-218930c94a8d", "metadata": {}, "source": ["# MateConv mini数据预处理"]}, {"cell_type": "markdown", "id": "d8db2794-497c-4292-81c2-68978bca446c", "metadata": {}, "source": ["- Step 1.导入库"]}, {"cell_type": "code", "execution_count": 1, "id": "837e1245-2535-4e76-ac71-ef52490eb44a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import itertools\n", "import re\n", "import json\n", "import jsonlines\n", "import psutil\n", "import ujson\n", "import numpy as np\n", "import pandas as pd\n", "from transformers import AutoTokenizer\n", "from datasets import load_dataset\n", "import os\n", "from tqdm import tqdm"]}, {"cell_type": "markdown", "id": "7cbaeb50-ec34-40b8-9108-fa897252ae33", "metadata": {}, "source": ["- Step 2.定义BOS和EOS标记，并加载分词器"]}, {"cell_type": "code", "execution_count": 2, "id": "a33e1b8f-1154-49b2-b4d8-0b3730c3e98c", "metadata": {}, "outputs": [], "source": ["# 定义BOS和EOS标记\n", "bos_token = \"<s>\"\n", "eos_token = \"</s>\""]}, {"cell_type": "code", "execution_count": 3, "id": "98940de6-b1a8-4819-ab5f-f1ef1688ccfd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["加载的tokenizer词表大小: 6400\n"]}], "source": ["# 加载训练好的分词器路径\n", "tokenizer = AutoTokenizer.from_pretrained('/root/autodl-tmp/MateConv/model/mateconv_tokenizer', use_fast=False)\n", "print(f'加载的tokenizer词表大小: {len(tokenizer)}')"]}, {"cell_type": "markdown", "id": "1bf35c99-8a44-4f17-97be-02a71112cd16", "metadata": {}, "source": ["- Step 3.读取部分数据"]}, {"cell_type": "code", "execution_count": 4, "id": "8b63afd6-91dc-4dc0-bb34-b715078e8b2a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["第 1 行数据: {'text': '在查处虚开增值税专用发票案件中，常常涉及进项留抵税额和税款损失的认定和处理。在计算税款损失时，要不要将进项留抵税额包括在内？\\n对此，实务中存在意见分歧。\\n有人主张归并，即计算税款损失时包括进项留抵税额；\\n有人主张剥离，即计算税款损失时剔除进项留抵税额。分析这个问题，需要确定进项留抵税额与税款损失之间是什么关系。\\n理清这二者之间的关系，首先需要了解增值税的概念和其抵扣机制。增值税是以商品（货物、服务等）在流转过程中产生的增值额作为计税依据而征收的一种流转税。为避免重复征税，在增值税中存在抵扣链条机制。\\n一般而言，交易上游企业缴纳的税额，交易下游企业可以对相应的税额进行抵扣。\\n对增值税一般纳税人来说，其购进货物、服务等取得增值税专用发票，发票上的税额是进项税额。\\n其出售货物、服务等，向购买方开具增值税专用发票，发票的税额是销项税额。\\n一般情况下，销项税额减去进项税额的金额是应纳税额，企业根据应纳税额按期申报纳税。\\n其次需要了解进项留抵税额的概念及产生原因。\\n在计算销项税额和进项税额的差额时，有时会出现负数，即当期进项税额大于当期销项税额。这个差额在当期未实现抵扣，为进项留抵税额，在以后纳税人有销项税额时再进行抵扣。\\n企业产生进项留抵税额的主要原因是其进项税额和销项税额时间上的不一致。\\n例如，企业前期集中采购货物和服务，投资大，销项税率低于进项税率等。\\n从税款抵扣的角度看，进项留抵税额只是购进的这部分进项税额参与到增值税应纳税额的计算过程中，但是其对应的进项税额抵扣还未真正实现，一般要等到其未来有相应的销项税额时，才能真正实现进项税额抵扣。\\n可见，进项留抵税额处于不确定状态，能否抵扣受到很多因素影响，例如企业经营中断，没有销项税额，这时进项留抵税额就无法实现抵扣。但如果企业按照税收政策规定申请进项留抵退税，进项税额抵扣就随之实现。\\n最后需要了解税款损失的概念。\\n税款损失，通常是指因虚开增值税专用发票，导致国家税款被骗或者流失的金额。关于税款损失，实务中有多种表述。\\n例如，北京大学法学院教授陈兴良曾谈到虚开行为本身不会造成国家税款损失，只有利用发票抵扣时才会造成国家税款损失。刘兵等编著的《虚开增值税专用发票案例司法观点和案例解析》一书中提到：“给国家税款造成损失的数额，实际上就是被骗取的国家税款在侦查终结以前无法追回的部分。”\\n赵清海与王家欣合著的《增值税专用发票虚开的判定与预防》一书中提到：“司法实践中，受票方用虚开的增值税专用发票予以抵扣的税款，从而导致受票方应纳税额的减少是法院所认定的国家税款流失的金额。”\\n从这些表述可见，税款损失应该是实际造成的损失，不应包括不确定的部分——进项留抵税额，进项留抵税额与税款损失之间不能直接画等号。\\n综上分析，进项留抵税额，只是使国家税款处于可能被抵扣的状态，还没有真正造成国家税款流失，一般情况下应将其从税款损失中剥离，特殊条件下将其归并入税款损失。\\n例如，当纳税人造假按照税收政策规定申请进项留抵税额退税后，有关税款损失将会从危险状态转化成危害结果，这时候要将有关进项留抵税额并入税款损失。\\n所以，在虚开增值税专用发票案件中，一般情况下，如果以纳税人的进项税额作为税款损失的计算基数，在对其进行行政处罚或刑事处罚时，应把进项留抵税额从税款损失中剔除，但纳税人申请进项留抵退税的除外。这样处理，把处罚与危害结果相对应，体现行政处罚法的过罚相当原则和刑法的罚当其罪原则。'}\n", "第 2 行数据: {'text': '读者在使用本《年鉴》时发现与以前本局出版、公布、或内部提供的资料有出入的，概以本《年鉴》为准。\\n《年鉴》正文内容分为三大部分。第一部分为文字部分，收录了《2012年政府工作报告》以及《2011年河源市国民经济和社会发展统计公报》。第二部分为统计图，形象地反映建市以来河源市国民经济发展变化情况。第三部分为统计资料，具体分为行政区划和自然资源，综合、核算、人口，农村经济，工业，能源，交通、邮电，贸易业、物价指数，对外经济、旅游，财政、金融和保险，固定资产投资与建筑业，劳动工资，人民生活，文教、卫生和其他，河源市乡镇主要经济指标，广东省县域主要经济指标,广东省各市主要经济指标等16部分。此外，为便于读者正确理解和使用统计资料，特附主要统计指标解释、统计术语简介及统计法律法规等资料。\\n《年鉴》中，本市的数据是根据我局及有关部门的统计年报整理汇编而成，由于某些专业统计制度和统计口径的变化，有些数据空缺。使用本《年鉴》时，请注意指标名称的含义、统计口径、统计范围、计算单位、可比价与现行价(当年价)等。\\n《年鉴》第一部分中的有些数据为初步统计数，凡与本《年鉴》中第三部分的数据有出入的，则以第三部分的统计数据为准。\\n本《年鉴》部分统计数据使用了四舍五入的进位方法，因此，可能令统计表内个别项目相加与总数略有出入。\\n本《年鉴》统计表中符号使用说明：“＃”表示其中主要项；“空格”表示该项统计指标数据不详或无该项数据。\\n本《年鉴》的编辑出版，得到县、区及市直有关部门和单位的大力支持，在此表示感谢！本书疏漏之处敬请批评指正。\\n下载说明： �本站下载的文件一律为压缩文件，请使用 WinRAR 解压。\\n�PDF格式的资料请使用 Adobe Reader 浏览。\\n�本站提供的一些资料是供学习研究之用，如用于商业用途，请购买正版。'}\n", "第 3 行数据: {'text': '初中阶段是学生身心发育的一个突变期。尤其是初一学生，从小学到中学，随着环境改变，课程增多，难度加大，他们内心发生了急剧变化，产生了许多烦恼、困惑，造成较大的心理偏差，这就需要教师和家长及时给予心理指导和帮助。\\n一、心理偏差的种种表现\\n1、骄傲自负心理。这种心理偏差主要表现在思维敏捷、小学成绩拔尖的学生身上，特别是一些长期担任班干部、竞赛获奖、父母有权力的学生表现尤为明显。\\n2．单纯求趣心理。求趣激趣，这是教学的原则之一，但是，有些初一学生过分地追求接受知识要符合自己的兴趣，还想回到幼儿园、小学时“游戏教育”和“愉快教育”中去，不能努力适应初中阶段的学习生活。\\n3．自卑孤僻心理。多数来自普通工薪家庭及农村贫困地区或遭遇父母婚变的学生，往往在干部、富家子弟、有特长的同学面前感到自卑，心理压抑，行为孤僻，甚至变态的自尊，影响学习。\\n4．胆怯畏惧心理。部分性格内向、胆小的学生，主要是女生，羞于用语言表达思想，沉溺于内心活动和笔头表达。内心活动不能外显，妨碍了思维素质的深入发展。\\n5．浮躁马虎心理。部分活泼好动的学生，智力水平不低，但就是不能静下心学习，总是浅尝辄止，马虎应付，不愿作深入的思考，常常“半罐水响叮当”。\\n6．贪图享受心理。一些家境较好的学生，行为懒散，好逸恶劳，学习上畏难怕苦，生活上讲吃讲穿。\\n二、上述心理偏差的形成原因\\n1．生理上的原因。初中学生处于发育高峰期，身高体重剧增，性发育开始。生理上的急剧变化使儿童意识到自己不再是孩子，“成人感”增强。但是，青年身体成熟速度存在着很大的个体差异：不同性别之间相差两年左右，同性别之间相差四年左右。因此，同是初中学生，一部分学生生理已跨入青年期，而另一部分学生可能还停留在童年期。\\n2．心理上的原因。随着生理的变化，“成人感”的出现，初中学生心理产生“独立”，力求摆脱对成人的依赖，老师、家长在他们心目中的权威降低，同学之间相互影响增强。思维上发展了批判性，但由于经验的缺乏含有片面性和主观性；行为上出现“独特性”和“受暗示性”乃至“抗拒性”，即逆反心理；情绪上带有冲动性，不善于克制自己；兴趣和愿望上带有随意性、多变性、狂热性，常为了所谓讲“义气”而庇护同伴，或为同伴打抱不平；感情上具有“闭锁性”，而对于艰苦的学习活动特别重要的意志品质，则还处在比较软弱的状态。\\n3．环境的原因。心理学认为，个体的生物遗传因素规定了发展的潜在可能范围，而个体环境教育则确定他在此可能范围内的现实水平。环境条件有利与否对个体发展的现实水平起了决定性作用。\\n①家庭。社会的信仰、观念等社会化目标都是首先通过父母的过渡，以高度个性化了的、有选择的形式传递给儿童的。父母本身的个性特征、社会地位、教育水平、宗教信仰、价值标准等等都强烈地影响他们的后代。父母的教养方式、家庭结构、物质条件、人际环境、文化和情绪氛围，都在很大程度上影响着学生。\\n②学校。学校不仅是对学生传授文化科学知识，进行政治思想教育的社会基本教育单元，还是促进学生良好品格形成和发展的重要场所。学生在学校里形成良好的品格，才能顺利走向社会，适应社会生活。反之，则会发生各种问题。而现在的应试教育制度，像紧箍咒一样，时时冲击着素质教育，教师以升学率论质量给待遇，使一些教师对成绩好的学生倍加宠爱，对成绩差的学生则百般呵斥。更有少数教师将腐朽庸俗的人际关系引入师生和家长的关系，身教言传，污染了学生心灵；让孩子过早成人化、世故化。\\n③社会。社会上各种腐朽思想沉渣泛起，对学生负面影响很大。影视传媒、流失少年、勒索等等，浸染着学生稚嫩的心灵；电子游戏机、卡拉OK厅等，又使我们的孩子面临着极强的诱惑，意志薄弱者稍不留意，便坠入其间。\\n三、纠正初中学生的心理偏差的对策\\n为了纠正初中学生的心理偏差，我们必须对教育环境影响予以高度重视。在现有环境中，我们应做到：\\n1．坚持以德、智、体、美、劳全面的教育方针为指导思想进行教育管理，坚持“要成才先成人”的教育思想。\\n2．“学高为师，身正为范。”作为教师，必须加强道德修养，提高职业素质，全面关心和爱护每一位学生的身心健康发展。\\n3．以激励为心育的主要手段。我们要将思想教育和学生喜闻乐见的实践活动结合起来，不断提高学生对美的感受和鉴赏力，使其求真向善，茁壮成长。\\n4．形成教育合力。在抓好班集体建设的同时，我们必须密切联系家长，与家长一起研究分析学生，共同教育学生。\\n5．帮助学生正确认识、分析、评价自己的心理过程。让他们将社会化标准－－《中学生日常行为规范》逐渐内化，用以规范自己的言行，自觉抵制不良诱惑，不断提高自我意识水平和自我教育能力。\\n6．对各类心理偏差学生施以不同的教育。对有骄傲自负心理的学生施以“挫折教育”；对有自卑、胆怯畏惧心理的学生施以“磨难教育”；对有虚荣忌妒、趋同报复、庸俗心理的学生施以分辨真美善、假丑恶的“是非教育”等。\\n与此同时，还应努力提高、优化当代中学生的心理特点。\\n首先，作为家长必须转变观念。对自己的孩子，在作业和职业方面的“期望值”不能脱离子女的实际而好高骛远，每个孩子因智力因素、情趣爱好，性格意志和心理承力各不相同，如果孩子确实尽了自己的努力，而未达到你所期望的目标，不应过多责怪，更不能冷嘲热讽，惩罚打骂。诚然，家长望子成龙“天经地义”，无可厚非。但“龙”的内涵并不专指读大学、考研究生。“三百六十行，行行出状元”，如果每一位家长都能建立这样的“职业观”，让孩子在宽松的环境里读书，\\n其次，作为教育者----教师来说，则更要不断学习，及时吸收新鲜气息，不断提高自己的思想、政治教育水平，提高自己的专业知识和业务水平，做到不仅能教书育人，更能进行教育评价，尊重学生人格，依法执教，用先进的具有创造性的教育思想、理论、方法促进教育水平的提高，注重培养学生的全面发展，加强能力培养和思维训练，提高学生的综合素质。具体方法如下：\\n第一，让学生充分了解自己的心理特点，通过与周围的同学以及其他同龄人相比，通过同电影、小说电视里特定情景中的人物相比，如宣传奥斯特洛夫斯基、托尔斯泰、张海迪、贝多芬等等，通过对比，找出自己在哪些方面存在弱点，或者也可以通过父母、老师、同学对自己经常的评价了解自己在哪些方面存在不良心理特点，从而扬长避短。\\n第二，选择恰当的方法进行锻炼。例如：\\n1、教他们多读好书，如《周恩来》、《钢铁是怎样炼成的》等优化心理品质。人类的几千年文明，其智慧、经验、真知灼见，都浓缩于书中，如果多读好书，能经常与这样一些“高尚朋友”对话，听听他们的“指点”以此开阔视野，启迪智慧，这对优化学生的心理品质是大有裨益的，作为中学生，不仅要读好的故事书，还应该读一些伟人的传记，读一些思想、修养方面的书籍，并且养成做读书笔记的习惯。\\n2、鼓励学生参加社会活动，锻炼心理品质，如送“温暖小组”、“助残小分队”等活动的开展，都是锻炼心理品质行之有效的方法。\\n3、也要注重培养学生琴、棋、书、画、音、体、美等美育活动，有助于疏导、排解不良情绪，给人以美的熏陶和享受，从而对心理产生良性刺激。让美来充实孩子的精神生活，让美来帮助塑造孩子健康的心理。\\n4、在条件可能的情况下，可组织学生春游、郊游、野炊等活动，学生也可以利用寒、曙假、节假日到一些名胜古迹去游览、旅游、参观、陶冶自己的情操，走进大自然，亲近大自然，细心体会大自然，不仅能使人心胸开阔、情绪放松，精神振奋，还常能使人领悟到人生的真谛。\\n只有这样，优化了学生的心理特点，才能促使学生健康成长，从而成为新世纪的合格人才。'}\n", "第 4 行数据: {'text': '我们生产的食品消泡剂，具有可以快速消除泡沫的特点。\\n丹东食品消泡剂相关内容：一般而言，纯水和纯表面活性剂不起泡，这是因为它们的表面和内部是均匀的，很难形成弹性薄膜，即使形成亦不稳定，会瞬间消失。\\n丹东食品消泡剂选择：\\n1. 相容性：相容性是指两种或者两种以上物质混合时，不产生相斥分离现象的能力，相容性好，消泡剂就能够长期、稳定、均匀地存在于体系中，进而发挥消抑泡的作用；反之，就会出现分层等现象，使消泡剂的消泡工作无法正常进行。\\n2. 消泡能力：消泡能力是消泡剂的最主要性能，鉴别此项性能的标准是在同等条件下，分别加入等量不同的消泡剂，观察消泡剂的消泡速度。'}\n", "第 5 行数据: {'text': '程总在座谈中首先向学校的客人介绍了三一集团和北京三一重机的情况，以及在公司快速发展过程中对人才的渴求，指出通过校企联合，学校可以依靠企业的参与制定人才培养方案，使培养的人才更贴近市场，贴近企业，又可以借助企业的资源充实学校的办学实力。同时校企联合有利于企业的可持续发展。校企联合是企业实现人才战略的途径。企业在与高等职业教育合作过程中可以贯彻自己的培养意向，满足对生产第一线实用型人才的需求。\\n武汉交通职业学院盛建龙院长和河北工业职业技术学院李军锁副院长分别介绍了各自学校人才培养情况，并对三一集团的高速发展表示钦佩和赞赏，表示将和公司开展深入、全面的合作，优势互补，使学校和企业实现充分的资源共享，建立全方位长效合作机制。\\n本次联合办学签约仪式，是北京桩机高起点校企合作的开始。按照北京桩机人力资源提升计划，明年北京桩机将和所高职高专院校进行联合办学成立“三一班”，均为统招大专高技学历层次，涉及焊接、装配、机加工、售后服务等紧缺工种，“三一班”学员将达到近300人，为北京桩机的下一个五年跨越式发展打下良好的人才基础。'}\n"]}], "source": ["def preview_dataset(file_path, num_lines=5):\n", "    \"\"\"\n", "    读取并展示数据集的前 num_lines 行\n", "    \"\"\"\n", "    # 检查文件是否存在\n", "    if not os.path.exists(file_path):\n", "        raise FileNotFoundError(f\"{file_path} 文件不存在，请检查路径！\")\n", "\n", "    # 逐行读取并展示前 num_lines 行\n", "    with jsonlines.open(file_path) as reader:\n", "        for idx, obj in enumerate(reader):\n", "            print(f\"第 {idx + 1} 行数据: {obj}\")\n", "            if idx + 1 >= num_lines:\n", "                break\n", "\n", "# 指定文件路径和需要展示的行数\n", "file_path = './Data/seq-monkey/mobvoi_seq_monkey_general_open_corpus.jsonl'\n", "preview_dataset(file_path, num_lines=5)"]}, {"cell_type": "markdown", "id": "0a386e30-4306-4327-81a6-50e2af27f6bf", "metadata": {}, "source": ["- Step 4.统计与清理数据"]}, {"cell_type": "code", "execution_count": 5, "id": "1b46245b-ef76-4990-aec2-7428afb026d9", "metadata": {}, "outputs": [], "source": ["def get_total_lines(file_path):\n", "    \"\"\"\n", "    获取 JSONL 文件的总行数，不忽略错误，保证能够全面统计。\n", "    \"\"\"\n", "    with open(file_path, 'rb') as f:  # 使用二进制模式避免编码问题\n", "        return sum(1 for _ in f)"]}, {"cell_type": "code", "execution_count": 6, "id": "894de9c8-a503-4605-bc31-8ab30f2dfa07", "metadata": {}, "outputs": [], "source": ["def check_jsonl_format(file_path):\n", "    \"\"\"\n", "    检查 JSONL 文件中的每一行是否是有效的 JSON 格式，带进度显示，并统计所有有问题的行。\n", "    \"\"\"\n", "    total_lines = get_total_lines(file_path)  # 获取文件总行数\n", "    valid_lines = 0\n", "    invalid_lines = 0\n", "\n", "    # 使用逐行读取，捕获 JSON 和编码错误\n", "    with open(file_path, 'rb') as f:  # 使用二进制读取避免编码问题\n", "        # 使用 tqdm 进度条显示检查进度\n", "        for idx, line in tqdm(enumerate(f), total=total_lines, desc=\"Checking JSONL format\"):\n", "            try:\n", "                # 先尝试将每行数据解码为 UTF-8\n", "                decoded_line = line.decode('utf-8')\n", "                # 然后检查是否是有效的 JSON 格式\n", "                obj = jsonlines.Reader([decoded_line]).read()\n", "                valid_lines += 1\n", "            except UnicodeDecodeError as e:\n", "                print(f\"Encoding error at line {idx + 1}: {e}\")\n", "                invalid_lines += 1\n", "            except jsonlines.InvalidLineError as e:\n", "                print(f\"Invalid JSON at line {idx + 1}: {e}\")\n", "                invalid_lines += 1\n", "\n", "    print(f\"检查完成，文件中共有 {valid_lines} 行有效的 JSON 数据，{invalid_lines} 行无效的 JSON 数据。\")\n", "    return valid_lines, invalid_lines"]}, {"cell_type": "code", "execution_count": 7, "id": "29696939-ea02-4ac1-97c6-abf977b355fc", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Checking JSONL format: 100%|█████████████████████████████████████████████████████████| 13000000/13000000 [02:59<00:00, 72338.63it/s]"]}, {"name": "stdout", "output_type": "stream", "text": ["检查完成，文件中共有 13000000 行有效的 JSON 数据，0 行无效的 JSON 数据。\n"]}, {"name": "stderr", "output_type": "stream", "text": ["\n"]}], "source": ["valid_lines, invalid_lines = check_jsonl_format(file_path)"]}, {"cell_type": "code", "execution_count": 8, "id": "74d6ec0f-c7bf-4b27-a1a2-ef7ff3cf810c", "metadata": {}, "outputs": [], "source": ["def remove_invalid_line(file_path, output_path, invalid_line_num):\n", "    \"\"\"\n", "    读取文件，跳过指定的无效行，并将结果写入新文件\n", "    \"\"\"\n", "    with open(file_path, 'rb') as infile, open(output_path, 'wb') as outfile:\n", "        for idx, line in enumerate(infile):\n", "            if idx + 1 != invalid_line_num:  # 跳过无效行\n", "                outfile.write(line)\n", "\n", "# 使用该函数删除第 9598787 行并保存为新文件\n", "remove_invalid_line('./Data/seq-monkey/mobvoi_seq_monkey_general_open_corpus.jsonl',\n", "                    './Data/seq-monkey/mobvoi_seq_monkey_general_open_corpus_cleaned.jsonl', \n", "                    invalid_line_num=9598787)"]}, {"cell_type": "markdown", "id": "71c875de-1c65-4131-afc4-1e32201a35b7", "metadata": {}, "source": ["最终会得到如下的文件 ↓"]}, {"cell_type": "markdown", "id": "17adb098-7205-4ef9-a3c5-e0e60d31b273", "metadata": {}, "source": ["<center><img src=\"https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/92.png\" alt=\"image-20241022201401913\" style=\"zoom:60%;\" />"]}, {"cell_type": "markdown", "id": "64cbb9b0-ae76-4f49-82e5-283f8799889f", "metadata": {}, "source": ["- Step 5.定义处理函数（逐块处理数据）"]}, {"cell_type": "code", "execution_count": 12, "id": "b61eb6d6-6a2d-40de-9d73-160a18e251b3", "metadata": {}, "outputs": [], "source": ["def process_seq_monkey(chunk_size=50000):\n", "    \"\"\"\n", "    逐块读取 mobvoi_seq_monkey_general_open_corpus.jsonl 文件，\n", "    对文本进行分词，并将分词结果保存为二进制文件，支持跳过无效行，并显示处理进度。\n", "    \"\"\"\n", "    doc_ids = []\n", "    chunk_idx = 0\n", "    total_lines = 0\n", "\n", "    # 先计算总行数以便显示进度\n", "    # 注意调整成你自己的目录\n", "    with open('./Data/seq-monkey/mobvoi_seq_monkey_general_open_corpus_cleaned.jsonl', 'r', encoding='utf-8') as f:\n", "        total_lines = sum(1 for _ in f)\n", "\n", "    # 打开jsonlines文件逐行读取\n", "    with jsonlines.open('./Data/seq-monkey/mobvoi_seq_monkey_general_open_corpus_cleaned.jsonl') as reader:\n", "        # 使用 tqdm 进度条显示进度\n", "        with tqdm(total=total_lines, desc=\"Processing lines\") as pbar:\n", "            while True:\n", "                try:\n", "                    # 使用 itertools.islice 按块读取文件，每次读取 chunk_size 行数据\n", "                    chunk = list(itertools.islice(reader, chunk_size))\n", "                except jsonlines.InvalidLineError as e:\n", "                    print(f\"Skipping invalid chunk at chunk {chunk_idx}: {e}\")\n", "                    continue\n", "\n", "                if not chunk:  # 如果读取到文件末尾，则停止\n", "                    break\n", "\n", "                # 遍历块中的每一行数据\n", "                # 逐行对数据进行编码（按token进行编码）\n", "                for idx, obj in enumerate(chunk):\n", "                    try:\n", "                        # 从每一行数据中提取'text'字段（即文本内容）\n", "                        content = obj.get('text', '')\n", "                        \n", "                        # 跳过长度超过512的文本\n", "                        if len(content) > 512:\n", "                            continue\n", "\n", "                        # 对文本进行分词，将其转为 token ids 序列，并加上BOS和EOS标记\n", "                        text_id = tokenizer(f'{bos_token}{content}{eos_token}').data['input_ids']\n", "                        \n", "                        # 将分词结果添加到 doc_ids 列表中\n", "                        doc_ids += text_id\n", "\n", "                    except UnicodeDecodeError as e:\n", "                        # 如果遇到编码错误，跳过该行，并打印错误信息\n", "                        print(f\"Skipping invalid line {chunk_idx * chunk_size + idx + 1}: {e}\")\n", "                        continue\n", "\n", "                # 每处理完一块数据，更新 chunk_idx 并打印进度信息\n", "                chunk_idx += 1\n", "                pbar.update(len(chunk))  # 更新进度条\n", "\n", "                # 如果累积的 token ids 超过 1,000,000 个，保存到文件中\n", "                if len(doc_ids) > 1000000:\n", "                    arr = np.array(doc_ids, dtype=np.uint16)\n", "                    with open(f'./Data/seq-monkey/clean_seq_monkey.bin', 'ab') as f:\n", "                        f.write(arr.tobytes())\n", "                    doc_ids = []\n", "\n", "    # 如果处理完所有数据后 doc_ids 中还有未保存的内容，最后再保存一次\n", "    if doc_ids:\n", "        arr = np.array(doc_ids, dtype=np.uint16)\n", "        with open(f'./Data/seq-monkey/clean_seq_monkey.bin', 'ab') as f:\n", "            f.write(arr.tobytes())"]}, {"cell_type": "code", "execution_count": 17, "id": "6fe07ac5-e5a5-4fc4-a1d7-0da6cc8b2633", "metadata": {}, "outputs": [], "source": ["def pretrain_process():\n", "    \"\"\"\n", "    函数的作用是调用 process_seq_monkey() 函数生成数据，\n", "    然后整合所有生成的二进制文件，并将其合并保存为一个总的预训练数据文件。\n", "    \"\"\"\n", "    # 调用 process_seq_monkey 函数处理数据\n", "    process_seq_monkey()\n", "\n", "    # 数据文件路径列表，目前只处理 clean_seq_monkey.bin 文件\n", "    data_path_list = [\n", "        './Data/seq-monkey/clean_seq_monkey.bin'\n", "    ]\n", "    \n", "    data_lst = []\n", "    \n", "    # 读取生成的二进制文件\n", "    for data_path in data_path_list:\n", "        with open(data_path, 'rb') as f:\n", "            # 将二进制文件中的内容加载到 numpy 数组中\n", "            data = np.fromfile(f, dtype=np.uint16)\n", "            data_lst.append(data)\n", "\n", "    # 将所有读取到的数据合并为一个大数组\n", "    arr = np.concatenate(data_lst)\n", "    print(f\"合并后的数据大小: {arr.shape}\")\n", "\n", "    # 将合并后的数据保存为最终的预训练数据文件\n", "    with open('./Data/seq-monkey/pretrain_data.bin', 'wb') as f:\n", "        f.write(arr.tobytes())"]}, {"cell_type": "markdown", "id": "9b465375-c682-45b3-9351-4a0a6091b183", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/image-20241022201432618.png\" alt=\"image-20241022201432618\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "0c3c6a64-d408-4e65-829b-ffca6a9877cd", "metadata": {}, "source": ["- 运行数据处理"]}, {"cell_type": "markdown", "id": "dd40c5e5-a9fe-430b-a745-e8003d1ff8f1", "metadata": {}, "source": ["【TIME WARNING：45分钟左右】"]}, {"cell_type": "code", "execution_count": 18, "id": "95f14945-92fd-43e3-a0b5-c27d093bbced", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Processing lines: 100%|███████████████████████████████████████████████████████████████| 12999999/12999999 [46:15<00:00, 4684.46it/s]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["合并后的数据大小: (1510396873,)\n"]}], "source": ["pretrain_process()"]}, {"cell_type": "markdown", "id": "5a35bb75-8253-436e-a306-fcfe71f8dbc1", "metadata": {}, "source": ["运行结束后会创建一个名为pretrain_data.bin的二进制数据文件，该文件也就是接下来进行模型预训练的文件："]}, {"cell_type": "markdown", "id": "553be73f-6edc-45ff-b66d-5371fc70d25b", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/image-20241023183247295.png\" alt=\"image-20241023183247295\" style=\"zoom:33%;\" />"]}, {"cell_type": "code", "execution_count": null, "id": "8a84ec36-985c-43fc-b91e-bd98e7ed8ec6", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}