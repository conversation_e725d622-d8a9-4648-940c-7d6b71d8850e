{"name": "activateParkingBrake", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Activates the parking brake of the vehicle.", "parameters": {"type": "dict", "properties": {"mode": {"type": "string", "description": "The mode to set. [Enum]: [\"engage\", \"release\"]"}}, "required": ["mode"]}, "response": {"type": "dict", "properties": {"parkingBrakeStatus": {"type": "string", "description": "The status of the brake. [Enum]: [\"engaged\", \"released\"]"}, "_parkingBrakeForce": {"type": "float", "description": "The force applied to the brake in Newtons."}, "_slopeAngle": {"type": "float", "description": "The slope angle in degrees."}}}}
{"name": "adjustClimateControl", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Adjusts the climate control of the vehicle.", "parameters": {"type": "dict", "properties": {"temperature": {"type": "float", "description": "The temperature to set in degree. Default to be celsius."}, "unit": {"type": "string", "description": "The unit of temperature. [Enum]: [\"celsius\", \"fahrenheit\"]", "default": "celsius"}, "fanSpeed": {"type": "integer", "description": "The fan speed to set from 0 to 100. Default is 50.", "default": 50}, "mode": {"type": "string", "description": "The climate mode to set. [Enum]: [\"auto\", \"cool\", \"heat\", \"defrost\"]", "default": "auto"}}, "required": ["temperature"]}, "response": {"type": "dict", "properties": {"currentTemperature": {"type": "float", "description": "The current temperature set in degree Celsius."}, "climateMode": {"type": "string", "description": "The current climate mode set."}, "humidityLevel": {"type": "float", "description": "The humidity level in percentage."}}}}
{"name": "check_tire_pressure", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Checks the tire pressure of the vehicle.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"tirePressure": {"type": "dict", "description": "The tire pressure of the vehicle.", "properties": {"frontLeftTirePressure": {"type": "float", "description": "The pressure of the front left tire in psi."}, "frontRightTirePressure": {"type": "float", "description": "The pressure of the front right tire in psi."}, "rearLeftTirePressure": {"type": "float", "description": "The pressure of the rear left tire in psi."}, "rearRightTirePressure": {"type": "float", "description": "The pressure of the rear right tire in psi."}, "healthy_tire_pressure": {"type": "boolean", "description": "True if the tire pressure is healthy, False otherwise."}, "car_info": {"type": "dict", "description": "The metadata of the car."}}}}}}
{"name": "displayCarStatus", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Displays the status of the vehicle based on the provided display option.", "parameters": {"type": "dict", "properties": {"option": {"type": "string", "description": "The option to display. [Enum]: [\"fuel\", \"battery\", \"doors\", \"climate\", \"headlights\", \"parkingBrake\", \"brakePadle\", \"engine\"]"}}, "required": ["option"]}, "response": {"type": "dict", "properties": {"status": {"type": "dict", "description": "The status of the vehicle based on the option.", "properties": {"fuelLevel": {"type": "float", "description": "[Optional] The fuel level of the vehicle in gallons."}, "batteryVoltage": {"type": "float", "description": "[Optional] The battery voltage of the vehicle in volts."}, "doorStatus": {"type": "dict", "description": "[Optional] The status of the doors.", "properties": {"driver": {"type": "string", "description": "The status of the driver door. [Enum]: [\"locked\", \"unlocked\"]"}, "passenger": {"type": "string", "description": "The status of the passenger door. [Enum]: [\"locked\", \"unlocked\"]"}, "rear_left": {"type": "string", "description": "The status of the rear left door. [Enum]: [\"locked\", \"unlocked\"]"}, "rear_right": {"type": "string", "description": "The status of the rear right door. [Enum]: [\"locked\", \"unlocked\"]"}}}, "currentACTemperature": {"type": "float", "description": "[Optional] The current temperature set in degree Celsius."}, "fanSpeed": {"type": "integer", "description": "[Optional] The fan speed set from 0 to 100."}, "climateMode": {"type": "string", "description": "[Optional] The climate mode set. [Enum]: [\"auto\", \"cool\", \"heat\", \"defrost\"]"}, "humidityLevel": {"type": "float", "description": "[Optional] The humidity level in percentage."}, "headlightStatus": {"type": "string", "description": "[Optional] The status of the headlights. [Enum]: [\"on\", \"off\"]"}, "parkingBrakeStatus": {"type": "string", "description": "[Optional] The status of the brake. [Enum]: [\"engaged\", \"released\"]"}, "parkingBrakeForce": {"type": "float", "description": "[Optional] The force applied to the brake in Newtons."}, "slopeAngle": {"type": "float", "description": "[Optional] The slope angle in degrees."}, "brakePedalStatus": {"type": "string", "description": "[Optional] The status of the brake pedal. [Enum]: [\"pressed\", \"released\"]"}, "brakePedalForce": {"type": "float", "description": "[Optional] The force applied to the brake pedal in Newtons."}, "engineState": {"type": "string", "description": "[Optional] The state of the engine. [Enum]: [\"running\", \"stopped\"]"}, "metadata": {"type": "string", "description": "[Optional] The metadata of the car."}}}}}}
{"name": "display_log", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Displays the log messages.", "parameters": {"type": "dict", "properties": {"messages": {"type": "array", "items": {"type": "string"}, "description": "The list of messages to display."}}, "required": ["messages"]}, "response": {"type": "dict", "properties": {"log": {"type": "array", "description": "The list of messages displayed.", "items": {"type": "string"}}}}}
{"name": "estimate_distance", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Estimates the distance between two cities.", "parameters": {"type": "dict", "properties": {"cityA": {"type": "string", "description": "The zipcode of the first city."}, "cityB": {"type": "string", "description": "The zipcode of the second city."}}, "required": ["cityA", "cityB"]}, "response": {"type": "dict", "properties": {"distance": {"type": "float", "description": "The distance between the two cities in km."}, "intermediaryCities": {"type": "array", "description": "[Optional] The list of intermediary cities between the two cities.", "items": {"type": "string"}}}}}
{"name": "estimate_drive_feasibility_by_mileage", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Estimates the milage of the vehicle given the distance needed to drive.", "parameters": {"type": "dict", "properties": {"distance": {"type": "float", "description": "The distance to travel in miles."}}, "required": ["distance"]}, "response": {"type": "dict", "properties": {"canDrive": {"type": "boolean", "description": "True if the vehicle can drive the distance, False otherwise."}}}}
{"name": "fillFuelTank", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Fills the fuel tank of the vehicle. The fuel tank can hold up to 50 gallons.", "parameters": {"type": "dict", "properties": {"fuelAmount": {"type": "float", "description": "The amount of fuel to fill in gallons; this is the additional fuel to add to the tank."}}, "required": ["fuelAmount"]}, "response": {"type": "dict", "properties": {"fuelLevel": {"type": "float", "description": "The fuel level of the vehicle in gallons."}}}}
{"name": "find_nearest_tire_shop", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Finds the nearest tire shop.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"shopLocation": {"type": "string", "description": "The location of the nearest tire shop."}}}}
{"name": "gallon_to_liter", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Converts the gallon to liter.", "parameters": {"type": "dict", "properties": {"gallon": {"type": "float", "description": "The amount of gallon to convert."}}, "required": ["gallon"]}, "response": {"type": "dict", "properties": {"liter": {"type": "float", "description": "The amount of liter converted."}}}}
{"name": "get_current_speed", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Gets the current speed of the vehicle.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"currentSpeed": {"type": "float", "description": "The current speed of the vehicle in km/h."}}}}
{"name": "get_outside_temperature_from_google", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Gets the outside temperature.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"outsideTemperature": {"type": "float", "description": "The outside temperature in degree Celsius."}}}}
{"name": "get_outside_temperature_from_weather_com", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Gets the outside temperature.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"outsideTemperature": {"type": "float", "description": "The outside temperature in degree Celsius."}}}}
{"name": "get_zipcode_based_on_city", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Gets the zipcode based on the city.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The name of the city."}}, "required": ["city"]}, "response": {"type": "dict", "properties": {"zipcode": {"type": "string", "description": "The zipcode of the city."}}}}
{"name": "liter_to_gallon", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Converts the liter to gallon.", "parameters": {"type": "dict", "properties": {"liter": {"type": "float", "description": "The amount of liter to convert."}}, "required": ["liter"]}, "response": {"type": "dict", "properties": {"gallon": {"type": "float", "description": "The amount of gallon converted."}}}}
{"name": "lockDoors", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Locks the doors of the vehicle.", "parameters": {"type": "dict", "properties": {"unlock": {"type": "boolean", "description": "True if the doors are to be unlocked, False otherwise."}, "door": {"type": "array", "items": {"type": "string"}, "description": "The list of doors to lock or unlock. [Enum]: [\"driver\", \"passenger\", \"rear_left\", \"rear_right\"]"}}, "required": ["unlock", "door"]}, "response": {"type": "dict", "properties": {"lockStatus": {"type": "string", "description": "The status of the lock. [Enum]: [\"locked\", \"unlocked\"]"}, "remainingUnlockedDoors": {"type": "integer", "description": "The number of remaining unlocked doors."}}}}
{"name": "pressBrakePedal", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Presses the brake pedal based on pedal position. The brake pedal will be kept pressed until released.", "parameters": {"type": "dict", "properties": {"pedalPosition": {"type": "float", "description": "Position of the brake pedal, between 0 (not pressed) and 1 (fully pressed)."}}, "required": ["pedalPosition"]}, "response": {"type": "dict", "properties": {"brakePedalStatus": {"type": "string", "description": "The status of the brake pedal. [Enum]: [\"pressed\", \"released\"]"}, "brakePedalForce": {"type": "float", "description": "The force applied to the brake pedal in Newtons."}}}}
{"name": "releaseBrakePedal", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Releases the brake pedal of the vehicle.", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"brakePedalStatus": {"type": "string", "description": "The status of the brake pedal. [Enum]: [\"pressed\", \"released\"]"}, "brakePedalForce": {"type": "float", "description": "The force applied to the brake pedal in Newtons."}}}}
{"name": "setCruiseControl", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Sets the cruise control of the vehicle.", "parameters": {"type": "dict", "properties": {"speed": {"type": "float", "description": "The speed to set in m/h. The speed should be between 0 and 120 and a multiple of 5."}, "activate": {"type": "boolean", "description": "True to activate the cruise control, False to deactivate."}, "distanceToNextVehicle": {"type": "float", "description": "The distance to the next vehicle in meters."}}, "required": ["speed", "activate", "distanceToNextVehicle"]}, "response": {"type": "dict", "properties": {"cruiseStatus": {"type": "string", "description": "The status of the cruise control. [Enum]: [\"active\", \"inactive\"]"}, "currentSpeed": {"type": "float", "description": "The current speed of the vehicle in km/h."}, "distanceToNextVehicle": {"type": "float", "description": "The distance to the next vehicle in meters."}}}}
{"name": "setHeadlights", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Sets the headlights of the vehicle.", "parameters": {"type": "dict", "properties": {"mode": {"type": "string", "description": "The mode of the headlights. [Enum]: [\"on\", \"off\", \"auto\"]"}}, "required": ["mode"]}, "response": {"type": "dict", "properties": {"headlightStatus": {"type": "string", "description": "The status of the headlights. [Enum]: [\"on\", \"off\"]"}}}}
{"name": "set_navigation", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Navigates to the destination.", "parameters": {"type": "dict", "properties": {"destination": {"type": "string", "description": "The destination to navigate in the format of street, city, state."}}, "required": ["destination"]}, "response": {"type": "dict", "properties": {"status": {"type": "string", "description": "The status of the navigation."}}}}
{"name": "startEngine", "description": "This tool belongs to the vehicle control system, which allows users to control various aspects of the car such as engine, doors, climate control, lights, and more. Tool description: Starts the engine of the vehicle.", "parameters": {"type": "dict", "properties": {"ignitionMode": {"type": "string", "description": "The ignition mode of the vehicle. [Enum]: [\"START\", \"STOP\"]"}}, "required": ["ignitionMode"]}, "response": {"type": "dict", "properties": {"engineState": {"type": "string", "description": "The state of the engine. [Enum]: [\"running\", \"stopped\"]"}, "fuelLevel": {"type": "float", "description": "The fuel level of the vehicle in gallons."}, "batteryVoltage": {"type": "float", "description": "The battery voltage of the vehicle in volts."}}}}
