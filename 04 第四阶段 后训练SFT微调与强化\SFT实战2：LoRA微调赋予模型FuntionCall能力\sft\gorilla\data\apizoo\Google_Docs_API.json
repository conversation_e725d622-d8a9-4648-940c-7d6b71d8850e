[{"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Docs API - documents.batchUpdate", "api_call": "service.documents().batchUpdate(documentId: str).execute()", "api_version": "v1", "api_arguments": {"documentId": "[REQUIRED] string: The ID of the document to update."}, "functionality": "Applies one or more updates to the document. Each request is validated before being applied. If any request is not valid, then the entire request will fail and nothing will be applied. Some requests have replies to give you some information about how they are applied. Other requests do not need to return information; these each return an empty reply. The order of replies matches that of the requests. For example, suppose you call batchUpdate with four updates, and only the third one returns information. The response would have two empty replies, the reply to the third request, and another empty reply, in that order. Because other users may be editing the document, the document might not exactly reflect your changes: your changes may be altered with respect to collaborator changes. If there are no collaborators, the document should reflect your changes. In any case, the updates in your request are guaranteed to be applied together atomically.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/docs/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Docs API - documents.create", "api_call": "service.documents().create().execute()", "api_version": "v1", "api_arguments": {}, "functionality": "Creates a blank document using the title given in the request. Other fields in the request, including any provided content, are ignored. Returns the created document.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/docs/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Docs API - documents.get", "api_call": "service.documents().get(documentId: str).execute()", "api_version": "v1", "api_arguments": {"documentId": "[REQUIRED] string: The ID of the document to retrieve.", "suggestionsViewMode": "string: The suggestions view mode to apply to the document. This allows viewing the document with all suggestions inline, accepted or rejected. If one is not specified, DEFAULT_FOR_CURRENT_ACCESS is used."}, "functionality": "Gets the latest version of the specified document.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/docs/api/reference/rest"}}]