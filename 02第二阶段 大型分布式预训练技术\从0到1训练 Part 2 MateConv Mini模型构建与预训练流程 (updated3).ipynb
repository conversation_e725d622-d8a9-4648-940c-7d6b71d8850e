{"cells": [{"cell_type": "markdown", "id": "121a0915-a78a-4921-87d0-0dc4fad7ce23", "metadata": {}, "source": ["# 从0到1训练大模型\n", "# Part 2 MateConv Mini模型构建与预训练流程"]}, {"cell_type": "markdown", "id": "caf72327-062f-4184-a176-7977fe8bae9b", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/80.png)"]}, {"cell_type": "markdown", "id": "f258a56a-af89-45dd-8ccc-2dd763a9cc62", "metadata": {}, "source": ["- **大模型训练流程概览**"]}, {"cell_type": "markdown", "id": "75d93552-3e40-4bcb-9b64-7ec14eebac89", "metadata": {}, "source": ["从 **0到1** 训练大模型是一个复杂而系统的工程，需要涵盖从数据准备到模型部署的多个环节。以下是一个完整的流程框架："]}, {"cell_type": "markdown", "id": "157a0895-5521-45bf-9e5c-2a07132350ab", "metadata": {}, "source": ["| 流程            | 说明                                                                                           |\n", "|:---------------:|--|\n", "| **数据准备**        | 收集高质量、覆盖面广的训练数据，对其进行清洗、去噪和格式化处理。划分<br>训练集、验证集，并存储为高效读取的格式。这一步为模型提供了扎实的输入基础。 |\n", "| **硬件与环境配置**  | 为模型训练准备高性能硬件（如 A800、A100 GPU），搭建分布式训练环境，<br>并优化深度学习框架的配置。这一步确保训练效率和稳定性。 |\n", "| **分词器训练**      | 根据训练数据量和模型任务需求，选择适合的分词算法（如 BPE 或 <br>SentencePiece）。分词器决定了模型如何理解数据，是数据与模型的桥梁。 |\n", "| **设计模型架构**    | 选择适合的模型结构（如 GPT、BERT），并配置参数量、层数、激活函数<br>等细节。对于大规模任务，可以结合领域特点定制模型。 |\n", "| **预训练**        | 使用无监督任务从海量数据中提取通用知识，比如语言模型的自回归建模或<br>掩码建模。预训练的效果直接影响模型后续的微调能力。 |\n", "| **意图对齐微调**        | 通过监督微调（SFT）或强化学习对齐（RLHF），让模型学习人类偏好，<br>避免输出无意义或有害内容。对齐步骤是模型实用化的关键。 |\n", "| **特定优化微调**  | 在特定任务（如文本分类、问答）上微调模型，结合冻结与解冻层的策略进一步<br>优化性能，满足应用需求。                          |\n", "| **模型量化**       | 通过剪枝、量化和知识蒸馏等技术优化模型，提高推理效率，降低计算与存储<br>成本，使模型更适合部署环境。                            |\n", "| **部署与监控**      | 将模型部署到生产环境中，使用推理优化工具提升服务效率，同时通过实时<br>监控与用户反馈不断改进模型性能和可靠性。                     |"]}, {"attachments": {}, "cell_type": "markdown", "id": "012de218-ee13-4481-9e2e-72fb9c7f58f2", "metadata": {}, "source": ["**在课程中我们呈现的环节有——**\n", "\n", "| 流程            | MateConv Mini<br> (0.02B) | MateConv <br> (1B) | 单独的案例 | 在这节课之前里应该掌握 |\n", "|:---------------:|:---------------------:|:-------------:|:-:|:-:|\n", "| **数据准备**<br>（数据收集、数据清洗、数据预处理、<br>数据分词、数据编码、掩码构建……）| ✅ | ✅ | ✅ |✅大部分已完成<br>剩下的在<font color=\"red\">**当前课程**|\n", "| **硬件与环境配置**<br><br> （硬件配置、环境配置、代理设置）  | ✅                     | ✅             | ✅|✅ 已完成 |\n", "| **分词器训练**      | ✅  | --              |--|✅ 已完成|\n", "| **设计模型架构**    | ✅  | ✅     |✅| ✅大部分已完成<br>剩下的在<font color=\"red\">**当前课程** |\n", "| **预训练**<br>（预训练基础理论、配置文件构建、<br>各类优化方法、分布式配置、<br>模型监控与保存配置……）          | ✅                     | ✅             |--| <font color=\"red\">**当前课程** |\n", "| **意图对齐微调**        | ✅                     | ✅             |--|--|\n", "| **特定优化微调**  | --                     | --             |✅|--|\n", "| **模型量化**       | --                   | --             |✅|--|\n", "| **部署与监控**     | ✅                     | --             |--|--|"]}, {"cell_type": "markdown", "id": "5c218f47-67f2-4fac-af64-6f207032efc6", "metadata": {}, "source": ["在进行课程讲解时，每个环节我们将采用【技术概览 - MateConv Mini代码实现 - MateConv代码实现 - 深度原理或补充】的4段式结构进行讲解、你可以挑选自己最感兴趣的部分进行学习。"]}, {"cell_type": "markdown", "id": "cc5bafd7-8e14-423b-9503-d593009c93aa", "metadata": {}, "source": ["## 1 MateConv Mini的模型构建"]}, {"cell_type": "markdown", "id": "3b481895-3be3-45f1-b18a-d321136cbe52", "metadata": {}, "source": ["MateConv Mini是基于LlaMA3.1 + DeepSeek构建的文字生成式模型，在课程中的《LlaMA架构原理精讲与复现》板块我们已详细解读过这个架构，核心结构如下 ↓ "]}, {"cell_type": "markdown", "id": "4a42190e-7b6a-4c63-83a5-8d6e3a04d53f", "metadata": {}, "source": ["<center><img src=\"https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/78.png\" alt=\"描述文字\" width=\"600\">"]}, {"cell_type": "markdown", "id": "1b7069f3-0235-4fff-bec5-cd0da4ed50d7", "metadata": {"jp-MarkdownHeadingCollapsed": true}, "source": ["**MateConv Mini** 的模型架构整体设计融合了 **LLaMA-like** 和 **DeepSeek-like** 的特点，该架构从 **输入嵌入层（Embeddings）** 开始，通过多层堆叠的 Transformer 模块完成特征建模，最终通过 Softmax 输出预测结果。其中、每个 Transformer 模块由 **多查询注意力（Grouped Multi-Query Attention with KV Cache）**、**Feed Forward 模块（或 MoE）** 以及归一化和残差连接组成。\n", "\n", "- **1. Input & Embeddings**：将离散的输入序列（如单词、字符或 Token）转换为连续向量表示，提供初始语义信息。作为整个模型的输入，提供每个 Token 的嵌入表示，用于后续的注意力和前馈处理。\n", "\n", "- **2. RMS Norm（RMS归一化）**：对输入数据进行均方根归一化，稳定模型的训练，并加速收敛。RMS Norm 相比 Layer Norm 更高效，因为它只需计算每个样本的均方根值，而无需计算均值。\n", "\n", "- **3. Grouped Multi-Query Attention with KV Cache**：实现自注意力机制，通过计算 Query、Key 和 Value 的相关性来捕获序列的上下文依赖。然而，我们使用的是KV共享的注意力机制、同时在推理阶段缓存 Key 和 Value，避免重复计算历史上下文，这能显著提高生成效率并且降低模型参数量。除此之外，我们还在 Query 和 Key 上引入旋转位置编码，增强模型对位置关系的建模能力，特别适用于长序列。\n", "\n", "- **4. Feed Forward（SwiGLU）与 MoE（Experts）**：对每个 Token 的表示单独进行非线性变换，增强模型的特征提取能力。其中FFN层使用门控激活单元，通过一组门控机制控制信息流动，能够捕获更复杂的特征，而MOE层则在多个专家模块中选择性激活一个或多个（通常互斥，即“Mutually Exclusive”），使模型在不同任务上具有更高的灵活性。\n", "\n", "- **5. Residual Connection（残差连接）**：将输入直接添加到子模块的输出上，缓解梯度消失问题、使模型能够在学习额外特征时保留输入信息，从而更有效地学习深层次的表示。\n", "\n", "- **6. Linear & Softmax**：线性层将 Transformer 模块的输出映射到词汇表大小，用于生成词分布。Softmax 将词分布转换为概率分布，完成预测。"]}, {"cell_type": "markdown", "id": "d9b90121-9d6e-4995-91a5-9490727fb014", "metadata": {}, "source": ["结合之前在讲解LlaMA时构建的代码、我们将该架构的模型脚本放在了model.py文件中 ↓"]}, {"cell_type": "markdown", "id": "7bf6473e-6607-4624-a9fa-7078ea2db4de", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/76.png)"]}, {"cell_type": "markdown", "id": "791f8e28-243a-4421-a13b-3b62e9c590e0", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/77.png)"]}, {"cell_type": "markdown", "id": "30e6c595-43bc-466d-9956-28a7700626c1", "metadata": {}, "source": ["| **代码与构建思路差异**            | **本次预训练中使用的model.py脚本**                                                                                       | **LLaMA课程中构建的LLaMA.py脚本**                                                                                          |\n", "|--------------------------|-------------------------------------------------------------------------------------------------|----------------------------------------------------------------------------------------------------|\n", "|**MOE架构**|实现了MOE模型、以及共享专家机制|实现了MOE模型、以及共享专家机制|\n", "| **KV 缓存更新**          |直接针对推理模式下 `seqlen == 1`的情况进行了优化（拼接新 Token 到缓存）、适用于自回归中只输出一个token的情况（适用于大部分情况）。                                     |通用实现、在自回归流程中无论一次输出几个token都可以实现，按照首次调用（缓存为空）和非首次调用（缓存更新）的逻辑对缓存进行选择性更新。                                       |\n", "| **掩码处理**            |使用固定掩码矩阵 `self.mask`，假设序列长度不会变化。                                            |动态扩展掩码矩阵，适配缓存增长时的长度需求。                                                      |\n", "| **旋转位置编码**        |在扩展 KV 之前执行旋转位置编码、高效简单避免重复计算                                                                |在扩展 KV 之后执行旋转位置编码，适配不同长度的张量输入、但计算逻辑更为复杂                                                      |\n", "| **注意力计算**          | - 支持 Flash Attention，未支持时手动加固定掩码矩阵计算注意力。                                      |优先使用 Flash Attention，支持动态扩展掩码以适配不同序列长度。                                      |\n", " **动态适配性**          |假设固定掩码与序列长度，扩展性有限。                                                            |动态掩码扩展和缓存机制，适配性更强，支持任意序列增长。                                             |\n", "| **适配性场景**          |偏向推理中已知固定长度的场景、<font color=\"red\">**整体效率更高**。                                                                  |适配更复杂的推理场景，支持动态长度序列处理、<font color=\"red\">**拓展性和通用性更强**。                                                      |"]}, {"cell_type": "markdown", "id": "fe1fe719-7f46-4a3e-89f6-84126045ab39", "metadata": {}, "source": ["同时，我们为这个模型构建了如下的参数设置 ↓ 该设置在LMConfig.py脚本中 ↓"]}, {"cell_type": "markdown", "id": "84608f12-bc91-422e-926f-5e31f914206b", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/79.png)"]}, {"cell_type": "markdown", "id": "10093ee4-3a04-4ccc-a5ec-2a7f0e51f693", "metadata": {}, "source": ["```python\n", "from transformers import PretrainedConfig\n", "\n", "\n", "class LMConfig(PretrainedConfig):\n", "    model_type = \"MateConv Mini\"\n", "\n", "    def __init__(\n", "            self,\n", "            dim: int = 512,  # 模型隐藏层维度\n", "            n_layers: int = 8,  # Transformer 堆叠的层数\n", "            n_heads: int = 16,  # 注意力头的数量\n", "            n_kv_heads: int = 8,  # KV（Key 和 Value）共享头的数量\n", "            vocab_size: int = 6400,  # 词汇表的大小\n", "            hidden_dim: int = None,  # 前馈网络中隐藏层的维度，默认为 None 时将使用 dim 的倍数\n", "            multiple_of: int = 64,  # 前馈网络中隐藏层维度需要是该值的整数倍\n", "            norm_eps: float = 1e-5,  # LayerNorm 或 RMSNorm 的 epsilon 参数，用于数值稳定性\n", "            max_seq_len: int = 512,  # 最大序列长度\n", "            dropout: float = 0.0,  # Dropout 的比例\n", "            flash_attn: bool = True,  # 是否使用 Flash Attention（更高效的注意力实现）\n", "            ####################################################\n", "            # 下面是关于 MOE（Mixture of Experts，专家网络）的特定配置\n", "            # 当 use_moe 为 False 时，以下配置无效\n", "            ####################################################\n", "            use_moe: bool = False,  # 是否使用专家网络（MOE）\n", "            num_experts_per_tok=2,  # 每个 Token 被分配的专家数量\n", "            n_routed_experts=4,  # MOE 模型中的总专家数量\n", "            n_shared_experts: bool = True,  # 是否启用共享专家（共享权重）\n", "            scoring_func='softmax',  # 专家选择的评分函数，默认为 'softmax'\n", "            aux_loss_alpha=0.01,  # 辅助损失的权重系数，用于保持专家负载平衡\n", "            seq_aux=True,  # 是否在序列级别上计算辅助损失\n", "            norm_topk_prob=True,  # 是否对 Top-K 专家选择的概率进行归一化\n", "            **kwargs,\n", "    ):\n", "        # 模型的主要参数\n", "        self.dim = dim\n", "        self.n_layers = n_layers\n", "        self.n_heads = n_heads\n", "        self.n_kv_heads = n_kv_heads\n", "        self.vocab_size = vocab_size\n", "        self.hidden_dim = hidden_dim\n", "        self.multiple_of = multiple_of\n", "        self.norm_eps = norm_eps\n", "        self.max_seq_len = max_seq_len\n", "        self.dropout = dropout\n", "        self.flash_attn = flash_attn\n", "        ####################################################\n", "        # MOE（专家网络）的相关配置\n", "        ####################################################\n", "        self.use_moe = use_moe  # 是否启用专家网络\n", "        self.num_experts_per_tok = num_experts_per_tok  # 每个 Token 被分配的专家数量\n", "        self.n_routed_experts = n_routed_experts  # 总的专家数量\n", "        self.n_shared_experts = n_shared_experts  # 是否共享专家\n", "        self.scoring_func = scoring_func  # 专家选择的评分函数\n", "        self.aux_loss_alpha = aux_loss_alpha  # 辅助损失的权重系数\n", "        self.seq_aux = seq_aux  # 是否在序列级别上计算辅助损失\n", "        self.norm_topk_prob = norm_topk_prob  # 是否对 Top-K 专家选择概率进行归一化\n", "        super().__init__(**kwargs)\n", "```"]}, {"cell_type": "markdown", "id": "b7ae7fd0-9321-41c8-9d58-b1a6633fce7c", "metadata": {}, "source": ["- **模型参数量的计算**"]}, {"cell_type": "markdown", "id": "ef56fcc8-ffbb-422b-870a-bf7905ea1859", "metadata": {}, "source": ["在这些参数中、以下这些参数会直接影响模型的参数规模——\n", "\n", "| **参数名称**        | **说明**                                                                                      |\n", "|---------------------|-----------------------------------------------------------------------------------------------|\n", "| **`dim`**           | 模型隐藏层的维度，影响各层的权重矩阵大小。                                                     |\n", "| **`n_layers`**      | Transformer 堆叠的层数，每层的参数规模会累加。                                                 |\n", "| **`n_heads`**       | 注意力头的数量，影响 Query、Key、Value 以及输出的维度分配。                                     |\n", "| **`n_kv_heads`**    | KV（Key 和 Value）的共享头数，影响 Key 和 Value 的参数规模。                                     |\n", "| **`vocab_size`**    | 词汇表大小，影响嵌入层和输出层的参数规模。                                                     |\n", "| **`hidden_dim`**    | 前馈网络中隐藏层的维度，直接影响前馈层的参数规模。若未设置，则为 `dim * multiple_of`。          |\n", "| **`use_moe`**       | 是否启用专家网络（MOE）。                                                                      |\n", "| **`n_routed_experts`** | 专家网络中路由的总专家数量，影响 MOE 模块的参数规模。                                         |"]}, {"cell_type": "markdown", "id": "87e2bef3-9f1d-47ec-9caf-d0a81fbcde57", "metadata": {}, "source": ["基于这些关键参数、当前架构的总参数量可以分为以下几部分计算：\n", "\n", "**1. 嵌入层参数量**"]}, {"cell_type": "markdown", "id": "8f33699a-9004-482a-9b80-73bd7a3d3b17", "metadata": {}, "source": ["> - **公式**：`vocab_size * dim`\n", ">   \n", "> - 嵌入层的参数来自词汇表大小和嵌入维度。\n", ">\n", "> -    $$\n", "   \\text{Embedding Params} = 6400 \\cdot 512 = 3,276,800\n", "   $$"]}, {"cell_type": "markdown", "id": "6274b123-235f-4603-a58f-c22896074365", "metadata": {}, "source": ["**2. 每层自注意力机制参数量**"]}, {"cell_type": "markdown", "id": "c5876b7c-e76e-4bd1-810e-2ed4ddd5e767", "metadata": {}, "source": ["> - 注意力机制的参数分为 Query、Key、Value 和输出权重（`wo`），但是由于我们使用了**kv共享**机制，且借用了参数`n_kv_heads`作为共享的基础、因此注意力机制的参数量实际计算如下——\n", "> 1. **Query 权重矩阵**：\n", ">    - Query 是全头独立计算，因此：\n", ">      $$\n", "     \\text{Query Params} = dim \\cdot dim = 512 \\cdot 512 = 262,144\n", "     $$\n", "> \n", "> 2. **Key 权重矩阵**（共享头）：\n", ">    - Key 的参数量基于 `n_kv_heads` 和切分后的维度：\n", ">      $$\n", "     \\text{Key Params} = n\\_kv\\_heads \\cdot head\\_dim \\cdot dim = 8 \\cdot 32 \\cdot 512 = 131,072\n", "     $$\n", "> \n", "> 3. **Value 权重矩阵**（共享头）：\n", ">    - Value 的参数量与 Key 相同：\n", ">      $$\n", "     \\text{Value Params} = 131,072\n", "     $$\n", "> \n", "> 4. **Output 权重矩阵**：\n", ">    - 输出层需要将 `n_heads` 的多头输出合并为原始维度：\n", ">      $$\n", "     \\text{Output Params} = dim \\cdot dim = 512 \\cdot 512 = 262,144\n", "     $$\n", "> \n", "> **合计注意力机制参数量**：\n", "> - Attention Params = Q Params+K Params+V Params+O Param\n", "> $$ 262,144 + 131,072 + 131,072 + 262,144 = 786,432 $$"]}, {"cell_type": "markdown", "id": "6e43e257-50cf-4bc6-aa6b-802decddb30b", "metadata": {}, "source": ["**3.1 每层前馈网络参数量**"]}, {"cell_type": "markdown", "id": "88e5fddf-1fa1-4c13-b63c-41ee1f767b43", "metadata": {}, "source": ["> - **公式**：\n", ">  $$\n", "  \\text{FFN Params} = 2 \\cdot (dim \\cdot hidden\\_dim) + hidden\\_dim \\cdot dim\n", "  $$\n", "> - `2 * (dim * hidden_dim)`：前馈网络中两层全连接权重。\n", "> - `hidden_dim * dim`：前馈层输出投影权重。\n", "> $$ \\text{FFN Params} = 3 \\cdot (dim \\times hidden\\_dim) = 3 \\cdot (512 \\times 2048) = 3,145,728 $$"]}, {"cell_type": "markdown", "id": "f32c8ea3-e8f1-49dc-9998-79f889189bd9", "metadata": {}, "source": ["**3.2 如果启用MOE网络架构**"]}, {"cell_type": "markdown", "id": "7723f72e-dd16-4e5b-aab0-df77ab994035", "metadata": {}, "source": ["> 在MateConv mini架构中、我们MOE的结构由专家、路由、以及共享专家三部分组成——\n", ">\n", ">1. **专家网络的参数量（Experts Params）**：每个专家是一个 `FeedForward` 层，总共有 `n_routed_experts` 个专家。\n", ">\n", "> **单个专家参数量**：来自 `FeedForward` 的参数：\n", ">     $$\n", "     \\text{Single Expert Params} = 3 \\cdot (dim \\cdot hidden\\_dim)\n", "     $$\n", "> - `w1`: $ dim \\rightarrow hidden\\_dim $\n", "> - `w2`: $ hidden\\_dim \\rightarrow dim $\n", "> - `w3`: $ dim \\rightarrow hidden\\_dim $\n", ">\n", ">  **所有专家的参数量**：\n", ">   $$\n", "   \\text{Experts Params} = n\\_routed\\_experts \\cdot \\text{Single Expert Params}\n", "   $$\n", ">\n", "> $$\n", "   \\text{Experts Params} = n\\_routed\\_experts \\cdot 3 \\cdot (dim \\cdot hidden\\_dim)\n", "   $$\n", ">   $$\n", "   \\text{Experts Params} = 4 \\cdot 3 \\cdot (512 \\cdot 2048)\n", "   $$\n", ">   $$\n", "   \\text{Experts Params} = 4 \\cdot 3 \\cdot 1,048,576 = 12,582,912\n", "   $$\n", ">\n", "> 2. **门控网络的参数量（Gating Params）**\n", ">   - 在 `MoEGate` 中，门控网络使用一个权重矩阵 `self.weight`：\n", ">     $$\n", "     \\text{Gating Params} = dim \\cdot n\\_routed\\_experts\n", "     $$\n", ">\n", ">    $$\n", "   \\text{Gating Params} = dim \\cdot n\\_routed\\_experts\n", "   $$\n", ">   $$\n", "   \\text{Gating Params} = 512 \\cdot 4 = 2,048\n", "   $$\n", "> \n", "> 3. **共享专家的参数量（Shared Expert Params，若启用）**\n", ">   - 如果配置了共享专家（`n_shared_experts is not None`），还需要计算共享专家的参数。\n", ">   - 共享专家是一个额外的 `FeedForward` 层，其参数量与单个专家相同：\n", ">     $$\n", "     \\text{Shared Expert Params} = 3 \\cdot (dim \\cdot hidden\\_dim)\n", "     $$\n", ">\n", ">   $$\n", "   \\text{Shared Expert Params} = 3 \\cdot (dim \\cdot hidden\\_dim)\n", "   $$\n", ">   $$\n", "   \\text{Shared Expert Params} = 3 \\cdot (512 \\cdot 2048)\n", "   $$\n", ">   $$\n", "   \\text{Shared Expert Params} = 3 \\cdot 1,048,576 = 3,145,728\n", "   $$\n", "> - 结合以上部分，MOE 的总参数量为：\n", ">   $$\n", "   \\text{MoE Params} = \\text{Experts Params} + \\text{Gating Params} + \\text{Shared Expert Params}\n", "   $$\n", ">   $$\n", "   \\text{<PERSON><PERSON>} = 12,582,912 + 2,048 + 3,145,728\n", "   $$\n", ">   $$\n", "   \\text{<PERSON><PERSON>} = 15,730,688\n", "   $$"]}, {"cell_type": "markdown", "id": "e6786f0d-1b54-4506-8c83-098fcd00b16b", "metadata": {}, "source": ["4. **输出层参数量**"]}, {"cell_type": "markdown", "id": "f7ac5d6b-2824-4074-a64c-f6933bbeac8f", "metadata": {}, "source": ["> - **公式**：\n", ">  $$\n", "  \\text{Output Params} = vocab\\_size \\cdot dim\n", "  $$\n", ">  在本次架构中、我们使用了**Embedding 和 Output 层共享权重**的参数优化技巧，因此 Output 层就不需要单独计算参数量，只需计算一次 Embedding 参数量即可。"]}, {"cell_type": "markdown", "id": "009dba4d-95bc-41c5-917f-f8c38945f559", "metadata": {}, "source": ["5. **每层总参数量**"]}, {"cell_type": "markdown", "id": "828d4c8d-aa9a-45a9-83de-02b0280bc0b2", "metadata": {}, "source": ["> 每层参数量为自注意力机制和前馈网络的参数之和：\n", "> $$\n", "\\text{Layer Params} = \\text{Attention Params} + \\text{FFN Params}\n", "$$\n", "> $$\n", "\\text{Layer Params} = 786,432 + 2,097,152 = 2,883,584\n", "$$"]}, {"cell_type": "markdown", "id": "9ce44d56-524d-43b9-9678-0944e77a5c27", "metadata": {}, "source": ["6. **所有层参数量**"]}, {"cell_type": "markdown", "id": "d2d63998-dcb9-4f76-8736-9a072ec5b6e1", "metadata": {}, "source": ["> Transformer 层数为 `n_layers = 8`：\n", "> $$\n", "\\text{Total Layer Params} = n\\_layers \\cdot \\text{Layer Params} = 8 \\cdot 2,883,584 = 23,068,672\n", "$$"]}, {"cell_type": "markdown", "id": "2152bc9c-f6e4-4ef3-90b2-a5e5ec361d3f", "metadata": {}, "source": ["7. **总参数量**"]}, {"cell_type": "markdown", "id": "d330d9bb-5820-4c00-835e-ba97c28a793a", "metadata": {}, "source": ["> 总参数量包含嵌入层和所有 Transformer 层参数：\n", "> $$\n", "\\text{Total Params} = \\text{Embedding Params} + \\text{Total Layer Params}\n", "$$\n", "> $$\n", "\\text{Total Params} = 3,276,800 + 23,068,672 = 26,345,472\n", "$$\n", "> 即26.3MB（两千六百万参数）、即0.02B模型。"]}, {"cell_type": "markdown", "id": "113cf785-a2af-4e14-82cb-3358f6510db1", "metadata": {}, "source": ["8. **如果启用MOE层**"]}, {"cell_type": "markdown", "id": "770c84eb-f73a-4bc9-afb5-acbc9b96fb5c", "metadata": {}, "source": ["> 如果启用MOE层、那每层参数量为自注意力机制和MOE参数之和：\n", "> $$\n", "\\text{Layer Params} = \\text{Attention Params} + \\text{MOE Params}\n", "$$\n", "> $$\n", "\\text{Layer Params} = 786,432 + 15,730,688 = 16,517,120\n", "$$\n", "> 在总层数`n_layers = 8`的情况下，所有层总参数量为：\n", "> $$\\text{Total Layer Params} = n\\_layers \\cdot \\text{Layer Params} = 8 \\cdot 16,517,120 = 132,136,960\n", "$$\n", "> 整体架构的总参数量包含嵌入层和所有 Transformer 层参数：\n", "> $$\n", "\\text{Total Params} = \\text{Embedding Params} + \\text{Total Layer Params}\n", "$$\n", "> $$\n", "\\text{Total Params} = 3,276,800 + 132,136,960 = 135,413,760\n", "$$\n", "> 即135.4MB（一亿三千五百万参数）、即0.1B模型。"]}, {"cell_type": "markdown", "id": "392803ca-b67e-4fe4-a460-ee2af7f251ff", "metadata": {}, "source": ["<center><img src=\"https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/78.png\" alt=\"描述文字\" width=\"600\">"]}, {"cell_type": "markdown", "id": "056214d2-ab6a-454d-831b-d3f5e9a3aac2", "metadata": {}, "source": ["## 2 MateConv Mini的预训练代码全解"]}, {"cell_type": "markdown", "id": "e8e292cd-e6af-46aa-9f63-f527dd2f4bd3", "metadata": {}, "source": ["正如之前所说，这样的架构所需的算力是——"]}, {"cell_type": "markdown", "id": "94037304-8b7b-433c-bd2e-1356b0e00fb3", "metadata": {}, "source": ["| 模型名称     | 模型参数量级      | 数据量级                | 硬件资源               | 训练时间              | 训练成本                |\n", "| ------------ | ----------------- | ----------------------- | ---------------------- | --------------------- | ----------------------- |\n", "| **MateConv mini** | 0.02B<br>（两千万参数） | 约60G（分词器约1G、预训练约35G、微调约20G） | RTX 3090 x2           | 全流程约3-4天          | AutoDL租赁，约250元     |\n", "| **MateConv**      | 1B<br>（10亿参数）      | 约3~4T文本+1T代码（实际收集超过10T数据、如果你们有更大的运存可以使用更大的数据集） | A800 x 8，硬盘4T      | 全流程约1个多月（预训练约20天） | AutoDL租赁2.5\\~3万元；购置设备需80\\~100万元，电费约3000r |"]}, {"cell_type": "markdown", "id": "1535b35b-98e5-4558-b87e-4109a6765d30", "metadata": {}, "source": ["预训练脚本 ↓"]}, {"cell_type": "markdown", "id": "3e89c650-28f5-450f-8adb-df0f7b99e8da", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/83.png)"]}, {"cell_type": "markdown", "id": "4203cfdf-01c7-434d-8507-bb638454d2db", "metadata": {}, "source": ["运行全流程的代码 ↓"]}, {"cell_type": "markdown", "id": "81b0e65b-4d7a-493e-949b-da91e771ddb6", "metadata": {}, "source": ["```shell\n", "cd ~/autodl-tmp/MateConv\n", "\n", "touch pretrain.py\n", "\n", "deepspeed --master_port 29500 --num_gpus=4 pretrain.py --epochs 30\n", "```"]}, {"cell_type": "markdown", "id": "6d2b8f75-fe65-427f-a92b-be4c09364643", "metadata": {}, "source": ["开始训练 ↓"]}, {"cell_type": "markdown", "id": "05580bb1-decc-433a-88d8-f00680bb936d", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/0d46acea891701fd573115782288e05.jpg\" alt=\"0d46acea891701fd573115782288e05\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "ddef31ef-0625-4651-bb19-a59fecb94488", "metadata": {}, "source": ["训练结束 ↓ 训练流程大约18个小时。"]}, {"cell_type": "markdown", "id": "6b0c9877-49d7-4a8a-89a7-c1dd11796924", "metadata": {}, "source": ["<center><img src=\"https://ml2022.oss-cn-hangzhou.aliyuncs.com/img/9f1b2321dc79550b1e0f194e165ff22.png\" alt=\"9f1b2321dc79550b1e0f194e165ff22\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "4752fd41-b452-412d-bd3c-74c3178a8302", "metadata": {}, "source": ["### 2.1 训练流程与训练脚本构建"]}, {"cell_type": "markdown", "id": "80c30e25-a449-4471-88e7-9255d7fd3590", "metadata": {}, "source": ["#### 2.1.1 深度学习训练的基本流程"]}, {"cell_type": "markdown", "id": "809f619f-9233-4908-bb42-69e940fcdbdc", "metadata": {}, "source": ["深度学习模型的训练流程本质上是一个优化的流程，其**目标是通过不断迭代模型参数$w$，使模型在给定数据上的预测尽可能接近真实值**，简单来说，模型是“从错误中学习、通过损失函数的指导、不断调整自身的参数让预测更加准确”。为了实现这个目标、我们通常需要掌握优化算法、迭代流程等具体技术细节（<font color=\"red\">**详细内容可参考《深度学习实战》课程**</font>）、但优化算法的内容本身繁多且难度较高、在这里我们跳开原始理论、以最快的速度、比较通俗的方式向大家展现深度学习的基本训练流程、为后续理解预训练的流程做好准备。\n", "\n", "---\n", "\n", "<font color=\"red\">**在定义好模型之后、深度学习训练的基本步骤与流程如下**——\n", "\n", "1. **数据分批/打乱/分训练集验证集等操作、定义使用的CPU/GPU等细节**\n", "\n", "2. **初始化模型参数（Parameter Initialization）**\n", "\n", "3. **训练循环（training loop）**：\n", "> **正向传播（Forward Pass）**：输入数据通过模型，得到预测输出。\n", "> \n", "> **计算损失（Compute Loss）**：根据模型的预测结果和真实标签，计算损失函数值。\n", "> \n", "> **反向传播（Backward Pass）**：根据损失函数的值，计算模型中各参数的梯度（使用自动微分）。\n", "> \n", "> **参数更新（Update Parameters）**：根据计算出的梯度，使用优化器更新模型的参数。\n", "> \n", "> **梯度清零（Zero Gradients）**：为了避免梯度累积，需要在每次迭代前将梯度归零。\n", "\n", "4. **训练监控与模型保存**\n", "\n", "---\n", "\n", "在实际训练时、我们可以自定义进行多少次循环、进行怎样的循环。同时，在深度学习训练过程中、你还需要掌握如下的概念——\n", "\n", "- **Epoch**：是**整个训练数据集被完整地传递一次通过模型**的过程，模型见过一次完整的数据、叫做执行了一个epoch。\n", "  \n", "- **Batch 与 Batch Size**：在深度学习中，由于计算资源的限制，训练时不会一次性将整个数据集输入模型，而是将数据分成多个小组，每个小组称为一个 Batch。模型会逐个处理这些 Batch，并在每个 Batch 上完成一次正向传播和反向传播。Batch Size 是指 每个 Batch 中包含的样本数量。\n", "  \n", "- **梯度（Gradients）**：是优化过程中计算出的方向信息，用于指导权重更新。从数学上来说，梯度是**损失函数相对于模型参数的导数构成的向量**，梯度决定了参数更新的方向、指向损失函数下降最快的方向、而梯度值的大小影响参数更新的幅度。\n", "\n", "- **优化算法（Optimizer）**：使用梯度来对参数进行迭代的实际算法、不同的优化算法有不同的迭代细节、但本质都是计算损失、反向传播求解梯度、进行参数更新的方式来迭代参数。常见的有Adam、SGD、RMSProp等等。\n", "\n", "- **学习率（Learning rate）**：是控制**每次权重更新步幅大小**的超参数，通常记为$\\eta$。学习率控制了优化步幅，影响训练稳定性和速度。\n", "\n", "- **提前停止（Early_stopping）**：在训练过程中监控模型在验证集上的性能，当性能不再提升时，提前结束训练，以避免模型在训练集上过度拟合。"]}, {"cell_type": "markdown", "id": "1666a9e9-f3c1-4dd9-9486-152b12b059d1", "metadata": {}, "source": ["下面是执行深度学习训练时、最简单的一套伪代码——"]}, {"cell_type": "markdown", "id": "bba8d1d0-77f9-43e8-a4bc-4ba5fbb9d020", "metadata": {}, "source": ["```python\n", "import torch\n", "from torch import nn, optim\n", "from torch.utils.data import DataLoader, Dataset\n", "\n", "# 假设我们有以下准备好的模块\n", "# - 自定义的模型：MyModel\n", "# - 数据集：MyDataset\n", "# - 损失函数：loss_fn\n", "# - 优化器：optimizer\n", "\n", "# 定义超参数\n", "num_epochs = 10        # 训练的轮数\n", "batch_size = 32        # 批次大小\n", "learning_rate = 0.001  # 学习率\n", "\n", "# 数据准备\n", "train_dataset = MyDataset(train=True)  # 定义训练数据集\n", "train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)  # 数据加载器\n", "\n", "# 模型初始化\n", "model = MyModel()  # 初始化模型\n", "model = model.to(\"cuda\" if torch.cuda.is_available() else \"cpu\")  # 将模型移至 GPU 或 CPU\n", "\n", "# 损失函数和优化器\n", "loss_fn = nn.CrossEntropyLoss()  # 假设是分类任务\n", "optimizer = optim.Adam(model.parameters(), lr=learning_rate)\n", "\n", "# 训练循环\n", "for epoch in range(num_epochs):\n", "    print(f\"Epoch {epoch + 1}/{num_epochs}\")\n", "    model.train()  # 进入训练模式\n", "\n", "    for batch_idx, (inputs, targets) in enumerate(train_loader):\n", "        # 将数据移动到设备\n", "        inputs, targets = inputs.to(\"cuda\"), targets.to(\"cuda\")\n", "\n", "        # 1. 正向传播\n", "        outputs = model(inputs)\n", "\n", "        # 2. 计算损失\n", "        loss = loss_fn(outputs, targets)\n", "\n", "        # 3. 梯度清零\n", "        optimizer.zero_grad()\n", "\n", "        # 4. 反向传播\n", "        loss.backward()\n", "\n", "        # 5. 更新参数\n", "        optimizer.step()\n", "\n", "        # 打印训练状态\n", "        if batch_idx % 10 == 0:  # 每 10 个批次打印一次\n", "            print(f\"Batch {batch_idx}, Loss: {loss.item():.4f}\")\n", "\n", "    # (可选) 每个 epoch 完成后，验证模型性能\n", "    print(f\"Epoch {epoch + 1} Completed!\")\n", "\n", "# (可选) 保存模型\n", "torch.save(model.state_dict(), \"model.pth\")\n", "print(\"Training Completed!\")"]}, {"cell_type": "markdown", "id": "a2febc46-7086-417f-90b8-f6b3e2045d6c", "metadata": {}, "source": ["现在你知道了，深度学习的训练流程通常包括从数据加载开始，将输入数据经过模型的正向传播得到预测结果，然后通过损失函数计算预测值与真实值之间的差异，再通过反向传播算法计算模型参数的梯度，利用优化器更新模型参数，最后清零梯度以准备下一轮迭代。\n", "\n", "**然而，在大模型训练中，这一流程被扩展为更复杂的分布式场景，涉及跨多机多卡的高效数据并行和模型并行**，使用更大的批次来稳定训练，同时引入梯度检查点、混合精度训练等优化技术以减少内存占用和计算开销。此外，大模型的训练还会包括预训练和微调两个阶段，分别用于学习通用特征和适应特定任务。通过这种分布式、高效的训练流程，大模型能够在大规模数据和复杂任务中表现出色。"]}, {"cell_type": "markdown", "id": "6065d320-11eb-4649-9a90-fabf3ba50a1d", "metadata": {}, "source": ["#### 2.1.2 从训练流程到完整预训练脚本"]}, {"cell_type": "markdown", "id": "caff7cb0-dd57-4b6f-872d-82b29dfdcd6f", "metadata": {}, "source": ["现在我们已经了解了深度学习模型训练的核心流程，然而**在核心训练流程之外、我们还要设置大量机制来辅助训练流程**，包括但不限于——"]}, {"cell_type": "markdown", "id": "529dcf05-0358-49f5-8d38-4b1487d27742", "metadata": {}, "source": ["**数据加载**：在训练深度学习模型之前，需要高效地加载和预处理数据。这包括数据的读取、预处理（如归一化、数据增强）、批量生成以及多线程数据加载优化等，以提高训练效率。在本次MateConv mini预训练流程中，我们定义了Dataset.py作为最终的数据处理脚本，并且我们会在最终的训练脚本中引用到它。\n", "\n", "**模型断点续训机制**：断点续训机制的意义在于保证深度学习模型在长时间训练过程中，即使因系统崩溃、设备重启或意外中断，也能从最近一次保存的状态继续训练，而无需从头开始。对于计算资源昂贵的深度学习任务，特别是大规模语言模型（LLM）或计算密集型任务，如计算机视觉和强化学习，断点续训可以有效减少训练时间和成本，提高训练的可靠性。\n", "\n", "**分布式训练设置与分布式相关的命令行参数设置**：分布式训练可以加速大规模模型的训练过程，通常采用数据并行或模型并行的方法。主流框架如 PyTorch 和 TensorFlow 提供了分布式训练支持，同时deepspeed和megatron等框架也支持更大规模、更灵活的分布式预训练构建、这样可以提高计算资源利用率、加速模型的训练。  \n", "\n", "**动态学习率调整**：学习率是深度学习训练中最核心的参数没有之一。动态调整学习率（如学习率衰减、Warmup、Cosine Annealing）在于可以提高神经网络训练的稳定性和收敛效率，使模型能够更快达到最优解，同时避免训练过程中的震荡或过早陷入局部最优。固定学习率在训练的不同阶段可能无法适应梯度变化，初始学习率过大会导致训练不稳定，甚至梯度爆炸，而过小的学习率可能导致收敛速度缓慢甚至陷入平稳区间。通过采用动态调整策略，例如学习率衰减（如 Step Decay、Exponential Decay）、Warmup 预热、Cosine Annealing 或自适应优化算法（如 Adam、SGD with momentum），可以在训练初期使用较大学习率加快收敛，在训练后期逐步减小学习率，以精细调整权重，从而提高泛化能力，减少损失震荡，并提升最终模型的性能和稳定性。\n", "\n", "**梯度优化机制**：神经网络训练讲解“稳”字决、在训练流程中我们会有许多技巧用于提升模型的稳定性（即模型的梯度稳定性）。梯度优化机制在深度学习训练中至关重要，它们旨在提高训练的稳定性、优化显存使用，并防止梯度异常波动对模型收敛造成影响。例如，梯度累积（Gradient Accumulation）是一种在显存受限的情况下模拟大 batch 训练的方法，通过在多个小批次中累积梯度后再更新参数，使得训练更加稳定，并提升模型的收敛质量。梯度裁剪（Gradient Clipping）则用于防止梯度爆炸，特别是在深度网络或长序列建模中，通过限制梯度的最大范数或值，避免因梯度过大导致的训练不稳定。此外，自适应梯度调整方法（如 Adam、RMSprop）可以根据参数的历史更新情况动态调整学习率，从而优化收敛速度，而梯度正则化（如 L2 正则化）能够防止过拟合，使模型在复杂任务中具备更强的泛化能力。这些梯度优化策略相互配合，使得深度学习训练更加高效、稳定，并提高最终模型的性能。\n", "\n", "**显存优化/训练加速策略**：在深度学习训练中，显存（VRAM）往往是一个瓶颈，尤其是在大规模模型（如 LLM）和高分辨率计算机视觉任务中，标准的 FP32 计算会占用大量显存。而 FP16 数据格式的数值表示范围更小，占用的显存空间仅为 FP32 的一半，因此可以在相同的 GPU 资源下处理更大的 batch size，提高训练吞吐量、从而提升整体的训练效率。在大模型训练阶段、我们常常使用混合精度训练机制（Mixed Precision Training），利用 FP16（半精度浮点数）与 FP32（单精度浮点数）混合计算，以减少显存占用并加速计算，同时借助自动损失缩放（Loss Scaling）避免数值不稳定问题。\n", "\n", "**日志记录**：训练过程中需要记录关键指标（如损失值、精度、学习率等），可以使用 TensorBoard、WandB 或日志文件，以便后续分析模型的训练过程和性能优化。  \n", "\n", "**模型保存**：训练完成后，需要保存模型的权重、优化器状态等，以便后续推理或继续训练。通常采用 `.pth`（PyTorch）或 `.h5`（TensorFlow/Keras）格式存储，并可以结合检查点（checkpoint）机制定期保存模型状态。"]}, {"cell_type": "markdown", "id": "2e511f17-83f1-4249-abed-dd862c9d7adc", "metadata": {}, "source": ["然而，在我们将这些功能组成脚本的时候，我们需要的是**模块化编程**的基本思路。整个训练脚本通常由多个独立的功能模块组成，例如数据加载、分布式训练、动态学习率调整、混合精度计算、梯度优化等，每个模块负责特定的功能，并被设计成独立的函数或类，以便单独调试和复用。在主程序中，这些模块被依次初始化并串联起来，形成完整的训练流程。**因此，整个脚本可以分为两大部分**——"]}, {"cell_type": "markdown", "id": "8d544e9c-c912-433d-afa9-5c29cb9bdefd", "metadata": {}, "source": ["1. **定义各个功能模块（函数）**<br><br>\n", "   - 这些函数是 **独立的、可复用的功能单元**，包括：<br><br>\n", "     - `Logger()` → 负责日志记录\n", "     - `get_lr()` → 计算动态学习率\n", "     - `train_epoch()` → 训练单个 epoch\n", "     - `init_model()` → 初始化模型（支持断点续训）\n", "     - `init_distributed_mode()` → 初始化 DDP 分布式训练\n", "     - 单独的Dataset.py和model.py用于支持模型和数据的处理\n", "      "]}, {"cell_type": "markdown", "id": "1144e7a7-da7f-4f34-9e74-4a44e3d4b349", "metadata": {}, "source": [" \n", "2. **主程序（`if __name__ == \"__main__\":`）**<br><br>\n", "   - 负责 **解析参数、加载数据、初始化模型、配置训练环境**。\n", "   - 依次 **调用前面定义的函数**，组织整个训练流程。\n", "\n", "这相当于先建积木（功能模块），然后搭建房子（主程序），既清晰又高效，符合工程化深度学习训练的最佳实践。"]}, {"cell_type": "markdown", "id": "6981ebd6-77b4-44cd-835d-d5857deaf080", "metadata": {}, "source": ["### 2.2 分布式预训练的基础概念与代码配置"]}, {"cell_type": "markdown", "id": "9f5c72e9-5bdb-45e0-a156-fbf9634adfde", "metadata": {}, "source": ["分布式（Distributed）是一种计算方式，指将计算任务分布到多个节点上进行协同处理，以提升计算效率或处理更大规模的任务。在深度学习中，**分布式训练的目标是利用多个设备（如 GPU 或 CPU）来加速模型的训练或支持训练更大的模型**。\n", "\n", "在分布式中，你常常会听到这些基础概念——"]}, {"cell_type": "markdown", "id": "79df3491-5e8e-4c5a-806d-579e0ceed46d", "metadata": {}, "source": ["- **进程（Process）**：**一个独立的程序运行实例、比如训练一个神经网络**。该实例本身拥有自己的内存空间、文件描述符等资源。**在分布式训练中，每个 GPU 通常只会被分配一个进程，每个进程独立处理模型计算任务**。进程之间相互独立，彼此的内存空间不能直接访问，需要通过进程间通信（如共享内存、消息队列）进行数据交换。\n", "\n", "-  **节点（Node）**：在多机训练中，每台机器是一个节点、每个节点可能有多个 GPU，多个节点通过高速网络（如 InfiniBand）进行通信，完成分布式训练。每个 GPU 可以运行一个或多个进程（通常是一个）。\n", "\n", "- **线程（Thread）**：线程是进程的执行单元，**通常对应程序的一条指令流（比如执行一段代码）**，一个进程可以包含多个线程，线程共享进程的内存和资源。线程共享进程的资源，而进程之间是独立的、线程创建和切换的开销比进程小。在深度学习中、多线程机制常常用于数据预处理、数据加载、并行模型计算等等。<font color=\"red\">**大部分时候、想要实现多线程操作、我们只需调整线程相关的超参数即可、但是想要多进程操作、则必须使用PyTorch DDP以及DeepSpeed这样的分布式框架来进行辅助**。\n", "\n", "-  **单机多卡（Single Node Multi-GPU）**：单机多卡是指在一台物理机器上使用多个 GPU 进行并行训练、也就是只有1个节点、但是可以有多个进程。在一台机器上、多个GPU之间通过共享内存或高速总线（如 PCIe）进行通信、含有PCIe功能的显卡价格会更昂贵。这种方式的 GPU 间通信延迟较低，数据传输通过共享内存完成。使用 `torch.nn.DataParallel` 或 `torch.nn.parallel.DistributedDataParallel` 可以方便实现单机多卡训练。\n", "\n", "- **多机多卡（Multi-Node Multi-GPU）**：多机多卡是指使用多台物理机器，每台机器上有多个 GPU，通过高速网络将这些机器连接起来，共同进行分布式训练。节点之间的通信需要通过网络（如 Ethernet 或 InfiniBand），通信延迟比单机多卡高，还需要使用 NCCL、GLOO 或 MPI 等通信协议来通信后端。在需要利用数百甚至数千个 GPU 时，通常采用多机多卡训练方式。例如 GPT-3、BERT 等大规模模型的预训练。"]}, {"cell_type": "markdown", "id": "6dd52e83-4d79-4415-a68a-202f4e78ca23", "metadata": {}, "source": ["不难发现，只有跨进程、跨设备的训练才需要用到多个设备，因此分布式训练是指跨进程的训练、多线程并行不是分布式运算的一部分。"]}, {"cell_type": "markdown", "id": "d48bee73-8b4c-4cf8-8eca-02bbe1a68adb", "metadata": {}, "source": ["在分布式训练中、我们有两种主要训练方式——\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/82.png)\n", "\n", "#### 2.2.1 数据并行（Data Parallelism）\n", "  \n", "数据并行是将训练数据划分为多个不重叠的部分，并分配给不同的设备（如 GPU），每个设备上独立计算正向传播和反向传播，然后同步梯度更新参数。在数据并行中、**模型副本在每个设备上完全相同、而训练数据被分片**，每个设备只处理其中一部分、计算负载与设备数成正比。\n", "\n", "在数据并行时，每个设备独立运行正向传播和反向传播、设备间同步梯度，更新模型参数。这种方式简单高效，适用于大多数场景、但是有设备之间存在通信开销。"]}, {"cell_type": "markdown", "id": "4fd4a9ba-95a7-4e4b-acac-21f571b8cbca", "metadata": {}, "source": ["- **为什么一定要通信？不通信不行吗？**"]}, {"cell_type": "markdown", "id": "b9a8a382-d8e6-4e67-9b02-ac545881a51f", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/81.png)\n", "\n", "上述图像展示了pytorch中的`DistributedDataParallel`类的具体流程——\n", "\n", "1. **Dataloader（左侧）**：\n", "   - 数据加载器（Dataloader）负责将训练数据划分为批次（Batch），然后分发给各个 GPU。\n", "   - 每个 GPU 处理的数据来自一个分片，确保数据互不重叠。\n", "<br><br>\n", "2. **模型副本（紫色 LLM）**：\n", "   - 每个 GPU 上都有一份相同的模型副本。\n", "   - 所有设备共享同一组初始参数，但独立运行正向传播和反向传播。\n", "<br><br>\n", "3. **Forward/Backward Pass（正向和反向传播）**：\n", "   - 每个 GPU 对其分配的数据进行正向传播（计算输出）和反向传播（计算梯度）。\n", "   - 这些计算完全独立，因此可以并行进行。\n", "<br><br>\n", "4. **梯度同步（Synchronize Gradients）**：\n", "   - 在反向传播完成后，各个 GPU 之间会通过通信操作（如 `all_reduce`）同步它们计算出的梯度。\n", "   - 同步后的梯度是所有 GPU 的平均值，用于确保模型参数的一致性。\n", "<br><br>\n", "5. **模型更新（Update Model）**：\n", "   - 每个 GPU 使用同步后的梯度更新自己的模型副本。\n", "   - 更新后的模型在下一轮迭代时仍保持一致。"]}, {"cell_type": "markdown", "id": "5d216201-f239-4da4-b949-662a410499da", "metadata": {}, "source": ["#### 2.2.2 模型并行（Model Parallelism）\n", "\n", "模型并行是将模型的不同部分分配到不同的设备上，多个设备合作完成正向传播和反向传播。在模型并行中、数据在多个设备间共享、每个设备负责模型的一个子部分。常用于超大模型（如 GPT-3、PaLM 等）的训练。\n", "\n", "在模型并行时、**数据还是会被分片、每次处理的数据是全局数据的一个子集**。同时，**分割在不同设备上的模型结构大多可以并行**，这样在一群可并行的设备处理完数据自己后、就会将信息传递给下一群可并行的设备。反向传播时，设备按相反顺序逐步计算梯度。这种方式能够训练内存需求极大的模型。但是实现起来极其复杂（因为需要对模型进行拆分），设备间通信更多。\n", "\n", "在模型并行中，模型的不同部分（如网络层或参数分块）被分配到不同的设备，每个设备只负责处理一部分模型权重。因此，**模型的前向传播和反向传播需要跨设备传递中间计算结果和梯度**，前向传播时，每个设备必须将模型中间激活值（activation）传递给下一个设备。激活值的大小通常很大（取决于 Batch Size 和神经元数），传输这些值需要大量通信。反向传播时，设备需要将梯度值传递给计算前一层的设备。\n", "每层梯度的大小与模型的权重规模相同，因此反向传播也会产生大量通信。为了解决这个问题，我们现在有管道并行（pipeline parallelism）、ZeRO优化、以及混合并行（Hybrid Parallelism）等更前沿的手段、不过这些手段在0.02B模型的训练流程中并不常见。"]}, {"cell_type": "markdown", "id": "ae4fb236-f9e8-4c2e-90f5-f7b64ee09ff9", "metadata": {}, "source": ["- **模型具体如何并行？**"]}, {"cell_type": "markdown", "id": "2869c014-ee40-412b-ae87-79d90ec862cd", "metadata": {}, "source": ["**1.层级并行（Layer-wise Parallelism）**：\n", "\n", "将模型的不同层分配到不同设备。例如，Transformer 的第 1-6 层分配到 GPU 0，第 7-12 层分配到 GPU 1。前向传播时，输入通过第 1-6 层后，将结果传递给第 7-12 层。反向传播时，梯度以相反顺序传递。\n", "\n", "**例如**：\n", "```python\n", "import torch\n", "import torch.nn as nn\n", "\n", "class TransformerLayer(nn.Module):\n", "    def __init__(self, dim):\n", "        super().__init__()\n", "        self.attention = nn.Linear(dim, dim)  # 简化的注意力层\n", "        self.feedforward = nn.Linear(dim, dim)  # 简化的前馈层\n", "\n", "    def forward(self, x):\n", "        x = self.attention(x)\n", "        x = self.feedforward(x)\n", "        return x\n", "\n", "# 分配层到不同设备\n", "device_0 = torch.device(\"cuda:0\")\n", "device_1 = torch.device(\"cuda:1\")\n", "\n", "layers = [TransformerLayer(512).to(device_0) for _ in range(6)] + \\\n", "         [TransformerLayer(512).to(device_1) for _ in range(6)]\n", "\n", "def forward(x):\n", "    for i, layer in enumerate(layers):\n", "        x = x.to(layer.attention.weight.device)  # 确保输入数据在对应设备上\n", "        x = layer(x)\n", "    return x\n", "\n", "这种方式实现简单。适合模型深度较大、层数较多的情况。层之间依赖较强，设备间通信多。可能导致部分设备计算空闲（负载不均）。"]}, {"cell_type": "markdown", "id": "074d994d-09db-406d-b22b-3d611a08a01c", "metadata": {}, "source": ["**2. 算子并行（Operator Parallelism）**\n", "\n", "将同一层中的算子（例如矩阵乘法、激活函数等）分配到不同设备。例如，Transformer 的注意力机制可以在多张 GPU 间分块计算。适合单层计算量巨大的情况、可均衡不同设备的负载。但是问题在于、通信开销大，因为算子之间的结果需要频繁交换、需要精细的实现和调度。\n", "\n", "**3. 张量切分（Tensor Parallelism）**\n", "\n", "将模型参数的张量切分到不同设备。例如，将 Transformer 中的注意力权重或前馈层的权重分为多个块，每个块分配给不同 GPU。前向传播时，各设备独立计算自己负责的部分，然后汇总。反向传播时，各设备计算梯度并同步。这种方式适合参数规模较大的情况、可避免单个设备内存不足的问题，但是实现非常复杂、需要频繁同步各种张量。\n", "\n", "**例如**：\n", "\n", "- 将每个 Transformer 层的权重矩阵沿某一维度切分到不同设备。例如，注意力权重分为两部分：\n", "  - GPU 0：负责权重的左半部分。\n", "  - GPU 1：负责权重的右半部分。\n", "- 前向传播：\n", "  - 每个设备独立计算其分配的部分，结果汇总后返回。\n", "- 反向传播：\n", "  - 梯度按张量分片同步。\n", "\n", "```python\n", "import torch\n", "\n", "class ParallelAttention(nn.Module):\n", "    def __init__(self, dim, device):\n", "        super().__init__()\n", "        self.q = nn.Linear(dim // 2, dim // 2).to(device)\n", "        self.k = nn.Linear(dim // 2, dim // 2).to(device)\n", "\n", "    def forward(self, x):\n", "        q = self.q(x[:, :x.shape[1] // 2])  # 分片输入\n", "        k = self.k(x[:, x.shape[1] // 2:])\n", "        return q @ k.T\n", "\n", "# GPU 分片\n", "device_0 = torch.device(\"cuda:0\")\n", "device_1 = torch.device(\"cuda:1\")\n", "\n", "parallel_attention = ParallelAttention(512, device_0)\n", "x = torch.randn(64, 512).to(device_0)\n", "output = parallel_attention(x)\n", "```"]}, {"cell_type": "markdown", "id": "44586251-a9d4-4749-895c-3e5074f5ca9a", "metadata": {}, "source": ["**4. 流水线并行（Pipeline Parallelism）**\n", "\n", "将模型按层分配到不同设备，同时分割数据为多个小批次（Micro-Batches），以流水线方式并行处理。例如，GPU 0 处理第 1-6 层的第 1 小批次数据时，GPU 1 可以同时处理第 7-12 层的前一个小批次数据。这样提高设备利用率，但是同样实现非常复杂、通信开销高。\n", "\n", "- 将 Transformer 的层按顺序划分到不同设备，并以小批次为单位进行流水线处理。\n", "  - GPU 0：处理第 1-6 层。\n", "  - GPU 1：处理第 7-12 层。\n", "- 流水线机制：\n", "  - 小批次 1：通过 GPU 0 后传递给 GPU 1。\n", "  - 小批次 2：在 GPU 0 计算时，GPU 1 同时处理前一个小批次。\n", "\n", "**例如**：\n", "```python\n", "# 使用 PyTorch 的 PipelineParallel 实现\n", "from torch.distributed.pipeline.sync import Pipe\n", "from torch.nn import Sequential\n", "\n", "device_0 = torch.device(\"cuda:0\")\n", "device_1 = torch.device(\"cuda:1\")\n", "\n", "model = Sequential(\n", "    *[TransformerLayer(512).to(device_0) for _ in range(6)],\n", "    *[TransformerLayer(512).to(device_1) for _ in range(6)]\n", ")\n", "model = Pipe(model, chunks=2)  # 设置流水线并行的分块数\n", "```"]}, {"cell_type": "markdown", "id": "d47cfd88-d2e0-4e4a-ab01-e65ad206b997", "metadata": {}, "source": ["#### 2.2.3 `torch.distributed`与`DDP`的数据并行实现"]}, {"cell_type": "markdown", "id": "a71c0670-5cdd-496d-b3c0-78397ccf9373", "metadata": {}, "source": ["- **数据并行 vs. 模型并行**\n", " "]}, {"cell_type": "markdown", "id": "1bbb8362-d2ad-4043-9399-d3b10cce0470", "metadata": {}, "source": [" \n", "| **对比项**         | **数据并行（Data Parallel, DDP）** | **模型并行（Model Parallel, MP）** |\n", "|--------------------|---------------------------------|---------------------------------|\n", "| **核心思想**       | 每个 GPU 训练一份完整的模型，但处理不同的数据 | 把模型拆分到多个 GPU，每个 GPU 只训练一部分 |\n", "| **适用场景**       | 适用于模型规模适中，单个 GPU 能存下整个模型 | 适用于超大模型，单 GPU 放不下整个模型 |\n", "| **梯度同步**       | 需要在所有 GPU 之间同步梯度 | 不需要同步整个模型梯度，但需要 GPU 之间传递计算结果 |\n", "| **适用任务**       | ImageNet 训练、GPT/BERT 训练、目标检测 | GPT-3 训练、T5-XXL、DeepLabV3+、长序列 Transformer |\n", "| **计算通信开销**   | 通信开销较小，适合高效扩展 | 通信开销大，训练更复杂 |\n", "| **扩展方式**       | 可以简单扩展到 **多机多卡（数百 GPU）** | 适用于 **单机多卡或有限多机多卡** |"]}, {"cell_type": "markdown", "id": "d6dad4bf-1d99-4940-a251-f57b97ba895f", "metadata": {}, "source": ["- **什么时候用数据并行？什么时候用模型并行？**"]}, {"cell_type": "markdown", "id": "c7baf0ef-b774-4b6d-98c3-62dd59d71226", "metadata": {}, "source": ["| **情况** | **使用数据并行（DDP）？** | **使用模型并行（MP）？** |\n", "|----------|-----------------|----------------|\n", "| **模型参数少于 10 亿** | ✅ DDP 更适合 | --模型并行 |\n", "| **单 GPU 无法存下模型** | ❌ 不适合 | ✅ 需要模型并行 |\n", "| **需要处理大数据** | ✅ DDP 并行加速 | ❌ 模型并行不会加速数据计算 |\n", "| **训练 GPT-3、PaLM 这样的大模型** | ❌ 不适合 | ✅ 需要模型并行 |"]}, {"cell_type": "markdown", "id": "fd12ce48-bf2f-4924-b3b1-ea45eb46773a", "metadata": {}, "source": ["因此在本次的MateConv mini训练过程中、面对小型的、能够一整个放置到单卡上的模型，**我们选择的是数据并行的实现方式**，并且我们是依赖于和`torch.distributed`这个分布式通信库和`torch.nn.parallel.DistributedDataParallel`这个数据并行实现类来实现分布式训练本身。"]}, {"cell_type": "markdown", "id": "0e601a17-e3c1-4e60-850c-18a13538865a", "metadata": {}, "source": ["- **torch.distributed**"]}, {"cell_type": "markdown", "id": "53043539-9269-4582-8b5e-6ef5094e9bae", "metadata": {}, "source": ["`torch.distributed` 是 **PyTorch 的底层分布式通信库**，它为分布式预训练提供**跨设备通信支持、跨设备参数管理、进程管理**等功能，它能够支持不同的通信后端（如 `NCCL`、`Gloo`、`MPI`）、也能支持跨设备管理分布式进程的同步、反向传播等流程、并且它有强大的兼容性、能够与各类分布式框架如deepspeed、MagatronLM等联合运行。在现代分布式训练流程中、无论是执行单机多卡续联、多机多卡训练、无论是执行数据并行、还是模型并行、`torch.distributed`都是我们必须依赖的根基库。\n", "\n", "在 PyTorch 中，我们通常 **`import torch.distributed as dist`**，然后使用它来管理分布式训练进程。下面是一些最常见的操作：\n", "\n", "| **操作** | **作用** |\n", "|---------|--------|\n", "| `dist.init_process_group()` | 初始化分布式训练环境 |\n", "| `dist.get_rank()` | 获取当前 GPU 进程的编号 |\n", "| `dist.get_world_size()` | 获取所有 GPU 进程的总数 |\n", "| `dist.barrier()` | **同步所有 GPU 进程** |\n", "| `dist.all_reduce(tensor, op=dist.ReduceOp.SUM)` | **让所有 GPU 计算出的 `tensor` 求和** |\n", "| `dist.broadcast(tensor, src=0)` | **让 `rank=0` 的 GPU 把 `tensor` 发送给所有 GPU** |\n", "| `dist.scatter(tensor, scatter_list, src=0)` | **让 `rank=0` 把不同的数据块分配给不同的 GPU** |\n", "| `dist.gather(tensor, gather_list, dst=0)` | **让所有 GPU 进程的 `tensor` 汇总到 `rank=0`** |\n", "| `dist.destroy_process_group()` | **关闭分布式训练进程** |"]}, {"cell_type": "markdown", "id": "f51a00d1-e0e6-4f45-b9db-4d6104c40820", "metadata": {}, "source": ["**其中我们需要关注的有——**"]}, {"cell_type": "markdown", "id": "27b418a5-7d6b-4d51-9c7a-4c162e323fa5", "metadata": {}, "source": ["\n", "<font color=\"green\">**1. 初始化分布式进程与分布式参数**</font>：每个 DDP 进程（每个 GPU）在启动时都需要进行初始化，以加入分布式训练进程组。\n", "  \n", "```python\n", "import torch.distributed as dist\n", "\n", "dist.init_process_group(backend=\"nccl\")  # 或者 backend=\"gloo\"（CPU）或 \"mpi\"\n", "```\n", "- `backend=\"nccl\"`：适用于 **NVIDIA GPU**，性能最好（推荐）。\n", "- `backend=\"gloo\"`：适用于 **CPU 和 GPU**，在 CPU-only 任务中使用。\n", "- `backend=\"mpi\"`：适用于 **MPI 通信协议**（较少用）。\n", "\n", "大部分时候，我们还需要手动额外传递 `world_size` 和 `rank` 参数，尤其是在多机多卡状态下、更需要明确具体的进程数。\n", "\n", "```python\n", "dist.init_process_group(backend=\"nccl\", world_size=4, rank=0)\n", "```\n", "- `world_size`：所有进程的总数（如 4 台机器，每台 2 张 GPU，则 `world_size=8`）。注意，对于分布式训练框架（如deepspeed）而言，进程总数就等同于GPU总数量、因为各类分布式训练框架总是在每个GPU上只设置一个进程的。\n", "- `rank`：当前进程的编号（0 表示主进程，其他进程按顺序编号）。\n", "\n", "然而大部分时候，我们会在命令行中直接设置world_size，例如 ↓\n", "\n", "```shell\n", "deepspeed --master_port 29500 --num_gpus=4 --num_nodes=2 pretrain.py\n", "\n", "#或者\n", "\n", "torchrun --nnodes=2 --nproc_per_node=4 pretrain.py\n", "\n", "```\n", "\n", "这段命令行中的`num_gpus`/`nproc_per_node`参数乘以`num_nodes`就等同于world_size，**当我们在命令行中指定了这个参数后、就等同于设置了环境变量[\"WORLD_SIZE\"]**，并且执行了下面的python代码 ↓\n", "\n", "```python\n", "\n", "import os\n", "import torch.distributed as dist\n", "\n", "# 设置分布式环境变量\n", "os.environ[\"MASTER_ADDR\"] = \"127.0.0.1\"  # 主节点 IP 地址\n", "os.environ[\"MASTER_PORT\"] = \"29500\"      # 主节点通信端口\n", "os.environ[\"WORLD_SIZE\"] = \"4\"           # 总进程数\n", "os.environ[\"RANK\"] = \"0\"                 # 当前进程编号（手动设置为 0，用于测试）\n", "os.environ[\"LOCAL_RANK\"] = \"0\"           # 本地 GPU 编号（自动设置为 0）\n", "\n", "# 从环境变量中读取\n", "world_size = int(os.environ[\"WORLD_SIZE\"])  # 总进程数\n", "rank = int(os.environ[\"RANK\"])  # 当前进程编号\n", "local_rank = int(os.environ[\"LOCAL_RANK\"])  # 当前 GPU 编号\n", "\n", "# 初始化分布式进程\n", "dist.init_process_group(backend=\"nccl\", world_size=world_size, rank=rank)\n", "torch.cuda.set_device(local_rank)  # 绑定 GPU\n", "\n", "\n", "```\n", "\n", "如你所见，deepseek会帮助我们自动计算 world_size（即 num_gpus 传递的值）、自动分配 rank（进程 ID）、自动绑定 local_rank 到每个 GPU、还会完成自动初始化 DistributedDataParallel (DDP)。同时，在分布式训练中，每个进程会在自己的上下文中独立执行预训练脚本，但环境变量的设置确保每个进程能够识别自己的身份（RANK）和所绑定的设备（LOCAL_RANK），因此不同GPU上的rank编号以及local_rank编号是不同的。\n", "\n", "---"]}, {"cell_type": "markdown", "id": "a79d5fc5-d0dc-4968-9f5c-bfeb77a7585c", "metadata": {}, "source": ["<font color=\"green\">**2. 进程间通信**</font>\n", "`torch.distributed` 提供了一系列 **分布式通信 API**，用于 GPU 进程之间的数据交换。\n", "\n", "-  **`dist.all_reduce()`**\n", "**所有 GPU 进程计算出的数据进行 `reduce` 操作（如求和、平均值等）**\n", "```python\n", "tensor = torch.tensor([1.0]).cuda()\n", "dist.all_reduce(tensor, op=dist.ReduceOp.SUM)\n", "print(f\"Rank {dist.get_rank()} has tensor: {tensor.item()}\")\n", "```\n", "例如，上述代码就等同于让**所有 GPU 进程的 `tensor` 进行求和**，然后同步到每个进程。`op=dist.ReduceOp.SUM`表示 **所有 GPU 计算出的值相加**（可以改成 `MAX`、`MIN` 等）。\n", "\n", "=========\n", "\n", "-  **`dist.broadcast()`**\n", "**让某个进程（如 rank=0）把数据广播给所有进程**\n", "```python\n", "tensor = torch.zeros(1).cuda()\n", "if dist.get_rank() == 0:\n", "    tensor.fill_(10)  # 只有 rank 0 设置值\n", "dist.broadcast(tensor, src=0)  # 让 rank 0 广播给所有 GPU\n", "print(f\"Rank {dist.get_rank()} has tensor: {tensor.item()}\")\n", "```\n", "\n", "让 `rank=0` 的 GPU 把 `tensor` 发送给所有 GPU。\n", "\n", "=========\n", "\n", "-  **`dist.scatter()`**\n", "**让 `rank=0` 把不同的数据块分配给不同的 GPU**\n", "```python\n", "if dist.get_rank() == 0:\n", "    tensor_list = [torch.tensor([i]).cuda() for i in range(dist.get_world_size())]\n", "else:\n", "    tensor_list = None\n", "tensor = torch.zeros(1).cuda()\n", "dist.scatter(tensor, scatter_list=tensor_list, src=0)\n", "print(f\"Rank {dist.get_rank()} received {tensor.item()}\")\n", "```\n", "让 `rank=0` 把 `tensor_list[i]` 发送给 `rank=i`。\n", "\n", "=========\n", "\n", "-  **`dist.gather()`**\n", "**让所有 GPU 进程把数据汇总到 `rank=0`**\n", "```python\n", "tensor = torch.tensor([dist.get_rank()]).cuda()\n", "gather_list = [torch.zeros(1).cuda() for _ in range(dist.get_world_size())] if dist.get_rank() == 0 else None\n", "dist.gather(tensor, gather_list=gather_list, dst=0)\n", "if dist.get_rank() == 0:\n", "    print(f\"Gathered list: {[t.item() for t in gather_list]}\")\n", "```\n", "所有 GPU 进程的 `tensor` 会被收集到 `rank=0`。\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/81.png)\n", "\n", "想想在数据并行过程中、可能会遇到哪些流程？首先要对数据进行分配、在经过每个线程上不同的模型处理后、还需要同步梯度（`dist.all_reduce()`功能）、在多机训练中，还需要保证各个设备上除了模型权重之外的超参数也一致（如学习率调整，可能就需要使用`dist.broadcast()`功能）。\n", "\n", "---"]}, {"cell_type": "markdown", "id": "98a44e74-eeb0-4715-af1f-3b27d7f713ed", "metadata": {}, "source": ["<font color=\"green\">**3. 获取进程信息**</font>\n", "在多 GPU 训练中，我们可能需要知道当前进程的 `rank` 和 `world_size`：\n", "\n", "```python\n", "rank = dist.get_rank()  # 获取当前进程的 rank\n", "world_size = dist.get_world_size()  # 获取总进程数\n", "print(f\"进程 {rank} / {world_size} 正在运行...\")\n", "```\n", "\n", "- **`dist.get_rank()`**：返回当前 GPU 进程的编号。\n", "- **`dist.get_world_size()`**：返回当前分布式环境中的总 GPU 数量。\n", "\n", "---"]}, {"cell_type": "markdown", "id": "93f26170-3f5d-4e7e-a31b-07853d02cd25", "metadata": {}, "source": ["<font color=\"green\">**4. 同步所有 GPU**</font>\n", "在某些情况下，我们需要**确保所有 GPU 进程都完成某个步骤后再继续执行**，这时候可以使用 `dist.barrier()`：\n", "```python\n", "dist.barrier()  # 阻塞所有 GPU 进程，直到所有进程都到达这里\n", "```\n", "确保所有进程同步，防止某些 GPU 过快进入下一步。\n", "\n", "---"]}, {"cell_type": "markdown", "id": "326c2d2c-ae02-44bd-920f-91f3eec57aed", "metadata": {}, "source": ["<font color=\"green\">**5. 关闭分布式进程**</font>\n", "当所有进程训练完毕时，需要手动关闭分布式进程：\n", "```python\n", "dist.destroy_process_group()\n", "```\n", "\n", "---"]}, {"cell_type": "markdown", "id": "ffb53ea5-31e0-4481-afac-b5a0109aeafa", "metadata": {}, "source": ["如你所见，`torch.disbributed`支持了丰富的底层自定义功能、用以支持丰富的数据流实现，然而这个过程如果全都要手动实现的话就过于复杂了。而`torch.nn.parallel.DistributedDataParallel(DDP)`类在此时出现、它为我们实现了基于torch.distributed的完整数据并行流程。<font color =\"red\">**DDP为我们封装并简化了整个数据并行流程、它使用 torch.distributed 的 API（如 dist.all_reduce 和 dist.broadcast）在内部完成梯度同步和权重更新，因此我们只需要调用`torch.nn.parallel.DistributedDataParallel`就可以简单运行多线程的分布式数据并行、而无需再认为进行任何的干涉**。"]}, {"cell_type": "markdown", "id": "fce6337d-ccb0-4a4e-acb9-5385606cb9e5", "metadata": {}, "source": ["- **`torch.nn.parallel.DistributedDataParallel (DDP)`**\n", "\n", "`torch.nn.parallel.DistributedDataParallel (DDP)` 是 PyTorch 提供的高性能分布式数据并行训练工具，用于在多 GPU 或多机多卡场景下高效地训练深度学习模型。DDP 的核心功能是通过**为每个 GPU 启动一个独立进程**，实现**模型副本的并行计算**，并在反向传播时**自动同步所有进程的梯度**。它能充分利用分布式硬件资源，避免传统 `DataParallel` 中的通信瓶颈，是当前大规模分布式训练的主流方法。\n", "\n", "**重要参数**\n", "\n", "1. **`module`**：传入需要并行训练的模型（通常是 `torch.nn.Module` 对象）。DDP 会为模型创建每个 GPU 的副本，并在各进程上运行。\n", "\n", "2. **`device_ids`**：指定当前进程使用的 GPU。如果在单机多卡训练中，可以传入 `[local_rank]`，如：\n", "     ```python\n", "     model = DistributedDataParallel(model, device_ids=[local_rank])\n", "     ```\n", "在多机训练时，通常由 `local_rank` 环境变量动态决定。\n", "\n", "3. **`output_device`**（可选）：指定输出数据或者模型的设备，通常与 `device_ids` 相同。默认值为 `device_ids[0]`。\n", "\n", "4. **`find_unused_parameters`**：是否在每次反向传播中查找未使用的参数。默认值为 `False`，建议只在模型中存在条件分支（如动态网络结构）时设置为 `True`，否则会增加开销。\n", "\n", "5. **`gradient_as_bucket_view`**：是否在梯度同步时使用内存高效的 `bucket view`。默认值为 `False`，在高效内存管理场景中建议设为 `True`。在`torch.nn.parallel.DistributedDataParallel (DDP)`中，所有模型参数的梯度在计算完成后需要同步到所有设备。这些梯度通常会分块（分组）进行同步，每一组梯度被称为一个 bucket。在使用`bucket view`机制时，DDP 会将模型参数按照大小排序，然后将它们分成多个 bucket、让每个 bucket 包含多个参数的梯度，然后在梯度同步时，DDP 会逐个 bucket 同步，而不是逐个参数同步，从而降低通信开销。\n", "\n", "6. **`static_graph`**（PyTorch 2.0 引入）：如果模型是静态计算图，可设置为 `True`，以优化梯度同步过程。能显著提高训练效率，但仅适用于图结构固定的模型。"]}, {"cell_type": "markdown", "id": "1375c635-b490-44b8-bdb1-049c0b78208d", "metadata": {}, "source": ["DDP的封装程度极高、大部分时候我们需要运行的代码类似于 ↓\n", "\n", "```python\n", "import torch\n", "import torch.distributed as dist\n", "from torch.nn.parallel import DistributedDataParallel as DDP\n", "\n", "model = MyModel().to(local_rank)\n", "model = DDP(model, device_ids=[local_rank])\n", "\n", "```"]}, {"cell_type": "markdown", "id": "a5418c46-3b05-4add-9eb0-41238ccaa00b", "metadata": {}, "source": ["或者 ↓ "]}, {"cell_type": "markdown", "id": "98c873b6-e392-4278-bf6a-d6db50356212", "metadata": {}, "source": ["```python\n", "if ddp:\n", "    model._ddp_params_and_buffers_to_ignore = {\"pos_cis\"}\n", "    model = DistributedDataParallel(model, device_ids=[ddp_local_rank])\n", "```"]}, {"cell_type": "markdown", "id": "28f3d3ec-7b53-4d08-a98e-9759eeef77f1", "metadata": {}, "source": ["#### 2.2.4 MateConv mini的分布式预训练初始化设置"]}, {"cell_type": "code", "execution_count": null, "id": "2c67c93f-b433-4a09-8fdf-a1774b364f33", "metadata": {}, "outputs": [], "source": ["import os\n", "import platform\n", "import argparse\n", "import time\n", "import math\n", "import warnings\n", "import torch\n", "import torch.distributed as dist\n", "from torch import optim\n", "from torch.nn.parallel import DistributedDataParallel\n", "from torch.optim.lr_scheduler import CosineAnnealingLR\n", "from torch.utils.data import DataLoader, DistributedSampler\n", "from contextlib import nullcontext\n", "from model.model import Transformer #确保你的脚本和model文件夹在同一个目录下\n", "from model.LMConfig import LMConfig\n", "from model.dataset import PretrainDataset"]}, {"cell_type": "code", "execution_count": 8, "id": "6b211b5d-a298-469c-8fd5-9af81473785b", "metadata": {}, "outputs": [], "source": ["def Logger(content):\n", "    if not ddp or dist.get_rank() == 0:\n", "        print(content)\n", "\n", "def init_distributed_mode():\n", "    if not ddp: return\n", "    global ddp_local_rank, DEVICE\n", "\n", "    dist.init_process_group(backend=\"nccl\")\n", "    ddp_rank = int(os.environ[\"RANK\"])\n", "    ddp_local_rank = int(os.environ[\"LOCAL_RANK\"])\n", "    ddp_world_size = int(os.environ[\"WORLD_SIZE\"])\n", "    DEVICE = f\"cuda:{ddp_local_rank}\"\n", "    torch.cuda.set_device(DEVICE)"]}, {"cell_type": "markdown", "id": "c20910e0-6efa-4223-bc9d-5e71c5793216", "metadata": {}, "source": ["这两个函数主要负责初始化分布式训练环境并控制日志打印行为。通过 `Logger` 函数，确保只有在非分布式模式或当前进程为主进程（`rank=0`）时输出日志信息，避免多进程重复打印。`init_distributed_mode` 函数用于初始化分布式训练的核心配置，如果启用了分布式训练（`ddp=True`），会通过 `torch.distributed.init_process_group` 初始化分布式进程组，依赖环境变量（如 `RANK`, `LOCAL_RANK`, `WORLD_SIZE`）获取当前进程的全局编号、节点内的 GPU 编号以及总进程数。同时，设置每个进程所绑定的 GPU 设备（通过 `local_rank` 计算），以确保多 GPU 训练中每个进程正确使用其对应的 GPU。这些步骤为分布式数据并行训练提供了基础环境配置。"]}, {"cell_type": "markdown", "id": "1d8aca09-f332-4d61-a923-c6079aab1a6d", "metadata": {}, "source": ["```python\n", "\n", "def Logger(content):\n", "    # 只有在非分布式训练（ddp=False）或当前进程是 rank 0（主进程）时，才打印日志\n", "    # 这样可以防止多个进程重复输出相同的日志信息\n", "    if not ddp or dist.get_rank() == 0:\n", "        print(content)\n", "\n", "def init_distributed_mode():\n", "    # 如果没有启用分布式训练（ddp=False），直接返回，不执行任何初始化\n", "    if not ddp: return\n", "\n", "    # 声明全局变量，确保 `ddp_local_rank` 和 `DEVICE` 在函数外部也可以访问\n", "    global ddp_local_rank, DEVICE\n", "\n", "    # 初始化 PyTorch 的分布式进程组\n", "    # backend=\"nccl\" 表示使用 NVIDIA 的 NCCL 后端（适用于 GPU 计算）\n", "    dist.init_process_group(backend=\"nccl\")\n", "\n", "    # 通过环境变量获取分布式训练的相关信息\n", "    # `RANK` 代表当前进程的全局 rank，范围是 0 到 world_size-1\n", "    ddp_rank = int(os.environ[\"RANK\"])  \n", "\n", "    # `LOCAL_RANK` 代表当前进程在当前节点（机器）上的 GPU 编号\n", "    # 例如，在单机多 GPU 训练中，rank 0 的 local_rank 可能是 0，rank 1 的 local_rank 可能是 1\n", "    ddp_local_rank = int(os.environ[\"LOCAL_RANK\"])\n", "\n", "    # `WORLD_SIZE` 代表所有参与训练的进程总数（即所有 GPU 的总数）\n", "    ddp_world_size = int(os.environ[\"WORLD_SIZE\"])\n", "\n", "    # 设置当前进程所使用的 GPU 设备\n", "    # 例如，如果 local_rank=1，则当前进程会使用 \"cuda:1\"\n", "    DEVICE = f\"cuda:{ddp_local_rank}\"\n", "    torch.cuda.set_device(DEVICE)\n", "```"]}, {"cell_type": "markdown", "id": "f0c55100-78f0-4636-b009-232d9899189b", "metadata": {}, "source": ["### 2.3 学习率的余弦退火与线性预热机制"]}, {"cell_type": "markdown", "id": "681843d1-cf30-4b56-b195-9e61bd691a91", "metadata": {}, "source": ["**学习率是深度学习模型训练中至关重要的超参数，它决定了每次参数更新的步长大小，对模型的收敛速度和最终性能有直接影响**。\n", "\n", "如果学习率过大，优化过程可能会出现震荡甚至无法收敛，因为模型会跳过最优解；相反，如果学习率过小，模型的训练速度会变得非常缓慢，可能需要更多的迭代才能接近最优解，甚至会陷入局部最优。为了解决这些问题，动态调整学习率的方法（如学习率衰减、余弦退火、线性预热等）被广泛使用，这些策略可以在训练初期使用较大学习率加速收敛，在训练后期逐渐减小学习率以实现更精细的参数调整，最终提升模型性能和稳定性。因此，选择合适的学习率和调度策略是训练高效且稳定的深度学习模型的关键。"]}, {"cell_type": "markdown", "id": "353f8b3c-b431-4b73-a3a0-4f2f44ed88aa", "metadata": {}, "source": ["在本次训练中、我们一共使用了三种学习率调度机制、分别是**Adam学习率自适应优化算法**、以及全局学习率调节机制**余弦退火（Cosine Annealing）** 和 **线性预热（Linear Warmup）**，三种机制共同作用、用于动态调整深度学习训练过程中的学习率。"]}, {"cell_type": "markdown", "id": "ddf18304-2c5c-4ca1-8866-0d3abdf8db66", "metadata": {}, "source": ["- 简述Adam学习率自适应优化算法"]}, {"cell_type": "markdown", "id": "f7b0320a-7cf6-426b-970f-f3e0d516376e", "metadata": {}, "source": ["Adam（Adaptive Moment Estimation）是一种自适应优化算法，它对学习率的调整十分精准又十分灵活。在传统Adam课程中，你会看到十分复杂的数学流程，严谨地说，Adam是通过一阶矩估计（动量）以及二阶矩估计（梯度方差）两个因子来调整学习率的，然而在现代神经网络训练的流程中、优化算法已经被高度封装、几乎没有修改的可能，因此我们深入地学习其数学流程的性价比就会相对变低。在今天的大模型训练流程中，关于Adam你只需要了解——\n", "\n", "1. Adam是一种自适应优化算法、这意味着它会**为每一个参数或每一组赋予一个独立的学习率、并依据每一个/每一组参数自己的情况来迭代学习率**。<br><br>\n", "1. **对梯度大的参数会被二阶矩的平方根（梯度方差）压制**，避免学习率过大。<br><br>\n", "2. **梯度小的参数会相对放大学习率**，提升收敛速度。<br><br>\n", "4. **一阶矩估计（动量）** 有助于平滑优化路径，减少震荡。\n", "\n", "因此，即便不加入任何的学习率调整策略、Adam优化器本身也会在迭代过程中为我们自适应地调整实时的学习率。\n", "\n", "在大语言模型的世界中、Adam + 线性预热 + 余弦退火的学习率组合方式非常经典，这一套组合尤其适用于大规模预训练任务和Transformer结构，让我们一起来看一看。"]}, {"cell_type": "markdown", "id": "f463abe0-e45a-4efb-9662-4e01b8077bc9", "metadata": {}, "source": ["#### 2.3.1 线性预热（Linear Warmup）\n", "\n", "在深度学习的训练初期，模型的权重还未适应数据分布，直接使用较大学习率可能导致不稳定的梯度更新，甚至损坏模型。但是、如果从非常小的学习率开始进行训练，模型可能很长一段时间都处于缓慢迭代的状态，又不利于在最初远离最优点时的迭代效率。\n", "因此，为了让模型逐步适应学习任务，避免过大的学习率带来的不稳定性和过小的学习率带来的低效迭代，我们在模型刚开始迭代时要采用一种**让学习率从小到大逐步增长**的预热策略。**线性预热就是通过设置预热部署、让学习率从0开始线性逐步增长、在训练初期为模型提供稳定优化过程的技术**。更具体地来说，具体来说，线性预热是让学习率在指定的预热步数（`warmup_iters`）内，从 $ 0 $ 平滑增长到目标学习率（`learning_rate`）。\n", "\n", "迭代的具体公式为：\n", "$$\n", "\\text{lr} = \\text{learning\\_rate} \\times \\frac{\\text{it}}{\\text{warmup\\_iters}}\n", "$$\n", "\n", "- **`it`（Iteration）**：当前的迭代步数，表示已经执行了多少次参数更新。通常从 0 开始，在每次训练中的迭代次数逐渐增加。<br><br>\n", "- **`warmup_iters`（Warmup Iterations）**：学习率预热阶段结束的迭代步数。如果设置 `warmup_iters = 1000`，则在前 1000 次迭代中，学习率将从 0 增长到预设的目标学习率（`learning_rate`）。<br><br>\n", "- 当 $ \\text{it} = 0 $：学习率为 $ 0 $。<br><br>\n", "- 当 $ \\text{it} = \\text{warmup\\_iters} $：代表着当前迭代部署就等于预热阶段结束的步数，因此预热阶段完全结束，此时学习率为 $ \\text{learning\\_rate} $。<br><br>\n", "- 训练初期（$ \\text{it} < \\text{warmup\\_iters} $），学习率随迭代次数线性增长。如果当前迭代步数小于预热步数（`warmup_iters`），那则处于预热阶段。"]}, {"cell_type": "markdown", "id": "434319a9-22b9-4bb8-b03c-4d6d69d0644c", "metadata": {}, "source": ["<font color=\"red\">**虽然线性预热给人感受上是一个学习率增长的过程，但从代码实现的角度来说，却是在完整的learning_rate上逐渐提高比例的过程**</font>，假设规定的目标learning_rate是0.1，那我们实现的其实是 ↓\n", "\n", "<center>0.1 * 1%<br><br>\n", "0.1 * 2%<br><br>\n", "0.1 * 3%<br><br>\n", "……\n", "\n", "这样的增长过程。"]}, {"cell_type": "markdown", "id": "5c0152d4-2b44-4624-8dc3-743f8a7c1f0f", "metadata": {}, "source": ["我们可以用类似下面的代码来实现线性预热机制：\n", "```python\n", "if it < warmup_iters:\n", "    return args.learning_rate * it / warmup_iters\n", "```"]}, {"cell_type": "markdown", "id": "816cc31b-ff7e-4905-82a6-2a6f270fc37b", "metadata": {}, "source": ["值得一提的是，这里的**args**是在整个脚本的主程序中导入的、包含所有参数信息的字典。在编写python脚本时（尤其是当这些脚本需要放在linux环境中运行时），我们一般会使用**argparse**库来解析命令行参数和命令行代码，argparse是python中专属的命令行参数解析工具，属于python内置库的一部分。我们训练过程中使用的所有参数，都需要在argparse中进行“认证”，然后再通过arg调出来使用。具体的细节会在我们开始写主程序时展现。"]}, {"cell_type": "markdown", "id": "a8ca65c1-5e61-4cc0-a981-0d3fe66787f6", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "a86de498-a984-4110-a628-106d1907f19f", "metadata": {}, "source": ["- **什么时候可以尝试预热技巧？**\n", "\n", "1. **梯度爆炸或迭代极其不稳定**：随机初始化的参数可能导致梯度值过大，较大学习率会进一步放大这种不稳定性，最终使模型无法正确优化。<br><br>\n", "2. **难以找到优化方向**：初始阶段，模型的优化方向尚未确定，较大学习率可能使优化过程跳跃过潜在的收敛路径。<br><br>\n", "3. **损失震荡**：大学习率可能导致损失函数值剧烈波动，训练效率降低。\n", "\n", "通过逐步增加学习率，模型可以以更平滑的方式进入稳定的优化状态。然而，如果学习率设置得足够小，例如 $ 10^{-5} $，初期大梯度的影响会被大幅削弱，可以跳过预热。"]}, {"cell_type": "markdown", "id": "03896ff3-36d0-4a5d-b68b-e4c1ac551e5b", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "f881c8cb-131a-4125-bc46-f93bddc1f08e", "metadata": {}, "source": ["#### 2.3.2 余弦退火机制（Cosine <PERSON>）"]}, {"cell_type": "markdown", "id": "0164cf53-18fa-4a73-a5c9-37f3f1519e0c", "metadata": {}, "source": ["在训练的中后期，模型逐渐趋于收敛，学习率的作用需要减弱。此时，如果学习率过大，就可能不断跳出最优解、导致模型无法收敛，因此在中后期我们会使用较小的学习率进行稳定的迭代。**余弦退火**通过让学习率按照余弦函数的下降曲线变化，逐渐减小学习率，最终趋向于一个最小学习率值（`min_lr`）。与直接的线性下降不同，余弦退火曲线更加平滑，可以避免突降学习率带来的震荡。\n", "\n", "在大模型迭代过程中，我们经常将预热与各种退火机制联用，比如说BERT使用的就是线性预热 + 多项式衰减（Polynomial Decay）的方式，GPT、LLaMA等大型模型则常常使用余弦退火机制。通常在大模型训练中，我们会设置学习率的变动方式如下所示 ↓ \n", "\n", "```\n", "学习率\n", " ^          ..........\n", " |         /          \\\n", " |        /            \\        \n", " |       /              \\..........      \n", " |      /                \n", " |_____/   \n", " +-------------------------------------> 迭代次数\n", "```\n", "- 左侧为线性预热阶段\n", "- 中间为学习率固定阶段（hold stage）\n", "- 右侧为余弦退火阶段（原则上来说应该是曲线下降）\n", "- 最右侧是余弦退火结束后的稳定阶段\n", "\n", "大模型通常需要更长的训练时间和更复杂的特征学习过程，因此会 在 warmup 之后维持高学习率一段时间，让模型更充分地进行特征学习，再逐步降低学习率以稳定收敛。"]}, {"cell_type": "markdown", "id": "9b03b01b-70f3-4b6e-9a36-dd587762b0c3", "metadata": {}, "source": ["然而，针对小型模型进行迭代时（例如尺寸小于2B的模型），由于小模型更容易收敛、模型没有那么大的迭代空间，因此我们可能采取更激进的学习率策略，例如取消学习率固定阶段，在预热之后直接进行退火↓"]}, {"cell_type": "markdown", "id": "2a5acbaa-6728-467b-bc8d-6d937afb9961", "metadata": {}, "source": ["```\n", "学习率\n", " ^          \n", " |         /\\\n", " |        /  \\        \n", " |       /    \\...............     \n", " |      /                \n", " |_____/   \n", " +-------------------------------------> 迭代次数\n", "```\n", "- 左侧为线性预热阶段\n", "- 中间为余弦退火阶段（原则上来说应该是曲线下降）\n", "- 右侧是余弦退火结束后的稳定阶段\n", "\n", "在这种情况下，我们通过调控余弦退火结束的迭代次数来控制退火的节奏，让学习率在整个训练流程中缓慢的下降。"]}, {"cell_type": "markdown", "id": "47278584-d864-414d-b032-7c11a30a7c22", "metadata": {}, "source": ["- **计算公式**"]}, {"attachments": {}, "cell_type": "markdown", "id": "5ba05f6b-69a6-444d-bfde-28b00ce7699c", "metadata": {}, "source": ["在余弦退火阶段，学习率的计算公式为：\n", "$$\n", "\\text{lr} = \\text{min\\_lr} +  (\\text{max\\_lr} - \\text{min\\_lr}) \\times \\frac{1}{2} \\left(1 + \\cos\\left(\\pi \\cdot \\text{decay\\_ratio}\\right)\\right)\n", "$$\n", "- $\\text{max\\_lr}$：开始退火的初始学习率，也是整个余弦退火阶段最大的学习率。<br><br>\n", "- $\\text{min\\_lr}$：余弦退火最终的目标、即我们所规定的学习率的最小值、之后模型依赖于这个最小值、结合优化算法本身的影响继续变动。<br><br>\n", "- $\\text{max\\_lr} - \\text{min\\_lr}$：余弦退火的起点与终点之间的差异，我们在这个差异上乘以余弦函数、代表按照余弦值不断令学习率衰减。\n", "\n", "其中下面这个部分叫做**余弦系数（`coeff`）**：\n", "  $$\n", "  \\text{coeff} = \\frac{1}{2} (1 + \\cos(\\pi \\cdot \\text{decay\\_ratio}))\n", "  $$\n", "\n", "其中，decay_ratio的计算公式为 ↓\n", "\n", "  $$\n", "  \\text{decay\\_ratio} = \\frac{\\text{it} - \\text{warmup\\_iters}}{\\text{lr\\_decay\\_iters} - \\text{warmup\\_iters}}\n", "  $$\n", "\n", "其中——\n", "\n", "1. **分子：$\\text{it} - \\text{warmup\\_iters}$**\n", "   - $ \\text{it} $ 是当前迭代步数，表示当前训练到了哪一步。\n", "   - $ \\text{warmup\\_iters} $ 是预热阶段结束的步数，同时也是余弦退火阶段开始的迭代步数。\n", "   - $\\text{it} - \\text{warmup\\_iters}$代表现在是预热阶段的第几步。\n", "<br><br>\n", "2. **分母：$\\text{lr\\_decay\\_iters} - \\text{warmup\\_iters}$**\n", "   - $ \\text{lr\\_decay\\_iters} $ 是学习率退火结束时的迭代步数。\n", "   - $ \\text{warmup\\_iters} $是预热阶段结束的步数，同时也是余弦退火阶段开始的迭代步数。\n", "   - $\\text{lr\\_decay\\_iters} - \\text{warmup\\_iters}$ 表示余弦退火阶段的总步数。\n", "\n", "因此你会发现，decay_ratio相当于是整个余弦退火流程的进度条，它表示现在进展到了余弦退火的具体阶段。例如——\n", "\n", "预热阶段是0\\~20步、余弦退火阶段是21\\~50步。\n", "\n", "- 假设现在迭代进展到了25步，则decay_ratio等于(25-20 / 50-20) = 1/6，代表退火已进行了1/6。\n", "\n", "- 假设现在迭代进展到了45步，则decay_ratio等于(45-20 / 50-20) = 5/6，代表退火已进行了5/6的程度。\n", "\n", "decay_ratio的取值范围是(0,1]，它乘在三角函数的自变量上、控制三角函数的值 ↓"]}, {"cell_type": "code", "execution_count": 3, "id": "a818512c-56df-47f1-9a6b-8b1b8822daad", "metadata": {"jupyter": {"source_hidden": true}}, "outputs": [{"data": {"image/png": "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\n", "text/plain": ["<Figure size 576x360 with 1 Axes>"]}, "metadata": {"needs_background": "light"}, "output_type": "display_data"}], "source": ["import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "# 定义 x (decay_ratio) 范围从 -1 到 1\n", "x = np.linspace(0, 1, 1000)\n", "\n", "# 计算 y (coeff)\n", "y = 0.5 * (1 + np.cos(np.pi * x))\n", "\n", "# 绘制曲线\n", "plt.figure(figsize=(8, 5))\n", "plt.plot(x, y, label=r'$\\text{coeff} = \\frac{1}{2} (1 + \\cos(\\pi \\cdot x))$', linewidth=2)\n", "plt.xlabel(\"decay_ratio\")\n", "plt.ylabel(\"coeff\")\n", "plt.title(\"Cosine Annealing Coefficient\")\n", "plt.axhline(y=0, color='black', linestyle='--', linewidth=0.5)\n", "plt.axvline(x=0, color='black', linestyle='--', linewidth=0.5)\n", "plt.legend()\n", "plt.grid()\n", "\n", "# 显示图像\n", "plt.show()"]}, {"cell_type": "markdown", "id": "1d25dc99-f169-4524-ad55-90cc63805241", "metadata": {}, "source": ["随着迭代的进行、明显decay_ratio越来越大、会引起余弦系数coeff越来越小。这个越来越小的值乘以$(\\text{max\\_lr} - \\text{min\\_lr})$，生成的值也越来越小，最终在整个公式上、引导学习率逐渐靠近设置好的$\\text{min\\_lr}$ ↓ 以此来实现衰减。\n", "\n", "$$\n", "\\text{lr} = \\text{min\\_lr} +  (\\text{max\\_lr} - \\text{min\\_lr}) \\times \\frac{1}{2} \\left(1 + \\cos\\left(\\pi \\cdot \\text{decay\\_ratio}\\right)\\right)\n", "$$"]}, {"cell_type": "markdown", "id": "8918cf8e-3fee-4421-8f2b-8e4e85395cbb", "metadata": {}, "source": ["<font color=\"red\">**虽然余弦退火给人感受上是一个学习率降低的过程，但从代码实现的角度来说，却是在最低的learning_rate上逐渐降低比例的过程**</font>，假设规定的目标learning_rate是0.01，现在的学习率是0.05，那我们实现的其实是 ↓\n", "\n", "<center>0.01 + (0.05-0.01) * 100%<br><br>\n", "0.01 + (0.05-0.01) * 98%<br><br>\n", "0.01 + (0.05-0.01) * 96%<br><br>\n", "……\n", "\n", "这样的下降过程。"]}, {"cell_type": "markdown", "id": "04ac1372-69db-4f4e-a8ed-805a7a6c0048", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "51dc0ffe-42f1-4597-84db-fe184e3733b1", "metadata": {}, "source": ["- 线性预热 + 余弦退火的代码实现"]}, {"cell_type": "code", "execution_count": 10, "id": "6801ce4d-8492-4860-8947-d1be1f4914c1", "metadata": {}, "outputs": [], "source": ["def get_lr(it, all):\n", "    warmup_iters = args.warmup_iters\n", "    lr_decay_iters = all\n", "    min_lr = args.learning_rate / 10\n", "\n", "    if it < warmup_iters:\n", "        return args.learning_rate * it / warmup_iters\n", "    if it > lr_decay_iters:\n", "        return min_lr\n", "    decay_ratio = (it - warmup_iters) / (lr_decay_iters - warmup_iters)\n", "    assert 0 <= decay_ratio <= 1\n", "    coeff = 0.5 * (1.0 + math.cos(math.pi * decay_ratio))\n", "    return min_lr + coeff * (args.learning_rate - min_lr)"]}, {"cell_type": "markdown", "id": "74565735-2bfb-4a2b-b5ab-5d99ee65be88", "metadata": {}, "source": ["代码说明 ↓"]}, {"cell_type": "markdown", "id": "9faf019e-2511-4815-9346-7fb65ddc1355", "metadata": {}, "source": ["```python\n", "def get_lr(it, all):\n", "    # 获取当前学习率函数，输入参数为当前迭代步数 it 和总训练迭代步数 all\n", "\n", "    warmup_iters = args.warmup_iters\n", "    # 从参数中获取学习率预热的迭代步数（warmup_iters 表示预热阶段持续的迭代次数）\n", "\n", "    lr_decay_iters = all\n", "    # 设置学习率衰减的总迭代步数，通常等于训练的总迭代步数\n", "\n", "    min_lr = args.learning_rate / 10\n", "    # 设置最小学习率，通常是初始学习率的十分之一，表示学习率的下界\n", "\n", "    if it < warmup_iters:\n", "        # 如果当前迭代步数小于预热结束的步数，则处于学习率预热阶段\n", "        return args.learning_rate * it / warmup_iters\n", "        # 学习率从 0 线性增长到初始学习率 args.learning_rate\n", "\n", "    if it > lr_decay_iters:\n", "        # 如果当前迭代步数超过衰减的最大步数，说明已经进入训练末期\n", "        return min_lr\n", "        # 返回最小学习率，确保学习率不会进一步下降\n", "\n", "    # 如果当前步数位于预热结束和衰减结束之间，则计算余弦退火学习率\n", "    decay_ratio = (it - warmup_iters) / (lr_decay_iters - warmup_iters)\n", "    # 计算衰减比例 decay_ratio，表示当前迭代步数在衰减阶段的相对进度\n", "    # decay_ratio 的取值范围为 [0, 1]\n", "\n", "    assert 0 <= decay_ratio <= 1\n", "    # 断言：确保衰减比例在 [0, 1] 范围内，防止非法值导致错误\n", "\n", "    coeff = 0.5 * (1.0 + math.cos(math.pi * decay_ratio))\n", "    # 使用余弦函数计算系数 coeff，该系数在 [1, 0] 之间变化\n", "    # 初始时 coeff = 1（decay_ratio=0），逐渐衰减到 coeff = 0（decay_ratio=1）\n", "\n", "    return min_lr + coeff * (args.learning_rate - min_lr)\n", "    # 当前学习率等于最小学习率加上余弦系数与最大变化范围的乘积\n", "    # 公式解释：学习率在最小学习率和初始学习率之间按照余弦函数平滑下降\n", "```"]}, {"cell_type": "markdown", "id": "79fef087-e253-42ca-bfd3-deb50bb99329", "metadata": {}, "source": ["同时我们还可以有更适应大型架构的、在预热之后将学习率保持一段时间、之后再进行退火的方案 ↓"]}, {"cell_type": "markdown", "id": "e9bbc5fd-e6c7-46a4-9beb-42c659ee4a36", "metadata": {}, "source": ["```python\n", "def get_lr_hold(it, all):\n", "    warmup_iters = args.warmup_iters  # 预热迭代数\n", "    hold_iters = int(0.1 * all)  # 设定一个保持阶段，比如总步数的10%\n", "    lr_decay_iters = all - hold_iters  # 余弦衰减从这里开始\n", "    min_lr = args.learning_rate / 10\n", "\n", "    if it < warmup_iters:\n", "        return args.learning_rate * it / warmup_iters  # 线性预热\n", "\n", "    if it < warmup_iters + hold_iters:\n", "        return args.learning_rate  # 保持高学习率一段时间\n", "\n", "    decay_ratio = (it - warmup_iters - hold_iters) / (lr_decay_iters - warmup_iters)\n", "    coeff = 0.5 * (1.0 + math.cos(math.pi * decay_ratio))\n", "    \n", "    return min_lr + coeff * (args.learning_rate - min_lr)  # 余弦退火"]}, {"cell_type": "markdown", "id": "5359d6c1-26f6-4776-93a7-2e0e41cc19f5", "metadata": {}, "source": ["### 2.4 模型初始化与断点续训机制"]}, {"cell_type": "markdown", "id": "9262a9b4-0f9f-4e86-b8b1-2f8302540a51", "metadata": {}, "source": ["断点续训机制的意义在于保证深度学习模型在长时间训练过程中，即使因系统崩溃、设备重启或意外中断，也能从最近一次保存的状态继续训练，而无需从头开始。对于计算资源昂贵的深度学习任务，特别是大规模语言模型（LLM）或计算密集型任务，如计算机视觉和强化学习，断点续训可以有效减少训练时间和成本，提高训练的可靠性。"]}, {"cell_type": "markdown", "id": "610c4414-3cd9-46ce-b023-439dd7537294", "metadata": {}, "source": ["```python\n", "#定义init_model函数、用于计算参数量、初始化模型参数、并且进行断点序训\n", "def init_model():\n", "    # 定义一个辅助函数，用于计算模型参数\n", "    def count_parameters(model):\n", "        return sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "\n", "    # 初始化 Transformer 模型，并将其移动到指定设备（CPU 或 GPU）\n", "    # lm_config是设置在主程序中、从我们之前定义好的LMConfig()类中导入的所有参数\n", "    # 而Transformer则是我们需要在model.py中定义好的包含全部流程的模型\n", "    model = Transformer(lm_config).to(args.device)\n", "\n", "    # 根据是否使用 Mixture of Experts (MoE) 机制，动态设置模型路径后缀\n", "    moe_path = '_moe' if lm_config.use_moe else ''\n", "    \n", "    # 定义检查点路径（checkpoint_path），用于存储或加载模型\n", "    # `args.save_dir` 是保存目录，`args.model_name` 是用户提供的模型名称\n", "    checkpoint_path = f'{args.save_dir}/{args.model_name}.pth'\n", "\n", "    # 如果存在已保存的模型检查点，则加载该模型\n", "    if os.path.exists(checkpoint_path):\n", "        # 记录日志，提示用户正在加载模型\n", "        Logger(f\"加载模型检查点 {checkpoint_path}\")\n", "        # 加载模型权重，并适配设备\n", "        model.load_state_dict(torch.load(checkpoint_path, map_location=args.device))  \n", "    else:\n", "        # 如果不存在已经保存的检查点，则从头开始训练\n", "        # 记录日志，提示用户将进行从零开始的训练\n", "        Logger(f\"没有找到模型检查点，开始从头训练\")  \n", "    \n", "    # 计算并打印模型的总参数量（百万级别），方便查看模型规模\n", "    Logger(f'LLM总参数量：{count_parameters(model) / 1e6:.3f} 百万')\n", "\n", "    return model  # 返回初始化后的模型\n", "\n", "```"]}, {"cell_type": "markdown", "id": "c00c0c76-3dd1-4ef6-a243-a525571e0c92", "metadata": {}, "source": ["### 2.5 预训练原理与预训练数据处理脚本"]}, {"cell_type": "markdown", "id": "2a863134-38aa-4862-9a48-89692bc2e79c", "metadata": {}, "source": ["在之前的课程当中、我们为大家梳理了基于深度学习的基本训练流程——\n", "\n", "---\n", "\n", "<font color=\"red\">**在定义好模型之后、深度学习训练的基本步骤与流程如下**——\n", "\n", "1. **数据分批/打乱/分训练集验证集等操作、定义使用的CPU/GPU等细节**\n", "\n", "2. **初始化模型参数（Parameter Initialization）**\n", "\n", "3. **训练循环（training loop）**：\n", "> **正向传播（Forward Pass）**：输入数据通过模型，得到预测输出。\n", "> \n", "> **计算损失（Compute Loss）**：根据模型的预测结果和真实标签，计算损失函数值。\n", "> \n", "> **反向传播（Backward Pass）**：根据损失函数的值，计算模型中各参数的梯度（使用自动微分）。\n", "> \n", "> **参数更新（Update Parameters）**：根据计算出的梯度，使用优化器更新模型的参数。\n", "> \n", "> **梯度清零（Zero Gradients）**：为了避免梯度累积，需要在每次迭代前将梯度归零。\n", "\n", "4. **训练监控与模型保存**\n", "\n", "---"]}, {"cell_type": "markdown", "id": "77525715-ca85-4b3c-a635-354a08cb90b1", "metadata": {}, "source": ["在这个流程中，模型的迭代方向由“损失函数”决定，而损失函数是用于衡量真实可靠的答案与模型预测出的结果之间的差异，因此**从哪里获得真实可靠的答案、使用什么损失函数、怎么衡量模型预测出的结果是不是足够好**就成为深度学习流程中的核心问题之一。为模型设置正确的目标是所有训练的核心诉求、无论是深度学习、强化学习还是其他琳琅满目的学习方式都是如此。\n", "\n", "常规来说，我们按照<font color=\"green\">**真实标签容易获得的程度**</font>来划分、有以下两种最经典的学习模式 ↓\n", "\n", "|任务类型|标签来源|适合的学习类型|适用的损失函数|\n", "|:-:|:-:|:-:|:-:|\n", "|图像识别、情感分类<br>年龄预测、双语翻译<br>信贷预测、行为预测|**标签是客观存在的**<br>只要人工标注上即可|有监督学习|交叉熵损失函数、MSE|\n", "|文字生成、图像生成<br>图像降噪、客户分群<br>降维流程、异常检测|**一般没有绝对正确的标签，<br>只要合理即可**<br>或者标签很难标注出来、<br>根本不可能大批量获得|自监督或者<br>无监督学习|依具体任务而定|"]}, {"cell_type": "markdown", "id": "24ea3f0a-9b99-4842-9b59-f68b77fdd45e", "metadata": {}, "source": ["因此不难发现、**<font color=\"green\">每种不存在客观标签的任务都需要自己独特的学习流程</font>**，这也导致在文字生成这一领域我们有大量可用的训练方法。在文字生成领域中，无论我们希望文字完成什么任务，都要构建“对文字具备理解能力的模型”，下面介绍两种最常见的文字生成领域学习方法。"]}, {"cell_type": "markdown", "id": "8fa0483e-bfdd-43eb-b75f-2780352625a4", "metadata": {}, "source": ["#### 2.5.1 两种文字模型的预训练方式"]}, {"cell_type": "markdown", "id": "b4b83729-42c8-49e3-9392-a3de84c6e751", "metadata": {}, "source": ["- **因果语言模型 Casual Language Model**"]}, {"cell_type": "markdown", "id": "219e43ea-3469-4119-85d5-8fb4a481f645", "metadata": {}, "source": ["因果语言模型（Casual Language Model）是一种“对句子的下一个字进行预测”的自监督学习方法，它的基本流程如下：\n", "\n", "1. 准备一段文字序列作为输入特征X，例如——\n", "\n", "**<font color =\"red\"><center>竹外桃花三两枝，春江水暖鸭先知**\n", "\n", "    在这个过程中，每个字/词生成一个词向量，作为一行。\n", "\n", "2. 将序列向左移动一个单位作为标签y，例如——\n", "\n", "**<font color =\"red\"><center>外桃花三两枝，春江水暖鸭先知**\n", "\n", "    在这个过程中，每个字/词被编码成一个数字，作为一个标签。然而，由于序列向左移动了一个单位，因此整个句子比原来的序列少了一个字，为了让特征X与标签y能够匹配，我们往往会删除特征X中的最后一个token，将结构转变为 ↓\n", "    \n", "**<font color =\"red\"><center>X ：竹外桃花三两枝，春江水暖鸭先<br>Y ：外桃花三两枝，春江水暖鸭先知**\n", "\n", "3. 让每一个字/词结合自己前面所有的字、预测下一个字/词，**对比模型生成的下一个词的编号和真实的下一个词的编号之间的差异**，以此来计算损失函数，并逼迫模型理解语义。"]}, {"cell_type": "markdown", "id": "6b61f6eb-6940-4f2b-9513-f75b6803a687", "metadata": {}, "source": ["这就是GPT等当代大语言模型所采用的自监督学习方式，也**几乎是所有生成模型所采用预训练方式，它被称为“自回归预测”**。这个方式不需要人工打标签，**且每次推理只能输出一个字**。因此对大语言模型来说，要想输出一句话、一个段落，就需要对模型生成过程进行循环。"]}, {"cell_type": "markdown", "id": "b8f81cd7-7644-4945-80b2-5a55097f5974", "metadata": {}, "source": ["需要注意的是，在这个过程中，编码后的数据呈现为 ↓ \n", "\n", "|token|embd1|embd2|...|embd_dim|\n", "|:-:  |:-:  |:-:  |:-:|:-:|\n", "|竹|...|...|...|...|...|\n", "|外|...|...|...|...|...|\n", "|桃|...|...|...|...|...|\n", "|花|...|...|...|...|...|\n", "|三|...|...|...|...|...|\n", "\n", "这个矩阵在经过带**前瞻掩码的注意力机制**之后会转变为 ↓\n", "\n", "|token|embd1|embd2|...|embd_dim|\n", "|:-:  |:-:  |:-:  |:-:|:-:|\n", "|竹|...|...|...|...|...|\n", "|竹外|...|...|...|...|...|\n", "|竹外桃|...|...|...|...|...|\n", "|竹外桃花|...|...|...|...|...|\n", "|竹外桃花三|...|...|...|...|...|"]}, {"cell_type": "markdown", "id": "3c7ceca0-b5b4-41ca-8513-4c82e85c7b78", "metadata": {}, "source": ["因此GPT等大语言模型是在用“前半部分句子”预测下一个词、而不是用上一个词预测下一个词。在最终的计算过程中、为了对比模型预测的“下一个词”和“真实的下一个词”之间的差异，我们计算的实际是模型预测出的词的编号与真实的下一个词的编号之间的差异、本质就是计算类别的差异，因此我们使用的是分类算法中常见的**多分类交叉熵损失**作为损失函数。"]}, {"cell_type": "markdown", "id": "6bcc75d4-365d-4247-90c5-bb5c35486d06", "metadata": {}, "source": ["- **掩码语言模型 Masked Language Model**"]}, {"cell_type": "markdown", "id": "cbdb007f-4349-4d76-834d-a258d1f8e2a5", "metadata": {}, "source": ["掩码语言模型（Masked Language Model）是一种“把句子的中间词掩盖掉、让模型预测被掩盖的词”的自监督学习方法，它的基本流程如下：\n", "\n", "1. 准备一段文字序列，例如——\n", "\n", "**<font color =\"red\"><center>竹外桃花三两枝，春江水暖鸭先知**\n", "\n", "2. 在序列中随机掩盖一些词、形成带掩码的序列、作为模型的输入X，例如——\n", "\n", "**<font color =\"red\"><center>竹外[masked]花三两[masked]，[masked]江水暖[masked]先知**\n", "\n", "在这个过程中，每个字/词生成一个词向量，作为一行。<br><br>\n", "\n", "3. 使用掩码前的文字序列作为标签y，在这个过程中，每个字/词都转化成一个字，作为标签。<br><br>\n", "\n", "4. 将上述掩码后的句子X放入注意力机制，**取消前瞻掩码、并在生成的所有行中、只计算被masked掉的行的损失函数**，以此来逼迫模型理解语义。"]}, {"cell_type": "markdown", "id": "468bba33-0be7-4107-94de-296aa069d5e7", "metadata": {}, "source": ["具体地来说，经过掩码后、编码的数据呈现为 ↓\n", "\n", "|token|embd1|embd2|...|embd_dim|\n", "|:-:  |:-:  |:-:  |:-:|:-:|\n", "|竹|...|...|...|...|...|\n", "|外|...|...|...|...|...|\n", "|[masked]|...|...|...|...|...|\n", "|花|...|...|...|...|...|\n", "|三|...|...|...|...|...|\n", "\n", "由于进行前瞻掩码、所以经过多头注意力机制后、所有行都可以获得上下文信息（相比之下、带前瞻掩码的注意机制输出的结果中、每行只能获得上文信息、不能获得下文信息）。因此在掩码语言模型的注意力机制中我们会得到——\n", "\n", "|token|embd1|embd2|...|embd_dim|\n", "|:-:  |:-:  |:-:  |:-:|:-:|\n", "|竹外 [MASK] 花三两 [MASK]，[MASK] 江水暖 [MASK] 先知|...|...|...|...|...|\n", "|竹外 [MASK] 花三两 [MASK]，[MASK] 江水暖 [MASK] 先知|...|...|...|...|...|\n", "|竹外 [MASK] 花三两 [MASK]，[MASK] 江水暖 [MASK] 先知|...|...|...|...|...|\n", "|竹外 [MASK] 花三两 [MASK]，[MASK] 江水暖 [MASK] 先知|...|...|...|...|...|\n", "|竹外 [MASK] 花三两 [MASK]，[MASK] 江水暖 [MASK] 先知|...|...|...|...|...|"]}, {"cell_type": "markdown", "id": "5e923031-97fc-4311-8aca-a05b6ed199b7", "metadata": {}, "source": ["然后我们再从这个序列中、只取第3、第6、第9、第13行出来与原始未掩码的序列的第3、第6、第9、第13行计算损失，其他行的结果就不再计算。同样，我们考虑的是“模型预测出的字”和“被掩码的真实的字”之间的差异，因此本质还是计算编号与编号的差异、因此掩码语言模型使用的也依然是**多分类交叉熵损失函数**。\n", "\n", "这就是掩码语言模型的预训练方式，如你所见，这个流程中输出的并不是“下一个字”，因此也不能用于文字生成任务，大部分时候掩码语言模型预训练被用于训练“文字理解算法”，这样训练出来的模型能够很好地理解上下文、但不能对文字进行回应。因此这样的算法常常被我们接上“输出层”来完成基于文字理解的有监督任务。"]}, {"cell_type": "markdown", "id": "815442f5-907c-4461-8219-91d9cc0b0783", "metadata": {}, "source": ["**为什么在看实际的预训练代码之前，我们要讲解不同的预训练流程呢**？不难发现，不同的预训练流程提供的输入数据X和标签y不同，同时可能存在不同的损失函数，如果不理解预训练流程、我们就无法完成数据的划分、标签的划分、更无法设置损失函数、完成最终的训练脚本。"]}, {"cell_type": "code", "execution_count": null, "id": "3a8214aa-4ed0-47bc-a2bd-380983272825", "metadata": {}, "outputs": [], "source": ["raw data ==> 去重、火星文、广告/暴力涩情/脏话/低俗、构建算法来进行祛毒、繁体/简体字、乱码、链接、图像\n", "分块/压缩/打包 ==> jsonl/parquet/csv + 多线程并行数据拉取\n", "jsonl（错误检查、筛选） ==> bin、parquet\n", "分词（tokenzier）、编码（0,1,2,3,4），样本化(X,y)，嵌入（embedding）\n", "\n", "\n", "样本化(X,y)  ==> 分割整理 (batch_size, seq_len)、Tensor化、加载方式、存储方式、能够跟分布式预训练配合的一系列设置\n", "\n", "\n", "Dataset.py"]}, {"cell_type": "markdown", "id": "c765c8c3-70ba-4611-8a7b-2c550b5d0687", "metadata": {}, "source": ["#### 2.5.2 因果语言模型的数据处理方法"]}, {"cell_type": "markdown", "id": "6dae9194-72da-4d2f-901e-87b407561f55", "metadata": {}, "source": ["在之前的课程中、我们已经对原始的JSONL数据进行了处理，我们检查了其有效性、并且将JSONL转变成了更高效的二进制bin文件进行存储。现在我们距离将数据放入预训练架构中仅有一步之遥。通常、在进行真正的预训练之前，我们需要定义**继承自pytorch的Dataset或者继承自transformers的PretrainedDataset**的类帮助我们确认单个样本的加载流程，同时还会需要**Pytorch中所规定的Dataloader**类帮助我们定义一整个批次数据的加载流程，最后再加上分布式的数据处理技巧，三重规则帮助我们一起实现数据的整合。\n", "\n", "在本次续联中，这个**继承自Dataset**的类被我们命名为`PretrainDataset`。在`PretrainDataset`中，除了定义数据的加载方式，我们还需要将数据明确与PyTorch、Transformers、DeepSpeed、Megatron-LM等分部署框架的需求链接起来、还需要定义单一样本的具体样式。具体地来说，在我们自定义的`PretrainDataset`中，我们需要对数据做出以下的处理：\n", "\n", "- **明确数据加载方式**\n", "\n", "数据的加载方式决定了内存占用和数据访问速度，因此在训练中需要慎重考虑。**通常我们谈到数据加载，是指从硬盘中将数据加载到CPU内存或GPU显存的过程**。对任意大模型而言，在数据加载流程中，我们需要有以下设置——\n", "\n", "1. **支持多文件载入的设置**。大部分大语言模型训练的数据都不是一个jsonl或bin文件，而是很多文件共同构成数据集 ↓ ，有时候我们甚至需要将多个文件中加载的内容合并成一个批次或一个样本，因此我们的`PretrainDataset`需要支持多文件一次性加载（尽管在MateConv Mini中我们加载的是一个文件）。\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/28.png)"]}, {"cell_type": "markdown", "id": "2b4ed92d-621e-456f-b0ad-45d89da9bfa0", "metadata": {}, "source": ["2. **支持memmap进行磁盘映射加载的设置**。文件有很多种加载形式，最简单的pd.read_csv可以加载文件、pyarrow、dask也可以分布式超快速加载文件，而对于巨型的jsonl数据、我们需要依赖于磁盘映射加载。**磁盘映射是一种按需加载（Lazy Loading）机制，它只在磁盘中创建内存映射、允许我们访问小部分数据、但实际上并不把数据加载到内存或显存中**。\n", "\n", "    当我们使用较小的数据集和普通数据加载方式时，在实例化`PretrainDataset`类的时候、全体数据集就会被加载到内存/显存中（通常默认加载到CPU的内存RAM中）；而当我们使用`memmap`进行加载时、只会在RAM中创建很小的内存映射，数据不会真正被放入内存中。只有当我们开始进行训练循环、需要把数据放置到GPU上时，数据才会真正地按批次被加载进来、这样就可以避免OOM错误的产生。这个操作在计算机视觉的世界中非常常见、毕竟视觉数据（无论是图像还是视频）的内存占用都非常可怕。\n", "\n", "    当然，如果不把数据真正加载到内存中、就会产生交互问题。在传统加载时，我们需要的是内存到显存的信息交互，但使用`memmap`时，我们需要的是硬盘到显存的交互，因此会收到硬盘I/O限制，因此加载速度会相对较慢。"]}, {"cell_type": "markdown", "id": "060149ff-655e-40aa-91c5-e96b127070c7", "metadata": {}, "source": ["| 加载方式  | 适用数据规模 | 内存使用 | 访问速度 | 适用场景 |\n", "|-----------|-----------|-----------|-----------|-----------|\n", "| **`memmap=True`** | TB 级大数据 | 低（仅映射） | 适中（磁盘 I/O 依赖） | 大规模分布式训练 |\n", "| **`memmap=False`** | 小～中等规模（几十 GB） | 高（全加载） | 快（RAM 访问） | 本地 GPU 训练 |\n", "\n", "    如果数据格式是 **多个 JSONL 或 Parquet 文件**，我们通常先**处理成二进制格式（如 `.bin`）**，这样可以直接使用 `np.fromfile()` 或 `np.memmap()` 进行高效加载了。"]}, {"cell_type": "markdown", "id": "d984c8c3-4c36-482b-9a7f-4cfbb6d6fd14", "metadata": {}, "source": ["如果数据规模过大，还可以**从远程存储（如 S3、HDFS）进行流式加载**等更先进的技术来避免本地存储压力，Huggingface中的load_dataset就支持流式加载。"]}, {"cell_type": "markdown", "id": "9c3b0c0e-d8a6-4d2a-bf23-f722f22cad7d", "metadata": {}, "source": ["3. **在加载的同时对数据进行形状调整、为后续分批次做准备**。通常来说，我们会规定一个批次中的`max_seq_len`，并按照该`max_seq_len`的倍数来取给到模型的token总数，这样所有的token就可以被均匀划分成具体的批次了。在DeepSpeed/Megatron-LM进行预训练时，我们通常倾向于使用静态的batch_size和静态的seq_len，因此在对数据进行准备的时候就可以直接分好`max_seq_len`。"]}, {"cell_type": "code", "execution_count": 1, "id": "25ab5c27-d94e-4674-a691-65b260848d09", "metadata": {}, "outputs": [], "source": ["import json\n", "import random\n", "import re\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from torch.utils.data import Dataset, DataLoader\n", "import torch\n", "from sklearn.model_selection import train_test_split\n", "import os\n", "\n", "os.environ[\"TOKENIZERS_PARALLELISM\"] = \"false\"\n", "\n", "class PretrainDataset(Dataset):\n", "    # 在代码的init部分，就需要定义数据的载入形式\n", "    # 需要输入的参数是，数据所在目录、最大的seq_len，以及是否进行memmap映射\n", "    def __init__(self, data_path_lst, max_length=512, memmap=False):\n", "        super().__init__()\n", "\n", "        if memmap:\n", "            # 如果是使用memmap模式，则使用np.memmap进行加载\n", "            # 并且只导入第一个文件、按照该文件的格式对数据格式进行修改\n", "            with open(data_path_lst[0], 'r') as f:\n", "                # 把指针移动到文件夹末尾、获取文件的字节大小\n", "                # 通过总字节大小除以uint16元素字节的方式\n", "                # 计算一共有多少个uint16元素，也就是计算一共有多少token\n", "                # 在控制结构的时候，用总token整除max_length\n", "                nbytes = f.seek(0, 2)\n", "                flen = f.tell() // np.dtype('uint16').itemsize\n", "            self.data = np.memmap(data_path_lst[0], dtype=np.dtype('uint16')\n", "                                  , shape=(flen // max_length, max_length))\n", "        else:\n", "            # 如果不是memmap模式，那就一次性读入目录中所有文件（np.fromfile）\n", "            data_lst = []\n", "            for data_path in data_path_lst:\n", "                with open(data_path, 'rb') as f:\n", "                    # 读入的格式是unit16，并且将所有数据保存在列表中\n", "                    data = np.fromfile(f, dtype=np.uint16)\n", "                    data_lst.append(data)\n", "            # 对数据进行合并、所有token链接在一起，依据token进行max_length划分\n", "            data = np.concatenate(data_lst)\n", "            data = data[:max_length * int(len(data) / max_length)]\n", "            # np.random.shuffle(data)\n", "            # 这里的-1就是分好的batch_size了\n", "            self.data = data.reshape(-1, max_length)\n", "        print(\"memmap:{} train data.shape:{}\".format(memmap, self.data.shape))\n", "        print(\"downloading finished.....\")\n", "\n", "    def __len__(self):\n", "        return self.data.shape[0]\n", "\n", "    def __getitem__(self, index: int):\n", "        sample = self.data[index]\n", "        X = np.array(sample[:-1]).astype(np.int64)\n", "        Y = np.array(sample[1:]).astype(np.int64)\n", "\n", "        return torch.from_numpy(X), torch.from_numpy(Y)"]}, {"cell_type": "markdown", "id": "de60f01e-9f0a-4204-b646-f789e1e3d3e6", "metadata": {}, "source": ["除了定义数据加载方式、并且对格式进行规定之外，在`PretrainDataset`中我们还需要关注 ↓"]}, {"cell_type": "markdown", "id": "26911020-83af-49da-bc25-6b26563df816", "metadata": {}, "source": ["- **`__len__`与`__getitem__`方法的实现**"]}, {"cell_type": "markdown", "id": "708de017-bd5d-4670-957f-cdb1293ece53", "metadata": {}, "source": ["`__len__()`是返回`return self.data.shape[0]` 或 `return len(self.data)`的内部方法，为 `DataLoader` 提供争取的token综述、确保`Dataloader`能正确计算 `batch_size`。\n", "\n", "`__getitem__()`则是定义单个样本结构、并按索引获取单个样本的内部方法。其输入的数据通常是 **Token ID**，形如 `[101, 2345, 6789, 102]`。一般来说，对当我们运行`dataloader`类时，`dataloader`会按照`__getitem__()`中定义的方法将模型批量取出。\n", "\n", "这两个内部类为`dataloader`提供的支持确保了数据可以被用于任何依托于`dataloader`的框架，例如DeepSpeed和Megatron-LM。"]}, {"cell_type": "markdown", "id": "ab5638d3-6fc5-4eac-91bd-271b66c776f1", "metadata": {}, "source": ["由于`__getitem__()`中需要定义单个样本的结构，因此我们一般需要在`__getitem__()`内部完成数据整理，比如分割数据中的X和y，对数据添加适合的掩码、如果需要的话、对较短的序列进行Padding填充、对较长的序列进行Truncation截断等等。`__getitem()`还可以与 Hugging Face中的`collate_fn`联用、对数据进行更细致的定义和处理。在本次预训练中我们的数据比较规整、因此本次`__getitm__()`定义较为简单 ↓ "]}, {"cell_type": "code", "execution_count": null, "id": "fd616c1e-d0c0-4391-8dbb-9f41a3f0e700", "metadata": {}, "outputs": [], "source": ["import json\n", "import random\n", "import re\n", "\n", "import pandas as pd\n", "import numpy as np\n", "from torch.utils.data import Dataset, DataLoader\n", "import torch\n", "from sklearn.model_selection import train_test_split\n", "import os\n", "\n", "os.environ[\"TOKENIZERS_PARALLELISM\"] = \"false\"\n", "\n", "class PretrainDataset(Dataset):\n", "    def __init__(self, data_path_lst, max_length=512, memmap=False):\n", "        super().__init__()\n", "\n", "        if memmap:\n", "            with open(data_path_lst[0], 'r') as f:\n", "                nbytes = f.seek(0, 2)\n", "                flen = f.tell() // np.dtype('uint16').itemsize\n", "            self.data = np.memmap(data_path_lst[0], dtype=np.dtype('uint16')\n", "                                  , shape=(flen // max_length, max_length))\n", "        else:\n", "            data_lst = []\n", "            for data_path in data_path_lst:\n", "                with open(data_path, 'rb') as f:\n", "                    data = np.fromfile(f, dtype=np.uint16)\n", "                    data_lst.append(data)\n", "            data = np.concatenate(data_lst)\n", "            data = data[:max_length * int(len(data) / max_length)]\n", "            self.data = data.reshape(-1, max_length)\n", "        print(\"memmap:{} train data.shape:{}\".format(memmap, self.data.shape))\n", "        print(\"downloading finished.....\")\n", "\n", "    def __len__(self):\n", "        # 读取数据的shape[0]，也就是之前读取的总token数\n", "        return self.data.shape[0]\n", "\n", "    def __getitem__(self, index: int):\n", "        # 允许输入索引的编号、对数据进行索引\n", "        sample = self.data[index]\n", "        \n", "        # 按照因果语言模型的模式、最后一个字之前的所有字是X\n", "        # 并且按照int64进行编码\n", "        X = np.array(sample[:-1]).astype(np.int64)\n", "        # 向左移动一个单位、整个序列作为y\n", "        # 并且按照int64进行编码\n", "        Y = np.array(sample[1:]).astype(np.int64)\n", "\n", "        return torch.from_numpy(X), torch.from_numpy(Y)"]}, {"cell_type": "markdown", "id": "8f680f1f-18ad-4f3e-bfc5-6e49feefde7c", "metadata": {}, "source": ["这段代码被我们单独编码为`Dataset.py`脚本，并放置在model文件夹下 ↓ 。和这个py文件放在一起的还有之前我们定义的model.py以及模型的参数文件LMConfig.py。"]}, {"cell_type": "markdown", "id": "55241a40-f563-45f4-876e-15a892c41f7b", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/93.png)"]}, {"cell_type": "markdown", "id": "f828af47-7131-45f1-a280-dd02b7ae8dff", "metadata": {}, "source": ["你可能发现了，在Dataset.py中我们没有设置分布式的支持，这是因为加载数据用的`torch.distributed`等工具可以和`PretrainDataset`类同级来进行使用，因此我们将在预训练的脚本中配置分布式相关的信息。"]}, {"cell_type": "markdown", "id": "6f74087c-ddc3-4c35-8623-dd74ef7eb3c6", "metadata": {}, "source": ["### 2.6 预训练主程序与主流程深度解析"]}, {"cell_type": "markdown", "id": "9f124100-813f-4e1b-aaf8-56c0da614007", "metadata": {}, "source": ["在某些 Python 脚本中，我们会设置一段以`if __name__ == \"__main__\"`开头的代码，这段代码存在代表着当前Python脚本可以作为一个主程序被使用（直接在CLI中与deepspeed命令或者Megatron-LM命令联合使用），也可以被作为一个import到其他脚本中的library来使用。\n", "\n", "作为主程序使用时——\n", "\n", "```shell\n", "deepspeed --master_port 29500 --num_gpus=4 pretrain.py --epochs 30\n", "```\n", "\n", "作为导入的library来使用时——\n", "\n", "```python\n", "from pretrain import get_lr\n", "```\n", "\n", "当这段脚本以被导入的library来使用时，只会运行脚本中定义的各个函数与类，但当这段脚本以主程序方式运行时，除了运行脚本中定义的各个函数、类之外，还会运行`if __name__ == \"__main__\"`下定义的内容。因此，**`if __name__ == \"__main__\"`可以被认为是主程序中最核心的、最上层的程序，它会关联到所有已经定义好的模块、并完成综合性的任务**。因此，它往往也是所有程序中嵌入层次最多、复杂程度最高的代码。"]}, {"cell_type": "markdown", "id": "11afded9-ed55-4315-8e10-2a86988b7075", "metadata": {}, "source": ["在我们整个预训练的脚本中，我们有如下代码 ↓ 它覆盖了以下的全部11个流程：\n", "\n", "\n", "| **步骤编号** | **流程名称**                    | **关键操作** |\n", "|-----------|----------------------------|----------|\n", "| **1️⃣** | **解析命令行参数**（argparse）  | 读取超参数 |\n", "| **2️⃣** | **创建存储目录 & Checkpoint**  | 确保模型存储路径 |\n", "| **3️⃣** | **设置设备 & 随机种子**         | 确保可复现性 |\n", "| **4️⃣** | **初始化 WandB**              | 远程监控训练 |\n", "| **5️⃣** | **开启混合精度训练（AMP）**    | autocast + GradScaler |\n", "| **6️⃣** | **分布式训练（DDP）**          | 多 GPU 训练 |\n", "| **7️⃣** | **数据加载（DataLoader）**      | 并行数据加载 |\n", "| **8️⃣** | **模型 & 优化器初始化**        | Adam 优化器 |\n", "| **9️⃣** | **恢复 Checkpoint**            | 断点续训 |\n", "| **🔟** | **编译模型（Torch 2.0）**      | 提升计算速度 |\n", "| **1️⃣1️⃣** | **训练循环**（train_epoch）    | 实现多轮训练 |"]}, {"cell_type": "markdown", "id": "0d50d7f2-8fbb-459d-8eb5-fb85b67abeea", "metadata": {}, "source": ["让我们来看一下关键的代码设置 ↓"]}, {"cell_type": "markdown", "id": "d7158544-6af6-42d6-88ed-a554bf050d9a", "metadata": {}, "source": ["```python\n", "import argparse #解析并设置命令行参数的库\n", "import torch\n", "import os\n", "import platform\n", "from torch.utils.data import DataLoader\n", "from torch.utils.data.distributed import DistributedSampler\n", "from torch.nn.parallel import DistributedDataParallel\n", "from contextlib import nullcontext\n", "import torch.optim as optim\n", "\n", "#############\n", "# 解析命令行参数\n", "#############\n", "if __name__ == \"__main__\":\n", "    parser = argparse.ArgumentParser(description=\"MateConv Pretraining\")\n", "    \n", "    # 训练参数、包括模型输出目录\n", "    parser.add_argument(\"--out_dir\", type=str, default=\"out\", help=\"Output directory\")\n", "    parser.add_argument(\"--epochs\", type=int, default=20, help=\"Number of epochs\")\n", "    parser.add_argument(\"--batch_size\", type=int, default=32, help=\"Batch size\")\n", "    parser.add_argument(\"--learning_rate\", type=float, default=2e-4, help=\"Learning rate\")\n", "    \n", "    # 设备与精度相关参数\n", "    parser.add_argument(\"--device\", type=str, default=\"cuda:0\" if torch.cuda.is_available() else \"cpu\", help=\"Device to use\")\n", "    parser.add_argument(\"--dtype\", type=str, default=\"bfloat16\", help=\"Data type\")\n", "    \n", "    # 监控与日志记录\n", "    # 这里如果使用WandB、则可以在WandB页面同步查看到模型迭代的损失函数曲线\n", "    parser.add_argument(\"--use_wandb\", action=\"store_true\", help=\"Use Weights & Biases\")\n", "    parser.add_argument(\"--wandb_project\", type=str, default=\"MateConv-Pretrain\", help=\"Weights & Biases project name\")\n", "    \n", "    # 数据加载相关参数\n", "    parser.add_argument(\"--num_workers\", type=int, default=8, help=\"Number of workers for data loading\")\n", "    parser.add_argument(\"--data_path\", type=str, default=\"./dataset/pretrain_data.bin\", help=\"Path to training data\")\n", "    \n", "    # 分布式训练相关参数\n", "    parser.add_argument(\"--ddp\", action=\"store_true\", help=\"Use DistributedDataParallel\")\n", "    parser.add_argument(\"--local_rank\", type=int, default=-1, help=\"Local rank for distributed training\")\n", "    \n", "    # 训练细节\n", "    # 梯度累积技术\n", "    parser.add_argument(\"--accumulation_steps\", type=int, default=8, help=\"Gradient accumulation steps\")\n", "    # 梯度裁剪技术\n", "    parser.add_argument(\"--grad_clip\", type=float, default=1.0, help=\"Gradient clipping threshold\")\n", "    # lr线性预热技术\n", "    parser.add_argument(\"--warmup_iters\", type=int, default=0, help=\"Number of warmup iterations\")\n", "    \n", "    # 日志与模型存储\n", "    parser.add_argument(\"--log_interval\", type=int, default=100, help=\"Logging interval\")\n", "    parser.add_argument(\"--save_interval\", type=int, default=1000, help=\"Model saving interval\")\n", "    parser.add_argument(\"--model_name\", type=str, default=\"pretrain_512\", help=\"模型名称，用于保存和加载检查点\")\n", "\n", "    args = parser.parse_args()\n", "\n", "#############\n", "# 目录与检查点管理\n", "#############\n", "    lm_config = LMConfig()\n", "    max_seq_len = lm_config.max_seq_len\n", "    args.save_dir = os.path.join(args.out_dir)\n", "    os.makedirs(args.save_dir, exist_ok=True)\n", "    os.makedirs(args.out_dir, exist_ok=True)\n", "    checkpoint_path = f'{args.save_dir}/{args.model_name}.pth'\n", "\n", "#############\n", "# 设备与计算环境设置\n", "#############\n", "    tokens_per_iter = args.batch_size * max_seq_len\n", "    torch.manual_seed(1337)\n", "    device_type = \"cuda\" if \"cuda\" in args.device else \"cpu\"\n", "\n", "#############\n", "# Weights & Biases 配置\n", "#############\n", "    \n", "    # 设置W&B所保存的project的名称\n", "    args.wandb_run_name = f\"MateConv-Pretrain-Epoch-{args.epochs}-BatchSize-{args.batch_size}-LearningRate-{args.learning_rate}\"\n", "    \n", "    # WandB初始化\n", "    if args.use_wandb and (not ddp or ddp_local_rank == 0):\n", "        import wandb\n", "        wandb.init(project=args.wandb_project, name=args.wandb_run_name)\n", "    else:\n", "        wandb = None\n", "    \n", "#############\n", "# 混合精度训练配置\n", "#############\n", "    \n", "    # 初始化混合精度训练的条件\n", "    # 如果设备是gpu的话就使用混合精度训练\n", "    # 并且使用autocast()来自动决定某些操作应该使用的精度\n", "    ctx = nullcontext() if device_type == \"cpu\" else torch.cuda.amp.autocast()\n", "\n", "    # 初始化梯度缩放器GradScaler、这里的缩放其实是放大\n", "    # 在精度为float16和bf16的时候、启动梯度缩放（乘以一个放大因子）\n", "    # 如果是其他更大的精度（比如float32），则不启动缩放\n", "    scaler = torch.cuda.amp.GradScaler(enabled=(args.dtype in ['float16', 'bfloat16']))\n", "    \n", "#############\n", "# 分布式训练设置\n", "#############\n", "\n", "    # 从环境变量中取出rank，如果是-1则没有DDP\n", "    ddp = int(os.environ.get(\"RANK\", -1)) != -1  # 是否为 DDP 运行\n", "\n", "    # 将主设备编号设置为0\n", "    ddp_local_rank, DEVICE = 0, \"cuda:0\"\n", "    \n", "    if ddp:\n", "        # 给设备开启分布式模式\n", "        init_distributed_mode()\n", "        args.device = torch.device(DEVICE)\n", "        \n", "        # 在模型中执行分布式数据并行\n", "        model._ddp_params_and_buffers_to_ignore = {\"pos_cis\"}\n", "        model = DistributedDataParallel(model, device_ids=[ddp_local_rank])\n", "\n", "#############\n", "# 数据加载\n", "#############\n", "    # 从目录加载文件list\n", "    data_path_list = [args.data_path]\n", "    # 用PretrainDataset加载文件\n", "    train_ds = PretrainDataset(data_path_list, max_length=max_seq_len, memmap=True)\n", "\n", "    # 对文件进行数据并行的分布式划分\n", "    # 确保每个GPU只加载属于自己的数据子集\n", "    train_sampler = DistributedSampler(train_ds) if ddp else None\n", "\n", "    # 使用Dataloader对数据进行划分\n", "    train_loader = DataLoader(\n", "        train_ds,\n", "        batch_size=args.batch_size,\n", "        pin_memory=True,\n", "        drop_last=False, # 保证所有batch_size中的样本数量相同\n", "        shuffle=False,\n", "        # 每个GPU进程用多个worker线程来并行加载数据\n", "        num_workers=args.num_workers, \n", "        sampler=train_sampler\n", "    )\n", "\n", "#############\n", "# 模型初始化、优化器初始化\n", "#############\n", "    model = init_model()\n", "    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate)\n", "\n", "#############\n", "#  恢复优化器状态（如果存在现有的checkpoint）\n", "#############\n", "    optimizer_state_path = f'{args.save_dir}/{args.model_name}_optimizer.pth'\n", "    if os.path.exists(checkpoint_path):\n", "        if os.path.exists(optimizer_state_path):\n", "            Logger(f\"加载优化器状态 {optimizer_state_path}\")\n", "            optimizer.load_state_dict(torch.load(optimizer_state_path, map_location=args.device))\n", "        else:\n", "            Logger(f\"没有找到优化器状态，使用新的优化器\")\n", "\n", "#############\n", "# 编译模型（如果支持 Torch 2.0）\n", "#############\n", "    if False and platform.system() != 'Windows' and float(torch.__version__.split('.')[0]) >= 2:\n", "        Logger(\"compiling the model... (takes a ~minute)\")\n", "        unoptimized_model = model\n", "        model = torch.compile(model)\n", "\n", "#############\n", "# 训练循环\n", "#############\n", "    iter_per_epoch = len(train_loader)\n", "    for epoch in range(args.epochs):\n", "        train_epoch(epoch, wandb)\n", "```"]}, {"cell_type": "markdown", "id": "5d880021-dde8-4f49-b308-aa99a23db31f", "metadata": {}, "source": ["#### 2.6.1 混合精度训练MTP与量化"]}, {"cell_type": "markdown", "id": "c9fb89c9-4d00-4337-81c0-0013b0a68957", "metadata": {}, "source": ["在神经网络的训练流程中，**不同的计算过程对数值精度的需求不同**。一般来说，计算量大的部分，如 **前向传播（Forward Pass）** 和 **反向传播中的梯度计算**，可以容忍低精度，而**权重更新（Optimizer Update）** 等等流程则需要更高精度来保持数值稳定性。在大模型续联中、数据的精度越高计算负担越大、同时对内存的要求也会越高，因此**我们可以通过在不同的阶段使用不同的精度来实现计算效率/存储效率与计算精度的平衡，这也是混合精度训练的核心概念**。\n", "\n", "我们可以按照是否对高精度有需求将训练流程如下划分 ↓\n", "\n", "---\n", "\n", "**<center>神经网络经典计算流程精度需求表** \n", "\n", "🟢 **低精度**（float16 / bfloat16 ）：一般来说、计算量很大的部分我们愿意牺牲精度换取效率\n", "\n", "🔴 **高精度**（float32）：对稳定性要求很高的部分、我们则会保持高精度、牺牲效率\n", "\n", "| **计算流程**                      | **精度需求**          | **原因** |\n", "|----------------------------------|---------------------|--------|\n", "| **前向传播（Forward Pass）**      | 🟢 **低精度（FP16或BF16）** | 计算量大，FP16 可以提高吞吐量并减少显存使用 |\n", "| **矩阵乘法（GEMM，线性层）**     | 🟢 **低精度（FP16或BF16）** | 计算密集型操作，FP16 提升计算效率 |\n", "| **激活函数（ReLU, GELU）**      | 🟢 **低精度（FP16或BF16）** | 计算量大，对数值精度不敏感 |\n", "| **反向传播（Backward Pass）** | 🟢 **低精度（FP16或BF16）** | 包括计算图中所有的张量（中间变量、激活值、权重等）的梯度，都可以容忍较低的精度。 |\n", "| **权重存储（Model Weights）**    | 🟢 **低精度（FP16或BF16）** | 低精度存储权重，减少显存占用 |\n", "| **权重梯度（Weight Gradient）** | 🟢 **低精度（FP16或BF16）** | 最后用于迭代的梯度，可以使用低精度 |\n", "| **损失计算（Loss Computation）** | 🔴 **高精度（FP32）** | 需要较高数值稳定性，使用 FP32 进行累积 |\n", "| **MoE 门控（MoE Gating）**        | 🔴 **高精度（FP32）** | MoE 门控机制对精度要求高 |\n", "| **注意力计算（Attention Mechanism）** | 🔴 **高精度（FP32 / BF16）** | 注意力机制对精度要求高，低精度可能不稳定 |\n", "| **Softmax / LogSoftmax**        | 🔴 **高精度（FP32）** | 低精度可能导致 underflow，使用 FP32 计算 |\n", "| **指数运算（Exp）**              | 🔴 **高精度（FP32）** | 低精度可能导致溢出或 underflow |\n", "| **归一化（BatchNorm / LayerNorm）** | 🔴 **高精度（FP32 / BF16）** | 低精度导致不稳定，保持 FP32 或 BF16 |\n", "\n", "---\n", "\n", "- **那不同流程中的精度一般是如何相互切换的呢**？\n", "\n", "通常我们会给输入数据（保存的数据）一个具体的梯度、在进行计算之前、会对该梯度进行调整、可能上升到更高的精度进行计算、计算结束后又再下调回原来的精度。从高精度下调到低精度的做法就是**量化Quantization**，而从低精度上升到高精度的做法就是**解量化 Dequantization**。\n", "\n", "<font color=\"red\">**<center>精度由数据可覆盖的范围大小、以及该范围内每个数据点之间的间隔大小共同决定。**\n", "\n", "例如——"]}, {"cell_type": "markdown", "id": "133e6ab5-bfee-4a40-9a89-6c1da87e090c", "metadata": {}, "source": ["<center>[1,2,3,4,5] 的范围 小于 [1,2,3,4,5,6,7,8,9,10] 的范围\n", "    <br><br>\n", "<center>[1,2,3,4,5] 的间隔 大于 [1.1,1.2,1.3,1.4,1.5] 的间隔"]}, {"cell_type": "markdown", "id": "dfbeb4c8-58ec-4c07-9436-b779f0f01f84", "metadata": {}, "source": ["在计算机的定义中、可表示的范围越大精度就越高、同时数字之间间隔越小（分辨率越高）、精度就越高，可惜范围大和间隔小不总能同时达成，如果一个数据格式**范围广且间隔小，那这个数据格式占用的内存就大**。因此，内存、范围和间隔是无法都实现的。量化和解量化这个过程的本质是**改变数据映射范围大小、以及改变数据表示的分辨率**、而非单纯的改变数据格式。BF16和FP16虽然看起来都是16位的表示方式，但是BF16表示范围更大、FP16表示更精细，在数据量巨大的大语言模型训练流程中，BF16优势更大。"]}, {"cell_type": "markdown", "id": "a5e122fe-d19d-43d3-9a43-45b3b028170d", "metadata": {}, "source": ["**量化与解量化的过程可以被简单表示为**——\n", "\n", "- **量化（Quantization）**：除以缩放因子并进行离散化、将数字映射到更小的动态范围。\n", "  $$\n", "  x_q = \\text{round}(x / s).to(低精度)\n", "  $$\n", "  其中：\n", "  - $ x_q $ 是 **低精度量化权重**\n", "  - $ x $ 是 **高精度原始权重**\n", "  - $ s $ 是 **缩放因子（scale factor）**\n", "\n", "- **解量化（Dequantization）**：乘以缩放因子，将数字映射到更大的动态范围。\n", "  $$\n", "  x = (x_q \\times s).to(高精度)\n", "  $$\n", "  其中：\n", "  - $ x_q $ 是 **存储的低精度量化权重**\n", "  - $ s $ 是 **量化时使用的缩放因子**\n", "  - $ x $ 是 **恢复的高精度权重**\n", "\n", "很多量化过程的本质正是基于这个公式，逐块（block-wise）地执行量化和解量化。"]}, {"cell_type": "markdown", "id": "7dcb4ea0-da42-4027-8cf9-5f6864d74403", "metadata": {}, "source": ["#### 2.6.2 梯度累积与梯度裁剪技术"]}, {"cell_type": "markdown", "id": "f5e894f9-ceaa-4951-9f0a-ef4f64ec7b42", "metadata": {}, "source": ["- **梯度累积**"]}, {"cell_type": "markdown", "id": "dda1398f-a227-4988-92d4-585e2f0e74b7", "metadata": {}, "source": ["**梯度累积** 是一种 **在多个小批次（mini-batches）上累积梯度**，然后再进行一次优化器更新的技术。它的主要目的是 **模拟更大的 batch size**，在显存受限的情况下提高训练稳定性和效果。\n", "\n", "---\n", "\n", "<font color=\"red\">**在定义好模型之后、深度学习训练的基本步骤与流程如下**——\n", "\n", "1. **数据分批/打乱/分训练集验证集等操作、定义使用的CPU/GPU等细节**\n", "\n", "2. **初始化模型参数（Parameter Initialization）**\n", "\n", "3. **训练循环（training loop）**：\n", "> **正向传播（Forward Pass）**：输入数据通过模型，得到预测输出。\n", "> \n", "> **计算损失（Compute Loss）**：根据模型的预测结果和真实标签，计算损失函数值。\n", "> \n", "> **反向传播（Backward Pass）**：根据损失函数的值，计算模型中各参数的梯度（使用自动微分）。\n", "> \n", "> **参数更新（Update Parameters）**：根据计算出的梯度，使用优化器更新模型的参数。\n", "> \n", "> **梯度清零（Zero Gradients）**：为了避免梯度累积，需要在每次迭代前将梯度归零。\n", "\n", "4. **训练监控与模型保存**\n", "\n", "---\n", "\n", "梯度累积就是在多个批次中取消梯度清零这一步、让多个小批次的梯度**不会立即清零**，而是累积起来。直到累积了一定数量（`accumulation_steps`）的小批次后，才执行一次优化器更新（optimizer.step()）。这适合于batch_size不足时的情况、在多个batch上积累了梯度后再进行迭代，可以让迭代更加稳定。\n", "\n", "📌 示例代码：\n", "```python\n", "accumulation_steps = 4  # 4 个小批次累积一次更新\n", "for step, (inputs, targets) in enumerate(data_loader):\n", "    outputs = model(inputs)\n", "    loss = loss_fn(outputs, targets)\n", "    \n", "    loss.backward()  # 计算梯度但不更新参数\n", "    \n", "    if (step + 1) % accumulation_steps == 0:  # 每 4 个 step 进行一次参数更新\n", "        optimizer.step()\n", "        optimizer.zero_grad()  # 清空梯度，准备下一次累积\n", "```"]}, {"cell_type": "markdown", "id": "3c2d3a08-f531-42e1-9ad4-12c30bd90874", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "e0c03790-3f4d-4897-83bd-d3e2b06fc593", "metadata": {}, "source": ["- **梯度裁剪**"]}, {"cell_type": "markdown", "id": "95e9c5d5-c2f0-4c39-b658-d0a86543f037", "metadata": {}, "source": ["**梯度裁剪** 是一种 **防止梯度爆炸**（gradient explosion）的技术，它通过限制梯度的最大范数或绝对值，**避免参数更新过大，导致训练不稳定**。它主要用于梯度下降时，梯度过大可能导致损失（loss）发散的情况，通过设定梯度最大值或梯度范数上限，将超过阈值的梯度缩小。\n", "\n", "📌 **示例代码**：\n", "```python\n", "torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm=1.0)  # 限制梯度范数\n", "```\n", "或\n", "```python\n", "torch.nn.utils.clip_grad_value_(model.parameters(), clip_value=0.5)  # 限制梯度绝对值\n", "```"]}, {"cell_type": "markdown", "id": "169a7259-8abf-44a4-8b95-0c2e5fed4ca9", "metadata": {}, "source": ["**<center>改善精度的计算流程（混合精度 / 额外优化）精度需求表**\n", "\n", "🟢 **低精度**（float16 / bfloat16 / ）  \n", "🔴 **高精度**（float32）  \n", "\n", "| **计算流程**                      | **精度需求**          | **原因** |\n", "|----------------------------------|---------------------|--------|\n", "| **梯度累积（Gradient Accumulation）** | 🔴 **高精度（FP32）** | 需要高精度累积，避免精度下溢 |\n", "| **优化器裁剪（Gradient Clip）** | 🔴 **高精度（FP32）** |  需要高精度裁剪，这样才能保持梯度更稳定 |"]}, {"cell_type": "markdown", "id": "95fdb3b4-e9a1-49f9-a145-907275a84e61", "metadata": {}, "source": ["### 2.7 单epoch预训练函数深度解析"]}, {"cell_type": "markdown", "id": "f454595f-4bc0-436e-b0ae-1edb438bf31d", "metadata": {}, "source": ["这个函数的作用是 **实现深度学习模型的单个 epoch 的训练过程**，涵盖了从数据加载、前向传播、损失计算、反向传播、学习率调整到模型保存的完整逻辑。它实现了对训练集的所有数据（分为多个批次）进行一轮完整训练，包括了下面所有这些板块——\n", "\n", "| **模块** | **主要作用** |\n", "|----------------|----------------|\n", "| **1. 训练循环** | 遍历 `train_loader`，训练多个 epoch |\n", "| **2. 数据加载** | 通过 `to(args.device)` 将数据送入 GPU/CPU |\n", "| **3. 学习率调整** | `get_lr()` 计算动态学习率|\n", "| **4. 前向传播** | `model(X, Y)` 计算预测值 `out` 和损失 |\n", "| **5. 反向传播** | `scaler.scale(loss).backward()` 计算梯度 |\n", "| **6. 训练日志** | `Logger()` 记录损失、学习率等信息（支持 `wandb`） |\n", "| **7. 模型保存** | `torch.save()` 仅在主进程（rank 0）保存模型和优化器 |\n", "| **8. 备份优化器** | `torch.save(optimizer.state_dict())` 以支持断点续训 |\n", "\n", "在这些板块中、还实现了**累积梯度**、**分布式支持**、**混合精度训练**等优化流程。"]}, {"cell_type": "markdown", "id": "fcdcf39f-afe9-4424-9af9-5d076d07c126", "metadata": {}, "source": ["```python\n", "#############\n", "# 训练单个 Epoch\n", "#############\n", "def train_epoch(epoch, wandb):\n", "    start_time = time.time()\n", "\n", "    #############\n", "    # 遍历 Mini-Batches\n", "    #############\n", "    for step, (X, Y) in enumerate(train_loader):\n", "        X = X.to(args.device)\n", "        Y = Y.to(args.device)\n", "\n", "        #############\n", "        # 学习率调整（LR Scheduling）\n", "        #############\n", "        lr = get_lr(epoch * iter_per_epoch + step, args.epochs * iter_per_epoch)\n", "        \n", "        # Adam优化算法会将参数分组、按组进行学习率调整\n", "        for param_group in optimizer.param_groups:\n", "            param_group['lr'] = lr\n", "\n", "        #############\n", "        # 前向传播（Forward Pass） & 计算 Loss\n", "        # 混合精度计算AMP\n", "        #############\n", "        with ctx:\n", "            out = model(X, Y)\n", "            # 归一化 loss 以适应梯度累积\n", "            # 在之前计算的几次loss中、梯度都没有迭代\n", "            # 因此相比起正常迭代的梯度，loss应该会偏高\n", "            # 因此需要用损失/累积梯度的迭代次数来得到当前权重下的平均损失\n", "            loss = out.last_loss / args.accumulation_steps  \n", "\n", "        #############\n", "        # 反向传播（Backward Pass） - 梯度计算\n", "        # 使用 GradScaler 进行梯度缩放，防止 underflow\n", "        #############\n", "        scaler.scale(loss).backward()  \n", "\n", "        #############\n", "        # 梯度累积 & 梯度裁剪\n", "        #############\n", "        # 累积到一定梯度后、才进行迭代\n", "        if (step + 1) % args.accumulation_steps == 0:\n", "            # 解除缩放，转换回 FP32\n", "            scaler.unscale_(optimizer)  \n", "            # 进行梯度裁剪，防止梯度爆炸\n", "            torch.nn.utils.clip_grad_norm_(model.parameters(), args.grad_clip)  \n", "\n", "            #############\n", "            # 参数更新（Optimizer Step）\n", "            #############\n", "            scaler.step(optimizer)  # 使用 FP32 进行优化器更新\n", "            scaler.update()  # 动态调整梯度缩放因子\n", "\n", "            #############\n", "            # 清空梯度（Zero Grad）\n", "            #############\n", "            optimizer.zero_grad(set_to_none=True)  # 释放显存，减少计算开销\n", "\n", "        #############\n", "        # 日志记录 & 监控（Logging & Monitoring）\n", "        #############\n", "        if step % args.log_interval == 0:\n", "            spend_time = time.time() - start_time\n", "            <PERSON><PERSON>(\n", "                'Epoch:[{}/{}]({}/{}) loss:{:.3f} lr:{:.7f} epoch_Time:{}min:'.format(\n", "                    epoch,\n", "                    args.epochs,\n", "                    step,\n", "                    iter_per_epoch,\n", "                    loss.item() * args.accumulation_steps,  # 还原 loss\n", "                    optimizer.param_groups[-1]['lr'],\n", "                    spend_time / (step + 1) * iter_per_epoch // 60 - spend_time // 60))\n", "\n", "            #############\n", "            # WandB 记录（可视化监控）\n", "            #############\n", "            if (wandb is not None) and (not ddp or dist.get_rank() == 0):\n", "                wandb.log({\"loss\": loss.item() * args.accumulation_steps,\n", "                           \"lr\": optimizer.param_groups[-1]['lr'],\n", "                           \"epoch_Time\": spend_time / (step + 1) * iter_per_epoch // 60 - spend_time // 60})\n", "\n", "        #############\n", "        # 模型检查点保存（Checkpointing）\n", "        #############\n", "        if (step + 1) % args.save_interval == 0 and (not ddp or dist.get_rank() == 0):\n", "            model.eval()  # 切换到评估模式，避免 BN 层更新\n", "            moe_path = '_moe' if lm_config.use_moe else ''\n", "            ckp = f'{args.save_dir}/{args.model_name}.pth'  # 用户提供的模型名称\n", "\n", "            #############\n", "            # 保存模型权重\n", "            #############\n", "            if isinstance(model, torch.nn.parallel.DistributedDataParallel):\n", "                state_dict = model.module.state_dict()  # DDP 模型需要 `.module` 获取权重\n", "            else:\n", "                state_dict = model.state_dict()\n", "\n", "            torch.save(state_dict, ckp)\n", "            Logger(f\"保存模型到 {ckp}\")\n", "\n", "            #############\n", "            # 保存优化器状态\n", "            #############\n", "            optimizer_state_path = f'{args.save_dir}/{args.model_name}_optimizer.pth'\n", "            torch.save(optimizer.state_dict(), optimizer_state_path)\n", "            Logger(f\"保存优化器状态到 {optimizer_state_path}\")\n", "\n", "            model.train()  # 重新切换回训练模式\n", "```"]}, {"cell_type": "markdown", "id": "ac7900f0-13db-4182-8926-1ec6ed2213cb", "metadata": {}, "source": ["到目前为止，我们实现了以下的全部流程、构成了完整的pretrain.py脚本 ↓"]}, {"cell_type": "markdown", "id": "dec91f35-000c-44ca-ad06-73002165f542", "metadata": {}, "source": ["1. **定义各个功能模块（函数）**<br><br>\n", "   - 这些函数是 **独立的、可复用的功能单元**，包括：<br><br>\n", "     - `Logger()` → 负责日志记录\n", "     - `get_lr()` → 计算动态学习率\n", "     - `init_model()` → 初始化模型（支持断点续训）\n", "     - `init_distributed_mode()` → 初始化 DDP 分布式训练\n", "     - `train_epoch()` → 训练单个 epoch\n", "     - 单独的Dataset.py和model.py用于支持模型和数据的处理"]}, {"cell_type": "markdown", "id": "772ce71e-8dac-44a8-b9f4-ee9204aa26ef", "metadata": {}, "source": [" \n", "2. **主程序（`if __name__ == \"__main__\":`）**<br><br>\n", "   - 负责 **解析参数、加载数据、初始化模型、配置训练环境**。\n", "   - 依次 **调用前面定义的函数**，组织整个训练流程。\n", "\n", "这相当于先建积木（功能模块），然后搭建房子（主程序），既清晰又高效，符合工程化深度学习训练的最佳实践。"]}, {"cell_type": "markdown", "id": "cbeb0e68-dcdc-40cd-af11-4c894104c8f5", "metadata": {}, "source": ["将该脚本上传至线上服务器的MateConv文件夹下，就可以实现训练了 ↓"]}, {"cell_type": "markdown", "id": "d00446c2-77a8-4cad-b620-1313c2f4ef23", "metadata": {}, "source": ["```shell\n", "cd ~/autodl-tmp/MateConv\n", "\n", "touch pretrain.py\n", "\n", "deepspeed --master_port 29500 --num_gpus=2 pretrain.py --epochs 15\n", "```"]}, {"cell_type": "markdown", "id": "821a4000-1200-4d85-94f1-6c4a6d5929b0", "metadata": {}, "source": ["### 2.8 DeepSpeed训练CLI与训练成果展示"]}, {"cell_type": "markdown", "id": "27022b9b-7bd2-49a5-a873-ffc71f9529bb", "metadata": {}, "source": ["开始训练 ↓"]}, {"cell_type": "markdown", "id": "9f070409-f1a0-40ea-bcf4-388dd912df35", "metadata": {}, "source": ["<center><img src=\"http://ml2022.oss-cn-hangzhou.aliyuncs.com/img/0d46acea891701fd573115782288e05.jpg\" alt=\"0d46acea891701fd573115782288e05\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "1cd9dea8-d69f-4c7d-9f0c-058cdba5e319", "metadata": {}, "source": ["经过18小时的训练，得到如下结果 ↓"]}, {"cell_type": "markdown", "id": "1bdf9a0e-de9f-401a-a81e-d7fd5bcdbeae", "metadata": {}, "source": ["- wandb显示状态——"]}, {"cell_type": "markdown", "id": "2f03aeff-ddb1-497d-bd4f-46b8fbe84c3b", "metadata": {}, "source": ["<center><img src=\"http://ml2022.oss-cn-hangzhou.aliyuncs.com/img/image-20241023195052407.png\" alt=\"image-20241023195052407\" style=\"zoom:33%;\" />"]}, {"cell_type": "markdown", "id": "3a3487b2-d04b-4559-9d59-cd8e6eb66d54", "metadata": {}, "source": ["- CLI显示状态——"]}, {"cell_type": "markdown", "id": "29dd3784-f7d9-4c7e-8760-f50a8d97e1ea", "metadata": {}, "source": ["<center><img src=\"http://ml2022.oss-cn-hangzhou.aliyuncs.com/img/9f1b2321dc79550b1e0f194e165ff22.png\" alt=\"9f1b2321dc79550b1e0f194e165ff22\" style=\"zoom:50%;\" />"]}, {"cell_type": "markdown", "id": "50f137a2-975b-4607-a3ee-6597c59813f1", "metadata": {}, "source": ["我们会在目录下看到 ↓"]}, {"cell_type": "markdown", "id": "1993954e-73d0-4bc3-95b4-134d6197ccc3", "metadata": {}, "source": ["![](http://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/102.png)"]}, {"cell_type": "markdown", "id": "47a04f4d-cc55-477a-bc7b-fd83f430ee6f", "metadata": {}, "source": ["- pretrain_512.pth：模型权重参数（网络的权重和偏置）、用于保存和加载模型权重，适用于推理、继续训练\n", "\n", "- pretrain_512_optimizer.pth：用于保存优化器状态（学习率、动量、梯度累积状态等）、用于恢复优化器状态，适用于继续训练\n", "\n", "**为什么要同时保存模型和优化器状态**？\n", "\n", "在训练过程中，优化器会根据模型的梯度来调整权重，如果你在训练中断后只恢复了模型的权重，而没有恢复优化器的状态，优化器的学习率、动量等信息将会丢失，可能会导致继续训练时表现不佳，或者无法顺利收敛。因此，为了保证训练的连续性，继续训练时需要同时加载模型权重和优化器状态。而在推理或测试模型时，只需要加载模型权重文件就足够了。"]}, {"cell_type": "markdown", "id": "32c62bea-2983-4414-912d-f381566edfc8", "metadata": {}, "source": ["接下来我们可以用下面的代码来测试模型运行的效果 ↓ 这段代码被我们打包在了`inference.ipy`中，你可以在线上jupyter中运行。"]}, {"cell_type": "markdown", "id": "700a0f6f-b0aa-4f8e-9ff6-f21d8f0beb0b", "metadata": {}, "source": ["- 训练结束后测试运行"]}, {"cell_type": "code", "execution_count": null, "id": "3b7fb60d-785c-4644-b62b-cfbc90521f01", "metadata": {}, "outputs": [], "source": ["import itertools\n", "import re\n", "import json\n", "import jsonlines\n", "import psutil\n", "import ujson\n", "import numpy as np\n", "import pandas as pd\n", "from transformers import AutoTokenizer\n", "from datasets import load_dataset\n", "import os\n", "from tqdm import tqdm\n", "import torch\n", "from model.model import Transformer  # 确保路径正确\n", "from model.LMConfig import LMConfig   # 导入 LMConfig"]}, {"cell_type": "code", "execution_count": null, "id": "6e3c88b0-51de-4cc9-9db9-b1d41d900cc9", "metadata": {}, "outputs": [], "source": ["# 定义BOS和EOS标记\n", "bos_token = \"<s>\"\n", "eos_token = \"</s>\""]}, {"cell_type": "code", "execution_count": null, "id": "c01aceff-db6a-4989-a7b0-d68d18c61d16", "metadata": {}, "outputs": [], "source": ["# 加载训练好的分词器路径\n", "tokenizer = AutoTokenizer.from_pretrained('/root/autodl-tmp/MateConv/model/mateconv_tokenizer', use_fast=False)\n", "print(f'加载的tokenizer词表大小: {len(tokenizer)}')"]}, {"cell_type": "code", "execution_count": 211, "id": "e5f99165-7231-49a9-beb3-a64c4294999e", "metadata": {}, "outputs": [], "source": ["# 创建配置对象\n", "lm_config = LMConfig()"]}, {"cell_type": "code", "execution_count": 212, "id": "5dd02885-9f93-48fb-82f1-bf904c0197a2", "metadata": {}, "outputs": [], "source": ["# 初始化 Transformer 模型\n", "model = Transformer(lm_config)"]}, {"cell_type": "code", "execution_count": 213, "id": "73c34124-ae6e-4a98-81a0-0cb6fbff087d", "metadata": {}, "outputs": [], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")"]}, {"cell_type": "code", "execution_count": 214, "id": "740fa355-f0d6-466a-8756-14e446a781c2", "metadata": {}, "outputs": [{"data": {"text/plain": ["device(type='cuda')"]}, "execution_count": 214, "metadata": {}, "output_type": "execute_result"}], "source": ["device"]}, {"cell_type": "code", "execution_count": 215, "id": "08175ac3-5bf1-43c6-bf02-a2d8bdc91cd7", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transformer(\n", "  (tok_embeddings): Embedding(6400, 512)\n", "  (dropout): Dropout(p=0.0, inplace=False)\n", "  (layers): ModuleList(\n", "    (0-7): 8 x TransformerBlock(\n", "      (attention): Attention(\n", "        (wq): Linear(in_features=512, out_features=512, bias=False)\n", "        (wk): Linear(in_features=512, out_features=256, bias=False)\n", "        (wv): Linear(in_features=512, out_features=256, bias=False)\n", "        (wo): Linear(in_features=512, out_features=512, bias=False)\n", "        (attn_dropout): Dropout(p=0.0, inplace=False)\n", "        (resid_dropout): Dropout(p=0.0, inplace=False)\n", "      )\n", "      (attention_norm): RMSNorm()\n", "      (ffn_norm): RMSNorm()\n", "      (feed_forward): FeedForward(\n", "        (w1): Linear(in_features=512, out_features=1408, bias=False)\n", "        (w2): Linear(in_features=1408, out_features=512, bias=False)\n", "        (w3): Linear(in_features=512, out_features=1408, bias=False)\n", "        (dropout): Dropout(p=0.0, inplace=False)\n", "      )\n", "    )\n", "  )\n", "  (norm): RMSNorm()\n", "  (output): Linear(in_features=512, out_features=6400, bias=False)\n", ")\n"]}], "source": ["model.to(device)\n", "\n", "# 检查模型结构和参数\n", "print(model)"]}, {"cell_type": "code", "execution_count": 217, "id": "7cf1396b-bbfb-4364-9f83-9a7f39e3bcd8", "metadata": {}, "outputs": [{"data": {"text/plain": ["Transformer(\n", "  (tok_embeddings): Embedding(6400, 512)\n", "  (dropout): Dropout(p=0.0, inplace=False)\n", "  (layers): ModuleList(\n", "    (0-7): 8 x TransformerBlock(\n", "      (attention): Attention(\n", "        (wq): Linear(in_features=512, out_features=512, bias=False)\n", "        (wk): Linear(in_features=512, out_features=256, bias=False)\n", "        (wv): Linear(in_features=512, out_features=256, bias=False)\n", "        (wo): Linear(in_features=512, out_features=512, bias=False)\n", "        (attn_dropout): Dropout(p=0.0, inplace=False)\n", "        (resid_dropout): Dropout(p=0.0, inplace=False)\n", "      )\n", "      (attention_norm): RMSNorm()\n", "      (ffn_norm): RMSNorm()\n", "      (feed_forward): FeedForward(\n", "        (w1): Linear(in_features=512, out_features=1408, bias=False)\n", "        (w2): Linear(in_features=1408, out_features=512, bias=False)\n", "        (w3): Linear(in_features=512, out_features=1408, bias=False)\n", "        (dropout): Dropout(p=0.0, inplace=False)\n", "      )\n", "    )\n", "  )\n", "  (norm): RMSNorm()\n", "  (output): Linear(in_features=512, out_features=6400, bias=False)\n", ")"]}, "execution_count": 217, "metadata": {}, "output_type": "execute_result"}], "source": ["# 加载模型权重【这里要修改为你的模型地址】\n", "model.load_state_dict(torch.load('out/pretrain_512.pth', map_location=device))\n", "model.eval()  # 切换到评估模式"]}, {"cell_type": "code", "execution_count": 43, "id": "fad532cb-ff4a-4d08-8cb5-84033ff579ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["基于人类数据的计算方法。\n", "在本书中，作者通过对比分析了大脑和大脑之间的关系、思维方式以及行为模式等问题，并结合大量的数据资料进行了深入剖析，为读者提供了一个很好的参考工具。\n"]}], "source": ["# 准备输入文本\n", "input_text = \"决策树是机器学习中的一种算法，决策树是\"\n", "input_ids = tokenizer.encode(input_text, return_tensors='pt').to(device)\n", "\n", "# 生成文本\n", "max_new_tokens = 500  # 生成 100 个 token\n", "eos_token_id = tokenizer.eos_token_id  # 终止 token\n", "\n", "# 在model.py中我们定义了generate方法、专用于生成\n", "# 调用 model.generate() 进行生成\n", "output_ids = next(model.generate(\n", "    idx=input_ids,\n", "    eos=eos_token_id,\n", "    max_new_tokens=max_new_tokens,\n", "    temperature=0.2,  # 控制创造性\n", "    top_k=10,  # 限制 top-k 采样\n", "    rp=1.2,  # 避免重复\n", "    stream=False  # 关闭流式返回\n", "))\n", "\n", "# 解码生成的 token\n", "generated_text = tokenizer.decode(output_ids[0], skip_special_tokens=True)\n", "\n", "# 打印最终生成的文本\n", "print(generated_text)"]}, {"cell_type": "code", "execution_count": 45, "id": "780d225c-3a6d-42a4-80d5-dc9ba8a4325f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["在一起。\n", "我爱你，也许你会觉得有点不公平了，但我会努力奋斗一辈子！我爱你，也许只是一段美好的时光而已。”他如此的坚定信念：“对不起，我的爱，是我的力量。希望能帮助你的人生，让生命更加美丽！”\n", "“我们是最好的男人！”，他说到最后。“如果有一天你不会再见过我，我一定会珍惜这份温暖和关怀。\n"]}], "source": ["# 准备输入文本\n", "input_text = \"我与你\"\n", "input_ids = tokenizer.encode(input_text, return_tensors='pt').to(device)\n", "\n", "# 生成文本\n", "max_new_tokens = 500  # 生成 100 个 token\n", "eos_token_id = tokenizer.eos_token_id  # 终止 token\n", "\n", "# 在model.py中我们定义了generate方法、专用于生成\n", "# 调用 model.generate() 进行生成\n", "output_ids = next(model.generate(\n", "    idx=input_ids,\n", "    eos=eos_token_id,\n", "    max_new_tokens=max_new_tokens,\n", "    temperature=1,  # 控制创造性\n", "    top_k=10,  # 限制 top-k 采样\n", "    rp=1.2,  # 避免重复\n", "    stream=False  # 关闭流式返回\n", "))\n", "\n", "# 解码生成的 token\n", "generated_text = tokenizer.decode(output_ids[0], skip_special_tokens=True)\n", "\n", "# 打印最终生成的文本\n", "print(generated_text)"]}, {"cell_type": "markdown", "id": "5d9e2bef-f0df-4b28-a6bf-aa9630c90c94", "metadata": {}, "source": ["### 2.9【选学】借助wandb进行训练过程记录"]}, {"cell_type": "markdown", "id": "a72fc392-cc97-4fe2-8ae7-46e2659b519e", "metadata": {}, "source": ["在大规模模型训练中，我们往往需要监控和分析大量的训练数据，而WandB可以帮助我们实现这一目标。它提供了以下几个重要的功能——\n", "\n", "**实时可视化**：WandB可以实时展示训练过程中关键指标的变化，如损失函数、学习率、训练时间等。通过这些可视化数据，我们能够直观地了解模型的训练进展，快速发现训练中的异常或瓶颈。\n", "\n", "**自动记录与日志管理**：WandB会自动记录每次实验的参数、代码、输出结果，确保实验结果的可追溯性。无论是超参数的设置，还是模型的架构调整，WandB都能够帮助我们完整保留实验记录，方便后期对比与调优。\n", "\n", "**支持中断与恢复训练**：在长时间的预训练任务中，系统中断或需要暂停是常见的情况。通过WandB的checkpoint功能，我们可以随时恢复训练，从上次中断的地方继续进行，避免数据和时间的浪费。\n", "\n", "**多实验对比**：当我们尝试不同的模型配置或超参数时，WandB允许我们在多个实验之间轻松进行对比分析，帮助我们选择最优的模型配置。\n", "\n", "**团队协作**：WandB还支持团队协作，多个成员可以共同查看实验结果，协同调试模型。这对研究和项目开发中团队的合作非常有帮助。"]}, {"cell_type": "markdown", "id": "027ef6b1-89eb-4186-bd82-86eda7ae23a9", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "dbf9c2f6-6f21-4d76-862f-0f3d4ab15174", "metadata": {}, "source": ["要使用WandB，你需遵循下面的流程 ↓"]}, {"cell_type": "markdown", "id": "42c06d97-fb91-460e-81e9-f8a26c551ba1", "metadata": {}, "source": ["1. 注册你的WandB账号：http://wandb.ai/site"]}, {"cell_type": "markdown", "id": "c4887772-ca49-40f6-a932-a80e7b5b37dc", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/94.png)\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/95.png)\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/96.png)\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/97.png)\n", "\n", "获取你的API ↓\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/98.png)"]}, {"cell_type": "markdown", "id": "8f76b577-5c28-4672-bd73-61894bfbfaf5", "metadata": {}, "source": ["2. 在CLI安装并使用wandb\n", "\n", "在命令行中输入如下代码安装wandb ↓\n", "\n", "```shell\n", "#安装\n", "pip install wandb\n", "\n", "#进入你pretrain所在的虚拟环境\n", "conda activate MateConv\n", "cd ~/autodl-tmp/MateConv\n", "\n", "#登录wandb\n", "wandb login\n", "```"]}, {"cell_type": "markdown", "id": "3d34db13-6f26-40ea-b531-59819ec2fdd8", "metadata": {}, "source": ["你需要根据提示输入你的API-KEY："]}, {"cell_type": "markdown", "id": "65b66d31-9ecc-449c-ac1b-9ba4b36763a0", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/99.png)"]}, {"cell_type": "markdown", "id": "bbc2d9ba-f1f9-4a8c-ac8b-b9d6d5db709d", "metadata": {}, "source": ["即可在当前电脑上保存wandb账号信息，之后即可直接在wandb home主页上看到训练过程。"]}, {"cell_type": "markdown", "id": "69b04fd7-6134-4a67-97e9-bf0ad333cd4e", "metadata": {}, "source": ["3. 借助wandb监控当前运行效果\n", "\n", "接下来在命令行中尝试运行该指令，该指令是在执行deepspeed命令的同时让wandb完成监控，这个命令中没有显示设置epoch数量，当然你也可以将epoch数量补充进来。注意由于我们本质执行的是deepspeed，所以pretrain.py执行前需要先进入pretrain.py所在的具体目录 ↓"]}, {"cell_type": "markdown", "id": "5f9c7b08-7961-4275-afc2-6c58bdeaa6f9", "metadata": {}, "source": ["```shell\n", "torch pretrain.py\n", "\n", "deepspeed --num_gpus=4 pretrain.py --use_wandb --wandb_project \"MateConv-Pretrain\" --deepspeed ds_config.json\n", "```"]}, {"cell_type": "markdown", "id": "57902319-b46a-4c93-8cad-dc9736b57411", "metadata": {}, "source": ["你会看到这里返回的链接 ↓"]}, {"cell_type": "markdown", "id": "801372a6-ade6-42ad-abd3-737e20ee3cee", "metadata": {}, "source": ["![](http://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/100.png)"]}, {"cell_type": "markdown", "id": "dd7cb618-0b68-466a-a799-fa855958722e", "metadata": {}, "source": ["点击链接后即可进入模型监控页面 ↓"]}, {"cell_type": "markdown", "id": "55f349f4-bef0-4383-b1d3-a5cc571c0285", "metadata": {}, "source": ["![](http://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/101.png)"]}, {"cell_type": "markdown", "id": "1bcb4566-3e2e-4689-bb88-89ae882da92e", "metadata": {}, "source": ["## 【加餐】微调数据构建的基本思路"]}, {"cell_type": "markdown", "id": "1ac2e046-a72d-42e1-8120-e7cfde64fe7a", "metadata": {}, "source": ["### Q1：微调适用于哪些场景？我需要微调吗？"]}, {"cell_type": "markdown", "id": "cff7f99b-c6c2-433f-a36f-f81ccc8a26fd", "metadata": {}, "source": ["✅ 适合用微调的情况\n", "\n", "| **场景** | **原因** |\n", "|----------|--------|\n", "| **让模型“记住”特定领域知识** | 比如法律、医疗、金融等特定行业，模型需要大量内部化的知识。 |\n", "| **特定格式的输出** | 例如，代码补全、医学报告、法律合同等，输出必须严格符合行业格式。 |\n", "| **领域专用术语或风格** | 例如，医生对话、律师咨询、金融分析师报告等，需遵循行业术语和专业表达。 |\n", "| **任务特定的对话风格** | 例如 AI 助手需保持特定品牌语调，如 Apple Siri、微软 Copilot 的风格。 |\n", "\n", "**⚠️ 何时不适合微调**\n", "- **模型明显不需要学会、只是需要知道**：如某些规章制度、流程、公司内部机制、客观数据呈现等等。\n", "\n", "> 医疗方面，诊断疾病、健康建议需要微调，医生排班、挂号流程、疾病分科、不需要微调。<br><br>\n", "> 金融买卖策略、投资建议、行业分析需要微调，信息查询、实况数据不需要微调。<br><br>\n", "> 金融发放贷款流程不需要微调、信贷评分不需要微调、出具信贷报告可以通过微调实现。<br><br>\n", "> 数据分析流程中、生成SQL代码需要微调、获得数据后进行分析需要微调、获得数据的过程不需要微调。<br><br>\n", "> <font color=\"red\">**永远不要尝试让模型记住你的数据库，只能让模型学会并记住你的业务**。\n", "  \n", "- **当知识频繁变化**：如新闻、法规等（微调后模型的知识不会自动更新）。\n", "- **当训练数据不足**：如果数据量少，微调可能效果不佳，RAG 或 Prompt 更好。\n", "- **成本过高时**：训练大模型需要 GPU 资源，不适用于小规模应用。\n", "\n", "---\n", "\n", "✅ 适合用 RAG 的情况\n", "| **场景** | **原因** |\n", "|----------|--------|\n", "| **知识频繁更新** | 例如法律法规、公司政策、产品文档，RAG 可以随时查询最新信息。 |\n", "| **数据量大但不适合微调** | 例如包含大量文档（百万级 FAQ、医学指南、学术论文），微调太昂贵但 RAG 可高效检索。 |\n", "| **需要外部事实支撑** | 例如客户服务、技术支持，必须基于最新的文档或知识库回答。 |\n", "| **个性化回答** | 例如查询用户历史数据、个性化推荐，RAG 可动态拉取用户相关信息。 |\n", "| **特定领域的问答系统** | 例如企业内部 AI 助手，需要基于公司内部文档回答问题。 |\n", "\n", "**⚠️ 何时不适合 RAG**\n", "- **低延迟任务**：RAG 需要额外的检索步骤，比直接调用微调模型要慢。\n", "- **复杂推理任务**：如果任务涉及深度推理（如多步推理、数学计算），仅靠检索可能不够。\n", "- **小规模任务**：如果数据量很小，不如直接用 Prompt。\n", "\n", "---\n", "\n", "✅ 适合用 Prompt Engineering 的情况\n", "| **场景** | **原因** |\n", "|----------|--------|\n", "| **短期测试 & 快速迭代** | 比如 MVP（最小可行产品）阶段，调整 Prompt 比微调更快。 |\n", "| **非结构化任务** | 例如创意写作、头脑风暴、文案生成，Prompt 足以控制风格。 |\n", "| **规则明确的任务** | 比如固定格式的输出（如“请按 JSON 格式输出”），无需微调。 |\n", "| **无标注数据的任务** | 例如改进现有对话，不需要专门的训练数据。 |\n", "| **多任务适应** | 例如 GPT-4 既能写代码，又能写论文，Prompt 可以让它适应不同任务。 |\n", "\n", "**⚠️ 何时不适合 Prompt**\n", "- **当任务需要严格控制风格或术语**（例如医疗报告，微调更好）。\n", "- **当 Prompt 变得过长或复杂**（长 Prompt 会增加成本，也可能导致不稳定）。\n", "- **当 API 调用成本过高**（如果需要大量 API 交互，微调可能更经济）。"]}, {"cell_type": "markdown", "id": "7f192362-0d58-42ac-abf6-acc8bb638934", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "3b597c49-d955-4a3c-b741-f744e085d0af", "metadata": {}, "source": ["### Q2：微调需要什么样的数据？"]}, {"cell_type": "markdown", "id": "a9456500-7704-4d6a-8746-04ae3f8a8be0", "metadata": {}, "source": ["**微调数据是对话，也可以是文字/代码所构成的问答对（Q-A对）**。"]}, {"cell_type": "markdown", "id": "156dfbbf-1ecd-4e5a-bbc7-ecff90b812c2", "metadata": {}, "source": ["**✅ 示例 1：医学问答微调（QA Fine-tuning）**\n", "适用于医疗助手，让 AI 具备医学知识。\n", "\n", "```jsonl\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是一名专业的医学医生，擅长心血管疾病诊断。\"},\n", "              {\"role\": \"user\", \"content\": \"什么是高血压？\"},\n", "              {\"role\": \"assistant\", \"content\": \"高血压是一种长期疾病，指的是动脉血压长期高于正常范围，通常高于 140/90 mmHg。\"}]}\n", "\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是一名医学专家，专门解答关于糖尿病的问题。\"},\n", "              {\"role\": \"user\", \"content\": \"糖尿病患者应该避免哪些食物？\"},\n", "              {\"role\": \"assistant\", \"content\": \"糖尿病患者应避免高糖食品（如甜点、碳酸饮料）、高碳水化合物食品（如白米饭、面包）、高脂肪食品（如炸薯条、加工肉类）。\"}]}\n", "```\n", "\n", "---\n", "\n", "**✅ 示例 2：法律助理微调（Legal Assistant Fine-tuning）**\n", "适用于法律 AI，帮助回答法律咨询。\n", "\n", "```jsonl\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是一名资深律师，专门解答合同法相关问题。\"},\n", "              {\"role\": \"user\", \"content\": \"合同签署后是否可以反悔？\"},\n", "              {\"role\": \"assistant\", \"content\": \"一般而言，合同一旦签署即具有法律效力，除非存在欺诈、胁迫、重大误解等特殊情况，才可以申请撤销。\"}]}\n", "\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是一名法律专家，擅长劳动法领域。\"},\n", "              {\"role\": \"user\", \"content\": \"公司能随意解雇员工吗？\"},\n", "              {\"role\": \"assistant\", \"content\": \"根据《劳动合同法》，公司不能无故解雇员工。必须提供合法理由，例如严重违反公司规章制度或合同到期不续签。\"}]}\n", "```\n", "\n", "---\n", "\n", "**✅ 示例 3：客户服务微调（Customer Support Fine-tuning）**\n", "适用于 AI 客服，提升回答的准确性和品牌一致性。\n", "\n", "```jsonl\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是 XYZ 公司客户服务代表，回答要礼貌、专业。\"},\n", "              {\"role\": \"user\", \"content\": \"请问如何申请退款？\"},\n", "              {\"role\": \"assistant\", \"content\": \"您好，您可以在订单详情页点击‘申请退款’按钮，填写退款原因后提交。我们将在 3 个工作日内审核。\"}]}\n", "\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是 XYZ 品牌的客户服务代表，帮助用户解决订单问题。\"},\n", "              {\"role\": \"user\", \"content\": \"我收到了错误的商品，怎么办？\"},\n", "              {\"role\": \"assistant\", \"content\": \"很抱歉给您带来不便！请您提供订单号，我们将尽快安排换货或退款处理。\"}]}\n", "```\n", "\n", "---\n", "\n", "**✅ 示例 4：代码生成微调（Code Generation Fine-tuning）**\n", "适用于代码助手，帮助自动生成代码或优化代码。\n", "\n", "```jsonl\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是一名 Python 开发专家，专门优化代码。\"},\n", "              {\"role\": \"user\", \"content\": \"如何用 Python 读取 CSV 文件？\"},\n", "              {\"role\": \"assistant\", \"content\": \"您可以使用 Pandas 读取 CSV 文件：\\n```python\\nimport pandas as pd\\ndf = pd.read_csv('data.csv')\\nprint(df.head())\\n```\"}]}\n", "\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是 JavaScript 专家，擅长编写前端代码。\"},\n", "              {\"role\": \"user\", \"content\": \"写一个 JavaScript 函数来计算数组中所有数字的和。\"},\n", "              {\"role\": \"assistant\", \"content\": \"可以使用 `reduce()` 方法：\\n```javascript\\nfunction sumArray(arr) {\\n    return arr.reduce((acc, num) => acc + num, 0);\\n}\\nconsole.log(sumArray([1, 2, 3, 4])); // 输出: 10\\n```\"}]}\n", "```\n", "\n", "---\n", "\n", "**✅ 示例 5：摘要任务微调（Summarization Fine-tuning）**\n", "适用于文档摘要生成，提高 AI 概括能力。\n", "\n", "```jsonl\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是一名专业的文章摘要助手，擅长提取关键内容。\"},\n", "              {\"role\": \"user\", \"content\": \"以下是一个新闻报道，请用 50 字总结它。\\n\\n2024年，新型疫苗研发成功，全球疫情得到有效控制，多个国家宣布解除旅行限制……\"},\n", "              {\"role\": \"assistant\", \"content\": \"2024年新疫苗成功研发，全球疫情受控，多个国家解除旅行限制，经济活动恢复。\"}]}\n", "\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是一个商业报告助手，可以帮助生成公司财报摘要。\"},\n", "              {\"role\": \"user\", \"content\": \"以下是 2023 年 XYZ 公司的财报数据，请生成简要摘要。\\n\\nXYZ 公司 2023 年营收增长 15%，利润增长 8%，主要受新市场扩张推动……\"},\n", "              {\"role\": \"assistant\", \"content\": \"XYZ 公司 2023 年营收增长 15%，利润增长 8%，主要因新市场扩张带动增长。\"}]}\n", "```"]}, {"cell_type": "markdown", "id": "22551320-f488-4f96-9dac-be0f5149abb6", "metadata": {}, "source": ["### Q3：怎样将各种各样的数据转换成JSONL格式？"]}, {"cell_type": "markdown", "id": "b6847a59-4ebe-4c06-a1fc-dab89727c534", "metadata": {}, "source": ["**表格、sheet、数据不适合作为微调数据。如果你的数据是表格，则必须要配合相应的文字对话来训练**。"]}, {"cell_type": "markdown", "id": "5caa6e2b-36a6-4e30-a1ef-6fe01c689934", "metadata": {}, "source": ["- **从数据库到JSONL**"]}, {"cell_type": "markdown", "id": "d546bda8-8885-4df8-983a-de00bbe17022", "metadata": {}, "source": ["如果你的数据存储在**数据库（DB）**中，并且不同的表之间有**关联（关系型数据库的外键、主键）**，那么你需要按照一定的逻辑**先整合数据（Raw Data），然后再转换为 JSONL 格式**。\n", "\n", "**📌 1. 数据转换流程**\n", "**步骤：**\n", "1. **从数据库提取数据**\n", "   - 连接数据库，查询相关表，按业务逻辑合并数据。\n", "   - 处理多表关系（JOIN 操作）。\n", "2. **转换为统一结构（Raw Data）**\n", "   - 规范字段，使不同来源的数据保持一致性。\n", "   - 处理缺失值、数据清理。\n", "3. **映射到 JSONL 格式**\n", "   - 组织成符合任务需求的 JSON 结构（QA、对话、摘要等）。\n", "   - 确保每行数据是一个独立 JSON。\n", "\n", "---\n", "\n", "**✅ 场景 1：客户服务数据库 → JSONL（对话微调）**\n", "**数据库结构（关系型数据库）**\n", "- **customers**（客户信息表）：`id, name, email`\n", "- **orders**（订单表）：`order_id, customer_id, product_name, status`\n", "- **support_tickets**（客服工单）：`ticket_id, customer_id, issue, response`\n", "\n", "**目标**：将客服历史对话转化为 JSONL 格式用于微调。\n", "\n", "**🛠 SQL 查询（提取数据）**\n", "```sql\n", "SELECT c.name, s.issue, s.response\n", "FROM customers c\n", "JOIN support_tickets s ON c.id = s.customer_id;\n", "```\n", "\n", "**📌 转换为 JSONL**\n", "```jsonl\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是一名客户服务 AI，帮助用户解决订单问题。\"},\n", "              {\"role\": \"user\", \"content\": \"我收到了错误的商品，怎么办？\"},\n", "              {\"role\": \"assistant\", \"content\": \"很抱歉给您带来不便！请提供订单号，我们将安排换货或退款处理。\"}]}\n", "```\n", "\n", "**🛠 Python 代码**\n", "```python\n", "import json\n", "import sqlite3  # 适用于 SQLite，其他数据库可以使用 MySQL 或 PostgreSQL 连接\n", "\n", "# 连接数据库\n", "conn = sqlite3.connect(\"database.db\")\n", "cursor = conn.cursor()\n", "\n", "# 查询数据\n", "query = \"\"\"\n", "SELECT c.name, s.issue, s.response\n", "FROM customers c\n", "JOIN support_tickets s ON c.id = s.customer_id\n", "\"\"\"\n", "cursor.execute(query)\n", "data = cursor.fetchall()\n", "\n", "# 转换 JSONL\n", "jsonl_data = []\n", "for row in data:\n", "    user_message = row[1]  # 客户问题\n", "    assistant_response = row[2]  # AI 期望的回复\n", "    json_obj = {\n", "        \"messages\": [\n", "            {\"role\": \"system\", \"content\": \"你是一名客户服务 AI，帮助用户解决订单问题。\"},\n", "            {\"role\": \"user\", \"content\": user_message},\n", "            {\"role\": \"assistant\", \"content\": assistant_response}\n", "        ]\n", "    }\n", "    jsonl_data.append(json_obj)\n", "\n", "# 保存 JSONL 文件\n", "with open(\"customer_support.jsonl\", \"w\", encoding=\"utf-8\") as f:\n", "    for obj in jsonl_data:\n", "        f.write(json.dumps(obj, ensure_ascii=False) + \"\\n\")\n", "\n", "print(\"✅ JSONL 数据已成功转换！\")\n", "```\n", "\n", "---"]}, {"cell_type": "markdown", "id": "23e814f2-8340-4a90-ae27-629a66c56a90", "metadata": {}, "source": ["**✅ 场景 2：医学数据库 → JSONL（QA 训练数据）**\n", "**数据库结构**\n", "- **patients**（病人信息）：`id, name, age, condition`\n", "- **medical_cases**（病历记录）：`case_id, patient_id, diagnosis, treatment`\n", "- **faq**（医学问答知识库）：`question, answer`\n", "\n", "**目标**：将病历 + FAQ 结合，转换为 JSONL 格式，供 AI 训练医学问答能力。\n", "\n", "**🛠 SQL 查询**\n", "```sql\n", "SELECT f.question, f.answer\n", "FROM faq f\n", "UNION\n", "SELECT m.diagnosis, m.treatment\n", "FROM medical_cases m;\n", "```\n", "\n", "**📌 转换为 JSONL**\n", "```jsonl\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是一名专业医生，擅长诊断和治疗常见疾病。\"},\n", "              {\"role\": \"user\", \"content\": \"感冒应该如何治疗？\"},\n", "              {\"role\": \"assistant\", \"content\": \"感冒通常是病毒感染引起的，可以通过多喝水、休息、适当服用退烧药来缓解症状。\"}]}\n", "\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是一名医生，帮助分析病人病例并提供治疗建议。\"},\n", "              {\"role\": \"user\", \"content\": \"病人 45 岁，确诊高血压，应该如何治疗？\"},\n", "              {\"role\": \"assistant\", \"content\": \"建议低盐饮食、适量运动，并在医生指导下服用降压药，如氨氯地平或依那普利。\"}]}\n", "```\n", "\n", "**🛠 Python 代码**\n", "```python\n", "import json\n", "import sqlite3\n", "\n", "# 连接数据库\n", "conn = sqlite3.connect(\"medical.db\")\n", "cursor = conn.cursor()\n", "\n", "# 查询 FAQ 和病例数据\n", "query = \"\"\"\n", "SELECT 'FAQ' AS source, question, answer FROM faq\n", "UNION\n", "SELECT 'Case' AS source, diagnosis, treatment FROM medical_cases;\n", "\"\"\"\n", "cursor.execute(query)\n", "data = cursor.fetchall()\n", "\n", "# 转换 JSONL\n", "jsonl_data = []\n", "for row in data:\n", "    user_message = row[1]  # 问题/病例描述\n", "    assistant_response = row[2]  # 回答/治疗方案\n", "    json_obj = {\n", "        \"messages\": [\n", "            {\"role\": \"system\", \"content\": \"你是一名医学专家，专门解答健康和治疗问题。\"},\n", "            {\"role\": \"user\", \"content\": user_message},\n", "            {\"role\": \"assistant\", \"content\": assistant_response}\n", "        ]\n", "    }\n", "    jsonl_data.append(json_obj)\n", "\n", "# 保存 JSONL 文件\n", "with open(\"medical_data.jsonl\", \"w\", encoding=\"utf-8\") as f:\n", "    for obj in jsonl_data:\n", "        f.write(json.dumps(obj, ensure_ascii=False) + \"\\n\")\n", "\n", "print(\"✅ JSONL 数据已成功转换！\")\n", "```"]}, {"cell_type": "markdown", "id": "1cd5cfe1-cf08-4f76-8501-899383b66c2c", "metadata": {}, "source": ["---\n", "\n", "**📌 3. 数据库 → JSONL 的转换要点**\n", "1. **理解数据关系**\n", "   - 如果数据库有多个表，使用 `JOIN` 语句整合数据。\n", "   - 根据业务需求，提取 `问题` 和 `回答`，确保数据清晰。\n", "\n", "2. **数据清理**\n", "   - 处理 `NULL` 值（用默认值填充）。\n", "   - 统一数据格式，如 `日期格式` 转换。\n", "\n", "3. **批量转换**\n", "   - **小数据**：可以直接查询后 `json.dumps()` 转 JSONL。\n", "   - **大数据（百万级）**：\n", "     - 分批查询（分页 `LIMIT OFFSET`）。\n", "     - 用 **流式写入 JSONL** 避免内存占用过高。"]}, {"cell_type": "markdown", "id": "1c1bfb79-3e0b-411e-9da5-124afbfc8eef", "metadata": {}, "source": ["### Q4：没有对话信息怎么办？"]}, {"cell_type": "markdown", "id": "dd21e896-34fc-4a5e-9ba2-67751aa9933b", "metadata": {}, "source": ["如果你没有现成的对话库，只有数据库中的**原始数据**（如医疗病例、产品信息、法律条款等），你可以**合成微调数据集**，主要方法有两种：\n", "\n", "1. **规则生成（Rule-based Generation）**：适用于格式较固定、可程序化生成的场景。\n", "2. **大语言模型（LLM 合成）**：适用于需要自然语言表达、需要更多样化数据的场景（如 GPT-4 生成问答对）。\n", "\n", "---\n", "\n", "**📌 方法 1：规则生成（Rule-based Generation）**\n", "- 你的数据库数据结构清晰，比如**病人信息、产品FAQ、合同条款**等。\n", "- 你能定义固定的**问题模板**，程序化生成问答。\n", "\n", "**📌 示例 1：医疗数据库 → 规则生成对话数据**\n", "\n", "**数据库（Medical DB）示例**\n", "| patient_id | diagnosis     | treatment                |\n", "|------------|--------------|--------------------------|\n", "| 101        | 高血压       | 低盐饮食、服用降压药     |\n", "| 102        | 糖尿病       | 控制血糖、注射胰岛素     |\n", "| 103        | 哮喘         | 使用吸入剂、避免过敏原   |\n", "\n", "**合成问答规则**\n", "- **规则**：\n", "  - 问题：`\"患者被诊断为 {diagnosis}，应如何治疗？\"`\n", "  - 回答：`\"{treatment}\"`\n", "\n", "**Python 代码**\n", "```python\n", "import json\n", "\n", "# 假设从数据库中提取的原始数据\n", "medical_cases = [\n", "    {\"diagnosis\": \"高血压\", \"treatment\": \"低盐饮食、服用降压药\"},\n", "    {\"diagnosis\": \"糖尿病\", \"treatment\": \"控制血糖、注射胰岛素\"},\n", "    {\"diagnosis\": \"哮喘\", \"treatment\": \"使用吸入剂、避免过敏原\"},\n", "]\n", "\n", "jsonl_data = []\n", "\n", "for case in medical_cases:\n", "    question = f\"患者被诊断为 {case['diagnosis']}，应如何治疗？\"\n", "    answer = case[\"treatment\"]\n", "\n", "    json_obj = {\n", "        \"messages\": [\n", "            {\"role\": \"system\", \"content\": \"你是一名医生，专门提供治疗建议。\"},\n", "            {\"role\": \"user\", \"content\": question},\n", "            {\"role\": \"assistant\", \"content\": answer}\n", "        ]\n", "    }\n", "    jsonl_data.append(json_obj)\n", "\n", "# 保存 JSONL\n", "with open(\"synthetic_medical_data.jsonl\", \"w\", encoding=\"utf-8\") as f:\n", "    for obj in jsonl_data:\n", "        f.write(json.dumps(obj, ensure_ascii=False) + \"\\n\")\n", "\n", "print(\"✅ 规则生成的 JSONL 数据已完成！\")\n", "```\n", "\n", "**生成 JSONL 数据**\n", "```jsonl\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是一名医生，专门提供治疗建议。\"},\n", "              {\"role\": \"user\", \"content\": \"患者被诊断为 高血压，应如何治疗？\"},\n", "              {\"role\": \"assistant\", \"content\": \"低盐饮食、服用降压药\"}]}\n", "\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是一名医生，专门提供治疗建议。\"},\n", "              {\"role\": \"user\", \"content\": \"患者被诊断为 糖尿病，应如何治疗？\"},\n", "              {\"role\": \"assistant\", \"content\": \"控制血糖、注射胰岛素\"}]}\n", "```\n", "\n", "✅ **优点**：\n", "- 规则清晰，数据可靠，适用于**结构化数据**。\n", "- 不依赖大模型，成本低，适用于**大批量数据生成**。\n", "\n", "❌ **缺点**：\n", "- 生成的问答可能**缺乏多样性**，显得死板。\n", "- 适用于结构化数据，不适合复杂问答（如多轮对话）。\n", "\n", "---"]}, {"cell_type": "markdown", "id": "b360ab01-b53c-4d71-92a5-3a3132369d4f", "metadata": {}, "source": ["**📌 方法 2：大语言模型（LLM 合成）**\n", "- 你希望生成**更自然、更丰富的对话**。\n", "- 数据库没有标准问答，但有很多**有价值的信息**（如合同条款、产品手册）。\n", "- 你可以调用 GPT-4 或其他 LLM 来帮助生成对话数据。\n", "\n", "---\n", "\n", "**📌 示例 2：法律数据库 → LLM 生成对话**\n", "**数据库（Legal DB）示例**\n", "| law_id | law_title            | law_content                                 |\n", "|--------|----------------------|---------------------------------------------|\n", "| 201    | 劳动合同法 第 36 条  | 劳动者每日工作时间不得超过 8 小时。         |\n", "| 202    | 消费者权益保护法 第 24 条 | 经营者提供的商品或服务如有瑕疵，消费者可要求退货。 |"]}, {"cell_type": "markdown", "id": "3ef3ea93-1fca-4e76-8396-7ed5131df7e1", "metadata": {}, "source": ["**使用 GPT-4 生成问答**\n", "\n", "- **Prompt（提示词）**："]}, {"cell_type": "markdown", "id": "7034a9af-35ed-4136-93e2-46ca698340d0", "metadata": {}, "source": ["```\n", "你是一名法律专家。请根据以下法律条款生成 3 组高质量的问答对：\n", "---\n", "劳动合同法 第 36 条：\n", "劳动者每日工作时间不得超过 8 小时。\n", "---\n", "请生成问答：\n", "```"]}, {"cell_type": "markdown", "id": "fa8bb2a1-3b2c-4100-a806-c590ad5879c0", "metadata": {}, "source": ["**Python 代码**\n", "```python\n", "import json\n", "import openai  # 需要 OpenAI API\n", "\n", "api_key = \"your-openai-api-key\"\n", "\n", "legal_cases = [\n", "    {\"law_title\": \"劳动合同法 第 36 条\", \"law_content\": \"劳动者每日工作时间不得超过 8 小时。\"},\n", "    {\"law_title\": \"消费者权益保护法 第 24 条\", \"law_content\": \"经营者提供的商品或服务如有瑕疵，消费者可要求退货。\"},\n", "]\n", "\n", "jsonl_data = []\n", "\n", "for case in legal_cases:\n", "    prompt = f\"\"\"\n", "    你是一名法律专家。请根据以下法律条款生成 3 组高质量的问答对：\n", "    ---\n", "    {case['law_title']}：\n", "    {case['law_content']}\n", "    ---\n", "    \"\"\"\n", "\n", "    response = openai.ChatCompletion.create(\n", "        model=\"gpt-4\",\n", "        messages=[{\"role\": \"system\", \"content\": \"你是法律专家，擅长生成法律问答数据。\"},\n", "                  {\"role\": \"user\", \"content\": prompt}],\n", "        temperature=0.7,\n", "        max_tokens=500,\n", "        api_key=api_key\n", "    )\n", "\n", "    qa_pairs = response[\"choices\"][0][\"message\"][\"content\"].strip().split(\"\\n\\n\")\n", "\n", "    for qa in qa_pairs:\n", "        if \"Q:\" in qa and \"A:\" in qa:\n", "            question = qa.split(\"Q: \")[1].split(\"\\nA: \")[0]\n", "            answer = qa.split(\"\\nA: \")[1]\n", "            json_obj = {\n", "                \"messages\": [\n", "                    {\"role\": \"system\", \"content\": \"你是一名法律专家，帮助回答法律相关问题。\"},\n", "                    {\"role\": \"user\", \"content\": question},\n", "                    {\"role\": \"assistant\", \"content\": answer}\n", "                ]\n", "            }\n", "            jsonl_data.append(json_obj)\n", "\n", "# 保存 JSONL\n", "with open(\"synthetic_legal_data.jsonl\", \"w\", encoding=\"utf-8\") as f:\n", "    for obj in jsonl_data:\n", "        f.write(json.dumps(obj, ensure_ascii=False) + \"\\n\")\n", "\n", "print(\"✅ LLM 生成的 JSONL 数据已完成！\")\n", "```\n", "\n", "**生成 JSONL 数据**\n", "```jsonl\n", "{\"messages\": [{\"role\": \"system\", \"content\": \"你是一名法律专家，帮助回答法律相关问题。\"},\n", "              {\"role\": \"user\", \"content\": \"根据劳动合同法，员工每天最多能工作多少小时？\"},\n", "              {\"role\": \"assistant\", \"content\": \"根据《劳动合同法》第 36 条，劳动者每日工作时间不得超过 8 小时。\"}]}\n", "```\n", "\n", "✅ **优点**：\n", "- 问答多样化，语义更自然，适用于**开放性任务**。\n", "- 可以模拟**真实用户的提问方式**，提高训练效果。\n", "\n", "❌ **缺点**：\n", "- 依赖 GPT-4 API，有**调用成本**。\n", "- 可能生成**错误或偏差信息**，需要人工检查。\n", "\n", "---"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}