setuptools==75.8.0
wheel==0.45.1
pip==25.0
asttokens==3.0.0
attrs==24.3.0
babel==2.16.0
Brotli==1.0.9
certifi==2025.1.31
charset-normalizer==3.3.2
debugpy==1.8.11
decorator==5.1.1
defusedxml==0.7.1
exceptiongroup==1.2.0
executing==0.8.3
h11==0.14.0
idna==3.7
json5==0.9.25
MarkupSafe==3.0.2
nest-asyncio==1.6.0
overrides==7.4.0
packaging==24.2
pandocfilters==1.5.0
parso==0.8.4
platformdirs==3.10.0
prometheus_client==0.21.0
ptyprocess==0.7.0
pure-eval==0.2.2
pycparser==2.21
Pygments==2.15.1
PySocks==1.7.1
fastjsonschema==2.20.0
python-json-logger==3.2.1
PyYAML==6.0.2
pyzmq==26.2.0
rfc3986-validator==0.1.1
rpds-py==0.22.3
Send2Trash==1.8.2
six==1.16.0
sniffio==1.3.0
soupsieve==2.5
tomli==2.0.1
tornado==6.4.2
traitlets==5.14.3
typing_extensions==4.13.1
wcwidth==0.2.5
webencodings==0.5.1
websocket-client==1.8.0
anyio==4.6.2
async-lru==2.0.4
beautifulsoup4==4.12.3
bleach==6.2.0
cffi==1.17.1
comm==0.2.1
httpcore==1.0.2
jedi==0.19.2
jupyter_core==5.7.2
jupyterlab_pygments==0.3.0
matplotlib-inline==0.1.6
pexpect==4.8.0
prompt-toolkit==3.0.43
python-dateutil==2.9.0.post0
referencing==0.30.2
rfc3339-validator==0.1.4
stack-data==0.2.0
terminado==0.17.1
tinycss2==1.4.0
urllib3==2.3.0
argon2-cffi-bindings==21.2.0
httpx==0.27.0
jsonschema-specifications==2023.7.1
jupyter_client==8.6.3
jupyter_server_terminals==0.4.4
mistune==3.1.2
requests==2.32.3
argon2-cffi==21.3.0
ipython==8.30.0
jsonschema==4.23.0
ipykernel==6.29.5
jupyter-events==0.12.0
nbformat==5.10.4
nbclient==0.10.2
nbconvert==7.16.6
jupyter_server==2.15.0
jupyter-lsp==2.2.0
jupyterlab_server==2.27.3
notebook_shim==0.2.4
jupyterlab==4.3.4
sentencepiece==0.2.0
pytz==2025.2
py-cpuinfo==9.0.0
mpmath==1.3.0
jieba==0.42.1
hjson==3.1.0
xxhash==3.5.0
Werkzeug==3.1.3
watchdog==4.0.2
ujson==5.1.0
tqdm==4.67.1
toml==0.10.2
threadpoolctl==3.6.0
tenacity==8.5.0
sympy==1.13.3
smmap==5.0.2
shtab==1.7.1
safetensors==0.5.3
regex==2024.11.6
pyparsing==3.2.3
pyarrow-hotfix==0.6
pyarrow==19.0.1
psutil==5.9.8
protobuf==5.29.4
propcache==0.3.1
pillow==10.4.0
nvidia-nvtx-cu12==12.1.105
nvidia-nvjitlink-cu12==12.8.93
nvidia-nccl-cu12==2.18.1
nvidia-curand-cu12==**********
nvidia-cufft-cu12==*********
nvidia-cuda-runtime-cu12==12.1.105
nvidia-cuda-nvrtc-cu12==12.1.105
nvidia-cuda-cupti-cu12==12.1.105
nvidia-cublas-cu12==********
numpy==1.26.4
ninja==********
ngrok==1.4.0
networkx==3.4.2
narwhals==1.34.0
mdurl==0.1.2
marshmallow==3.22.0
kiwisolver==1.4.8
jsonlines==4.0.0
joblib==1.4.2
jiter==0.9.0
Jinja2==3.1.2
itsdangerous==2.2.0
fsspec==2023.10.0
frozenlist==1.5.0
fonttools==4.57.0
filelock==3.18.0
docstring_parser==0.16
distro==1.9.0
dill==0.3.7
cycler==0.12.1
click==8.1.8
cachetools==5.5.2
blinker==1.9.0
async-timeout==5.0.1
annotated-types==0.7.0
aiohappyeyeballs==2.6.1
typeguard==4.4.2
triton==2.1.0
tiktoken==0.5.1
simhash==2.1.2
scipy==1.15.2
pydeck==0.9.1
pydantic_core==2.20.1
pandas==1.5.3
nvidia-cusparse-cu12==**********
nvidia-cudnn-cu12==********
nltk==3.8
multiprocess==0.70.15
multidict==6.3.2
matplotlib==3.5.1
markdown-it-py==3.0.0
huggingface-hub==0.30.2
gitdb==4.0.12
Flask==3.0.3
aiosignal==1.3.2
yarl==1.19.0
tokenizers==0.19.1
scikit-learn==1.5.1
rich==13.7.1
pydantic==2.8.2
nvidia-cusolver-cu12==**********
GitPython==3.1.44
Flask-Cors==4.0.0
datasketch==1.6.4
tyro==0.9.18
transformers==4.44.0
torch==2.1.2
openai==1.42.0
altair==5.5.0
aiohttp==3.11.16
streamlit==1.38.0
sentence-transformers==2.3.1
deepspeed==0.15.1
accelerate==1.6.0
peft==0.7.1
datasets==2.16.1
trl==0.8.6
modelscope==1.24.1
setproctitle==1.3.5
sentry-sdk==2.25.1
docker-pycreds==0.4.0
wandb==0.19.9
