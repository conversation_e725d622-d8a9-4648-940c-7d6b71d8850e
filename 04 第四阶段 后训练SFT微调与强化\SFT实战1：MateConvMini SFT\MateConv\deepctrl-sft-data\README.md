---
configs:
- config_name: default
  data_files:
  - path: meta.jsonl
    split: train
license: Apache License 2.0
tags:
- llm
- "\u5927\u6A21\u578B"
text:
  text-generation:
    language:
    - zh
    type:
    - data-to-text
---

## 数据集描述
### 数据集简介

匠数大模型SFT数据集是一个由匠数科技精心搜集整理的高质量数据集。这个数据集的目标是为广大用户提供一个完整、格式统一、安全的大模型训练和研究资源。我们从网络上的公开数据源收集并整理了大量开源数据集，对其进行了格式统一，数据清洗，并使用本公司的[内容审核](https://www.deepctrl.net/)产品对其内容进行了严格的审核，最终获得了用于大模型SFT的包含10M条数据的中文数据集和包含2M条数据的英文数据集。随后，我们按照任务内容将其分为**50类**，并获得了每一类数据的关键词。

### 数据集支持的任务

该数据集整理并汇总了当前几乎所有开源的中文数据集，可以用于大模型的SFT任务。后续我们会持续更新和维护。

## 数据集的格式和结构
### 数据集格式

```
匠数大模型SFT数据集
├── README.md
├── sft_data_en.jsonl
├── sft_data_zh.jsonl
├── type_keywords_en.json
└── type_keywords_zh.json
sft_data_zh.jsonl：
    中文SFT数据集，共包含11381621条数据
    MD5：00ef549c4360958989a4eea36ba7a1f0           
sft_data_en.jsonl：
    英文SFT数据集，共包含2767403条数据
    MD5：08580a4b19e961928db9e026f1572ba7  
type_keywords_en.json，英文SFT数据集的类别关键词
type_keywords_zh.json，中文SFT数据集的类别关键词

```

数据集文件包含了共12M+条样本。数据格式为每行一个样本，里面包含12个字段:
- 1.id，用于追踪数据的唯一标识符。
- 2.instruction，系统提示词。
- 3.input，用户输入指令
- 4.output，输出
- 5.history，历史对话
- 6.language，语言
- 7.data_source，数据来源
- 8.input_len，用户平均单轮输入长度
- 9.output_len，平均输出长度
- 10.num_utter，对话轮次
- 11.type，数据类别
- 12.type_keyword，该类别数据的关键词

为了便于抽样和训练，我们将数据集分为中文数据集和英文数据集，英文数据集中仅包含英文数据，而中文数据集中会包含部分外文数据（这是因为存在部分语言翻译任务，在我们的数据处理过程中，我们将任何包含中文的数据标注为中文数据。）

### 数据集结构

示例样本（内容并非真实的训练数据，仅用来说明格式）
```json
{
"id": 103, 
"instruction": "", 
"input": "生成一首诗歌，赞美匠数科技追求卓越、勇于创新的精神。", 
"output": "
    在匠数科技，
    有一群执着的人，
    他们以梦想为名，
    追求卓越，不断前行。

    他们用汗水浇灌智慧，
    将创新融入每个细节，
    让每一个产品都成为经典，
    让每一项服务都让人惊叹。

    他们的脚步从未停歇，
    他们的目光始终坚定，
    他们用心去创造未来，
    用行动诠释匠数精神。

    在这个充满挑战的时代，
    匠数科技永远走在前沿，
    他们相信，只有不断超越自我，
    才能赢得未来的辉煌。", 
"history": [], 
"language": "chinese", 
"data_source": "https://huggingface.co/datasets/BelleGroup/train_3.5M_CN", 
"input_len": 12, 
"output_len": 78, 
"num_utter": 1, 
"type": 32, 
"type_keyword": ["生成", "描述", "有关", "诗歌", "文章", "旅游", "段落", "主题", "短文", "句子"]
}
```

## 数据集版权信息
数据集为开源数据集，License为Apache License 2.0，如有违反相关条款，随时联系modelscope删除。

## 数据集来源

数据集来源于以下数据集：

- [BelleGroup/train_3.5M_CN](https://huggingface.co/datasets/BelleGroup/train_3.5M_CN)
- [LinkSoul/instruction_merge_set](https://huggingface.co/datasets/LinkSoul/instruction_merge_set)
- [stingning/ultrachat](https://huggingface.co/datasets/stingning/ultrachat)
- [BAAI/COIG-PC-core](https://huggingface.co/datasets/BAAI/COIG-PC-core)
- [shibing624/sharegpt_gpt4](https://huggingface.co/datasets/shibing624/sharegpt_gpt4)
- [shareAI/ShareGPT-Chinese-English-90k](https://huggingface.co/datasets/shareAI/ShareGPT-Chinese-English-90k)
- [Tiger Research](https://huggingface.co/TigerResearch/sft_zh)
- [BelleGroup/school_math_0.25M](https://huggingface.co/datasets/BelleGroup/school_math_0.25M)
- [YeungNLP/moss-003-sft-data](https://huggingface.co/datasets/YeungNLP/moss-003-sft-data)


## 其他相关信息

请注意，匠数大模型SFT数据集是基于开源数据集整理而成的。尽管我们已经尽力对数据进行了清洗和预处理，但仍然可能存在一些有害信息。这些信息并不代表匠数科技的观点。

我们始终致力于创建安全、开放、共享的开源环境。如果您在使用数据集过程中发现任何不当内容，请及时与我们联系，我们将尽快进行核实和处理。我们非常感谢您的理解和支持，让我们共同努力，创造一个更美好的开源环境。