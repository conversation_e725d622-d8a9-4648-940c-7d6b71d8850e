[{"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.batchUpdate", "api_call": "service.spreadsheets().batchUpdate(spreadsheetId: str).execute()", "api_version": "v4", "api_arguments": {"spreadsheetId": "[REQUIRED] string: The spreadsheet to apply the updates to."}, "functionality": "Applies one or more updates to the spreadsheet. Each request is validated before being applied. If any request is not valid then the entire request will fail and nothing will be applied. Some requests have replies to give you some information about how they are applied. The replies will mirror the requests. For example, if you applied 4 updates and the 3rd one had a reply, then the response will have 2 empty replies, the actual reply, and another empty reply, in that order. Due to the collaborative nature of spreadsheets, it is not guaranteed that the spreadsheet will reflect exactly your changes after this completes, however it is guaranteed that the updates in the request will be applied together atomically. Your changes may be altered with respect to collaborator changes. If there are no collaborators, the spreadsheet should reflect your changes.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.create", "api_call": "service.spreadsheets().create().execute()", "api_version": "v4", "api_arguments": {}, "functionality": "Creates a spreadsheet, returning the newly created spreadsheet.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.get", "api_call": "service.spreadsheets().get(spreadsheetId: str).execute()", "api_version": "v4", "api_arguments": {"includeGridData": "boolean: True if grid data should be returned. This parameter is ignored if a field mask was set in the request.", "ranges": "string: The ranges to retrieve from the spreadsheet.", "spreadsheetId": "[REQUIRED] string: The spreadsheet to request."}, "functionality": "Returns the spreadsheet at the given ID. The caller must specify the spreadsheet ID. By default, data within grids is not returned. You can include grid data in one of 2 ways: * Specify a [field mask](https://developers.google.com/sheets/api/guides/field-masks) listing your desired fields using the `fields` URL parameter in HTTP * Set the includeGridData URL parameter to true. If a field mask is set, the `includeGridData` parameter is ignored For large spreadsheets, as a best practice, retrieve only the specific spreadsheet fields that you want. To retrieve only subsets of spreadsheet data, use the ranges URL parameter. Ranges are specified using [A1 notation](/sheets/api/guides/concepts#cell). You can define a single cell (for example, `A1`) or multiple cells (for example, `A1:D5`). You can also get cells from other sheets within the same spreadsheet (for example, `Sheet2!A1:C4`) or retrieve multiple ranges at once (for example, `?ranges=A1:D5&ranges=Sheet2!A1:C4`). Limiting the range returns only the portions of the spreadsheet that intersect the requested ranges.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.getByDataFilter", "api_call": "service.spreadsheets().getByDataFilter(spreadsheetId: str).execute()", "api_version": "v4", "api_arguments": {"spreadsheetId": "[REQUIRED] string: The spreadsheet to request."}, "functionality": "Returns the spreadsheet at the given ID. The caller must specify the spreadsheet ID. This method differs from GetSpreadsheet in that it allows selecting which subsets of spreadsheet data to return by specifying a dataFilters parameter. Multiple DataFilters can be specified. Specifying one or more data filters returns the portions of the spreadsheet that intersect ranges matched by any of the filters. By default, data within grids is not returned. You can include grid data one of 2 ways: * Specify a [field mask](https://developers.google.com/sheets/api/guides/field-masks) listing your desired fields using the `fields` URL parameter in HTTP * Set the includeGridData parameter to true. If a field mask is set, the `includeGridData` parameter is ignored For large spreadsheets, as a best practice, retrieve only the specific spreadsheet fields that you want.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.developerMetadata.get", "api_call": "service.spreadsheets().developerMetadata().get(spreadsheetId: str, metadataId: int).execute()", "api_version": "v4", "api_arguments": {"metadataId": "[REQUIRED] integer: The ID of the developer metadata to retrieve.", "spreadsheetId": "[REQUIRED] string: The ID of the spreadsheet to retrieve metadata from."}, "functionality": "Returns the developer metadata with the specified ID. The caller must specify the spreadsheet ID and the developer metadata's unique metadataId.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.developerMetadata.search", "api_call": "service.spreadsheets().developerMetadata().search(spreadsheetId: str).execute()", "api_version": "v4", "api_arguments": {"spreadsheetId": "[REQUIRED] string: The ID of the spreadsheet to retrieve metadata from."}, "functionality": "Returns all developer metadata matching the specified DataFilter. If the provided DataFilter represents a DeveloperMetadataLookup object, this will return all DeveloperMetadata entries selected by it. If the DataFilter represents a location in a spreadsheet, this will return all developer metadata associated with locations intersecting that region.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.sheets.copyTo", "api_call": "service.spreadsheets().sheets().copyTo(spreadsheetId: str, sheetId: int).execute()", "api_version": "v4", "api_arguments": {"sheetId": "[REQUIRED] integer: The ID of the sheet to copy.", "spreadsheetId": "[REQUIRED] string: The ID of the spreadsheet containing the sheet to copy."}, "functionality": "Copies a single sheet from a spreadsheet to another spreadsheet. Returns the properties of the newly created sheet.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.values.append", "api_call": "service.spreadsheets().values().append(spreadsheetId: str, range: str).execute()", "api_version": "v4", "api_arguments": {"includeValuesInResponse": "boolean: Determines if the update response should include the values of the cells that were appended. By default, responses do not include the updated values.", "insertDataOption": "string: How the input data should be inserted.", "range": "[REQUIRED] string: The [A1 notation](/sheets/api/guides/concepts#cell) of a range to search for a logical table of data. Values are appended after the last row of the table.", "responseDateTimeRenderOption": "string: Determines how dates, times, and durations in the response should be rendered. This is ignored if response_value_render_option is FORMATTED_VALUE. The default dateTime render option is SERIAL_NUMBER.", "responseValueRenderOption": "string: Determines how values in the response should be rendered. The default render option is FORMATTED_VALUE.", "spreadsheetId": "[REQUIRED] string: The ID of the spreadsheet to update.", "valueInputOption": "string: How the input data should be interpreted."}, "functionality": "Appends values to a spreadsheet. The input range is used to search for existing data and find a \"table\" within that range. Values will be appended to the next row of the table, starting with the first column of the table. See the [guide](/sheets/api/guides/values#appending_values) and [sample code](/sheets/api/samples/writing#append_values) for specific details of how tables are detected and data is appended. The caller must specify the spreadsheet ID, range, and a valueInputOption. The `valueInputOption` only controls how the input data will be added to the sheet (column-wise or row-wise), it does not influence what cell the data starts being written to.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.values.batchClear", "api_call": "service.spreadsheets().values().batchClear(spreadsheetId: str).execute()", "api_version": "v4", "api_arguments": {"spreadsheetId": "[REQUIRED] string: The ID of the spreadsheet to update."}, "functionality": "Clears one or more ranges of values from a spreadsheet. The caller must specify the spreadsheet ID and one or more ranges. Only values are cleared -- all other properties of the cell (such as formatting and data validation) are kept.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.values.batchClearByDataFilter", "api_call": "service.spreadsheets().values().batchClearByDataFilter(spreadsheetId: str).execute()", "api_version": "v4", "api_arguments": {"spreadsheetId": "[REQUIRED] string: The ID of the spreadsheet to update."}, "functionality": "Clears one or more ranges of values from a spreadsheet. The caller must specify the spreadsheet ID and one or more DataFilters. Ranges matching any of the specified data filters will be cleared. Only values are cleared -- all other properties of the cell (such as formatting, data validation, etc..) are kept.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.values.batchGet", "api_call": "service.spreadsheets().values().batchGet(spreadsheetId: str).execute()", "api_version": "v4", "api_arguments": {"dateTimeRenderOption": "string: How dates, times, and durations should be represented in the output. This is ignored if value_render_option is FORMATTED_VALUE. The default dateTime render option is SERIAL_NUMBER.", "majorDimension": "string: The major dimension that results should use. For example, if the spreadsheet data is: `A1=1,B1=2,A2=3,B2=4`, then requesting `ranges=[\"A1:B2\"],majorDimension=ROWS` returns `[[1,2],[3,4]]`, whereas requesting `ranges=[\"A1:B2\"],majorDimension=COLUMNS` returns `[[1,3],[2,4]]`.", "ranges": "string: The [A1 notation or R1C1 notation](/sheets/api/guides/concepts#cell) of the range to retrieve values from.", "spreadsheetId": "[REQUIRED] string: The ID of the spreadsheet to retrieve data from.", "valueRenderOption": "string: How values should be represented in the output. The default render option is ValueRenderOption.FORMATTED_VALUE."}, "functionality": "Returns one or more ranges of values from a spreadsheet. The caller must specify the spreadsheet ID and one or more ranges.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.values.batchGetByDataFilter", "api_call": "service.spreadsheets().values().batchGetByDataFilter(spreadsheetId: str).execute()", "api_version": "v4", "api_arguments": {"spreadsheetId": "[REQUIRED] string: The ID of the spreadsheet to retrieve data from."}, "functionality": "Returns one or more ranges of values that match the specified data filters. The caller must specify the spreadsheet ID and one or more DataFilters. Ranges that match any of the data filters in the request will be returned.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.values.batchUpdate", "api_call": "service.spreadsheets().values().batchUpdate(spreadsheetId: str).execute()", "api_version": "v4", "api_arguments": {"spreadsheetId": "[REQUIRED] string: The ID of the spreadsheet to update."}, "functionality": "Sets values in one or more ranges of a spreadsheet. The caller must specify the spreadsheet ID, a valueInputOption, and one or more ValueRanges.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.values.batchUpdateByDataFilter", "api_call": "service.spreadsheets().values().batchUpdateByDataFilter(spreadsheetId: str).execute()", "api_version": "v4", "api_arguments": {"spreadsheetId": "[REQUIRED] string: The ID of the spreadsheet to update."}, "functionality": "Sets values in one or more ranges of a spreadsheet. The caller must specify the spreadsheet ID, a valueInputOption, and one or more DataFilterValueRanges.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.values.clear", "api_call": "service.spreadsheets().values().clear(spreadsheetId: str, range: str).execute()", "api_version": "v4", "api_arguments": {"range": "[REQUIRED] string: The [A1 notation or R1C1 notation](/sheets/api/guides/concepts#cell) of the values to clear.", "spreadsheetId": "[REQUIRED] string: The ID of the spreadsheet to update."}, "functionality": "Clears values from a spreadsheet. The caller must specify the spreadsheet ID and range. Only values are cleared -- all other properties of the cell (such as formatting, data validation, etc..) are kept.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.values.get", "api_call": "service.spreadsheets().values().get(spreadsheetId: str, range: str).execute()", "api_version": "v4", "api_arguments": {"dateTimeRenderOption": "string: How dates, times, and durations should be represented in the output. This is ignored if value_render_option is FORMATTED_VALUE. The default dateTime render option is SERIAL_NUMBER.", "majorDimension": "string: The major dimension that results should use. For example, if the spreadsheet data in Sheet1 is: `A1=1,B1=2,A2=3,B2=4`, then requesting `range=Sheet1!A1:B2?majorDimension=ROWS` returns `[[1,2],[3,4]]`, whereas requesting `range=Sheet1!A1:B2?majorDimension=COLUMNS` returns `[[1,3],[2,4]]`.", "range": "[REQUIRED] string: The [A1 notation or R1C1 notation](/sheets/api/guides/concepts#cell) of the range to retrieve values from.", "spreadsheetId": "[REQUIRED] string: The ID of the spreadsheet to retrieve data from.", "valueRenderOption": "string: How values should be represented in the output. The default render option is FORMATTED_VALUE."}, "functionality": "Returns a range of values from a spreadsheet. The caller must specify the spreadsheet ID and a range.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Sheets API - spreadsheets.values.update", "api_call": "service.spreadsheets().values().update(spreadsheetId: str, range: str).execute()", "api_version": "v4", "api_arguments": {"includeValuesInResponse": "boolean: Determines if the update response should include the values of the cells that were updated. By default, responses do not include the updated values. If the range to write was larger than the range actually written, the response includes all values in the requested range (excluding trailing empty rows and columns).", "range": "[REQUIRED] string: The [A1 notation](/sheets/api/guides/concepts#cell) of the values to update.", "responseDateTimeRenderOption": "string: Determines how dates, times, and durations in the response should be rendered. This is ignored if response_value_render_option is FORMATTED_VALUE. The default dateTime render option is SERIAL_NUMBER.", "responseValueRenderOption": "string: Determines how values in the response should be rendered. The default render option is FORMATTED_VALUE.", "spreadsheetId": "[REQUIRED] string: The ID of the spreadsheet to update.", "valueInputOption": "string: How the input data should be interpreted."}, "functionality": "Sets values in a range of a spreadsheet. The caller must specify the spreadsheet ID, range, and a valueInputOption.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/sheets/api/reference/rest"}}]