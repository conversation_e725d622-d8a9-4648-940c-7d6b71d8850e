---
extra_gated_heading: >-
  Acknowledge to follow corresponding license and cite APIGen to access the
  repository
extra_gated_button_content: Agree and access repository
extra_gated_fields:
  First Name: text
  Last Name: text
  Country: country
  Affiliation: text
license: cc-by-4.0
task_categories:
- question-answering
- text-generation
- reinforcement-learning
language:
- en
tags:
- function-calling
- LLM Agent
- code
- synthetic
size_categories:
- 10K<n<100K
configs:
- config_name: dataset
  data_files:
  - split: train
    path: xlam_function_calling_60k.json
---


# APIGen Function-Calling Datasets

[Paper](https://arxiv.org/abs/2406.18518) | [Website](https://apigen-pipeline.github.io/) | [Models](https://huggingface.co/collections/Salesforce/xlam-models-65f00e2a0a63bbcd1c2dade4)

This repo contains 60,000 data collected by [APIGen](https://apigen-pipeline.github.io/), an automated data generation pipeline designed to produce verifiable high-quality datasets for function-calling applications. Each data in our dataset is verified through three hierarchical stages: format checking, actual function executions, and semantic verification, ensuring its reliability and correctness. 

We conducted human evaluation over 600 sampled data points, and the correct rate is above 95%, where the remaining 5% have minor issues like inaccurate arguments, etc.

The overall framework for the dataset collection procedure is shown below. See more details at our project [homepage](https://apigen-pipeline.github.io/).


<div style="text-align: center;">
    <img src="figures/overview.jpg" alt="overview" width="620" style="margin: auto;">
</div>

## 🎉 News
- **[July 2024]**: We are thrilled to announce the release of our two function-calling models: [xLAM-1b-fc-r](https://huggingface.co/Salesforce/xLAM-1b-fc-r) and [xLAM-7b-fc-r](https://huggingface.co/Salesforce/xLAM-7b-fc-r). These models have achieved impressive rankings, placing #3 and #25 on the [Berkeley Function-Calling Leaderboard](https://gorilla.cs.berkeley.edu/leaderboard.html#leaderboard), outperforming many significantly larger models. We also provide their GGUF files, which can be readily deployed on personal devices. Stay tuned for more powerful models coming soon.
- **[July 2024]**: We've addressed issues mentioned in discussion [#8](https://huggingface.co/datasets/Salesforce/xlam-function-calling-60k/discussions/8) by regenerating 1,896 affected data points. Thank you to the community for identifying these issues and helping us further improve the quality of our dataset!
- **[June 2024]**: We are pleased to see our work featured by [VentureBeat](https://venturebeat.com/ai/salesforce-proves-less-is-more-xlam-1b-tiny-giant-beats-bigger-ai-models/) and [新智元](https://mp.weixin.qq.com/s/B3gyaGwzlQaUXyI8n7Rguw).

## What is a Function-Calling Agent?

Function-calling agents are capable of executing functional API calls from plain language instructions. Imagine asking for today’s weather in Palo Alto. In response, a function-calling agent swiftly interprets this request, taps into the appropriate API—for example, `get_weather("Palo Alto", "today")`—and fetches real-time weather data. This advanced capability significantly broadens the practical applications of LLMs, allowing them to seamlessly interact with various digital platforms, from social media to financial services, enhancing our digital experiences in unprecedented ways.

<div style="text-align: center;">
    <img src="figures/function-call-overview.png" alt="function-call" width="620" style="margin: auto;">
</div>

## Datasets

The datasets were generated by [DeepSeek-V2-Chat](https://github.com/deepseek-ai/DeepSeek-V2) and [Mixtral-8x22B-Inst](https://huggingface.co/mistral-community/Mixtral-8x22B-v0.1). We leverage APIGen and collect 3,673 executable APIs across 21 different categories to generate diverse function-calling datasets in a scalable and structured manner. 

The first `33,659` data entries were generated by DeepSeek, i.e., from id `0` to id `33658`. The remaining ones were generated by Mixtral.

<div style="text-align: center;">
    <img src="figures/dataset_pie_chart.png" alt="Pie chart showing dataset distribution" width="380" style="margin: auto;">
</div>

The dataset is at `xlam_function_calling_60k.json`. After accepting the use terms and login in your Huggingface account, you can simply access the dataset by:

```python
from datasets import load_dataset
datasets = load_dataset("Salesforce/xlam-function-calling-60k")
```


## JSON Data Format for Query and Answers

This JSON data format is used to represent a query along with the available tools and the corresponding answers. Here's a description of the format:

## Structure

The JSON data consists of the following key-value pairs:

- `query` (string): The query or problem statement.
- `tools` (array): An array of available tools that can be used to solve the query.
  - Each tool is represented as an object with the following properties:
    - `name` (string): The name of the tool.
    - `description` (string): A brief description of what the tool does.
    - `parameters` (object): An object representing the parameters required by the tool.
      - Each parameter is represented as a key-value pair, where the key is the parameter name and the value is an object with the following properties:
        - `type` (string): The data type of the parameter (e.g., "int", "float", "list").
        - `description` (string): A brief description of the parameter.
        - `required` (boolean): Indicates whether the parameter is required or optional.
- `answers` (array): An array of answers corresponding to the query.
  - Each answer is represented as an object with the following properties:
    - `name` (string): The name of the tool used to generate the answer.
    - `arguments` (object): An object representing the arguments passed to the tool to generate the answer.
      - Each argument is represented as a key-value pair, where the key is the parameter name and the value is the corresponding value.

Note that we format the `query`, `tools`, and `answers` to a string, but you can easily recover each entry to the JSON object via `json.loads(...)`.

## Example

Here's an example JSON data:

```json
{
  "query": "Find the sum of all the multiples of 3 and 5 between 1 and 1000. Also find the product of the first five prime numbers.",
  "tools": [
    {
      "name": "math_toolkit.sum_of_multiples",
      "description": "Find the sum of all multiples of specified numbers within a specified range.",
      "parameters": {
        "lower_limit": {
          "type": "int",
          "description": "The start of the range (inclusive).",
          "required": true
        },
        "upper_limit": {
          "type": "int",
          "description": "The end of the range (inclusive).",
          "required": true
        },
        "multiples": {
          "type": "list",
          "description": "The numbers to find multiples of.",
          "required": true
        }
      }
    },
    {
      "name": "math_toolkit.product_of_primes",
      "description": "Find the product of the first n prime numbers.",
      "parameters": {
        "count": {
          "type": "int",
          "description": "The number of prime numbers to multiply together.",
          "required": true
        }
      }
    }
  ],
  "answers": [
    {
      "name": "math_toolkit.sum_of_multiples",
      "arguments": {
        "lower_limit": 1,
        "upper_limit": 1000,
        "multiples": [3, 5]
      }
    },
    {
      "name": "math_toolkit.product_of_primes",
      "arguments": {
        "count": 5
      }
    }
  ]
}
```

In this example, the query asks to find the sum of multiples of 3 and 5 between 1 and 1000, and also find the product of the first five prime numbers. The available tools are `math_toolkit.sum_of_multiples` and `math_toolkit.product_of_primes`, along with their parameter descriptions. The `answers` array provides the specific tool and arguments used to generate each answer.

## Benchmark Results

Along with the dataset, we also release two small-but-capable function-calling models as mentioned in the paper: [xLAM-1b-fc-r](https://huggingface.co/Salesforce/xLAM-1b-fc-r) and [xLAM-7b-fc-r](https://huggingface.co/Salesforce/xLAM-7b-fc-r).
We mainly test them on the [Berkeley Function-Calling Leaderboard (BFCL)](https://gorilla.cs.berkeley.edu/leaderboard.html), which offers a comprehensive evaluation framework for assessing LLMs' function-calling capabilities across various programming languages and application domains like Java, JavaScript, and Python. 


<div align="center">
<img src="https://github.com/apigen-pipeline/apigen-pipeline.github.io/blob/main/img/table-result-0718.png?raw=true" width="620" alt="Performance comparison on Berkeley Function-Calling Leaderboard">
<p>Performance comparison on the BFCL benchmark as of date 07/18/2024. Evaluated with <code>temperature=0.001</code> and <code>top_p=1</code></p> 
</div>

<p>Our <code>xLAM-7b-fc-r</code> secures the 3rd place with an overall accuracy of 88.24% on the leaderboard, outperforming many strong models. Notably, our <code>xLAM-1b-fc-r</code> model is the only tiny model with less than 2B parameters on the leaderboard, but still achieves a competitive overall accuracy of 78.94% and outperforming GPT3-Turbo and many larger models.
Both models exhibit balanced performance across various categories, showing their strong function-calling capabilities despite their small sizes.</p>

## Ethical Considerations

This release is for research purposes only in support of an academic paper. Our models, datasets, and code are not specifically designed or evaluated for all downstream purposes. We strongly recommend users evaluate and address potential concerns related to accuracy, safety, and fairness before deploying this model. We encourage users to consider the common limitations of AI, comply with applicable laws, and leverage best practices when selecting use cases, particularly for high-risk scenarios where errors or misuse could significantly impact people’s lives, rights, or safety. For further guidance on use cases, refer to our AUP and AI AUP. 

## Citation

If you found the dataset useful, please cite:

```bibtex
@article{liu2024apigen,
  title={APIGen: Automated Pipeline for Generating Verifiable and Diverse Function-Calling Datasets},
  author={Liu, Zuxin and Hoang, Thai and Zhang, Jianguo and Zhu, Ming and Lan, Tian and Kokane, Shirley and Tan, Juntao and Yao, Weiran and Liu, Zhiwei and Feng, Yihao and others},
  journal={arXiv preprint arXiv:2406.18518},
  year={2024}
}
```