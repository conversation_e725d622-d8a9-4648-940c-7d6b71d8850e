{"accuracy": 0.8, "correct_count": 320, "total_count": 400}
{"id": "simple_13", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'interval'. Expected outer type array with inner type <class 'float'>. Parameter value: [1, 3]."], "error_type": "type_error:nested", "prompt": {"id": "simple_13", "question": [[{"role": "user", "content": "Calculate the area under the curve y=x^2 from x=1 to x=3."}]], "function": [{"name": "calculate_area_under_curve", "description": "Calculate the area under a mathematical function within a given interval.", "parameters": {"type": "dict", "properties": {"function": {"type": "string", "description": "The mathematical function as a string."}, "interval": {"type": "array", "items": {"type": "float"}, "description": "An array that defines the interval to calculate the area under the curve from the start to the end point."}, "method": {"type": "string", "description": "The numerical method to approximate the area under the curve. The default value is 'trapezoidal'."}}, "required": ["function", "interval"]}}]}, "model_result_raw": "[calculate_area_under_curve(function='x**2', interval=[1, 3], method='trapezoidal')] \n", "model_result_decoded": [{"calculate_area_under_curve": {"function": "x**2", "interval": [1, 3], "method": "trapezoidal"}}], "possible_answer": [{"calculate_area_under_curve": {"function": ["x**2", "lambda x: x**2", "y=x**2"], "interval": [[1.0, 3.0]], "method": ["", "trapezoidal"]}}]}
{"id": "simple_18", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Incorrect type for parameter 'number'. Expected type integer, got dict. Parameter value: {'number': 123456}."], "error_type": "type_error:simple", "prompt": {"id": "simple_18", "question": [[{"role": "user", "content": "Find the prime factors of the number 123456."}]], "function": [{"name": "number_analysis.prime_factors", "description": "Compute the prime factors of a number.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number to be factored."}}, "required": ["number"]}}]}, "model_result_raw": "[number_analysis.prime_factors(number={'number': 123456})] \n", "model_result_decoded": [{"number_analysis.prime_factors": {"number": {"number": 123456}}}], "possible_answer": [{"number_analysis.prime_factors": {"number": [123456]}}]}
{"id": "simple_21", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'number1'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_21", "question": [[{"role": "user", "content": "Find the Greatest Common Divisor (GCD) of two numbers, say 36 and 48."}]], "function": [{"name": "number_theory.gcd", "description": "Compute the greatest common divisor of two given integers.", "parameters": {"type": "dict", "properties": {"number1": {"type": "integer", "description": "The first integer."}, "number2": {"type": "integer", "description": "The second integer."}}, "required": ["number1", "number2"]}}]}, "model_result_raw": "[number_theory.gcd({'number1': 36, 'number2': 48})] \n", "model_result_decoded": [{"number_theory.gcd": {}}], "possible_answer": [{"number_theory.gcd": {"number1": [36], "number2": [48]}}]}
{"id": "simple_23", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid syntax. Failed to decode AST. Unsupported AST type: <class 'ast.Set'>"], "error_type": "ast_decoder:decoder_failed", "prompt": {"id": "simple_23", "question": [[{"role": "user", "content": "What is the prime factorization of the number 60? Return them in the form of dictionary"}]], "function": [{"name": "prime_factorize", "description": "Calculate the prime factorization of a given integer.", "parameters": {"type": "dict", "properties": {"number": {"type": "integer", "description": "The number for which to calculate the prime factorization."}, "return_type": {"type": "string", "description": "Determines the format of the returned prime factorization. Can be 'list' for a list of all prime factors or 'dictionary' for a count of each prime factor. Default is 'list'."}}, "required": ["number"]}}]}, "model_result_raw": "[prime_factorize(number={60}, return_type='dictionary')] \n", "possible_answer": [{"prime_factorize": {"number": [60], "return_type": ["dictionary"]}}]}
{"id": "simple_35", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'operating_hours': 11. Expected one of [23]."], "error_type": "value_error:others", "prompt": {"id": "simple_35", "question": [[{"role": "user", "content": "Find an all vegan restaurant in New York that opens until at least 11 PM."}]], "function": [{"name": "vegan_restaurant.find_nearby", "description": "Locate nearby vegan restaurants based on specific criteria like operating hours.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. New York, NY, you should format it as City, State."}, "operating_hours": {"type": "integer", "description": "Preferred latest closing time of the restaurant. E.g. if 11 is given, then restaurants that close at or after 11 PM will be considered. This is in 24 hour format. Default is 24."}}, "required": ["location"]}}]}, "model_result_raw": "[vegan_restaurant.find_nearby(location='New York, NY', operating_hours=11)] \n", "model_result_decoded": [{"vegan_restaurant.find_nearby": {"location": "New York, NY", "operating_hours": 11}}], "possible_answer": [{"vegan_restaurant.find_nearby": {"location": ["New York, NY"], "operating_hours": [23]}}]}
{"id": "simple_42", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'inductance': 50.0. Expected one of [0.05]."], "error_type": "value_error:others", "prompt": {"id": "simple_42", "question": [[{"role": "user", "content": "Calculate the resonant frequency of an LC circuit given capacitance of 100\u00b5F and inductance of 50mH."}]], "function": [{"name": "calculate_resonant_frequency", "description": "Calculate the resonant frequency of an LC (inductor-capacitor) circuit.", "parameters": {"type": "dict", "properties": {"inductance": {"type": "float", "description": "The inductance (L) in henries (H)."}, "capacitance": {"type": "float", "description": "The capacitance (C) in farads (F)."}, "round_off": {"type": "integer", "description": "Rounding off the result to a certain decimal places, default is 2."}}, "required": ["inductance", "capacitance"]}}]}, "model_result_raw": "[calculate_resonant_frequency(inductance=50.0, capacitance=100e-6, round_off=2)] \n", "model_result_decoded": [{"calculate_resonant_frequency": {"inductance": 50.0, "capacitance": 0.0001, "round_off": 2}}], "possible_answer": [{"calculate_resonant_frequency": {"inductance": [0.05], "capacitance": [0.0001], "round_off": ["", 2]}}]}
{"id": "simple_55", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Optional parameter 'detailed' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_55", "question": [[{"role": "user", "content": "Find me detailed information about the structure of human cell"}]], "function": [{"name": "biology.get_cell_info", "description": "Retrieve information about the structure and functioning of a specified type of cell", "parameters": {"type": "dict", "properties": {"cell_type": {"type": "string", "description": "Type of cell you want information about"}, "detailed": {"type": "boolean", "description": "Indicate if you want a detailed description of the cell", "default": "false"}}, "required": ["cell_type"]}}]}, "model_result_raw": "[biology.get_cell_info(cell_type='human')] \n", "model_result_decoded": [{"biology.get_cell_info": {"cell_type": "human"}}], "possible_answer": [{"biology.get_cell_info": {"cell_type": ["human"], "detailed": [true]}}]}
{"id": "simple_60", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'snp_id'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_60", "question": [[{"role": "user", "content": "Find the type of gene mutation based on SNP (Single Nucleotide Polymorphism) ID rs6034464."}]], "function": [{"name": "mutation_type.find", "description": "Finds the type of a genetic mutation based on its SNP (Single Nucleotide Polymorphism) ID.", "parameters": {"type": "dict", "properties": {"snp_id": {"type": "string", "description": "The ID of the Single Nucleotide Polymorphism (SNP) mutation."}, "species": {"type": "string", "description": "Species in which the SNP occurs, default is 'Homo sapiens' (Humans)."}}, "required": ["snp_id"]}}]}, "model_result_raw": "[mutation_type.find(type={'snp_id': 'rs6034464'}, properties={'species': 'Homo sapiens'})] \n", "model_result_decoded": [{"mutation_type.find": {"type": {"snp_id": "rs6034464"}, "properties": {"species": "Homo sapiens"}}}], "possible_answer": [{"mutation_type.find": {"snp_id": ["rs6034464"], "species": ["Homo sapiens", ""]}}]}
{"id": "simple_61", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'weight'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_61", "question": [[{"role": "user", "content": "Predict whether a person with weight 150lbs and height 5ft 10in who is lightly active will get type 2 diabetes."}]], "function": [{"name": "diabetes_prediction", "description": "Predict the likelihood of diabetes type 2 based on a person's weight and height.", "parameters": {"type": "dict", "properties": {"weight": {"type": "integer", "description": "Weight of the person in lbs."}, "height": {"type": "integer", "description": "Height of the person in inches."}, "activity_level": {"type": "string", "enum": ["sedentary", "lightly active", "moderately active", "very active", "extra active"], "description": "Physical activity level of the person."}}, "required": ["weight", "height", "activity_level"]}}]}, "model_result_raw": "[diabetes_prediction(type={'weight': 150, 'height': 65, 'activity_level': 'lightly active'}, required=['weight', 'height', 'activity_level']) ] \n", "model_result_decoded": [{"diabetes_prediction": {"type": {"weight": 150, "height": 65, "activity_level": "lightly active"}, "required": ["weight", "height", "activity_level"]}}], "possible_answer": [{"diabetes_prediction": {"weight": [150], "height": [70], "activity_level": ["lightly active"]}}]}
{"id": "simple_65", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'population': 213. Expected one of [213000000]."], "error_type": "value_error:others", "prompt": {"id": "simple_65", "question": [[{"role": "user", "content": "Calculate the Population Density for Brazil in 2022 if the population is 213 million and the land area is 8.5 million square kilometers."}]], "function": [{"name": "calculate_density", "description": "Calculate the population density of a specific country in a specific year.", "parameters": {"type": "dict", "properties": {"country": {"type": "string", "description": "The country for which the density needs to be calculated."}, "year": {"type": "string", "description": "The year in which the density is to be calculated."}, "population": {"type": "integer", "description": "The population of the country."}, "land_area": {"type": "integer", "description": "The land area of the country in square kilometers."}}, "required": ["country", "year", "population", "land_area"]}}]}, "model_result_raw": "[calculate_density(country='Brazil', year='2022', population=213, land_area=8500000)] \n", "model_result_decoded": [{"calculate_density": {"country": "Brazil", "year": "2022", "population": 213, "land_area": 8500000}}], "possible_answer": [{"calculate_density": {"country": ["Brazil"], "year": ["2022"], "population": [213000000], "land_area": [8500000]}}]}
{"id": "simple_67", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Optional parameter 'size' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_67", "question": [[{"role": "user", "content": "Identify a small green bird in forest."}]], "function": [{"name": "identify_bird", "description": "Identify a bird species based on certain characteristics.", "parameters": {"type": "dict", "properties": {"color": {"type": "string", "description": "Color of the bird."}, "habitat": {"type": "string", "description": "Habitat of the bird."}, "size": {"type": "string", "enum": ["small", "medium", "large"], "description": "Size of the bird. Default is 'small'"}}, "required": ["color", "habitat"]}}]}, "model_result_raw": "[identify_bird(color='green', habitat='forest')] \n", "model_result_decoded": [{"identify_bird": {"color": "green", "habitat": "forest"}}], "possible_answer": [{"identify_bird": {"color": ["green"], "habitat": ["forest"], "size": ["small"]}}]}
{"id": "simple_69", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'species': False. Expected one of [True]."], "error_type": "value_error:others", "prompt": {"id": "simple_69", "question": [[{"role": "user", "content": "Find out the population and species of turtles in Mississippi river in 2020."}]], "function": [{"name": "ecology.get_turtle_population", "description": "Get the population and species of turtles in a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The name of the location."}, "year": {"type": "integer", "description": "The year of the data requested. Default is 2001."}, "species": {"type": "boolean", "description": "Whether to include species information. Default is false."}}, "required": ["location"]}}]}, "model_result_raw": "[ecology.get_turtle_population(location='Mississippi river', year=2020, species=False)] \n", "model_result_decoded": [{"ecology.get_turtle_population": {"location": "Mississippi river", "year": 2020, "species": false}}], "possible_answer": [{"ecology.get_turtle_population": {"location": ["Mississippi river"], "year": [2020], "species": [true]}}]}
{"id": "simple_70", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'vehicle_type'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_70", "question": [[{"role": "user", "content": "What is the carbon footprint of a gas-powered vehicle driving 1500 miles in a year?"}]], "function": [{"name": "calculate_vehicle_emission", "description": "Calculate the annual carbon emissions produced by a specific type of vehicle based on mileage.", "parameters": {"type": "dict", "properties": {"vehicle_type": {"type": "string", "description": "The type of vehicle. 'gas' refers to a gasoline vehicle, 'diesel' refers to a diesel vehicle, and 'EV' refers to an electric vehicle."}, "miles_driven": {"type": "integer", "description": "The number of miles driven per year."}, "emission_factor": {"type": "float", "description": "Optional emission factor to calculate emissions, in g/mile. Default factor is 355.48."}}, "required": ["vehicle_type", "miles_driven"]}}]}, "model_result_raw": "[calculate_vehicle_emission(type='gas', miles_driven=1500, emission_factor=355.48)] \n", "model_result_decoded": [{"calculate_vehicle_emission": {"type": "gas", "miles_driven": 1500, "emission_factor": 355.48}}], "possible_answer": [{"calculate_vehicle_emission": {"vehicle_type": ["gas"], "miles_driven": [1500], "emission_factor": ["", 355.48]}}]}
{"id": "simple_71", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Incorrect type for parameter 'preferences'. Expected type array, got dict. Parameter value: {'G': 5, 'C': 5}."], "error_type": "type_error:simple", "prompt": {"id": "simple_71", "question": [[{"role": "user", "content": "Generate a DNA sequence with 100 bases including more G (Guanine) and C (Cytosine)."}]], "function": [{"name": "generate_DNA_sequence", "description": "Generate a random DNA sequence with a specific length and nucleotide preference.", "parameters": {"type": "dict", "properties": {"length": {"type": "integer", "description": "The length of the DNA sequence to be generated."}, "preferences": {"type": "array", "items": {"type": "string", "enum": ["A", "T", "C", "G"]}, "description": "Preferred nucleotides to include more frequently in the DNA sequence."}}, "required": ["length", "preferences"]}}]}, "model_result_raw": "[generate_DNA_sequence(length=100, preferences={'G': 5, 'C': 5})] \n", "model_result_decoded": [{"generate_DNA_sequence": {"length": 100, "preferences": {"G": 5, "C": 5}}}], "possible_answer": [{"generate_DNA_sequence": {"length": [100], "preferences": [["G", "C"], ["C", "G"]]}}]}
{"id": "simple_72", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'trait_values': [0.8]. Expected one of [[0.8, 0.7]]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_72", "question": [[{"role": "user", "content": "Calculate the expected evolutionary fitness of a creature, with trait A contributing to 40% of the fitness and trait B contributing 60%, if trait A has a value of 0.8 and trait B a value of 0.7."}]], "function": [{"name": "calculate_fitness", "description": "Calculate the expected evolutionary fitness of a creature based on the individual values and contributions of its traits.", "parameters": {"type": "dict", "properties": {"trait_values": {"type": "array", "items": {"type": "float"}, "description": "List of trait values, which are decimal numbers between 0 and 1, where 1 represents the trait maximally contributing to fitness."}, "trait_contributions": {"type": "array", "items": {"type": "float"}, "description": "List of the percentage contributions of each trait to the overall fitness, which must sum to 1."}}, "required": ["trait_values", "trait_contributions"]}}]}, "model_result_raw": "[calculate_fitness(trait_values=[0.8], trait_contributions=[0.4, 0.6])] \n", "model_result_decoded": [{"calculate_fitness": {"trait_values": [0.8], "trait_contributions": [0.4, 0.6]}}], "possible_answer": [{"calculate_fitness": {"trait_values": [[0.8, 0.7]], "trait_contributions": [[0.4, 0.6]]}}]}
{"id": "simple_82", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'numbers'. Expected outer type array with inner type <class 'float'>. Parameter value: [12, 15, 18, 20, 21, 26, 30]."], "error_type": "type_error:nested", "prompt": {"id": "simple_82", "question": [[{"role": "user", "content": "Calculate the average of list of integers [12, 15, 18, 20, 21, 26, 30]."}]], "function": [{"name": "calculate_average", "description": "Calculates the average of a list of numbers.", "parameters": {"type": "dict", "properties": {"numbers": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to calculate the average of."}}, "required": ["numbers"]}}]}, "model_result_raw": "[calculate_average(numbers=[12, 15, 18, 20, 21, 26, 30])] \n", "model_result_decoded": [{"calculate_average": {"numbers": [12, 15, 18, 20, 21, 26, 30]}}], "possible_answer": [{"calculate_average": {"numbers": [[12.0, 15.0, 18.0, 20.0, 21.0, 26.0, 30.0]]}}]}
{"id": "simple_83", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Incorrect type for parameter 'coord1'. Expected type tuple, got dict. Parameter value: {'type': 'dict', 'properties': {'coord1': {'type': 'tuple', 'description': 'The first coordinate as (latitude, longitude).', 'items': {'type': 'float', 'value': 33.4484}}, 'coord2': {'type': 'tuple', 'description': 'The second coordinate as (latitude, longitude).', 'items': {'type': 'float', 'value': 34.0522}}, 'unit': {'type': 'string', 'description': \"The unit of distance. Options: 'miles', 'kilometers'.\"}}, 'required': ['coord1', 'coord2', 'unit']}."], "error_type": "type_error:simple", "prompt": {"id": "simple_83", "question": [[{"role": "user", "content": "Calculate the distance between two GPS coordinates (33.4484 N, 112.0740 W) and (34.0522 N, 118.2437 W) in miles."}]], "function": [{"name": "calculate_distance", "description": "Calculate the distance between two GPS coordinates.", "parameters": {"type": "dict", "properties": {"coord1": {"type": "tuple", "description": "The first coordinate as (latitude, longitude).", "items": {"type": "float"}}, "coord2": {"type": "tuple", "description": "The second coordinate as (latitude, longitude).", "items": {"type": "float"}}, "unit": {"type": "string", "description": "The unit of distance. Options: 'miles', 'kilometers'."}}, "required": ["coord1", "coord2", "unit"]}}]}, "model_result_raw": "[calculate_distance(coord1={'type': 'dict', 'properties': {'coord1': {'type': 'tuple', 'description': 'The first coordinate as (latitude, longitude).', 'items': {'type': 'float', 'value': 33.4484}}, 'coord2': {'type': 'tuple', 'description': 'The second coordinate as (latitude, longitude).', 'items': {'type': 'float', 'value': 34.0522}}, 'unit': {'type': 'string', 'description': \"The unit of distance. Options: 'miles', 'kilometers'.\"}}, 'required': ['coord1', 'coord2', 'unit']}, coord2={'type': 'dict', 'properties': {'coord1': {'type': 'tuple', 'description': 'The first coordinate as (latitude, longitude).', 'items': {'type': 'float', 'value': 112.0740}}, 'coord2': {'type': 'tuple', 'description': 'The second coordinate as (latitude, longitude).', 'items': {'type': 'float', 'value': 118.2437}}, 'unit': {'type': 'string', 'description': \"The unit of distance. Options: 'miles', 'kilometers'.\"}}, 'required': ['coord1', 'coord2', 'unit']}, unit='miles')]", "model_result_decoded": [{"calculate_distance": {"coord1": {"type": "dict", "properties": {"coord1": {"type": "tuple", "description": "The first coordinate as (latitude, longitude).", "items": {"type": "float", "value": 33.4484}}, "coord2": {"type": "tuple", "description": "The second coordinate as (latitude, longitude).", "items": {"type": "float", "value": 34.0522}}, "unit": {"type": "string", "description": "The unit of distance. Options: 'miles', 'kilometers'."}}, "required": ["coord1", "coord2", "unit"]}, "coord2": {"type": "dict", "properties": {"coord1": {"type": "tuple", "description": "The first coordinate as (latitude, longitude).", "items": {"type": "float", "value": 112.074}}, "coord2": {"type": "tuple", "description": "The second coordinate as (latitude, longitude).", "items": {"type": "float", "value": 118.2437}}, "unit": {"type": "string", "description": "The unit of distance. Options: 'miles', 'kilometers'."}}, "required": ["coord1", "coord2", "unit"]}, "unit": "miles"}}], "possible_answer": [{"calculate_distance": {"coord1": [[33.4484, -112.074]], "coord2": [[34.0522, -118.2437]], "unit": ["miles"]}}]}
{"id": "simple_87", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'list'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_87", "question": [[{"role": "user", "content": "Sort the list [5, 3, 4, 1, 2] in ascending order."}]], "function": [{"name": "array_sort", "description": "Sorts a given list in ascending or descending order.", "parameters": {"type": "dict", "properties": {"list": {"type": "array", "items": {"type": "float"}, "description": "The list of numbers to be sorted."}, "order": {"type": "string", "enum": ["ascending", "descending"], "description": "Order of sorting."}}, "required": ["list", "order"]}}]}, "model_result_raw": "[array_sort({'list': [5, 3, 4, 1, 2], 'order': 'ascending'})] \n", "model_result_decoded": [{"array_sort": {}}], "possible_answer": [{"array_sort": {"list": [[5.0, 3.0, 4.0, 1.0, 2.0]], "order": ["ascending"]}}]}
{"id": "simple_89", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'table_name': 'Student'. Expected one of ['students']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_89", "question": [[{"role": "user", "content": "Fetch all records for students studying Science in 'Bluebird High School' from the StudentDB."}]], "function": [{"name": "db_fetch_records", "description": "Fetch records from a specified database table based on certain conditions.", "parameters": {"type": "dict", "properties": {"database_name": {"type": "string", "description": "The name of the database."}, "table_name": {"type": "string", "description": "The name of the table from which records need to be fetched."}, "conditions": {"type": "dict", "properties": {"department": {"type": "string", "description": "The name of the department of students."}, "school": {"type": "string", "description": "The name of the school students are enrolled in."}}, "description": "The conditions based on which records are to be fetched."}, "fetch_limit": {"type": "integer", "description": "Limits the number of records to be fetched. Default is 0, which means no limit."}}, "required": ["database_name", "table_name", "conditions"]}}]}, "model_result_raw": "[db_fetch_records(database_name='StudentDB', table_name='Student', conditions={'department': 'Science', 'school': 'Bluebird High School' })] \n", "model_result_decoded": [{"db_fetch_records": {"database_name": "StudentDB", "table_name": "Student", "conditions": {"department": "Science", "school": "Bluebird High School"}}}], "possible_answer": [{"db_fetch_records": {"database_name": ["StudentDB"], "table_name": ["students"], "conditions": [{"department": ["Science"], "school": ["Bluebird High School", "Bluebird HS"]}], "fetch_limit": ["", 0]}}]}
{"id": "simple_91", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'condition': 'operating days=Sunday'. Expected one of ['open on Sundays', 'opens on Sundays']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_91", "question": [[{"role": "user", "content": "Get the highest rated sushi restaurant in Boston, that opens on Sundays."}]], "function": [{"name": "get_restaurant", "description": "Retrieve highest rated restaurant given cuisine, location, and a condition.", "parameters": {"type": "dict", "properties": {"cuisine": {"type": "string", "description": "Cuisine of the restaurant."}, "location": {"type": "string", "description": "City where restaurant is located."}, "condition": {"type": "string", "description": "Condition to be met by the restaurant (e.g., operating days, amenities, etc.)"}}, "required": ["cuisine", "location", "condition"]}}]}, "model_result_raw": "[get_restaurant(cuisine='sushi', location='Boston', condition='operating days=Sunday')] \n", "model_result_decoded": [{"get_restaurant": {"cuisine": "sushi", "location": "Boston", "condition": "operating days=Sunday"}}], "possible_answer": [{"get_restaurant": {"cuisine": ["sushi"], "location": ["Boston"], "condition": ["open on Sundays", "opens on Sundays"]}}]}
{"id": "simple_96", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid syntax. Failed to decode AST. ':' expected after dictionary key (<unknown>, line 1)"], "error_type": "ast_decoder:decoder_failed", "prompt": {"id": "simple_96", "question": [[{"role": "user", "content": "Find records in database in user table where age is greater than 25 and job is 'engineer'."}]], "function": [{"name": "database.query", "description": "Query the database based on certain conditions.", "parameters": {"type": "dict", "properties": {"table": {"type": "string", "description": "Name of the table to query."}, "conditions": {"type": "array", "items": {"type": "dict", "properties": {"field": {"type": "string", "description": "The field to apply the condition."}, "operation": {"type": "string", "description": "The operation to be performed.", "enum": ["<", ">", "=", ">=", "<="]}, "value": {"type": "string", "description": "The value to be compared."}}, "required": ["field", "operation", "value"]}, "description": "Conditions for the query."}}, "required": ["table", "conditions"]}}]}, "model_result_raw": "[database.query(table='user', conditions=[{'field': 'age', 'operation': '>', 'value': '25'}, {'field': 'job', 'operation': '=', 'engineer'}]) ] \n", "possible_answer": [{"database.query": {"table": ["user"], "conditions": [[{"field": ["age"], "operation": [">"], "value": ["25"]}, {"field": ["job"], "operation": ["="], "value": ["engineer"]}]]}}]}
{"id": "simple_98", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'hours': 18. Expected one of [6]."], "error_type": "value_error:others", "prompt": {"id": "simple_98", "question": [[{"role": "user", "content": "What will be the angle between the hour and minute hands of a clock at 6:30 PM?"}]], "function": [{"name": "calculate_clock_angle", "description": "Calculate the angle between the hour and minute hands of a clock at a given time.", "parameters": {"type": "dict", "properties": {"hours": {"type": "integer", "description": "The hour on the clock face."}, "minutes": {"type": "integer", "description": "The minutes on the clock face."}, "round_to": {"type": "integer", "description": "The number of decimal places to round the result to, default is 2."}}, "required": ["hours", "minutes"]}}]}, "model_result_raw": "[calculate_clock_angle(hours=18, minutes=30, round_to=2)] \n", "model_result_decoded": [{"calculate_clock_angle": {"hours": 18, "minutes": 30, "round_to": 2}}], "possible_answer": [{"calculate_clock_angle": {"hours": [6], "minutes": [30], "round_to": ["", 2]}}]}
{"id": "simple_99", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'end_range': 6.283185307179586. Expected one of [6.2832]."], "error_type": "value_error:others", "prompt": {"id": "simple_99", "question": [[{"role": "user", "content": "Plot a sine wave from 0 to 2 pi with a frequency of 5 Hz."}]], "function": [{"name": "plot_sine_wave", "description": "Plot a sine wave for a given frequency in a given range.", "parameters": {"type": "dict", "properties": {"start_range": {"type": "float", "description": "Start of the range in radians. Four decimal places."}, "end_range": {"type": "float", "description": "End of the range in radians. Four decimal places."}, "frequency": {"type": "integer", "description": "Frequency of the sine wave in Hz."}, "amplitude": {"type": "integer", "description": "Amplitude of the sine wave. Default is 1."}, "phase_shift": {"type": "integer", "description": "Phase shift of the sine wave in radians. Default is 0."}}, "required": ["start_range", "end_range", "frequency"]}}]}, "model_result_raw": "[plot_sine_wave(start_range=0, end_range=2*3.141592653589793, frequency=5, amplitude=1, phase_shift=0)] \n", "model_result_decoded": [{"plot_sine_wave": {"start_range": 0, "end_range": 6.283185307179586, "frequency": 5, "amplitude": 1, "phase_shift": 0}}], "possible_answer": [{"plot_sine_wave": {"start_range": [0.0], "end_range": [6.2832], "frequency": [5], "amplitude": [1, ""], "phase_shift": [0, ""]}}]}
{"id": "simple_103", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Nested type checking failed for parameter 'polynomial'. Expected outer type array with inner type <class 'float'>. Parameter value: [3, 2, 1]."], "error_type": "type_error:nested", "prompt": {"id": "simple_103", "question": [[{"role": "user", "content": "Calculate the area under the curve y=3x^2 + 2x - 4, between x = -1 and x = 2."}]], "function": [{"name": "mathematics.calculate_area_under_curve", "description": "Calculate the area under the curve for a given polynomial function within a specified interval.", "parameters": {"type": "dict", "properties": {"polynomial": {"type": "array", "items": {"type": "float"}, "description": "The coefficients of the polynomial, in decreasing order of exponent, where the first element is the coefficient for x^n, the second element is the coefficient for x^(n-1), and so on. The last element is the constant term."}, "limits": {"type": "array", "items": {"type": "float"}, "description": "A list of two numbers specifying the lower and upper limit for the integration interval."}}, "required": ["polynomial", "limits"]}}]}, "model_result_raw": "[mathematics.calculate_area_under_curve(polynomial=[3, 2, 1], limits=[-1, 2])] \n", "model_result_decoded": [{"mathematics.calculate_area_under_curve": {"polynomial": [3, 2, 1], "limits": [-1, 2]}}], "possible_answer": [{"mathematics.calculate_area_under_curve": {"polynomial": [[3.0, 2.0, -4.0]], "limits": [[-1.0, 2.0]]}}]}
{"id": "simple_112", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'total_outcomes'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_112", "question": [[{"role": "user", "content": "Calculate the probability of drawing a king from a deck of cards."}]], "function": [{"name": "calculate_probability", "description": "Calculate the probability of an event.", "parameters": {"type": "dict", "properties": {"total_outcomes": {"type": "integer", "description": "Total number of possible outcomes."}, "favorable_outcomes": {"type": "integer", "description": "Number of outcomes considered as 'successful'."}, "round_to": {"type": "integer", "description": "Number of decimal places to round the result to.", "default": 2}}, "required": ["total_outcomes", "favorable_outcomes"]}}]}, "model_result_raw": "[calculate_probability(type={'total_outcomes': 52, 'favorable_outcomes': 4, 'round_to': 2}, required=['total_outcomes', 'favorable_outcomes']) ] \n", "model_result_decoded": [{"calculate_probability": {"type": {"total_outcomes": 52, "favorable_outcomes": 4, "round_to": 2}, "required": ["total_outcomes", "favorable_outcomes"]}}], "possible_answer": [{"calculate_probability": {"total_outcomes": [52], "favorable_outcomes": [4], "round_to": ["", 2]}}]}
{"id": "simple_117", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'success_outcomes'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_117", "question": [[{"role": "user", "content": "What are the odds of pulling a heart suit from a well-shuffled standard deck of 52 cards? Format it as ratio."}]], "function": [{"name": "probability_of_event", "description": "Calculates the probability of an event.", "parameters": {"type": "dict", "properties": {"success_outcomes": {"type": "integer", "description": "The number of successful outcomes."}, "total_outcomes": {"type": "integer", "description": "The total number of possible outcomes."}, "format_as_ratio": {"type": "boolean", "description": "When true, formats the output as a ratio instead of a decimal. Default is false."}}, "required": ["success_outcomes", "total_outcomes"]}}]}, "model_result_raw": "[probability_of_event(params_type=dict, params_success_outcomes=13, params_total_outcomes=52, params_format_as_ratio=True)] \n", "model_result_decoded": [{"probability_of_event": {"params_type": "dict", "params_success_outcomes": 13, "params_total_outcomes": 52, "params_format_as_ratio": true}}], "possible_answer": [{"probability_of_event": {"success_outcomes": [13], "total_outcomes": [52], "format_as_ratio": [true]}}]}
{"id": "simple_119", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'sample1'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_119", "question": [[{"role": "user", "content": "Perform a hypothesis test for two independent samples with scores of Sample1: [22,33,42,12,34] and Sample2: [23,45,44,14,38] at a significance level of 0.05."}]], "function": [{"name": "hypothesis_testing.ttest_ind", "description": "Conducts a hypothesis test for two independent samples.", "parameters": {"type": "dict", "properties": {"sample1": {"type": "array", "items": {"type": "integer"}, "description": "First set of observations (array of numbers)."}, "sample2": {"type": "array", "items": {"type": "integer"}, "description": "Second set of observations (array of numbers)."}, "significance_level": {"type": "float", "description": "Significance level of the test (default: 0.05)"}}, "required": ["sample1", "sample2"]}}]}, "model_result_raw": "[hypothesis_testing.ttest_ind({'sample1': [22,33,42,12,34], 'sample2': [23,45,44,14,38], 'significance_level': 0.05})] \n", "model_result_decoded": [{"hypothesis_testing.ttest_ind": {}}], "possible_answer": [{"hypothesis_testing.ttest_ind": {"sample1": [[22, 33, 42, 12, 34]], "sample2": [[23, 45, 44, 14, 38]], "significance_level": [0.05]}}]}
{"id": "simple_130", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'cash_flows'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_130", "question": [[{"role": "user", "content": "What's the NPV (Net Present Value) of a series of cash flows: [-50000, 10000, 15000, 20000, 25000, 30000] discounted at 8% annually?"}]], "function": [{"name": "finance_calculator.npv", "description": "Calculate the Net Present Value (NPV) for a series of cash flows discounted at a certain interest rate.", "parameters": {"type": "dict", "properties": {"cash_flows": {"type": "array", "items": {"type": "integer"}, "description": "A list of cash flows."}, "discount_rate": {"type": "float", "description": "The annual interest rate used to discount the cash flows."}, "years": {"type": "array", "items": {"type": "integer"}, "description": "A list of years when the cash flow occurs. Default is empty array."}}, "required": ["cash_flows", "discount_rate"]}}]}, "model_result_raw": "[finance_calculator.npv({'cash_flows': [-50000, 10000, 15000, 20000, 25000, 30000], 'discount_rate': 0.08, 'years': []})] \n", "model_result_decoded": [{"finance_calculator.npv": {}}], "possible_answer": [{"finance_calculator.npv": {"cash_flows": [[-50000, 10000, 15000, 20000, 25000, 30000]], "discount_rate": [0.08], "years": ["", []]}}]}
{"id": "simple_136", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'annual_rate': 0.05. Expected one of [5.0]."], "error_type": "value_error:others", "prompt": {"id": "simple_136", "question": [[{"role": "user", "content": "Find the compound interest for an investment of $10000 with an annual interest rate of 5% compounded monthly for 5 years."}]], "function": [{"name": "compound_interest", "description": "Calculate compound interest for a certain time period.", "parameters": {"type": "dict", "properties": {"principal": {"type": "integer", "description": "The initial amount of money that was invested or loaned out."}, "annual_rate": {"type": "float", "description": "The interest rate for a year as a percentage."}, "compounding_freq": {"type": "string", "enum": ["monthly", "quarterly", "annually"], "description": "The number of times that interest is compounded per unit period."}, "time_in_years": {"type": "integer", "description": "The time the money is invested for in years."}}, "required": ["principal", "annual_rate", "compounding_freq", "time_in_years"]}}]}, "model_result_raw": "[compound_interest(principal=10000, annual_rate=0.05, compounding_freq='monthly', time_in_years=5)] \n", "model_result_decoded": [{"compound_interest": {"principal": 10000, "annual_rate": 0.05, "compounding_freq": "monthly", "time_in_years": 5}}], "possible_answer": [{"compound_interest": {"principal": [10000], "annual_rate": [5.0], "compounding_freq": ["monthly"], "time_in_years": [5]}}]}
{"id": "simple_139", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'yearly_yield': 0.05. Expected one of [5.0]."], "error_type": "value_error:others", "prompt": {"id": "simple_139", "question": [[{"role": "user", "content": "What is the estimated return on a mutual fund, given that it has a yearly yield of 5%, an investment amount of $2000 and a time period of 3 years?"}]], "function": [{"name": "estimate_mutual_fund_return", "description": "Calculate the estimated return on a mutual fund given the yearly yield, the investment amount and the time period.", "parameters": {"type": "dict", "properties": {"yearly_yield": {"type": "float", "description": "The yearly yield of the mutual fund as a percentage."}, "investment_amount": {"type": "integer", "description": "The initial investment amount in the mutual fund."}, "years": {"type": "integer", "description": "The time period for which the investment is made in years."}}, "required": ["yearly_yield", "investment_amount", "years"]}}]}, "model_result_raw": "[estimate_mutual_fund_return(yearly_yield=0.05, investment_amount=2000, years=3)] \n", "model_result_decoded": [{"estimate_mutual_fund_return": {"yearly_yield": 0.05, "investment_amount": 2000, "years": 3}}], "possible_answer": [{"estimate_mutual_fund_return": {"yearly_yield": [5.0], "investment_amount": [2000], "years": [3]}}]}
{"id": "simple_158", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'location': 'New York'. Expected one of ['New York, NY']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_158", "question": [[{"role": "user", "content": "Find the information of criminal cases of Mr. X in New York between 2012 and 2015."}]], "function": [{"name": "get_criminal_records", "description": "Retrieve the criminal records of a specific person in a specific area during a certain time period.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the person."}, "location": {"type": "string", "description": "The city and state, e.g. New York, NY"}, "from_year": {"type": "integer", "description": "The start year of the time frame."}, "to_year": {"type": "integer", "description": "The end year of the time frame."}}, "required": ["name", "location", "from_year", "to_year"]}}]}, "model_result_raw": "[get_criminal_records(name='Mr. X', location='New York', from_year=2012, to_year=2015)] \n", "model_result_decoded": [{"get_criminal_records": {"name": "Mr. X", "location": "New York", "from_year": 2012, "to_year": 2015}}], "possible_answer": [{"get_criminal_records": {"name": ["Mr. X"], "location": ["New York, NY"], "from_year": [2012], "to_year": [2015]}}]}
{"id": "simple_162", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'contract_type': 'rental'. Expected one of ['Rental Agreement', 'rental agreement']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_162", "question": [[{"role": "user", "content": "Generate a customized law contract between John and Alice for rental agreement in California."}]], "function": [{"name": "generate_law_contract", "description": "Generates a customized law contract given involved parties, contract type and location.", "parameters": {"type": "dict", "properties": {"parties": {"type": "array", "items": {"type": "string"}, "description": "Parties involved in the contract."}, "contract_type": {"type": "string", "description": "Type of the contract."}, "location": {"type": "string", "description": "Location where the contract will be in effect."}}, "required": ["parties", "contract_type", "location"]}}]}, "model_result_raw": "[generate_law_contract(parties=['John', 'Alice'], contract_type='rental', location='California')] \n", "model_result_decoded": [{"generate_law_contract": {"parties": ["John", "Alice"], "contract_type": "rental", "location": "California"}}], "possible_answer": [{"generate_law_contract": {"parties": [["John", "Alice"], ["John", "Alice"]], "contract_type": ["Rental Agreement", "rental agreement"], "location": ["California", "CA"]}}]}
{"id": "simple_166", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'city': 'Chicago'. Expected one of ['Chicago, IL.', 'Chicago, IL']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_166", "question": [[{"role": "user", "content": "Find a lawyer specializing in divorce cases and charge fee less than 400 dollars per hour in Chicago."}]], "function": [{"name": "lawyer.find_nearby", "description": "Locate nearby lawyers based on specific criteria like specialty, fee per hour and city.", "parameters": {"type": "dict", "properties": {"city": {"type": "string", "description": "The city and state, e.g. Chicago, IL."}, "specialty": {"type": "array", "items": {"type": "string", "enum": ["Civil", "Divorce", "Immigration", "Business", "Criminal"]}, "description": "Specialization of the lawyer."}, "fee": {"type": "integer", "description": "Hourly fee charged by lawyer"}}, "required": ["city", "specialty", "fee"]}}]}, "model_result_raw": "[lawyer.find_nearby(city='Chicago', specialty=['Divorce'], fee=400)] \n", "model_result_decoded": [{"lawyer.find_nearby": {"city": "Chicago", "specialty": ["Divorce"], "fee": 400}}], "possible_answer": [{"lawyer.find_nearby": {"city": ["Chicago, IL.", "Chicago, IL"], "specialty": [["Divorce"]], "fee": [400]}}]}
{"id": "simple_173", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Optional parameter 'judicial_system' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_173", "question": [[{"role": "user", "content": "Find state law cases related to land disputes in the past 5 years from 2015 to 2021 in New York."}]], "function": [{"name": "law_case_search", "description": "Search and retrieve law cases based on the topic, timeline, and location.", "parameters": {"type": "dict", "properties": {"topic": {"type": "string", "description": "The subject matter of the case."}, "year_range": {"type": "array", "items": {"type": "integer"}, "description": "The start and end year for searching cases."}, "location": {"type": "string", "description": "The location where the case is being heard."}, "judicial_system": {"type": "string", "description": "The specific judicial system in which to search (e.g. 'federal', 'state').", "default": "all"}}, "required": ["topic", "year_range", "location"]}}]}, "model_result_raw": "[law_case_search(topic='land disputes', year_range=[2015, 2021], location='New York')] \n", "model_result_decoded": [{"law_case_search": {"topic": "land disputes", "year_range": [2015, 2021], "location": "New York"}}], "possible_answer": [{"law_case_search": {"topic": ["land disputes"], "year_range": [[2015, 2021]], "location": ["New York"], "judicial_system": ["state"]}}]}
{"id": "simple_177", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'status': 'open'. Expected one of ['all', '']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_177", "question": [[{"role": "user", "content": "Find all Patent lawsuit cases of Facebook in 2018."}]], "function": [{"name": "get_lawsuit_cases", "description": "Retrieve all lawsuit cases related to a specific company during a particular year.", "parameters": {"type": "dict", "properties": {"company_name": {"type": "string", "description": "The name of the company."}, "year": {"type": "integer", "description": "The specific year to search for lawsuit cases."}, "status": {"type": "string", "enum": ["open", "closed", "all"], "description": "The status of the lawsuit cases to retrieve. If not specified, defaults to 'all'."}}, "required": ["company_name", "year"]}}]}, "model_result_raw": "[get_lawsuit_cases(company_name='Facebook', year=2018, status='open')] \n", "model_result_decoded": [{"get_lawsuit_cases": {"company_name": "Facebook", "year": 2018, "status": "open"}}], "possible_answer": [{"get_lawsuit_cases": {"company_name": ["Facebook"], "year": [2018], "status": ["all", ""]}}]}
{"id": "simple_192", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Incorrect type for parameter 'point1'. Expected type array, got dict. Parameter value: {'latitude': 40.7128, 'longitude': -74.006}."], "error_type": "type_error:simple", "prompt": {"id": "simple_192", "question": [[{"role": "user", "content": "Calculate the slope gradient in degree between two points on a landscape with coordinates (40.7128, -74.0060) and (34.0522, -118.2437)."}]], "function": [{"name": "calculate_slope_gradient", "description": "Calculate the slope gradient between two geographical coordinates.", "parameters": {"type": "dict", "properties": {"point1": {"type": "array", "items": {"type": "float"}, "description": "The geographic coordinates for the first point [Latitude, Longitude]."}, "point2": {"type": "array", "items": {"type": "float"}, "description": "The geographic coordinates for the second point [Latitude, Longitude]."}, "unit": {"type": "string", "enum": ["degree", "percent", "ratio"], "description": "The unit for the slope gradient. Default is 'degree'."}}, "required": ["point1", "point2"]}}]}, "model_result_raw": "[calculate_slope_gradient(point1={latitude: 40.7128, longitude:-74.0060}, point2={latitude: 34.0522, longitude:-118.2437}, unit='degree')] \n", "model_result_decoded": [{"calculate_slope_gradient": {"point1": {"latitude": 40.7128, "longitude": -74.006}, "point2": {"latitude": 34.0522, "longitude": -118.2437}, "unit": "degree"}}], "possible_answer": [{"calculate_slope_gradient": {"point1": [[40.7128, -74.006]], "point2": [[34.0522, -118.2437]], "unit": ["degree", ""]}}]}
{"id": "simple_196", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'date': '2022/08/16'. Expected one of ['08-16-2022']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_196", "question": [[{"role": "user", "content": "What is the air quality index in London 2022/08/16?"}]], "function": [{"name": "air_quality", "description": "Retrieve the air quality index for a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the air quality index for."}, "date": {"type": "string", "description": "The date (month-day-year) you want to get the air quality index for."}}, "required": ["location", "date"]}}]}, "model_result_raw": "[air_quality(location='London', date='2022/08/16')] \n", "model_result_decoded": [{"air_quality": {"location": "London", "date": "2022/08/16"}}], "possible_answer": [{"air_quality": {"location": ["London"], "date": ["08-16-2022"]}}]}
{"id": "simple_197", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'time': '12:00:00'. Expected one of ['12pm', '12:00']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_197", "question": [[{"role": "user", "content": "Find the air quality index in San Diego at 12pm."}]], "function": [{"name": "get_air_quality_index", "description": "Retrieve the air quality index at a specified location and time.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location to get the air quality index for."}, "time": {"type": "string", "description": "The specific time to check the air quality. Default is the current time."}}, "required": ["location", "time"]}}]}, "model_result_raw": "[get_air_quality_index(location='San Diego', time='12:00:00')] \n", "model_result_decoded": [{"get_air_quality_index": {"location": "San Diego", "time": "12:00:00"}}], "possible_answer": [{"get_air_quality_index": {"location": ["San Diego"], "time": ["12pm", "12:00"]}}]}
{"id": "simple_202", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'energy_type': 'solar'. Expected one of ['renewable']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_202", "question": [[{"role": "user", "content": "How many greenhouse gas emissions would I save if I switched to renewable energy sources for 3 months in California?"}]], "function": [{"name": "calculate_emission_savings", "description": "Calculate potential greenhouse gas emissions saved by switching to renewable energy sources.", "parameters": {"type": "dict", "properties": {"energy_type": {"type": "string", "description": "Type of the renewable energy source."}, "usage_duration": {"type": "integer", "description": "Usage duration in months."}, "region": {"type": "string", "description": "The region where you use energy. Default is 'Texas'."}}, "required": ["energy_type", "usage_duration"]}}]}, "model_result_raw": "[calculate_emission_savings(energy_type='solar', usage_duration=3, region='California')] \n", "model_result_decoded": [{"calculate_emission_savings": {"energy_type": "solar", "usage_duration": 3, "region": "California"}}], "possible_answer": [{"calculate_emission_savings": {"energy_type": ["renewable"], "usage_duration": [3], "region": ["California", "CA"]}}]}
{"id": "simple_203", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Optional parameter 'detail' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_203", "question": [[{"role": "user", "content": "Can you find me the latest information about air quality index and pollution data for Chicago?"}]], "function": [{"name": "get_air_quality", "description": "Retrieve real-time air quality and pollution data for a specific location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city that you want to get the air quality data for."}, "detail": {"type": "boolean", "description": "If true, additional data like PM2.5, PM10, ozone levels, and pollution sources will be retrieved. Default is false."}}, "required": ["location"]}}]}, "model_result_raw": "[get_air_quality(location='Chicago')] \n", "model_result_decoded": [{"get_air_quality": {"location": "Chicago"}}], "possible_answer": [{"get_air_quality": {"location": ["Chicago"], "detail": [true]}}]}
{"id": "simple_208", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'avoid': ['highways', 'ferries']. Expected one of [['highways', 'tolls'], ['tolls', 'highways']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_208", "question": [[{"role": "user", "content": "Get me the directions from New York to Los Angeles avoiding highways and toll roads."}]], "function": [{"name": "map_service.get_directions", "description": "Retrieve directions from a starting location to an ending location, including options for route preferences.", "parameters": {"type": "dict", "properties": {"start": {"type": "string", "description": "Starting location for the route."}, "end": {"type": "string", "description": "Ending location for the route."}, "avoid": {"type": "array", "items": {"type": "string", "enum": ["tolls", "highways", "ferries"]}, "description": "Route features to avoid. Default is ['highways', 'ferries']"}}, "required": ["start", "end"]}}]}, "model_result_raw": "[map_service.get_directions(start='New York', end='Los Angeles', avoid=['highways', 'ferries'])] \n", "model_result_decoded": [{"map_service.get_directions": {"start": "New York", "end": "Los Angeles", "avoid": ["highways", "ferries"]}}], "possible_answer": [{"map_service.get_directions": {"start": ["New York", "NYC"], "end": ["Los Angeles", "LA"], "avoid": [["highways", "tolls"], ["tolls", "highways"]]}}]}
{"id": "simple_216", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid syntax. Failed to decode AST. unterminated string literal (detected at line 1) (<unknown>, line 1)"], "error_type": "ast_decoder:decoder_failed", "prompt": {"id": "simple_216", "question": [[{"role": "user", "content": "Analyze the sentiment of a customer review 'I love the food here! It's always fresh and delicious.'."}]], "function": [{"name": "sentiment_analysis", "description": "Perform sentiment analysis on a given piece of text.", "parameters": {"type": "dict", "properties": {"text": {"type": "string", "description": "The text on which to perform sentiment analysis."}, "language": {"type": "string", "description": "The language in which the text is written."}}, "required": ["text", "language"]}}]}, "model_result_raw": "[sentiment_analysis(text='I love the food here! It's always fresh and delicious.', language='en')] \n", "possible_answer": [{"sentiment_analysis": {"text": ["I love the food here! It's always fresh and delicious."], "language": ["english", "English", "en"]}}]}
{"id": "simple_218", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Incorrect type for parameter 'patient_id'. Expected type string, got int. Parameter value: 546382."], "error_type": "type_error:simple", "prompt": {"id": "simple_218", "question": [[{"role": "user", "content": "Given patient with id 546382, retrieve their brain MRI report with the status 'concluded'."}]], "function": [{"name": "patient.get_mri_report", "description": "Fetch the brain MRI report of the patient for a given status.", "parameters": {"type": "dict", "properties": {"patient_id": {"type": "string", "description": "The patient identifier."}, "mri_type": {"type": "string", "description": "Type of the MRI. Default to be 'brain'.", "enum": ["brain", "spinal", "chest", "abdominal"]}, "status": {"type": "string", "description": "Status of the report, could be 'in progress', 'concluded' or 'draft'.", "enum": ["in progress", "concluded", "draft"]}}, "required": ["patient_id", "status"]}}]}, "model_result_raw": "[patient.get_mri_report(patient_id=546382, mri_type='brain', status='concluded')] \n", "model_result_decoded": [{"patient.get_mri_report": {"patient_id": 546382, "mri_type": "brain", "status": "concluded"}}], "possible_answer": [{"patient.get_mri_report": {"patient_id": ["546382"], "mri_type": ["brain", ""], "status": ["concluded"]}}]}
{"id": "simple_224", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'sub_topics': []. Expected one of [['behaviour', 'group dynamics']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_224", "question": [[{"role": "user", "content": "Find the most followed person on twitter who tweets about psychology related to behaviour and group dynamics."}]], "function": [{"name": "social_media_analytics.most_followed", "description": "Find the most followed Twitter user related to certain topics.", "parameters": {"type": "dict", "properties": {"topic": {"type": "string", "description": "The main topic of interest."}, "sub_topics": {"type": "array", "items": {"type": "string"}, "description": "Sub-topics related to main topic. Default is empty."}, "region": {"type": "string", "description": "Region of interest for twitter search. Default is 'all'."}}, "required": ["topic"]}}]}, "model_result_raw": "[social_media_analytics.most_followed(topic='psychology', sub_topics=[], region='all')] \n", "model_result_decoded": [{"social_media_analytics.most_followed": {"topic": "psychology", "sub_topics": [], "region": "all"}}], "possible_answer": [{"social_media_analytics.most_followed": {"topic": ["psychology"], "sub_topics": [["behaviour", "group dynamics"]], "region": ["", "all"]}}]}
{"id": "simple_227", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Unexpected parameter: 'properties'."], "error_type": "simple_function_checker:unexpected_param", "prompt": {"id": "simple_227", "question": [[{"role": "user", "content": "Get me strength and weakness traits for ENFJ personality type."}]], "function": [{"name": "get_personality_traits", "description": "Retrieve the personality traits for a specific personality type, including their strengths and weaknesses.", "parameters": {"type": "dict", "properties": {"type": {"type": "string", "description": "The personality type."}, "traits": {"type": "array", "items": {"type": "string", "enum": ["strengths", "weaknesses"]}, "description": "List of traits to be retrieved, default is ['strengths']."}}, "required": ["type"]}}]}, "model_result_raw": "[get_personality_traits(type='ENFJ', properties={'traits': {'type': 'array', 'items': {'type': 'string', 'enum': ['strengths', 'weaknesses']}, 'description': \"List of traits to be retrieved, default is ['strengths'].\"}})] \n", "model_result_decoded": [{"get_personality_traits": {"type": "ENFJ", "properties": {"traits": {"type": "array", "items": {"type": "string", "enum": ["strengths", "weaknesses"]}, "description": "List of traits to be retrieved, default is ['strengths']."}}}}], "possible_answer": [{"get_personality_traits": {"type": ["ENFJ"], "traits": [["strengths", "weaknesses"]]}}]}
{"id": "simple_234", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'century'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_234", "question": [[{"role": "user", "content": "Find important Wars in European history during the 19th century."}]], "function": [{"name": "history_eu.fetch_events", "description": "Fetches significant historical events within a specific time period in European history.", "parameters": {"type": "dict", "properties": {"century": {"type": "integer", "description": "The century you are interested in."}, "region": {"type": "string", "description": "The region of Europe you are interested in.", "enum": ["Northern", "Southern", "Eastern", "Western"]}, "category": {"type": "string", "description": "Category of the historical events. Default is 'Culture'.", "enum": ["Wars", "Culture", "Politics", "Scientific", "Others"]}}, "required": ["century", "region"]}}]}, "model_result_raw": "[history_eu.fetch_events(type='dict', properties={'century': 19, 'region': 'Western'}, category='Wars')] \n", "model_result_decoded": [{"history_eu.fetch_events": {"type": "dict", "properties": {"century": 19, "region": "Western"}, "category": "Wars"}}], "possible_answer": [{"history_eu.fetch_events": {"century": [19], "region": ["Northern", "Southern", "Eastern", "Western"], "category": ["Wars"]}}]}
{"id": "simple_244", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'author'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_244", "question": [[{"role": "user", "content": "What year was the law of universal gravitation published by Isaac Newton?"}]], "function": [{"name": "publication_year.find", "description": "Fetches the year a particular scientific work was published.", "parameters": {"type": "dict", "properties": {"author": {"type": "string", "description": "Name of the author of the work."}, "work_title": {"type": "string", "description": "Title of the scientific work."}, "location": {"type": "string", "description": "Place of the publication, if known. Default to 'all'."}}, "required": ["author", "work_title"]}}]}, "model_result_raw": "[publication_year.find({'author': 'Isaac Newton', 'work_title': 'Law of Universal Gravitation' })] \n", "model_result_decoded": [{"publication_year.find": {}}], "possible_answer": [{"publication_year.find": {"author": ["Isaac Newton"], "work_title": ["Law of Universal Gravitation", "Universal Law of Gravitation", "The law of universal gravitation"], "location": ["", "all"]}}]}
{"id": "simple_247", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'category': 'Physics'. Expected one of ['', 'all']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_247", "question": [[{"role": "user", "content": "What was Albert Einstein's contribution to science on March 17, 1915?"}]], "function": [{"name": "historical_contrib.get_contrib", "description": "Retrieve historical contribution made by a scientist on a specific date.", "parameters": {"type": "dict", "properties": {"scientist": {"type": "string", "description": "The scientist whose contributions need to be searched."}, "date": {"type": "string", "description": "The date when the contribution was made in yyyy-mm-dd format."}, "category": {"type": "string", "description": "The field of the contribution, such as 'Physics' or 'Chemistry'. Default is 'all'."}}, "required": ["scientist", "date"]}}]}, "model_result_raw": "[historical_contrib.get_contrib(scientist='Albert Einstein', date='1915-03-17', category='Physics')] \n", "model_result_decoded": [{"historical_contrib.get_contrib": {"scientist": "Albert Einstein", "date": "1915-03-17", "category": "Physics"}}], "possible_answer": [{"historical_contrib.get_contrib": {"scientist": ["Albert Einstein"], "date": ["1915-03-17", "03/17/1915", "Mar.17,1915"], "category": ["", "all"]}}]}
{"id": "simple_249", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'till_century': 1400. Expected one of [14]."], "error_type": "value_error:others", "prompt": {"id": "simple_249", "question": [[{"role": "user", "content": "Tell me more about Christianity and its history till the 14th century"}]], "function": [{"name": "religion.history_info", "description": "Provides comprehensive historical details about a specified religion till a specified century.", "parameters": {"type": "dict", "properties": {"religion": {"type": "string", "description": "The name of the religion for which historical details are needed."}, "till_century": {"type": "integer", "description": "The century till which historical details are needed."}, "include_people": {"type": "boolean", "description": "To include influential people related to the religion during that time period, default is False"}}, "required": ["religion", "till_century"]}}]}, "model_result_raw": "[religion.history_info(religion='Christianity', till_century=1400, include_people=False)] \n", "model_result_decoded": [{"religion.history_info": {"religion": "Christianity", "till_century": 1400, "include_people": false}}], "possible_answer": [{"religion.history_info": {"religion": ["Christianity"], "till_century": [14], "include_people": [false, ""]}}]}
{"id": "simple_260", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Unexpected dict key parameter: 'description'."], "error_type": "value_error:dict_key", "prompt": {"id": "simple_260", "question": [[{"role": "user", "content": "Calculate how many gallons of paint is required to paint a wall with width of 20ft and height of 12ft, assuming 1 gallon covers approximately 350 sq.ft. Don't include window area of 15 sq.ft."}]], "function": [{"name": "paint_requirement.calculate", "description": "Calculate the amount of paint required to paint a given area. Account for coverage efficiency of the paint and exclusions (like windows).", "parameters": {"type": "dict", "properties": {"area": {"type": "dict", "properties": {"width": {"type": "integer", "description": "The width of the area to be painted in feet."}, "height": {"type": "integer", "description": "The height of the area to be painted in feet."}}, "description": "The area to be painted."}, "paint_coverage": {"type": "integer", "description": "Coverage area per gallon of the paint in square feet.", "default": 350}, "exclusion": {"type": "dict", "properties": {"type": {"type": "string", "description": "The type of the exclusion e.g window, door etc."}, "area": {"type": "integer", "description": "The area of the exclusion in square feet."}}, "description": "Area not to be painted. Default to not use any exclusion if not specified."}}, "required": ["area", "paint_coverage"]}}]}, "model_result_raw": "[paint_requirement.calculate(area={'width': 20, 'height': 12, 'description': 'The area to be painted.'}, paint_coverage=350, exclusion={'type': 'wall', 'area': 15}, required=['area', 'paint_coverage']),] \n", "model_result_decoded": [{"paint_requirement.calculate": {"area": {"width": 20, "height": 12, "description": "The area to be painted."}, "paint_coverage": 350, "exclusion": {"type": "wall", "area": 15}, "required": ["area", "paint_coverage"]}}], "possible_answer": [{"paint_requirement.calculate": {"area": [{"width": [20], "height": [12]}], "paint_coverage": [350], "exclusion": [{"type": ["window"], "area": [15]}]}}]}
{"id": "simple_267", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'month': 'upcoming'. Expected one of ['']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_267", "question": [[{"role": "user", "content": "Find the top rated modern sculpture exhibition happening in New York in the upcoming month."}]], "function": [{"name": "find_exhibition", "description": "Locate the most popular exhibitions based on criteria like location, time, art form, and user ratings.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state where the exhibition is held, e.g., New York City, NY."}, "art_form": {"type": "string", "description": "The form of art the exhibition is displaying e.g., sculpture."}, "month": {"type": "string", "description": "The month of exhibition. Default value will return upcoming events if not specified."}, "user_ratings": {"type": "string", "enum": ["low", "average", "high"], "description": "Select exhibitions with user rating threshold. Default is 'low'"}}, "required": ["location", "art_form"]}}]}, "model_result_raw": "[find_exhibition(location='New York City, NY', art_form='sculpture', month='upcoming')] \n", "model_result_decoded": [{"find_exhibition": {"location": "New York City, NY", "art_form": "sculpture", "month": "upcoming"}}], "possible_answer": [{"find_exhibition": {"location": ["New York City, NY"], "art_form": ["sculpture", "modern sculpture"], "month": [""], "user_ratings": ["high"]}}]}
{"id": "simple_273", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'name': 'Louvre'. Expected one of ['Louvre Museum']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_273", "question": [[{"role": "user", "content": "Find out the open hours for the Louvre Museum in Paris."}]], "function": [{"name": "museum.get_hours", "description": "Retrieve the open hours for a museum based on its name and location.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "The name of the museum."}, "location": {"type": "string", "description": "The city where the museum is located."}, "day": {"type": "string", "description": "Optional: Day of the week for specific open hours. Default 'Monday'."}}, "required": ["name", "location"]}}]}, "model_result_raw": "[museum.get_hours(name='Louvre', location='Paris')] \n", "model_result_decoded": [{"museum.get_hours": {"name": "Louvre", "location": "Paris"}}], "possible_answer": [{"museum.get_hours": {"name": ["Louvre Museum"], "location": ["Paris", "Paris, France"], "day": ["", "Monday"]}}]}
{"id": "simple_277", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Optional parameter 'information' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_277", "question": [[{"role": "user", "content": "Find the working hours and ticket price of The British Museum for this weekend, Jun.20,2023."}]], "function": [{"name": "museum_info", "description": "Get information about a museum including its opening hours and ticket prices for a specific date range.", "parameters": {"type": "dict", "properties": {"museum": {"type": "string", "description": "The name of the museum."}, "date": {"type": "string", "description": "The specific date for which information is needed, in the format of YYYY-MM-DD such as '2022-12-01'."}, "information": {"type": "array", "items": {"type": "string", "enum": ["opening_hours", "ticket_price", "address"]}, "description": "The type of information needed from the museum. This is optional and defaults to 'all' if not specified.", "default": "all"}}, "required": ["museum", "date"]}}]}, "model_result_raw": "[museum_info(museum='The British Museum', date='2023-06-20') ] \n", "model_result_decoded": [{"museum_info": {"museum": "The British Museum", "date": "2023-06-20"}}], "possible_answer": [{"museum_info": {"museum": ["The British Museum"], "date": ["2023-06-20"], "information": [["opening_hours", "ticket_price"], ["ticket_price", "opening_hours"]]}}]}
{"id": "simple_278", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Optional parameter 'features' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_278", "question": [[{"role": "user", "content": "Find me the average price and ratings of piano from Yamaha."}]], "function": [{"name": "get_instrument_details", "description": "Retrieve the average price and ratings of an instrument from a particular manufacturer.", "parameters": {"type": "dict", "properties": {"instrument": {"type": "string", "description": "The name of the instrument."}, "manufacturer": {"type": "string", "description": "The manufacturer of the instrument."}, "features": {"type": "array", "items": {"type": "string", "enum": ["price", "rating"]}, "description": "The features to retrieve about the instrument. Default is 'price'"}}, "required": ["instrument", "manufacturer"]}}]}, "model_result_raw": "[get_instrument_details(instrument='piano', manufacturer='Yamaha')] \n", "model_result_decoded": [{"get_instrument_details": {"instrument": "piano", "manufacturer": "Yamaha"}}], "possible_answer": [{"get_instrument_details": {"instrument": ["piano"], "manufacturer": ["Yamaha"], "features": [["price", "rating"]]}}]}
{"id": "simple_285", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'location': 'Chicago'. Expected one of ['Chicago, Illinois', 'Chicago, IL']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_285", "question": [[{"role": "user", "content": "Find me a Rock concert in Chicago with ticket availability under $100."}]], "function": [{"name": "find_concert", "description": "Locate a concert in a specified location within a certain budget.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city where you are looking for a concert. In the format City, State."}, "price": {"type": "integer", "description": "Maximum ticket price."}, "genre": {"type": "string", "description": "Music genre of the concert. Default to 'Jazz'. ", "enum": ["Rock", "Pop", "Country", "Jazz", "Classical"]}}, "required": ["location", "price"]}}]}, "model_result_raw": "[find_concert(location='Chicago', price=100, genre='Rock')] \n", "model_result_decoded": [{"find_concert": {"location": "Chicago", "price": 100, "genre": "Rock"}}], "possible_answer": [{"find_concert": {"location": ["Chicago, Illinois", "Chicago, IL"], "price": [100], "genre": ["Rock"]}}]}
{"id": "simple_298", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Incorrect type for parameter 'key'. Expected type string, got dict. Parameter value: {'type': 'string', 'value': 'C'}."], "error_type": "type_error:simple", "prompt": {"id": "simple_298", "question": [[{"role": "user", "content": "What key signature does C# major have?"}]], "function": [{"name": "music_theory.key_signature", "description": "Return the key signature of a major or minor scale.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The root of the scale, e.g., 'C', 'F#', 'Ab'."}, "scale_type": {"type": "string", "enum": ["major", "minor"], "description": "Type of the scale, either 'major' or 'minor'. Default is 'major'."}}, "required": ["key"]}}]}, "model_result_raw": "[music_theory.key_signature(key={'type': 'string', 'value': 'C'}, scale_type={'type': 'string', 'value': 'major'})] \n", "model_result_decoded": [{"music_theory.key_signature": {"key": {"type": "string", "value": "C"}, "scale_type": {"type": "string", "value": "major"}}}], "possible_answer": [{"music_theory.key_signature": {"key": ["C#"], "scale_type": ["major", ""]}}]}
{"id": "simple_299", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'key': 'C# major'. Expected one of ['C#', 'C sharp']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_299", "question": [[{"role": "user", "content": "What is the musical scale associated with C sharp major?"}]], "function": [{"name": "musical_scale", "description": "Get the musical scale of a specific key in music theory.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The musical key for which the scale will be found."}, "scale_type": {"type": "string", "default": "major", "description": "The type of musical scale."}}, "required": ["key"]}}]}, "model_result_raw": "[musical_scale(key='C# major')] \n", "model_result_decoded": [{"musical_scale": {"key": "C# major"}}], "possible_answer": [{"musical_scale": {"key": ["C#", "C sharp"], "scale_type": ["major", ""]}}]}
{"id": "simple_301", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'key': 'C major'. Expected one of ['C']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_301", "question": [[{"role": "user", "content": "What is the third major chord in C major scale?"}]], "function": [{"name": "get_third_chord", "description": "Calculate the third major chord in a given key.", "parameters": {"type": "dict", "properties": {"key": {"type": "string", "description": "The key of the scale."}, "type": {"type": "string", "description": "Type of the scale, either major or minor. Default is 'major'."}}, "required": ["key"]}}]}, "model_result_raw": "[get_third_chord(key='C major')] \n", "model_result_decoded": [{"get_third_chord": {"key": "C major"}}], "possible_answer": [{"get_third_chord": {"key": ["C"], "type": ["major", ""]}}]}
{"id": "simple_304", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'team': ''. Expected one of ['Los Angeles Lakers', 'LAL', 'Lakers']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_304", "question": [[{"role": "user", "content": "Get point and rebound stats for player 'LeBron James' from last basketball game"}]], "function": [{"name": "player_stats.getLastGame", "description": "Get last game statistics for a specific player in basketball", "parameters": {"type": "dict", "properties": {"player_name": {"type": "string", "description": "The name of the basketball player."}, "team": {"type": "string", "description": "The team that player currently plays for."}, "metrics": {"type": "array", "items": {"type": "string", "enum": ["Points", "Rebounds", "Assists", "Blocks"]}, "description": "Specific metrics to retrieve. If no value is specified, all available metrics will be returned by default."}}, "required": ["player_name", "team"]}}]}, "model_result_raw": "[player_stats.getLastGame(player_name='LeBron James', team='') ] \n", "model_result_decoded": [{"player_stats.getLastGame": {"player_name": "LeBron James", "team": ""}}], "possible_answer": [{"player_stats.getLastGame": {"player_name": ["LeBron James"], "team": ["Los Angeles Lakers", "LAL", "Lakers"], "metrics": [["Points", "Rebounds"]]}}]}
{"id": "simple_307", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'teams'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_307", "question": [[{"role": "user", "content": "Who won the basketball game between Lakers and Clippers on Jan 28, 2021?"}]], "function": [{"name": "game_result.get_winner", "description": "Get the winner of a specific basketball game.", "parameters": {"type": "dict", "properties": {"teams": {"type": "array", "items": {"type": "string"}, "description": "List of two teams who played the game."}, "date": {"type": "string", "description": "The date of the game, formatted as YYYY-MM-DD."}, "venue": {"type": "string", "optional": true, "description": "Optional: The venue of the game. Default is 'home'."}}, "required": ["teams", "date"]}}]}, "model_result_raw": "[game_result.get_winner({'teams': ['Lakers', 'Clippers'], 'date': '2021-01-28' })] \n", "model_result_decoded": [{"game_result.get_winner": {}}], "possible_answer": [{"game_result.get_winner": {"teams": [["Lakers", "Clippers"], ["Clippers", "Lakers"]], "date": ["2021-01-28", "01/28/2021", "Jan.28,2021"], "venue": ["", true]}}]}
{"id": "simple_325", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'season': 'current'. Expected one of ['']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_325", "question": [[{"role": "user", "content": "Who won the last match between Chicago Bulls and Los Angeles Lakers?"}]], "function": [{"name": "sports.match_results", "description": "Returns the results of a given match between two teams.", "parameters": {"type": "dict", "properties": {"team1": {"type": "string", "description": "The name of the first team."}, "team2": {"type": "string", "description": "The name of the second team."}, "season": {"type": "string", "description": "The season when the match happened. Default is the current season."}}, "required": ["team1", "team2"]}}]}, "model_result_raw": "[sports.match_results(team1='Chicago Bulls', team2='Los Angeles Lakers', season='current')] \n", "model_result_decoded": [{"sports.match_results": {"team1": "Chicago Bulls", "team2": "Los Angeles Lakers", "season": "current"}}], "possible_answer": [{"sports.match_results": {"team1": ["Chicago Bulls"], "team2": ["Los Angeles Lakers"], "season": [""]}}]}
{"id": "simple_328", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Incorrect type for parameter 'parameters'. Expected type array, got dict. Parameter value: {'type': 'array', 'items': {'type': 'string', 'enum': ['player count', 'playing time', 'age', 'mechanics', 'rating']}, 'description': 'Game characteristics interested.'}."], "error_type": "type_error:simple", "prompt": {"id": "simple_328", "question": [[{"role": "user", "content": "Find the rating and player count of the board game 'Ticket to Ride'."}]], "function": [{"name": "boardgame.get_info", "description": "Retrieve detailed information of a board game.", "parameters": {"type": "dict", "properties": {"name": {"type": "string", "description": "Name of the board game."}, "parameters": {"type": "array", "items": {"type": "string", "enum": ["player count", "playing time", "age", "mechanics", "rating"]}, "description": "Game characteristics interested."}, "language": {"type": "string", "description": "The preferred language for the game information, default is English"}}, "required": ["name", "parameters"]}}]}, "model_result_raw": "[boardgame.get_info(name='Ticket to Ride', parameters={'type': 'array', 'items': {'type': 'string', 'enum': ['player count', 'playing time', 'age', 'mechanics', 'rating']}, 'description': 'Game characteristics interested.'}, language='English')] \n", "model_result_decoded": [{"boardgame.get_info": {"name": "Ticket to Ride", "parameters": {"type": "array", "items": {"type": "string", "enum": ["player count", "playing time", "age", "mechanics", "rating"]}, "description": "Game characteristics interested."}, "language": "English"}}], "possible_answer": [{"boardgame.get_info": {"name": ["Ticket to Ride"], "parameters": [["rating", "player count"], ["player count", "rating"]], "language": ["", "English"]}}]}
{"id": "simple_334", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'player_cards'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_334", "question": [[{"role": "user", "content": "Check who is the winner in a game of blackjack given player having A and 10, dealer having 10 and 9. The Ace is considered 1."}]], "function": [{"name": "blackjack.check_winner", "description": "Checks and determines the winner in a game of blackjack.", "parameters": {"type": "dict", "properties": {"player_cards": {"type": "array", "items": {"type": "string"}, "description": "Cards held by the player."}, "dealer_cards": {"type": "array", "items": {"type": "string"}, "description": "Cards held by the dealer."}, "ace_value": {"type": "integer", "description": "The value considered for the ace card, can be either 1 or 11.", "default": 11}}, "required": ["player_cards", "dealer_cards"]}}]}, "model_result_raw": "[blackjack.check_winner({'player_cards': ['A', '10'], 'dealer_cards': ['10', '9']}, ace_value=11)] \n", "model_result_decoded": [{"blackjack.check_winner": {"ace_value": 11}}], "possible_answer": [{"blackjack.check_winner": {"player_cards": [["A", "10"]], "dealer_cards": [["10", "9"]], "ace_value": [1]}}]}
{"id": "simple_335", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Incorrect type for parameter 'deck'. Expected type array, got dict. Parameter value: {}."], "error_type": "type_error:simple", "prompt": {"id": "simple_335", "question": [[{"role": "user", "content": "Find a Card of rank 'Queen' and suit 'Hearts' in the deck."}]], "function": [{"name": "find_card_in_deck", "description": "Locate a particular card in a deck based on rank and suit.", "parameters": {"type": "dict", "properties": {"rank": {"type": "string", "description": "Rank of the card (e.g. Ace, Two, King)."}, "suit": {"type": "string", "description": "Suit of the card (e.g. Hearts, Spades, Diamonds, Clubs)."}, "deck": {"type": "array", "items": {"type": "dict", "properties": {"rank": {"type": "string"}, "suit": {"type": "string"}}}, "description": "Deck of cards. If not provided, the deck will be a standard 52 card deck"}}, "required": ["rank", "suit"]}}]}, "model_result_raw": "[find_card_in_deck(rank='Queen', suit='Hearts', deck={})] \n", "model_result_decoded": [{"find_card_in_deck": {"rank": "Queen", "suit": "Hearts", "deck": {}}}], "possible_answer": [{"find_card_in_deck": {"rank": ["Queen"], "suit": ["Hearts"], "deck": [""]}}]}
{"id": "simple_337", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'players'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_337", "question": [[{"role": "user", "content": "In a texas holdem game, Who won in the poker game with players Alex, Sam, Robert and Steve given the cards Alex':['A of spades', 'K of spades'], 'Sam': ['2 of diamonds', '3 of clubs'], 'Robert': ['Q of hearts', '10 of hearts'], 'Steve': ['4 of spades', '5 of spades']?"}]], "function": [{"name": "poker_game_winner", "description": "Identify the winner in a poker game based on the cards.", "parameters": {"type": "dict", "properties": {"players": {"type": "array", "items": {"type": "string"}, "description": "Names of the players in a list."}, "cards": {"type": "dict", "description": "An object containing the player name as key and the cards as values in a list."}, "type": {"type": "string", "description": "Type of poker game. Defaults to 'Texas Holdem'"}}, "required": ["players", "cards"]}}]}, "model_result_raw": "[poker_game_winner({'players': ['Alex', 'Sam', 'Robert', 'Steve'], 'cards': {'Alex': ['A of spades', 'K of spades'], 'Sam': ['2 of diamonds', '3 of clubs'], 'Robert': ['Q of hearts', '10 of hearts'], 'Steve': ['4 of spades', '5 of spades']}}, 'Texas Holdem')] \n", "model_result_decoded": [{"poker_game_winner": {}}], "possible_answer": [{"poker_game_winner": {"players": [["Alex", "Sam", "Robert", "Steve"]], "cards": [{"Alex": [["A of spades", "K of spades"]], "Sam": [["2 of diamonds", "3 of clubs"]], "Robert": [["Q of hearts", "10 of hearts"]], "Steve": [["4 of spades", "5 of spades"]]}], "type": ["Texas Holdem", ""]}}]}
{"id": "simple_338", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'desired_cards': 1. Expected one of [13]."], "error_type": "value_error:others", "prompt": {"id": "simple_338", "question": [[{"role": "user", "content": "What is the probability of drawing a heart card from a deck of 52 cards?"}]], "function": [{"name": "card_game_probability.calculate", "description": "Calculate the probability of drawing a certain card or suit from a deck of cards.", "parameters": {"type": "dict", "properties": {"total_cards": {"type": "integer", "description": "Total number of cards in the deck."}, "desired_cards": {"type": "integer", "description": "Number of cards in the deck that satisfy the conditions."}, "cards_drawn": {"type": "integer", "default": 1, "description": "Number of cards drawn from the deck."}}, "required": ["total_cards", "desired_cards"]}}]}, "model_result_raw": "[card_game_probability.calculate(total_cards=52, desired_cards=1, cards_drawn=1)] \n", "model_result_decoded": [{"card_game_probability.calculate": {"total_cards": 52, "desired_cards": 1, "cards_drawn": 1}}], "possible_answer": [{"card_game_probability.calculate": {"total_cards": [52], "desired_cards": [13], "cards_drawn": ["", 1]}}]}
{"id": "simple_340", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'player1'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_340", "question": [[{"role": "user", "content": "Determine the winner in a Poker game with John having a Hand of 8\u2665, 10\u2665, J\u2665, Q\u2665, K\u2665 and Mike having 9\u2660, J\u2660, 10\u2660, Q\u2660, K\u2660."}]], "function": [{"name": "card_games.poker_determine_winner", "description": "Determines the winner in a game of Poker based on the cards in each players' hands.", "parameters": {"type": "dict", "properties": {"player1": {"type": "string", "description": "The first player's name."}, "hand1": {"type": "array", "items": {"type": "string"}, "description": "The list of cards (as strings) in first player's hand. E.g ['10\u2660', 'J\u2660']"}, "player2": {"type": "string", "description": "The second player's name."}, "hand2": {"type": "array", "items": {"type": "string"}, "description": "The list of cards (as strings) in second player's hand. E.g ['9\u2665', '10\u2665']"}}, "required": ["player1", "hand1", "player2", "hand2"]}}]}, "model_result_raw": "[card_games.poker_determine_winner({'player1': 'John', 'hand1': ['8\u2665', '10\u2665', 'J\u2665', 'Q\u2665', 'K\u2665'], 'player2': 'Mike', 'hand2': ['9\u2660', 'J\u2660', '10\u2660', 'Q\u2660', 'K\u2660']})] \n", "model_result_decoded": [{"card_games.poker_determine_winner": {}}], "possible_answer": [{"card_games.poker_determine_winner": {"player1": ["John"], "hand1": [["8\u2665", "10\u2665", "J\u2665", "Q\u2665", "K\u2665"]], "player2": ["Mike"], "hand2": [["9\u2660", "J\u2660", "10\u2660", "Q\u2660", "K\u2660"]]}}]}
{"id": "simple_353", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'diet'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_353", "question": [[{"role": "user", "content": "What are some gluten-free recipes for dinner?"}]], "function": [{"name": "find_recipes", "description": "Find recipes based on dietary restrictions, meal type, and preferred ingredients.", "parameters": {"type": "dict", "properties": {"diet": {"type": "string", "description": "The dietary restrictions, e.g., 'vegan', 'gluten-free'."}, "meal_type": {"type": "string", "description": "The type of meal, e.g., 'dinner', 'breakfast'."}, "ingredients": {"type": "array", "items": {"type": "string"}, "description": "The preferred ingredients. If left blank, it will default to return general recipes."}}, "required": ["diet", "meal_type"]}}]}, "model_result_raw": "[find_recipes(type={'diet': 'gluten-free'}, meal_type='dinner')] \n", "model_result_decoded": [{"find_recipes": {"type": {"diet": "gluten-free"}, "meal_type": "dinner"}}], "possible_answer": [{"find_recipes": {"diet": ["gluten-free"], "meal_type": ["dinner"], "ingredients": [""]}}]}
{"id": "simple_357", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Optional parameter 'diet_preference' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_357", "question": [[{"role": "user", "content": "Get the recipe for vegan chocolate cake including the steps for preparation."}]], "function": [{"name": "get_recipe", "description": "Fetch the recipe for a specific dish along with preparation steps.", "parameters": {"type": "dict", "properties": {"dish_name": {"type": "string", "description": "Name of the dish whose recipe needs to be fetched."}, "diet_preference": {"type": "string", "description": "Preferred dietary consideration like vegan, vegetarian, gluten-free etc. Default is none.", "default": "none"}}, "required": ["dish_name"]}}]}, "model_result_raw": "[get_recipe(dish_name='vegan chocolate cake')] \n", "model_result_decoded": [{"get_recipe": {"dish_name": "vegan chocolate cake"}}], "possible_answer": [{"get_recipe": {"dish_name": ["chocolate cake", "vegan chocolate cake"], "diet_preference": ["vegan"]}}]}
{"id": "simple_358", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Incorrect type for parameter 'diet'. Expected type array, got dict. Parameter value: {'Gluten Free': True}."], "error_type": "type_error:simple", "prompt": {"id": "simple_358", "question": [[{"role": "user", "content": "Find a gluten-free cookie recipe that takes less than 30 minutes to prepare."}]], "function": [{"name": "recipe_search", "description": "Search for a cooking recipe based on specific dietary needs and time constraint.", "parameters": {"type": "dict", "properties": {"diet": {"type": "array", "items": {"type": "string", "enum": ["Gluten Free", "Dairy Free", "Vegan", "Vegetarian"]}, "description": "Specific dietary need."}, "time_limit": {"type": "integer", "description": "The maximum time to prepare the recipe in minutes. Default is 60 minutes."}, "dish": {"type": "string", "description": "The name of the dish to search for. Default is not use if not specified."}}, "required": ["dish", "diet"]}}]}, "model_result_raw": "[recipe_search(dish='cookie', diet={'Gluten Free': True}, time_limit=30)] \n", "model_result_decoded": [{"recipe_search": {"dish": "cookie", "diet": {"Gluten Free": true}, "time_limit": 30}}], "possible_answer": [{"recipe_search": {"diet": [["Gluten Free"], ["GF"], ["gluten free"]], "time_limit": [30], "dish": ["cookie"]}}]}
{"id": "simple_360", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'maxCalories': 499. Expected one of [500]."], "error_type": "value_error:others", "prompt": {"id": "simple_360", "question": [[{"role": "user", "content": "Find a recipe for pasta carbonara which contains only less than 500 calories."}]], "function": [{"name": "find_recipe", "description": "Locate a recipe based on name and its calorie content", "parameters": {"type": "dict", "properties": {"recipeName": {"type": "string", "description": "The recipe's name."}, "maxCalories": {"type": "integer", "description": "The maximum calorie content of the recipe.", "default": 1000}}, "required": ["recipeName"]}}]}, "model_result_raw": "[find_recipe(recipeName='pasta carbonara', maxCalories=499)] \n", "model_result_decoded": [{"find_recipe": {"recipeName": "pasta carbonara", "maxCalories": 499}}], "possible_answer": [{"find_recipe": {"recipeName": ["pasta carbonara"], "maxCalories": [500]}}]}
{"id": "simple_367", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'recipe_type'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_367", "question": [[{"role": "user", "content": "Find me a vegan recipe for brownies which prep time is under 30 minutes."}]], "function": [{"name": "find_recipe", "description": "Find a recipe based on the dietary restrictions, recipe type, and time constraints.", "parameters": {"type": "dict", "properties": {"dietary_restrictions": {"type": "string", "description": "Dietary restrictions e.g. vegan, vegetarian, gluten free, dairy free."}, "recipe_type": {"type": "string", "description": "Type of the recipe. E.g. dessert, main course, breakfast."}, "time": {"type": "integer", "description": "Time limit in minutes to prep the meal."}}, "required": ["dietary_restrictions", "recipe_type", "time"]}}]}, "model_result_raw": "[find_recipe(type='dessert', dietary_restrictions='vegan', time=30)] \n", "model_result_decoded": [{"find_recipe": {"type": "dessert", "dietary_restrictions": "vegan", "time": 30}}], "possible_answer": [{"find_recipe": {"dietary_restrictions": ["vegan"], "recipe_type": ["dessert"], "time": [30]}}]}
{"id": "simple_370", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'quantity': [3, 5]. Expected one of [[3, 1]]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_370", "question": [[{"role": "user", "content": "Order three bottles of olive oil and a five pound bag of rice from Safeway in Palo Alto."}]], "function": [{"name": "safeway.order", "description": "Order specified items from a Safeway location.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The location of the Safeway store, e.g. Palo Alto, CA."}, "items": {"type": "array", "items": {"type": "string"}, "description": "List of items to order."}, "quantity": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item in the order list."}}, "required": ["location", "items", "quantity"]}}]}, "model_result_raw": "[safeway.order(location='Palo Alto, CA', items=['olive oil', 'rice'], quantity=[3, 5]) ] \n", "model_result_decoded": [{"safeway.order": {"location": "Palo Alto, CA", "items": ["olive oil", "rice"], "quantity": [3, 5]}}], "possible_answer": [{"safeway.order": {"location": ["Palo Alto", "Palo Alto, CA"], "items": [["olive oil", "rice"], ["olive oil", "bag of rice"]], "quantity": [[3, 1]]}}]}
{"id": "simple_372", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'product': 'banana'. Expected one of ['bananas']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_372", "question": [[{"role": "user", "content": "Find the top five organic bananas brands on the basis of rating from Whole Foods store."}]], "function": [{"name": "whole_foods.find_top_brands", "description": "Get top brands based on a specific product from Whole Foods", "parameters": {"type": "dict", "properties": {"product": {"type": "string", "description": "The product for which the top brands should be fetched."}, "number": {"type": "integer", "description": "Number of top brands to be fetched. Default is 5"}, "organic": {"type": "boolean", "description": "If the product should be organic. Default is false"}}, "required": ["product"]}}]}, "model_result_raw": "[whole_foods.find_top_brands(product='banana', number=5, organic=True)] \n", "model_result_decoded": [{"whole_foods.find_top_brands": {"product": "banana", "number": 5, "organic": true}}], "possible_answer": [{"whole_foods.find_top_brands": {"product": ["bananas"], "number": [5, ""], "organic": [true]}}]}
{"id": "simple_373", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'product_list': ['apple', 'rice', 'water']. Expected one of [['apples', 'rice', 'bottled water'], ['apples', 'rice', 'water']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_373", "question": [[{"role": "user", "content": "I want to buy apples, rice, and 12 pack of bottled water from a Walmart near San Jose. Show me the product information and stock availability."}]], "function": [{"name": "walmart.purchase", "description": "Retrieve information of items from Walmart including stock availability.", "parameters": {"type": "dict", "properties": {"loc": {"type": "string", "description": "Location of the nearest Walmart."}, "product_list": {"type": "array", "items": {"type": "string"}, "description": "Items to be purchased listed in an array."}, "pack_size": {"type": "array", "items": {"type": "integer"}, "description": "Size of the product pack if applicable. The size of the array should be equal to product_list. Default is not use it if not specified."}}, "required": ["loc", "product_list"]}}]}, "model_result_raw": "[walmart.purchase(loc='San Jose', product_list=['apple', 'rice', 'water'], pack_size=[12])] \n", "model_result_decoded": [{"walmart.purchase": {"loc": "San Jose", "product_list": ["apple", "rice", "water"], "pack_size": [12]}}], "possible_answer": [{"walmart.purchase": {"loc": ["San Jose", "San Jose, CA"], "product_list": [["apples", "rice", "bottled water"], ["apples", "rice", "water"]], "pack_size": [[1, 1, 12]]}}]}
{"id": "simple_375", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'items': ['pumpkin', 'pumpkin', 'pumpkin']. Expected one of [['pumpkins', 'eggs'], ['pumpkin', 'dozen eggs']]."], "error_type": "value_error:list/tuple", "prompt": {"id": "simple_375", "question": [[{"role": "user", "content": "Check the total price for three pumpkins and two dozen eggs at Walmart."}]], "function": [{"name": "walmart.check_price", "description": "Calculate total price for given items and their quantities at Walmart.", "parameters": {"type": "dict", "properties": {"items": {"type": "array", "items": {"type": "string"}, "description": "List of items to be priced."}, "quantities": {"type": "array", "items": {"type": "integer"}, "description": "Quantity of each item corresponding to the items list."}, "store_location": {"type": "string", "description": "The store location for specific pricing (optional). Default to all if not specified."}}, "required": ["items", "quantities"]}}]}, "model_result_raw": "[walmart.check_price(items=['pumpkin', 'pumpkin', 'pumpkin'], quantities=[3, 2], store_location='')] \n", "model_result_decoded": [{"walmart.check_price": {"items": ["pumpkin", "pumpkin", "pumpkin"], "quantities": [3, 2], "store_location": ""}}], "possible_answer": [{"walmart.check_price": {"items": [["pumpkins", "eggs"], ["pumpkin", "dozen eggs"]], "quantities": [[3, 24], [3, 2]], "store_location": [""]}}]}
{"id": "simple_382", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Missing required parameter: 'hotel_name'."], "error_type": "simple_function_checker:missing_required", "prompt": {"id": "simple_382", "question": [[{"role": "user", "content": "Book a single room for two nights at the Hilton Hotel in Chicago, starting from 10th December 2022."}]], "function": [{"name": "book_hotel", "description": "Book a room of specified type for a particular number of nights at a specific hotel, starting from a specified date.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The city in which the hotel is located."}, "room_type": {"type": "string", "description": "The type of room to be booked."}, "start_date": {"type": "string", "description": "The start date for the booking."}, "nights": {"type": "integer", "description": "The number of nights for which the booking is to be made."}}, "required": ["hotel_name", "location", "room_type", "start_date", "nights"]}}]}, "model_result_raw": "[book_hotel(type={'hotel_name': 'Hilton', 'location': 'Chicago', 'room_type': 'single', 'start_date': '2022-12-10', 'nights': 2})] \n", "model_result_decoded": [{"book_hotel": {"type": {"hotel_name": "Hilton", "location": "Chicago", "room_type": "single", "start_date": "2022-12-10", "nights": 2}}}], "possible_answer": [{"book_hotel": {"hotel_name": ["Hilton Hotel", "Hilton"], "location": ["Chicago"], "room_type": ["single"], "start_date": ["2022-12-10", "10/12/2022", "Dec 10, 2022", "December 10, 2022"], "nights": [2]}}]}
{"id": "simple_386", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Optional parameter 'view' not provided and not marked as optional."], "error_type": "simple_function_checker:missing_optional", "prompt": {"id": "simple_386", "question": [[{"role": "user", "content": "Book a luxury room in Hotel Paradise, Las Vegas, with a city view for 3 days starting from May 12, 2022."}]], "function": [{"name": "book_hotel", "description": "Book a room in a specific hotel with particular preferences", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The location of the hotel."}, "room_type": {"type": "string", "description": "The type of room preferred."}, "start_date": {"type": "string", "description": "The starting date of the stay in format MM-DD-YYYY."}, "stay_duration": {"type": "integer", "description": "The duration of the stay in days."}, "view": {"type": "string", "description": "The preferred view from the room, can be ignored if no preference. If none provided, assumes no preference.", "default": "No preference"}}, "required": ["hotel_name", "location", "room_type", "start_date", "stay_duration"]}}]}, "model_result_raw": "[book_hotel(hotel_name='Hotel Paradise', location='Las Vegas', room_type='luxury', start_date='05-12-2022', stay_duration=3)] \n", "model_result_decoded": [{"book_hotel": {"hotel_name": "Hotel Paradise", "location": "Las Vegas", "room_type": "luxury", "start_date": "05-12-2022", "stay_duration": 3}}], "possible_answer": [{"book_hotel": {"hotel_name": ["Hotel Paradise"], "location": ["Las Vegas", "LV"], "room_type": ["luxury", "Luxury"], "start_date": ["05-12-2022", "2022-05-12", "12/05/2022", "May.12,2022", "May 12, 2022"], "stay_duration": [3], "view": ["city view", "city"]}}]}
{"id": "simple_387", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'location': 'New York'. Expected one of ['New York City, NY', 'New York, NY']. Case insensitive."], "error_type": "value_error:string", "prompt": {"id": "simple_387", "question": [[{"role": "user", "content": "Book a hotel room at the Plaza Hotel in New York for 3 nights starting from 1st June 2022"}]], "function": [{"name": "hotel_booking", "description": "Books a hotel room for a specific date range.", "parameters": {"type": "dict", "properties": {"hotel_name": {"type": "string", "description": "The name of the hotel."}, "location": {"type": "string", "description": "The city and state, e.g. New York, NY."}, "start_date": {"type": "string", "description": "The start date of the reservation. Use format 'YYYY-MM-DD'."}, "end_date": {"type": "string", "description": "The end date of the reservation. Use format 'YYYY-MM-DD'."}, "rooms": {"type": "integer", "default": 1, "description": "The number of rooms to reserve."}}, "required": ["hotel_name", "location", "start_date", "end_date"]}}]}, "model_result_raw": "[hotel_booking(hotel_name='Plaza Hotel', location='New York', start_date='2022-06-01', end_date='2022-06-04', rooms=3)] \n", "model_result_decoded": [{"hotel_booking": {"hotel_name": "Plaza Hotel", "location": "New York", "start_date": "2022-06-01", "end_date": "2022-06-04", "rooms": 3}}], "possible_answer": [{"hotel_booking": {"hotel_name": ["Plaza Hotel"], "location": ["New York City, NY", "New York, NY"], "start_date": ["2022-06-01", "06/01/2022", "Jun.1,2022"], "end_date": ["2022-06-04", "06/04/2022", "Jun.4,2022"], "rooms": [1, ""]}}]}
{"id": "simple_399", "model_name": "google_gemma-2-2b-it", "test_category": "simple", "valid": false, "error": ["Invalid value for parameter 'rating': 5. Expected one of [4]."], "error_type": "value_error:others", "prompt": {"id": "simple_399", "question": [[{"role": "user", "content": "Find me the best Italian restaurants in New York City with average customer ratings of more than 4 and accepts credit cards."}]], "function": [{"name": "restaurant_search", "description": "Locates top rated restaurants based on specific criteria such as type of cuisine, ratings, and facilities.", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The city and state, e.g. New York City, NY"}, "cuisine": {"type": "string", "description": "Preferred type of cuisine e.g., Italian, Indian, American, etc."}, "rating": {"type": "integer", "description": "Minimum average customer rating out of 5"}, "accepts_credit_cards": {"type": "boolean", "description": "If the restaurant should accept credit cards."}}, "required": ["location", "cuisine", "rating", "accepts_credit_cards"]}}]}, "model_result_raw": "[restaurant_search(location='New York City', cuisine='Italian', rating=5, accepts_credit_cards=True)] \n", "model_result_decoded": [{"restaurant_search": {"location": "New York City", "cuisine": "Italian", "rating": 5, "accepts_credit_cards": true}}], "possible_answer": [{"restaurant_search": {"location": ["New York City", "New York City, NY", "NYC"], "cuisine": ["Italian"], "rating": [4], "accepts_credit_cards": [true]}}]}