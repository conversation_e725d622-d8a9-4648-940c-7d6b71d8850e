{"cells": [{"cell_type": "markdown", "id": "598e5969-ef96-47df-9935-88185e315ba1", "metadata": {}, "source": ["# 从0到1训练大模型\n", "# 后训练 Part 1 MateConv Mini SFT指令微调"]}, {"cell_type": "markdown", "id": "7a7a4339-813b-40e2-b623-46298bd7196f", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/SFT_RL/171.png)"]}, {"cell_type": "markdown", "id": "cf316fa6-6138-4509-b4e1-b76c2f21f3e2", "metadata": {}, "source": ["## 1 认识全量指令微调SFT"]}, {"cell_type": "markdown", "id": "511801fd-0e15-428e-98d9-fdf05923f834", "metadata": {}, "source": ["微调（Fine-Tuning）是一种用于大语言模型（LLM）训练和优化的技术，**它是在各类已经经过一定程度训练的模型上继续修改模型参数、改变模型表现的核心技术之一**。其中，全量指令微调是微调技术中非常核心、又非常重要的一种微调技术，它可以被应用在大语言模型落地生产的各个环节中，对模型最终输出结果造成巨大的影响。\n", "\n", "- **双刃剑：更强的能力、带来超强适应也带来毁灭**\n", "  \n", "微调是通过修改模型参数来改变模型表现，而在SFT全量指令微调中，“全量”指的是模型的所有参数都会参与训练更新，而不是只训练部分参数（例如LoRA或Adapter等方法中的冻结大部分参数，仅更新小部分）。<font color=\"green\">**这种方式可以在一定程度上获得更高的任务性能和更强的泛化能力，但也意味着对算力资源和训练样本质量提出了更高的要求**</font>。\n", "\n", "同时，<font color=\"red\">**全量SFT可能改变模型对语言模式的整体理解方式（Catastrophic Fogetting）**</font>，适合模型在原始训练之后的大规模迁移任务；而参数高效方法则更像是在已有能力上叠加一种“任务适配层”。\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/SFT_RL/172.png)"]}, {"cell_type": "markdown", "id": "435279fd-7aef-4b55-86ee-799678e79deb", "metadata": {}, "source": ["### 1.1 全量指令微调的三大应用场景"]}, {"cell_type": "markdown", "id": "9a2ea18d-6d99-4570-9b73-9e5d5f103c78", "metadata": {}, "source": ["- **应用场景1：用于模型构建后、承担与其他微调一样的职责**\n", "\n", "即使模型已经完成了全量预训练和初步微调、成为一个可用的Chat Model，SFT仍然可以继续承担优化和提升的任务——\n", "\n", "> **知识灌注（Knowledge Injection）**：当模型需要被应用于某一特定领域（如医疗、法律、教育等），可通过SFT使用领域指令数据进行训练，将领域知识、有时效性的事实或特定信息体系嵌入到大语言模型中、从而提升该领域任务表现。\n", "> \n", "> **风格迁移（Style Transfer）**：让模型的输出呈现出特定语气、格式、文化特征等表达风格。通过SFT训练具有某种风格特征的文本对，可以引导模型学习并模仿这种风格。\n", "> \n", "> **角色扮演（Custom persona modeling）**：让模型以特定角色身份进行对话，例如扮演医生、律师、心理咨询师、虚拟导游、小说人物等。配置模型的人格特征，包括语气、价值观、说话风格、行为习惯等，通常用于打造定制的AI助手或虚拟形象。\n", "> \n", "> **更新知识或持续学习**：如果模型使用的预训练数据已过时，SFT可以用更新的指令-答案对来引导模型学习更贴近当前现实的行为方式。在模型部署后收集的高质量交互数据可以周期性用于SFT训练，从而保持模型在现实环境中的表现持续提升。\n", ">\n", "> **修正行为偏差**：对于已有chat model中观察到的不稳定回答、错误理解、多轮交互不连贯等问题，可以通过SFT使用人类打标的指令-回答修正数据进行纠偏。\n", "\n", "---\n", "\n", "**核心问题：在这一场景下，SFT的优势与劣势是？**\n", "\n", "| 维度             | 全量SFT                          | 参数高效微调（LoRA / Adapter）        |\n", "|------------------|----------------------------------|----------------------------------------|\n", "| 参数更新范围     | 所有参数                         | 局部参数（新增/插入模块）             |\n", "| 微调成本         | 高（数据要求高、计算资源要求高、时间长）            | <font color=\"green\">**低（数据要求低、轻量级、快速）**|\n", "| 模型改造能力     | <font color=\"green\">**强（行为重构、知识迁移）**        | 中（适合任务适配、局部增强）         |\n", "| 泛化能力         | <font color=\"green\">**强（尤其是多任务SFT）**          | 较弱（易过拟合于小数据）              |\n", "| 部署灵活性       | 弱（不可逆、难切换）            | <font color=\"green\">**强（多模型并存、可动态加载）**         |\n", "| 原有能力保留     | 风险较大（需精心设计数据）      | <font color=\"green\">**较好（主模型参数不变）**                |\n", "\n", "- 如果目标是**对成熟模型进行深度能力重构或领域迁移**，并且有足够资源与数据支撑，那么SFT是效果最优的选择；\n", "- 如果需求是**快速适配一个任务、控制成本、保证主模型能力不受影响**，LoRA、Adapter 等参数高效微调方法会更合适；\n", "- 在实际工程中，常见的做法是：**先用SFT做“基线强化”，再用LoRA或Prompt Tuning做“快速定制”。**\n", "\n", "---"]}, {"attachments": {}, "cell_type": "markdown", "id": "14d34d56-b826-4ff5-aaa1-12942be1e3f8", "metadata": {}, "source": ["- **应用场景2：用于模型构建中、作为后训练的一环、为强化学习打底**\n", "\n", "从 **0到1** 训练大模型是一个复杂而系统的工程，需要涵盖从数据准备到模型部署的多个环节。以下是一个完整的流程框架：\n", "\n", "| 流程            | 说明                                                                                           |\n", "|:---------------:|--|\n", "| **数据准备**        | 收集高质量、覆盖面广的训练数据，对其进行清洗、去噪和格式化处理。划分<br>训练集、验证集，并存储为高效读取的格式。这一步为模型提供了扎实的输入基础。 |\n", "| **硬件与环境配置**  | 为模型训练准备高性能硬件（如 A800、A100 GPU），搭建分布式训练环境，<br>并优化深度学习框架的配置。这一步确保训练效率和稳定性。 |\n", "| **分词器训练**      | 根据训练数据量和模型任务需求，选择适合的分词算法（如 BPE 或 <br>SentencePiece）。分词器决定了模型如何理解数据，是数据与模型的桥梁。 |\n", "| **设计模型架构**    | 选择适合的模型结构（如 GPT、BERT），并配置参数量、层数、激活函数<br>等细节。对于大规模任务，可以结合领域特点定制模型。 |\n", "| **预训练**        | 使用无监督任务从海量数据中提取通用知识，比如语言模型的自回归建模或<br>掩码建模。预训练的效果直接影响模型后续的微调能力。<br><font color=\"red\">**此时我们得到的模型是Base model，基座模型。**</font><br>有时也称之为completion model，模型唯一的效果就是补全。|\n", "| <font color=\"red\">**意图对齐微调**</font>        | 通过监督微调（SFT）或强化学习对齐（RLHF），让模型学习人类偏好，<br>避免输出无意义或有害内容。对齐步骤是模型实用化的关键。<br><font color=\"red\">**经过后训练的模型是instruct model，也被认为是Chat Model。**</font>|\n", "| **特定优化微调**  | 在特定任务（如文本分类、问答）上微调模型，结合冻结与解冻层的策略进一步<br>优化性能，满足应用需求。                          |\n", "| **模型量化**       | 通过剪枝、量化和知识蒸馏等技术优化模型，提高推理效率，降低计算与存储<br>成本，使模型更适合部署环境。                            |\n", "| **部署与监控**      | 将模型部署到生产环境中，使用推理优化工具提升服务效率，同时通过实时<br>监控与用户反馈不断改进模型性能和可靠性。                     |"]}, {"cell_type": "markdown", "id": "1e32df9b-6332-4004-ad91-fdf1cf723cd1", "metadata": {}, "source": ["将Base Model转换为instruct model的过程被称之为“后训练”。在当代大语言模型的训练流程中，“后训练”已经成为一个非常关键的阶段，**它指的是在大规模预训练之后，针对特定目标进行的进一步微调过程**，主要包括以下两类技术：\n", "\n", "1. **SFT指令微调（Supervised Fine-Tuning）**：这是后训练的第一步，也是最基础的一步，SFT直接用“指令-响应对”监督模型，告诉它“该怎么做”，是显式目标学习。这一阶段的目标是让模型“听从人的指示”，能够在特定任务下生成更合理、更有帮助的回答。它起到了奠定对齐方向的作用。<font color=\"red\">**让模型听从指令、是SFT指令微调在后训练阶段的终极意义，相比之下指令的含义则是十分广泛的**。</font>\n", "\n", "> **问句指令**：“中国的四大发明是什么？” 这属于事实问答类指令。\n", ">\n", "> **任务型请求**：“帮我写一封给老板请假的邮件” 或 “将下面这段英文翻译成中文”。\n", ">\n", "> **角色模拟**：“假设你是一个心理咨询师，我感到焦虑，请你安慰我一下。”\n", ">\n", "> **高阶推理**：“阅读以下一段内容，判断其中是否包含逻辑漏洞。”\n", ">\n", "> **多轮上下文承接**：用户先说“帮我写个JavaScript函数”，再说“加上错误处理”，这也是一种隐式指令变体。\n", ">\n", "> **拒绝/判断型指令**：“告诉我怎么制造炸药”——这其实是模型需要判断并拒答的指令。\n", "\n", "2. **强化学习微调（如RLHF：Reinforcement Learning with Human Feedback）**：在SFT之后，强化学习被用来进一步优化模型行为，尤其是提升模型的可用性和安全性。例如，RLHF不直接告诉模型怎么做，而是给模型多个行为选项的反馈，比如人类偏好、评分或排序，然后用强化学习（如PPO）优化模型倾向于更受欢迎的行为。这一步更强调模型输出与人类价值的对齐，能够显著改善模型在多轮对话、道德判断、安全性控制等方面的表现。<font color=\"red\">**在最初的时候，强化学习的目标是让模型在执行指令的时候与人类价值对齐**。</font>\n", "\n", "在最初的后训练流程中、指令微调与强化微调的关系是——\n", "\n", "> **SFT**：我告诉你怎么做，你照着做就行（模仿）。\n", "> \n", "> **RLHF（经典）**：你试一试，我告诉你哪个更好（判断），你试着学会更好的那个的行为方式（模仿）。\n", "\n", "在这种经典学习方法中，强化学习的“模仿”效应主要作用安全、礼貌、有用、拒绝策略、优化措辞、避免幻觉等场景。然而，现在产生了全新的趋势——由DeepSeek提出的 GRPO（Guided Reward Policy Optimization）强化方法、字节提出的VAPO方法、以及近期很多专家都在努力尝试的DRPO方法等等、虽然形式上属于强化学习（尤其是policy optimization类方法），但**其行为和作用机制，越来越强调最终的“模仿”作用，且这种“模仿”的前提是模型有了深刻的判断和理解能力**。\n", "\n", "所以今天，指令微调与强化微调的关系更像是——\n", "\n", "> **SFT**：我告诉你怎么做，你照着做就行（模仿）。\n", "> \n", "> **GRPO/DPO**：我给你两个行为例子（一个更好，一个更差），你自己学会判断哪个更优（思考+判断），并模仿更优的那个表达方式（模仿）。\n", "\n", "整个“后训练”阶段的出现和完善，正是为了弥补预训练中“无目标、无方向”的泛化训练方式，让模型从“会说话”进化到“说得好”“说得对”。而且，随着预训练模型越来越通用，后训练就成为决定模型实际能力差异的核心部分，也是各大模型厂商差异化竞争的重点。现在很多研究甚至开始探索**后训练端到端统一优化**、**高效后训练方法（比如指令合成、偏好建模自动化）**、以及**个性化/垂类领域后训练**等方向，说明整个社区已经把“后训练”从一个附属步骤提升为真正的能力核心。"]}, {"cell_type": "markdown", "id": "00f88b1f-a84b-4389-8c0d-462246fb9a6c", "metadata": {}, "source": ["- **应用场景3：用于模型构建中、作为垂类模型打造的一环、替代继续预训练做知识灌注**"]}, {"cell_type": "markdown", "id": "10372ea5-abc9-403b-bc77-2ed9aba42b93", "metadata": {}, "source": ["SFT（Supervised Fine-Tuning）和继续预训练（Continued Pretraining / Further Pretraining）都可以用于对已有模型进行知识灌注，但它们的方法、数据形式和影响方式有本质上的差异。\n", "\n", "- **继续预训练（Continued Pretraining）**：是指在已有预训练模型基础上，**继续使用无监督方式（如掩码语言建模、Causal LM）**对新语料进行训练，它强调“语言建模能力”的继续提升或知识更新或知识灌注。例如，用最新的新闻数据继续训练GPT模型，让它掌握最近发生的事件。\n", "\n", "- **SFT（Supervised Fine-Tuning）**：是指使用**人工构造的指令-输出对（指令数据）**进行有监督训练，虽然可以进行知识灌注、但是本质上还是更接近于让模型进行**模仿**、让模型更好地“理解并执行人类指令”。它强调的是“行为模式”和“任务完成能力”的塑造。\n", "\n", "如果你用SFT来做知识灌注（Knowledge Injection）**，它的表面效果看起来和**继续预训练**很像——两者都可以让模型掌握“新知识”或“特定知识”。但它们之间有着**根本的不同**，不只是训练方式不同，更在于：\n", "\n", "1. **知识灌注的“路径”不同**\n", "\n", "| 项目                 | 继续预训练                              | SFT灌注知识                                |\n", "|----------------------|------------------------------------------|----------------------------------------------|\n", "| 数据类型             | 未标注文本，模型自由“阅读学习”         | 精选问题+答案（指令对），明确告诉模型知识点 |\n", "| 学习方式             | 自主建模语言分布，隐式吸收知识           | 明确指令监督，显式“记住知识”               |\n", "| 表现形式             | 知识内化为语言模型的分布                | 知识表现为“如何回答特定问题”               |\n", "| 对知识的控制程度     | 较弱（模型读到了什么就学什么）           | 强（人为设计、引导模型如何使用知识）        |\n", "| 泛化能力             | 容易泛化但不可靠（有时答不准）           | 精确性高但覆盖面有限（靠问题集引导）        |\n", "\n", "2. **你获得的“知识能力”是不同的**\n", "\n", "- 用**继续预训练**灌注知识，模型变得像“读了很多百科全书的人”，它知道很多东西，但你问它时，它可能不一定答得准或答得规范。\n", "  - 优点：语言自然、语境理解强、可以自由发挥。\n", "  - 缺点：对一些问题答不全、容易出现幻觉或答错（尤其是结构化知识）。\n", "\n", "- 用**SFT**灌注知识，模型像是“参加了答题训练班的人”，它知道你会问什么，也被训练成如何规范地回答。\n", "  - 优点：答题风格统一、准确率高、稳定性强。\n", "  - 缺点：知识覆盖范围受限，超出题库就表现不稳定。\n", "\n", "\n", "3. **SFT知识灌注的使用场景**\n", "\n", "你让SFT来做知识灌注，**其实不是为了让模型“知道某个知识”本身，而是让它“会在需要时正确表达这个知识”**，这在以下场景中尤其有价值：\n", "\n", "1. **专业领域知识问答**（如金融、医疗、法律）：答错不可接受，要求SFT明确灌注规范说法。\n", "2. **产品知识库、FAQ系统**：你要模型回答的是标准答案，而不是自由发挥。\n", "3. **数据敏感或政策合规内容**：如政府公开数据、公司规定，必须有确定答法。\n", "\n", "**“虽然训练方式不同，但SFT也能完成类似继续预训练的知识功能”**。  但本质上两者的“目的”和“表达方式”还是**非常不一样**的，而且在项目设计中可以互补使用。"]}, {"cell_type": "markdown", "id": "1e01e3cd-6a0b-49bc-8b42-f715aa6797fc", "metadata": {}, "source": ["### 1.2 当前课程的必备基础与硬件选择"]}, {"cell_type": "markdown", "id": "48baf749-2037-4ccd-9b10-135ca84a1189", "metadata": {}, "source": ["- **授课形式与必备前置基础**"]}, {"cell_type": "markdown", "id": "dfadaa21-0a07-4978-9140-2b5929c9ecba", "metadata": {}, "source": ["在之前的课程中、我们已经基于MateConv Mini详解了预训练过程中的各类流程以及优化流程、预训练课程本身已有14+小时内容，因此本次课程中涉及的与预训练相似的内容将不会再细致展开探讨训练原理。因此、<font color=\"red\">**如果你需要的是快速跑通代码、那你几乎无需任何基础知识即可学习**</font>、只需按照我所设置的流程一步步运行代码即可。\n", "\n", "<font color=\"red\">**如果你的目标是彻底吃透SFT流程、那你在跑通代码的同时，还会需要之前我为SFT课程所设置的一系列基础**</font>——\n", "\n", "> **1 【Transformer】1～4、10～16**\n", "\n", "如果你不理解参数超参数的区别、如果你不理解QKV是如何诞生、你不能轻松说出Decoder-only架构的训练和推理有什么区别、你不能理解生成模型的输出层是怎么输出文字的、那你需要学习Transformer内容。\n", "\n", "> **2 【llama】1～2、4～6、8～9、11～14、17～19**\n", "\n", "DeepSeek架构与我们实现的LlaMA架构高度相似、在LlaMA架构中我们详解了MOE混合专家模型、详解了KV缓存机制、详解了门控机制、这些都是DeepSeek模型的关键基础，掌握LLaMA架构能让你在学习deepseek时事半功倍！\n", "\n", "> **3 【分布式预训练】0.1、0.2、1～4，12~后续全部内容**\n", "\n", "在分布式预训练章节中，我们搭建了基于Llama + MOE架构自建的【对话模型MateConv】、其环境搭建、数据收集、训练流程和训练脚本将会在后续的DeepSeekv3课程和DeepSeekR1课程中发挥巨大作用，了解经典的预训练流程将会对你有很大的帮助！如果你有足够的时间，可以将整个分布式预训练流程看完。"]}, {"cell_type": "markdown", "id": "49c5725d-3633-426b-a5bf-6ac36728af18", "metadata": {}, "source": ["---"]}, {"cell_type": "markdown", "id": "481c9a42-755b-4b5a-9f20-7a1f7f5d2cf4", "metadata": {}, "source": ["- **所需硬件基础及Autodl租赁指南**"]}, {"cell_type": "markdown", "id": "4a874f4c-0ae3-4ee3-96f2-30674345ff91", "metadata": {}, "source": ["我的设备为、AutoDL租赁的——\n", "\n", "```python\n", "####################################################\n", "####   8 x 96 vCPU Intel(R) Xeon(R) Gold 6430   ####\n", "####   8 x vGPU (A100), VRAM 8 x 32G            ####\n", "####   Hard Disk Drive 1000G                    ####\n", "####################################################\n", "```\n", "\n", "该设备的成本越为9r/小时、AutoDL支持的最大卡位是1机8卡、因此如果你和我一样使用vGPU，那你的成本最多是18r/小时。硬件无上限、当前代码支持A800、A100、RTX6000等显卡、如果你有更好的设备，也欢迎使用。\n", "\n", "**你所需要的最低硬盘要求与我一样是1000G，最次也要有6~700G，CPU和显卡要求是我的一半、这是能够运行多进程分布式的最低要求**。最低要求的租赁成本约为每小时4r，数据处理所需时长大约为6~8小时、在我所设置的超参数下里所需的训练时长大约为4小时（5个epoch）。\n", "\n", "```python\n", "####################################################\n", "####   2 x 32 vCPU 型号任选                      ####\n", "####   RAM 2 x 80G                              ####\n", "####   2 x RTX4090, VRAM 2 x 24G                ####\n", "####   Hard Disk Drive 1000G                    ####\n", "####################################################\n", "```\n", "\n", "你可以通过缩小模型尺寸、或者缩小所使用的数据尺寸来完成整个训练流程、这样可以大幅缩减你所需的数据处理和训练时间。\n", "\n", "同时，在选择环境及基础配置时我选择的是 ↓\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/117.png)\n", "\n", "这一选择下默认的python是12.3版本，但是cuda版本比较合适。\n", "\n", "- <font color=\"red\">**什么时候需要开始租赁设备呢？租设备流程在哪里？**</font>\n", "\n", "建议是可以把课程听完、对整个流程有自己的认知后、再租赁设备、照着课件开始一步步进行运行。当然，如果你GPU预算十分充足、也可以跟着课程一起开启GPU。\n", "\n", "租设备流程看【第二阶段 环境搭建与分布式预训练】中的这两节内容 ↓ 但需要注意的是，这两节内容中已对环境进行了一些配置，在我们进行deepseekv3预训练的时候我们会重新进行配置，因此你只需要按照这个流程租好设备、设置好final shell等流程即可，**无需follow之前课程中对环境进行的配置**。\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/118.png)\n", "\n", "到这个环节就好了 ↓ 无需配置requirements.txt。\n", "\n", "![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/120.png)"]}, {"cell_type": "markdown", "id": "b796464b-55e0-42d2-927f-888b77f559bd", "metadata": {}, "source": ["## 2 从0到1实现MateConv Mini的SFT"]}, {"cell_type": "markdown", "id": "81086e3c-2d91-46f8-b30c-bdd7bb617fd8", "metadata": {}, "source": ["### 2.1 回顾MateConv Mini模型"]}, {"cell_type": "markdown", "id": "7cb7336a-0425-490f-8373-cd434100962e", "metadata": {}, "source": ["<center><img src=\"https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/training/78.png\" alt=\"描述文字\" width=\"600\">"]}, {"cell_type": "markdown", "id": "75fa63d7-d569-43a3-88a2-a6d56ab06d92", "metadata": {}, "source": ["我们将该架构的模型脚本放在了model.py文件中 ↓ 你可以从这里进行下载 ↓"]}, {"cell_type": "markdown", "id": "ed7ed77d-932b-42cb-a605-27fee58c5d67", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/SFT_RL/173.png)"]}, {"cell_type": "markdown", "id": "e4642770-012e-4903-8b47-ec016f3b0697", "metadata": {}, "source": ["```python\n", "from transformers import PretrainedConfig\n", "\n", "\n", "class LMConfig(PretrainedConfig):\n", "    model_type = \"MateConv Mini\"\n", "\n", "    def __init__(\n", "            self,\n", "            dim: int = 512,  # 模型隐藏层维度\n", "            n_layers: int = 8,  # Transformer 堆叠的层数\n", "            n_heads: int = 16,  # 注意力头的数量\n", "            n_kv_heads: int = 8,  # KV（Key 和 Value）共享头的数量\n", "            vocab_size: int = 6400,  # 词汇表的大小\n", "            hidden_dim: int = None,  # 前馈网络中隐藏层的维度，默认为 None 时将使用 dim 的倍数\n", "            multiple_of: int = 64,  # 前馈网络中隐藏层维度需要是该值的整数倍\n", "            norm_eps: float = 1e-5,  # LayerNorm 或 RMSNorm 的 epsilon 参数，用于数值稳定性\n", "            max_seq_len: int = 512,  # 最大序列长度\n", "            dropout: float = 0.0,  # Dropout 的比例\n", "            flash_attn: bool = True,  # 是否使用 Flash Attention（更高效的注意力实现）\n", "            ####################################################\n", "            # 下面是关于 MOE（Mixture of Experts，专家网络）的特定配置\n", "            # 当 use_moe 为 False 时，以下配置无效\n", "            ####################################################\n", "            use_moe: bool = False,  # 是否使用专家网络（MOE）\n", "            num_experts_per_tok=2,  # 每个 Token 被分配的专家数量\n", "            n_routed_experts=4,  # MOE 模型中的总专家数量\n", "            n_shared_experts: bool = True,  # 是否启用共享专家（共享权重）\n", "            scoring_func='softmax',  # 专家选择的评分函数，默认为 'softmax'\n", "            aux_loss_alpha=0.01,  # 辅助损失的权重系数，用于保持专家负载平衡\n", "            seq_aux=True,  # 是否在序列级别上计算辅助损失\n", "            norm_topk_prob=True,  # 是否对 Top-K 专家选择的概率进行归一化\n", "            **kwargs,\n", "    ):\n", "        # 模型的主要参数\n", "        self.dim = dim\n", "        self.n_layers = n_layers\n", "        self.n_heads = n_heads\n", "        self.n_kv_heads = n_kv_heads\n", "        self.vocab_size = vocab_size\n", "        self.hidden_dim = hidden_dim\n", "        self.multiple_of = multiple_of\n", "        self.norm_eps = norm_eps\n", "        self.max_seq_len = max_seq_len\n", "        self.dropout = dropout\n", "        self.flash_attn = flash_attn\n", "        ####################################################\n", "        # MOE（专家网络）的相关配置\n", "        ####################################################\n", "        self.use_moe = use_moe  # 是否启用专家网络\n", "        self.num_experts_per_tok = num_experts_per_tok  # 每个 Token 被分配的专家数量\n", "        self.n_routed_experts = n_routed_experts  # 总的专家数量\n", "        self.n_shared_experts = n_shared_experts  # 是否共享专家\n", "        self.scoring_func = scoring_func  # 专家选择的评分函数\n", "        self.aux_loss_alpha = aux_loss_alpha  # 辅助损失的权重系数\n", "        self.seq_aux = seq_aux  # 是否在序列级别上计算辅助损失\n", "        self.norm_topk_prob = norm_topk_prob  # 是否对 Top-K 专家选择概率进行归一化\n", "        super().__init__(**kwargs)\n", "```"]}, {"cell_type": "markdown", "id": "f77d4903-474e-4bac-aa75-4f3c6ad080fc", "metadata": {}, "source": ["- **参数量计算**"]}, {"cell_type": "markdown", "id": "f2a573bf-68a3-4126-8e15-77265836d766", "metadata": {}, "source": ["基于这些关键参数、当前架构的总参数量可以分为以下几部分计算：\n", "\n", "**1. 嵌入层参数量**"]}, {"cell_type": "markdown", "id": "30d85732-e9ec-4d8d-bcf3-652c988c18ee", "metadata": {}, "source": ["> - **公式**：`vocab_size * dim`\n", ">   \n", "> - 嵌入层的参数来自词汇表大小和嵌入维度。\n", ">\n", "> -    $$\n", "   \\text{Embedding Params} = 6400 \\cdot 512 = 3,276,800\n", "   $$"]}, {"cell_type": "markdown", "id": "69a2260e-7b70-48f5-8f3a-8118980a7dd1", "metadata": {}, "source": ["**2. 每层自注意力机制参数量**"]}, {"cell_type": "markdown", "id": "5930c40e-acac-4ce9-bb14-c23406ce03a1", "metadata": {}, "source": ["> - 注意力机制的参数分为 Query、Key、Value 和输出权重（`wo`），但是由于我们使用了**kv共享**机制，且借用了参数`n_kv_heads`作为共享的基础、因此注意力机制的参数量实际计算如下——\n", "> 1. **Query 权重矩阵**：\n", ">    - Query 是全头独立计算，因此：\n", ">      $$\n", "     \\text{Query Params} = dim \\cdot dim = 512 \\cdot 512 = 262,144\n", "     $$\n", "> \n", "> 2. **Key 权重矩阵**（共享头）：\n", ">    - Key 的参数量基于 `n_kv_heads` 和切分后的维度：\n", ">      $$\n", "     \\text{Key Params} = n\\_kv\\_heads \\cdot head\\_dim \\cdot dim = 8 \\cdot 32 \\cdot 512 = 131,072\n", "     $$\n", "> \n", "> 3. **Value 权重矩阵**（共享头）：\n", ">    - Value 的参数量与 Key 相同：\n", ">      $$\n", "     \\text{Value Params} = 131,072\n", "     $$\n", "> \n", "> 4. **Output 权重矩阵**：\n", ">    - 输出层需要将 `n_heads` 的多头输出合并为原始维度：\n", ">      $$\n", "     \\text{Output Params} = dim \\cdot dim = 512 \\cdot 512 = 262,144\n", "     $$\n", "> \n", "> **合计注意力机制参数量**：\n", "> - Attention Params = Q Params+K Params+V Params+O Param\n", "> $$ 262,144 + 131,072 + 131,072 + 262,144 = 786,432 $$"]}, {"cell_type": "markdown", "id": "7efc22c7-9127-459e-a7ff-7f3c3bd5e4b4", "metadata": {}, "source": ["**3.1 每层前馈网络参数量**"]}, {"cell_type": "markdown", "id": "b2fea41c-830a-4878-a459-99c7d0786f8c", "metadata": {}, "source": ["> - **公式**：\n", ">  $$\n", "  \\text{FFN Params} = 2 \\cdot (dim \\cdot hidden\\_dim) + hidden\\_dim \\cdot dim\n", "  $$\n", "> - `2 * (dim * hidden_dim)`：前馈网络中两层全连接权重。\n", "> - `hidden_dim * dim`：前馈层输出投影权重。\n", "> $$ \\text{FFN Params} = 3 \\cdot (dim \\times hidden\\_dim) = 3 \\cdot (512 \\times 2048) = 3,145,728 $$"]}, {"cell_type": "markdown", "id": "5d4ff9bd-702d-4479-9212-37ae68727ed9", "metadata": {}, "source": ["**3.2 如果启用MOE网络架构**"]}, {"cell_type": "markdown", "id": "cb9b98c3-133f-4975-b892-bc390fe69f9c", "metadata": {}, "source": ["> 在MateConv mini架构中、我们MOE的结构由专家、路由、以及共享专家三部分组成——\n", ">\n", ">1. **专家网络的参数量（Experts Params）**：每个专家是一个 `FeedForward` 层，总共有 `n_routed_experts` 个专家。\n", ">\n", "> **单个专家参数量**：来自 `FeedForward` 的参数：\n", ">     $$\n", "     \\text{Single Expert Params} = 3 \\cdot (dim \\cdot hidden\\_dim)\n", "     $$\n", "> - `w1`: $ dim \\rightarrow hidden\\_dim $\n", "> - `w2`: $ hidden\\_dim \\rightarrow dim $\n", "> - `w3`: $ dim \\rightarrow hidden\\_dim $\n", ">\n", ">  **所有专家的参数量**：\n", ">   $$\n", "   \\text{Experts Params} = n\\_routed\\_experts \\cdot \\text{Single Expert Params}\n", "   $$\n", ">\n", "> $$\n", "   \\text{Experts Params} = n\\_routed\\_experts \\cdot 3 \\cdot (dim \\cdot hidden\\_dim)\n", "   $$\n", ">   $$\n", "   \\text{Experts Params} = 4 \\cdot 3 \\cdot (512 \\cdot 2048)\n", "   $$\n", ">   $$\n", "   \\text{Experts Params} = 4 \\cdot 3 \\cdot 1,048,576 = 12,582,912\n", "   $$\n", ">\n", "> 2. **门控网络的参数量（Gating Params）**\n", ">   - 在 `MoEGate` 中，门控网络使用一个权重矩阵 `self.weight`：\n", ">     $$\n", "     \\text{Gating Params} = dim \\cdot n\\_routed\\_experts\n", "     $$\n", ">\n", ">    $$\n", "   \\text{Gating Params} = dim \\cdot n\\_routed\\_experts\n", "   $$\n", ">   $$\n", "   \\text{Gating Params} = 512 \\cdot 4 = 2,048\n", "   $$\n", "> \n", "> 3. **共享专家的参数量（Shared Expert Params，若启用）**\n", ">   - 如果配置了共享专家（`n_shared_experts is not None`），还需要计算共享专家的参数。\n", ">   - 共享专家是一个额外的 `FeedForward` 层，其参数量与单个专家相同：\n", ">     $$\n", "     \\text{Shared Expert Params} = 3 \\cdot (dim \\cdot hidden\\_dim)\n", "     $$\n", ">\n", ">   $$\n", "   \\text{Shared Expert Params} = 3 \\cdot (dim \\cdot hidden\\_dim)\n", "   $$\n", ">   $$\n", "   \\text{Shared Expert Params} = 3 \\cdot (512 \\cdot 2048)\n", "   $$\n", ">   $$\n", "   \\text{Shared Expert Params} = 3 \\cdot 1,048,576 = 3,145,728\n", "   $$\n", "> - 结合以上部分，MOE 的总参数量为：\n", ">   $$\n", "   \\text{MoE Params} = \\text{Experts Params} + \\text{Gating Params} + \\text{Shared Expert Params}\n", "   $$\n", ">   $$\n", "   \\text{<PERSON><PERSON>} = 12,582,912 + 2,048 + 3,145,728\n", "   $$\n", ">   $$\n", "   \\text{<PERSON><PERSON>} = 15,730,688\n", "   $$"]}, {"cell_type": "markdown", "id": "b3f8a610-72fd-401a-b515-35dedf705fd4", "metadata": {}, "source": ["4. **输出层参数量**"]}, {"cell_type": "markdown", "id": "f234c6e7-aa96-418e-bcff-b21db56d97e8", "metadata": {}, "source": ["> - **公式**：\n", ">  $$\n", "  \\text{Output Params} = vocab\\_size \\cdot dim\n", "  $$\n", ">  在本次架构中、我们使用了**Embedding 和 Output 层共享权重**的参数优化技巧，因此 Output 层就不需要单独计算参数量，只需计算一次 Embedding 参数量即可。"]}, {"cell_type": "markdown", "id": "7b547040-17dc-446b-bceb-c5639afb7336", "metadata": {}, "source": ["5. **每层总参数量**"]}, {"cell_type": "markdown", "id": "c3f207c5-bbf9-4404-b29d-a238f3a7c9e4", "metadata": {}, "source": ["> 每层参数量为自注意力机制和前馈网络的参数之和：\n", "> $$\n", "\\text{Layer Params} = \\text{Attention Params} + \\text{FFN Params}\n", "$$\n", "> $$\n", "\\text{Layer Params} = 786,432 + 2,097,152 = 2,883,584\n", "$$"]}, {"cell_type": "markdown", "id": "0579fbb3-834b-4271-a3d0-b0759a285ca2", "metadata": {}, "source": ["6. **所有层参数量**"]}, {"cell_type": "markdown", "id": "f19368aa-a6ab-475a-a3e0-07dfd11b5e08", "metadata": {}, "source": ["> Transformer 层数为 `n_layers = 8`：\n", "> $$\n", "\\text{Total Layer Params} = n\\_layers \\cdot \\text{Layer Params} = 8 \\cdot 2,883,584 = 23,068,672\n", "$$"]}, {"cell_type": "markdown", "id": "4f3b5a63-195b-4fbc-9439-9fa5c43a559c", "metadata": {}, "source": ["7. **总参数量**"]}, {"cell_type": "markdown", "id": "5d6b7836-d5db-4d29-8ca9-0a8e0e74e8f5", "metadata": {}, "source": ["> 总参数量包含嵌入层和所有 Transformer 层参数：\n", "> $$\n", "\\text{Total Params} = \\text{Embedding Params} + \\text{Total Layer Params}\n", "$$\n", "> $$\n", "\\text{Total Params} = 3,276,800 + 23,068,672 = 26,345,472\n", "$$\n", "> 即26.3MB（两千六百万参数）、即0.02B模型。"]}, {"cell_type": "markdown", "id": "a48c5c25-96e6-4bb9-b216-a9fdc638e035", "metadata": {}, "source": ["### 2.3 微调必备环境配置与所有必备文件"]}, {"cell_type": "markdown", "id": "0f81dca0-987f-4379-a3fb-9b752148454a", "metadata": {}, "source": ["```shell\n", "\n", "#建立线上虚拟环境，命名为MateConv\n", "conda create --name MateConv python=3.10\n", "conda init\n", "source ~/.bashrc\n", "conda activate MateConv\n", "\n", "#创建<PERSON><PERSON><PERSON>\n", "conda install jupyterlab\n", "conda install ipykernel\n", "python -m ipykernel install --user --name MateConv --display-name \"Python (MateConv)\"\n", "\n", "#创建项目主目录\n", "cd ~/autodl-tmp\n", "mk<PERSON>\n", "\n", "#打开jupyter\n", "cd ~/autodl-tmp/MateConv\n", "jupyter lab --allow-root\n", "\n", "#根据requirements.txt配置环境\n", "cd ~/autodl-tmp/MateConv\n", "pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple\n", "```"]}, {"cell_type": "markdown", "id": "e62fd5f3-f1ec-436a-bce5-d757d610d0d4", "metadata": {}, "source": ["全部必备文件——"]}, {"cell_type": "markdown", "id": "bfbb22e9-ed43-427a-bba3-862b2d90e0a6", "metadata": {}, "source": ["![](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/SFT_RL/174.png)"]}, {"cell_type": "markdown", "id": "d64c2a82-9213-4f7e-ad93-457386285ddc", "metadata": {}, "source": ["### 2.4 数据准备与数据清洗流程"]}, {"cell_type": "markdown", "id": "fc83e7f7-d729-4274-9258-afc503b22501", "metadata": {}, "source": ["这里我们选取数据规模相对较小的匠数科技的sft数据集。该数据集是一个是一个完整、格式统一、安全的大模型训练和研究资源。从网络上的公开数据源收集并整理了大量开源数据集，对其进行了格式统一，数据清洗，包含10M条数据的中文数据集和包含2M条数据的英文数据集。总量大约在3B token，适合小尺寸中文大语言模型进行指令微调："]}, {"cell_type": "markdown", "id": "f5d72f13-cb85-4285-8140-384f07ac8a7f", "metadata": {}, "source": ["匠数科技大模型sft数据集官方地址：https://www.modelscope.cn/datasets/deepctrl/deepctrl-sft-data"]}, {"cell_type": "markdown", "id": "30b39a5c-f229-4600-9126-d4aa11b176b6", "metadata": {}, "source": ["![166](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/SFT_RL/166.png)"]}, {"cell_type": "markdown", "id": "1b2c1e2b-e9d3-4849-a9fb-c2c676924908", "metadata": {}, "source": ["![168](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/SFT_RL/168.png)"]}, {"cell_type": "markdown", "id": "fc0142f2-a1cc-452e-af97-481dca289373", "metadata": {}, "source": ["- 指令微调数据集获取与数据集清洗"]}, {"cell_type": "markdown", "id": "98da9866-b505-4c8d-aa60-a2dea6fce738", "metadata": {}, "source": ["```shell\n", "conda activate MateConv\n", "pip install modelscope\n", "\n", "cd /root/autodl-tmp/MateConv\n", "\n", "sudo apt update\n", "sudo apt install git-lfs -y\n", "\n", "#设置modelscope环境为阿里云，加速下载\n", "export MODELSCOPE_ENVIRONMENT=cloud\n", "\n", "git lfs install\n", "# 大约需要5s时间进行拉取\n", "git clone https://www.modelscope.cn/datasets/deepctrl/deepctrl-sft-data.git\n", "\n", "# 拉取完所有git文件后，执行下面的代码，正式开始拉取数据\n", "# 速度大约在8MB/s下，拉取32G文件、需拉取1小时左右\n", "# 其中18G为中文文件，12G为英文文件\n", "cd deepctrl-sft-data\n", "git lfs install\n", "git lfs pull\n", "\n", "# 大部分modelscope的数据是通过下面CLI的方式下载\n", "# 下载流程都不太稳定，依据网速可能需要3-4小时时间\n", "# 可以直接从百度网盘中下载后上传，在autodl限速下大概传1.5小时时间\n", "# modelscope download --dataset deepctrl/deepctrl-sft-data --local_dir ./data\n", "\n", "# 拉取完毕后、执行清洗代码\n", "cd /root/autodl-tmp/MateConv\n", "python data_process.py\n", "\n", "```"]}, {"cell_type": "markdown", "id": "1e51c732-3c58-44ee-9573-4ca272e0be39", "metadata": {}, "source": ["![169](https://skojiangdoc.oss-cn-beijing.aliyuncs.com/2024LLM/SFT_RL/169.png)"]}, {"cell_type": "markdown", "id": "d2c8a2cc-3845-4237-9b10-137bf7d4291a", "metadata": {}, "source": ["### 2.5 数据清洗代码脚本深度解读"]}, {"cell_type": "markdown", "id": "3bb93524-4426-4a14-b0b0-e32cc1648070", "metadata": {}, "source": ["在之前的预训练流程中、我们已经见过常规的巨量数据清洗流程 ↓ "]}, {"cell_type": "markdown", "id": "dad548b5-1f42-448e-a6d0-ad20ec7a8d2f", "metadata": {}, "source": ["```\n", "【step 1】📂 raw data (PDF/HTML/TXT)\n", "            ↓  (转换)\n", "【step 2】📂 JSONL {\"text\": \"...\"}\n", "            ↓  (清洗)\n", "          📂 cleaned JSONL\n", "            ↓  (质量控制)\n", "【step 3】📂 filtered JSONL\n", "            ↓  (tokenizer)\n", "          📂 Tokenized JSONL {\"tokens\": [...]}\n", "            ↓  (拼接)\n", "【step 4】📂 Pretrain Data (bin/lmdb)\n", "            ↓  (匹配pytorch + deepspeed规则)\n", "          📂 能够索引的token\n", "            ↓  (匹配神经网络所需结构)\n", "          📂 分好批次的3d数据\n", "```"]}, {"cell_type": "markdown", "id": "e00e6a1d-49ab-4ed4-8137-e329c0767ab4", "metadata": {}, "source": ["在本次的数据集清洗过程中，data_process.py会执行step1、2、以及step3中的数据拼接部分，让我们来仔细看看完整的data_process.py代码解读。"]}, {"cell_type": "markdown", "id": "60c5922f-7ecd-44e6-87b8-e986c22869de", "metadata": {}, "source": ["```shell\n", "# 导入必要的标准库和第三方库\n", "import csv\n", "import re\n", "import jsonlines\n", "import psutil\n", "import pandas as pd\n", "from tqdm import tqdm\n", "from transformers import AutoTokenizer\n", "\n", "# ---------- ✅ 配置区 ----------\n", "\n", "# 是否包含历史对话记录，True 会将 history 字段写入输出数据\n", "CONTAIN_HISTORY = False\n", "\n", "# 每批写入的样本数量（防止一次性写入内存过大）\n", "CHUNK_SIZE = 50000\n", "\n", "# 指定分词器路径，用于判断中英文比例\n", "TOKENIZER_PATH = './model/mateconv_tokenizer'\n", "\n", "# 指定目标样本数（估算：一条样本约 2~3KB）\n", "CHINESE_TARGET_COUNT = 6_000_000  # 中文样本数量（约占1/2）\n", "ENGLISH_TARGET_COUNT = 950_000    # 英文样本数量（约占1/3）\n", "\n", "# 指定原始数据路径\n", "CHINESE_JSONL_PATH = './deepctrl-sft-data/sft_data_zh.jsonl'\n", "ENGLISH_JSONL_PATH = './deepctrl-sft-data/sft_data_en.jsonl'\n", "\n", "# 输出目录与文件名配置\n", "OUTPUT_DIR = './data'\n", "OUTPUT_FILENAME = 'sft_data_mixed.csv' if CONTAIN_HISTORY else 'sft_data_mixed_single.csv'\n", "OUTPUT_PATH = f'{OUTPUT_DIR}/{OUTPUT_FILENAME}'\n", "\n", "# ---------- 🔁 加载分词器 ----------\n", "# 加载指定路径下的分词器（use_fast=False 表示使用 PyTorch 实现）\n", "tokenizer = AutoTokenizer.from_pretrained(TOKENIZER_PATH, use_fast=False)\n", "print('✅ tokenizer词表大小：', len(tokenizer))\n", "\n", "\n", "# 打印内存使用情况，方便监控\n", "def log_memory_usage():\n", "    mem = psutil.virtual_memory()\n", "    print(f\"[MEMORY] Used: {mem.used / 1e9:.2f} GB / {mem.total / 1e9:.2f} GB\")\n", "\n", "\n", "# 计算文本中中文字符的比例\n", "def chinese_ratio(text):\n", "    chinese_chars = re.findall(r'[\\u4e00-\\u9fff]', text)\n", "    return len(chinese_chars) / len(text) if text else 0\n", "\n", "\n", "# 将数据批量处理并写入 CSV 文件\n", "def process_and_write_data(data, file_path):\n", "    # 创建三个空列表用于存储处理后的字段\n", "    q_lst, a_lst, history_lst = [], [], []\n", "\n", "    # 遍历每一条样本数据\n", "    for per in data:\n", "        history = per.get('history', '')\n", "        q = (per.get('q') or '').strip()\n", "        a = (per.get('a') or '').strip()\n", "\n", "        # 过滤无效样本：\n", "        # 1. 若要求包含历史但没有历史内容，则跳过\n", "        # 2. 问题或回答为空，跳过\n", "        if (CONTAIN_HISTORY and (not history or len(history) == 0)) or not q or not a:\n", "            continue\n", "\n", "        # 过滤极短或极长样本\n", "        if len(q) < 10 or len(a) < 5:\n", "            continue\n", "        if len(q) > 256 or len(a) > 256:\n", "            continue\n", "\n", "        # 根据文件路径和中英文比例过滤非目标语言样本\n", "        if not (chinese_ratio(q) > 0.9 and chinese_ratio(a) > 0.9) and 'zh' in file_path:\n", "            continue\n", "        if not (chinese_ratio(q) < 0.1 and chinese_ratio(a) < 0.1) and 'en' in file_path:\n", "            continue\n", "\n", "        # 将有效样本加入对应列表\n", "        q_lst.append(q)\n", "        a_lst.append(a)\n", "        history_lst.append(history if CONTAIN_HISTORY else [])\n", "\n", "    # 构造 DataFrame 并写入 CSV 文件（追加模式）\n", "    df = pd.DataFrame({'history': history_lst, 'q': q_lst, 'a': a_lst})\n", "    df.to_csv(file_path, mode='a', header=False, index=False,\n", "              lineterminator='\\r\\n', escapechar='\\\\', quoting=csv.QUOTE_MINIMAL)\n", "\n", "\n", "# 主函数：处理并抽样中英文 jsonl 数据，写入合并后的 CSV 文件\n", "def sft_process():\n", "    # 数据源配置：中文和英文路径、目标抽样数量\n", "    data_sources = [\n", "        {\"path\": CHINESE_JSONL_PATH, \"target_count\": CHINESE_TARGET_COUNT},\n", "        {\"path\": ENGLIS<PERSON>_JSONL_PATH, \"target_count\": ENGLISH_TARGET_COUNT}\n", "    ]\n", "\n", "    # 先创建输出文件，并写入列标题（history、q、a）\n", "    with open(OUTPUT_PATH, 'w', encoding='utf-8') as f:\n", "        f.write('history,q,a\\n')\n", "\n", "    # 遍历两个数据源（中文、英文）\n", "    for src in data_sources:\n", "        # 先统计文件总行数（用于 tqdm 显示进度）\n", "        with open(src['path'], 'r', encoding='utf-8') as f:\n", "            total_lines = sum(1 for _ in f)\n", "\n", "        selected = 0               # 已选样本计数\n", "        valid_buffer = []          # 有效样本暂存区（用于分批写入）\n", "\n", "        print(f\"📂 开始处理文件：{src['path']}，目标抽取 {src['target_count']} 条\")\n", "\n", "        # 逐行读取 jsonl 文件\n", "        with jsonlines.open(src['path']) as reader:\n", "            for obj in tqdm(reader, desc=f\"Sampling from {src['path']}\", total=total_lines):\n", "                # 从 input 或 q 字段获取问题，从 output 或 a 字段获取回答\n", "                q = (obj.get('input') or '') + (obj.get('q') or '')\n", "                a = (obj.get('output') or '') + (obj.get('a') or '')\n", "                history = obj.get('history', '')\n", "\n", "                # 清洗逻辑：满足以下任一条件则跳过\n", "                if (CONTAIN_HISTORY and (not history or len(history) == 0)) or not q or not a:\n", "                    continue\n", "                if len(q) < 10 or len(a) < 5 or len(q) > 512 or len(a) > 512:\n", "                    continue\n", "                if not (chinese_ratio(q) > 0.7 and chinese_ratio(a) > 0.7) and 'zh' in src['path']:\n", "                    continue\n", "                if not (chinese_ratio(q) < 0.1 and chinese_ratio(a) < 0.1) and 'en' in src['path']:\n", "                    continue\n", "\n", "                # 添加到缓存列表中\n", "                valid_buffer.append({'history': history if CONTAIN_HISTORY else [], 'q': q, 'a': a})\n", "                selected += 1\n", "\n", "                # 达到批量阈值则写入磁盘\n", "                if len(valid_buffer) >= CHUNK_SIZE:\n", "                    process_and_write_data(valid_buffer, OUTPUT_PATH)\n", "                    valid_buffer = []\n", "\n", "                # 达到目标数量提前结束\n", "                if selected >= src['target_count']:\n", "                    break\n", "\n", "            # 写入剩余未达一批的缓存数据\n", "            if valid_buffer:\n", "                process_and_write_data(valid_buffer, OUTPUT_PATH)\n", "\n", "        print(f\"✅ 完成：{src['path']} 实际采样：{selected} 条\")\n", "\n", "    # 显示内存使用情况\n", "    log_memory_usage()\n", "    print(\"🎉 数据抽样与写入完成！\")\n", "\n", "\n", "# 入口函数，启动整个流程\n", "if __name__ == \"__main__\":\n", "    sft_process()\n", "```"]}, {"cell_type": "markdown", "id": "383ccb39-8808-444f-96c2-372c4fa1cd05", "metadata": {}, "source": ["- 处理之后的数据是什么状态？"]}, {"cell_type": "code", "execution_count": 1, "id": "7ff32724-ae7e-4805-b1e6-fef9ee0e70ac", "metadata": {}, "outputs": [], "source": ["import pandas as pd"]}, {"cell_type": "code", "execution_count": 3, "id": "c5562468-5a5c-4ce0-ae97-469ef8e4a9d6", "metadata": {}, "outputs": [], "source": ["data = pd.read_csv(r\"D:\\pythonwork\\2025LLMtraining\\MateConvMini\\SFT\\data\\sft_data_mixed_single.csv\")"]}, {"cell_type": "code", "execution_count": 4, "id": "660cf522-d15f-41b5-b820-0e802d2372a9", "metadata": {}, "outputs": [{"data": {"text/plain": ["(4218792, 3)"]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["data.shape"]}, {"cell_type": "code", "execution_count": 5, "id": "edf2fd80-5e0d-4880-9ec9-4a372f7d2e20", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>history</th>\n", "      <th>q</th>\n", "      <th>a</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[]</td>\n", "      <td>好的。现在请你将这个文本中的所有的逗号都替换成空格。</td>\n", "      <td>好的，请稍等一下，现在我会将文本中的所有逗号替换为空格。处理后文本为：\"这是一个句子 目的是...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>[]</td>\n", "      <td>帮我回答一道历史题目。清朝时期的八旗共有多少旗人？</td>\n", "      <td>清朝时期八旗旗人总数约为200万人左右，其中正黄旗、正蓝旗、正白旗、正红旗的人数较多，其他旗...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>[]</td>\n", "      <td>请给我介绍一下人工智能的技术和发展。</td>\n", "      <td>人工智能的技术发展已经越来越快，包括深度学习、机器学习、自然语言处理、计算机视觉等。随着算力...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>[]</td>\n", "      <td>很好，现在请你计算一下这篇文章中出现了多少个“获得”字样。</td>\n", "      <td>经过计数，这篇文章中总共出现了两次“获得”这个词汇。</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>[]</td>\n", "      <td>A公司去年亏损了500万美元，今年净利润增长了50%，今年的净利润是多少？</td>\n", "      <td>今年净利润为750万美元</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  history                                      q  \\\n", "0      []             好的。现在请你将这个文本中的所有的逗号都替换成空格。   \n", "1      []              帮我回答一道历史题目。清朝时期的八旗共有多少旗人？   \n", "2      []                     请给我介绍一下人工智能的技术和发展。   \n", "3      []          很好，现在请你计算一下这篇文章中出现了多少个“获得”字样。   \n", "4      []  A公司去年亏损了500万美元，今年净利润增长了50%，今年的净利润是多少？   \n", "\n", "                                                   a  \n", "0  好的，请稍等一下，现在我会将文本中的所有逗号替换为空格。处理后文本为：\"这是一个句子 目的是...  \n", "1  清朝时期八旗旗人总数约为200万人左右，其中正黄旗、正蓝旗、正白旗、正红旗的人数较多，其他旗...  \n", "2  人工智能的技术发展已经越来越快，包括深度学习、机器学习、自然语言处理、计算机视觉等。随着算力...  \n", "3                         经过计数，这篇文章中总共出现了两次“获得”这个词汇。  \n", "4                                       今年净利润为750万美元  "]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["data.head()"]}, {"cell_type": "markdown", "id": "cac3c805-185e-4852-a355-21e8162234fc", "metadata": {}, "source": ["### 2.6 数据加载与混合脚本深度解读"]}, {"cell_type": "markdown", "id": "f0d71de7-c40e-4d4d-873e-ea1c40ca1695", "metadata": {}, "source": ["- **Chat-Style SFT 微调数据处理标准流程**"]}, {"cell_type": "markdown", "id": "8763b1f3-a950-4fdf-afa8-3d6e8e198337", "metadata": {}, "source": ["在我们已经准备好初步的微调数据集后、我们还需要执行整个数据处理流程中的第三步和第四步 ↓ 使用tokenizer进行编码、以及将数据处理成神经网络当前训练方式所需的格式。\n", "\n", "```\n", "【step 1】📂 raw data (PDF/HTML/TXT)\n", "            ↓  (转换)\n", "【step 2】📂 JSONL {\"text\": \"...\"}\n", "            ↓  (清洗)\n", "          📂 cleaned JSONL\n", "            ↓  (质量控制)\n", "【step 3】📂 filtered JSONL\n", "            ↓  (tokenizer)\n", "          📂 Tokenized JSONL {\"tokens\": [...]}\n", "            ↓  (拼接)\n", "【step 4】📂 Pretrain Data (bin/lmdb)\n", "            ↓  (匹配pytorch + deepspeed规则)\n", "          📂 能够索引的token\n", "            ↓  (匹配神经网络所需结构)\n", "          📂 分好批次的3d数据\n", "```"]}, {"cell_type": "markdown", "id": "fe7809e7-6964-4280-9908-a66c31ca26fd", "metadata": {}, "source": ["但是、在进行分词、编码等操作之前，Chat-Style微调数据还具有自己独特的数据处理流程 ↓"]}, {"cell_type": "markdown", "id": "c5fabae4-c0ac-464a-a22b-6fcf331c534e", "metadata": {}, "source": ["> **1. 整理为问答对（QA对）+ 可选历史（这是我们在data_process.py中做的）**\n", ">      \n", "> **2. 将Q、A、历史信息这些信息，整合到Message格式中，让所有历史信息+Q+A按顺序封装成一个标准的Chat-style message列表。**\n", "> \n", "> ```python\n", ">   [\n", ">     {\"role\": \"user\", \"content\": \"你好\"},\n", ">     {\"role\": \"assistant\", \"content\": \"你好，我能为你做什么？\"},\n", ">     {\"role\": \"user\", \"content\": \"请介绍 Transformer\"},\n", ">     {\"role\": \"assistant\", \"content\": \"Transformer 是一种神经网络架构...\"}\n", ">   ]\n", "> ```\n", "> <br>\n", "> \n", "> **3. 通过tokenizer将message格式信息转化为带角色的纯文本信息、明确不同语料的角色边界和生成起点、构成类似于prompts一样的文本。**\n", "> \n", ">   ```\n", ">   <|user|>: 你好\n", ">   <|assistant|>: 你好，我能为你做什么？\n", ">   <|user|>: 请介绍 Transformer\n", ">   <|assistant|>:\n", ">   ```\n", "> <br>\n", "> \n", "> **4. 将纯文本信息编码为token IDs**\n", ">      \n", "> **5. 构建损失上的Loss Mask，找到Assistant部分构建掩码并覆盖**\n", ">>  找到 `<|assistant|>` 最后一次出现的位置（即当前任务的回答起点），在这个点之前的 token 设置 loss mask 为 0，在这个点之后的 token 设置为 1（即参与 loss 计算），然后再将padding部分重新设置为tokenizer.pad_id。"]}, {"cell_type": "markdown", "id": "c971388e-d944-4c30-839a-6883660fb1be", "metadata": {}, "source": ["- **SFT训练方式与损失掩码**"]}, {"cell_type": "markdown", "id": "6ddfd749-98f9-4f09-b0ed-1621b952432d", "metadata": {}, "source": ["在大语言模型训练中有一种说法——预训练（Pretraining）和有监督微调（Supervised Fine-Tuning, SFT）在训练代码上看上去几乎99%的相似，但实际上这两种手段在训练本质和掩码机制两方面存在根本性差异。这种差异决定了预训练是让模型“吸收知识”、而SFT微调是让模型实现“模仿”。我们来看看具体的流程——\n", "\n", "\n", "> 预训练：自回归语言建模（Causal Language Modeling）：预训练阶段采用**自监督学习**方式，模型的目标是**预测下一个 token**。训练时不需要人工标注数据，而是使用大量自然文本构造输入和目标。例如，模型看到：\n", "\n", "```\n", "Input:   I love deep\n", "Target:              learning\n", "```\n", "\n", "为了防止模型“偷看未来”，此阶段使用**前瞻掩码（causal attention mask）**，即 Transformer 的每个位置只能看到前面的 token，不能访问后续 token，从而建立因果顺序。\n", "\n", "同时，预训练的 loss 是对整个 token 序列计算的（除了 padding），**所有 token 都被预测、都参与损失计算**。\n", "\n", "---\n", "\n", "> SFT：有监督的指令微调（Supervised Fine-Tuning）\n", "\n", "SFT 阶段，模型已经具备基础语言建模能力，目标转向**学习模仿人类完成任务的方式**。我们使用指令-回答对（如用户问题 + AI 回答）进行训练。不同于预训练，SFT 会将**完整的对话上下文 + 标准答案**同时输入模型：\n", "\n", "```\n", "<|user|>: 请介绍 Transformer\n", "<|assistant|>: Transformer 是由 Google 提出的一种神经网络架构……\n", "```\n", "\n", "此时，模型**可以看到整个上下文和回答本身**，但训练时我们只对 **目标回答部分计算损失**，即：\n", "\n", "- 不对用户输入部分进行监督；\n", "- 不对历史对话进行监督；\n", "- 只在当前 assistant 回复的 token 上产生 loss。\n", "\n", "因此，SFT 使用的是**损失掩码（loss mask）**，控制**哪些 token 的预测参与损失计算**，而不是通过 attention mask 限制可见范围。\n", "\n", "---\n", "\n", "> - **掩码机制对比总结**\n", "\n", "| 掩码类型        | 使用阶段       | 掩码作用                          | 是否遮住答案 | 控制目标                      |\n", "|------------------|----------------|-----------------------------------|----------------|-------------------------------|\n", "| **前瞻掩码（Causal Attention Mask）** | 预训练         | 控制 Transformer 的可见范围        | ✅ 是           | 限制“未来”不可见，建模因果语言 |\n", "| **损失掩码（Loss Mask）**             | SFT 微调       | 控制哪些位置产生监督信号            | ❌ 否           | 明确哪些 token 计算 loss        |"]}, {"cell_type": "markdown", "id": "e61d067f-aeb7-46fb-9d7a-ad979ce8b51e", "metadata": {}, "source": ["可见，预训练遮住的部分，是模型需要自己去“预测”的，模型要完全自己构造答案，所以因果语言模型是自监督/半监督的算法。\n", "\n", "而SFT是把所有的东西都给了模型，然后相当于assistant的部分就是模型现在要学习的标签，我们只检查模型有没有学会当前标签内容，所以我们只在assistant部分计算损失，因此它是有监督的。\n", "\n", "虽然看起来都是有掩码，但其实我们掩码和训练的逻辑完全不同。"]}, {"cell_type": "markdown", "id": "d31e5f83-4bfc-4ce6-9b74-cf792578db4a", "metadata": {}, "source": ["为此，完整的数据处理流程如下 ↓"]}, {"cell_type": "markdown", "id": "9ec9df5b-dc65-41a6-a956-127472b0c25b", "metadata": {}, "source": ["```python\n", "# 导入 PyTorch 的 Dataset 基类\n", "from torch.utils.data import Dataset\n", "import torch\n", "import numpy as np\n", "\n", "# 自定义数据集类，用于构建SFT训练任务的输入（支持Chat模板格式）\n", "# 仅仅支持csv文件输入，你需要根据你自己的文件格式进行改写\n", "class SFTDataset(Dataset):\n", "    # 构造函数，传入DataFrame、Tokenizer、最大长度\n", "    def __init__(self, df, tokenizer, max_length=1024):\n", "        super().__init__()\n", "\n", "        # 重置 DataFrame 索引\n", "        self.df = df.reset_index(drop=True)\n", "\n", "        # 保存 tokenizer 和最大长度\n", "        self.tokenizer = tokenizer\n", "        self.max_length = max_length\n", "\n", "        # 若没有pad_token，则设置为eos_token，保证分词器完整性\n", "        # 通常我们训练的tokenizer都自带pad_token和eos_token\n", "        # 开源模型的tokenizer则设置有不同的pad_token和eos_token\n", "        if tokenizer.pad_token is None:\n", "            tokenizer.pad_token = tokenizer.eos_token\n", "\n", "        # 获取 pad token 的ID\n", "        self.pad_token_id = tokenizer.pad_token_id\n", "\n", "        # 保存 assistant 回复的开始标记（用来识别loss mask起点）\n", "        self.bos_id = tokenizer(\"<s>assistant\").input_ids\n", "\n", "    # 数据集长度为DataFrame行数\n", "    def __len__(self):\n", "        return len(self.df)\n", "\n", "    # 工具函数：在主列表中查找子列表最后一次出现的位置\n", "    def find_sublist_index(self, main_list, sub_list):\n", "        last_index = -1\n", "        for i in range(len(main_list) - len(sub_list) + 1):\n", "            if main_list[i:i + len(sub_list)] == sub_list:\n", "                last_index = i\n", "        return last_index\n", "\n", "    # 安全地解析字符串格式的历史（例如 \"[(q1, a1), (q2, a2)]\"）\n", "    def safe_eval(self, s):\n", "        try:\n", "            return eval(s) if isinstance(s, str) else s\n", "        except Exception:\n", "            return []\n", "\n", "    # 获取单个样本，并转换为训练所需格式（X, Y, Mask）\n", "    def __getitem__(self, index):\n", "        # 读取第 index 条样本\n", "        sample = self.df.iloc[index]\n", "\n", "        # 解析 history 字段（字符串转为列表）\n", "        history = self.safe_eval(sample['history'])\n", "\n", "        # 转为字符串并清理\n", "        q = str(sample['q'])\n", "        a = str(sample['a'])\n", "\n", "        # 构造 chat 格式的 message 序列\n", "        messages = []\n", "        for turn in history:\n", "            # 每一轮必须是长度为2的 list 或 tuple，分别是用户输入和助手回答\n", "            if isinstance(turn, (list, tuple)) and len(turn) == 2:\n", "                messages.append({\"role\": \"user\", \"content\": str(turn[0])})\n", "                messages.append({\"role\": \"assistant\", \"content\": str(turn[1])})\n", "\n", "        # 添加当前问答轮\n", "        messages.append({\"role\": \"user\", \"content\": q})\n", "        messages.append({\"role\": \"assistant\", \"content\": a})\n", "\n", "        # 应用chat模板，将消息转为完整对话文本（不生成，纯拼接）\n", "        full_text = self.tokenizer.apply_chat_template(\n", "            messages, tokenize=False, add_generation_prompt=False\n", "        )\n", "\n", "        # 编码为token ID，裁剪到最大长度\n", "        input_ids = self.tokenizer(full_text, add_special_tokens=False).input_ids[:self.max_length]\n", "\n", "        # 查找assistant回复的起始位置（即loss开始计算的位置）\n", "        answer_start = self.find_sublist_index(input_ids, self.bos_id)\n", "\n", "        # fallback策略：如果找不到，则整个都不计算loss\n", "        if answer_start == -1:\n", "            answer_start = len(input_ids)\n", "\n", "        # 计算padding长度，补齐输入到 max_length\n", "        padding_len = self.max_length - len(input_ids)\n", "\n", "        # 填充input_ids到最大长度\n", "        input_ids = input_ids + [self.pad_token_id] * padding_len\n", "\n", "        # 构造loss mask（只对 assistant 回复部分进行监督）\n", "        loss_mask = [0] * answer_start + [1] * (len(input_ids) - answer_start - padding_len) + [0] * padding_len\n", "\n", "        # 构造训练输入X（去掉最后一个token）、目标输出Y（去掉第一个token）、mask（对齐Y）\n", "        input_ids = np.array(input_ids)\n", "        X = input_ids[:-1]\n", "        Y = input_ids[1:]\n", "        M = np.array(loss_mask[1:])\n", "\n", "        # 长度不一致时打印错误信息并返回下一个样本（防止训练崩溃）\n", "        if not (len(X) == len(Y) == len(M)):\n", "            print(f\"⛔ Invalid sample at index {index} | lens = {len(X)}, {len(Y)}, {len(M)}\")\n", "            return self.__getitem__((index + 1) % len(self.df))\n", "\n", "        # 返回训练输入X、标签Y、loss mask M（均为tensor）\n", "        return (\n", "            torch.tensor(X, dtype=torch.long),\n", "            torch.tensor(Y, dtype=torch.long),\n", "            torch.tensor(M, dtype=torch.long)\n", "        )\n", "```"]}, {"cell_type": "markdown", "id": "d00cfcb6-5ca2-432e-bc9b-07705b175be6", "metadata": {}, "source": ["- **在多轮对话中，我们是针对所有的Answer部分都进行掩码吗？**"]}, {"cell_type": "markdown", "id": "3577bd01-c8dc-47db-aa96-7133e5d03c39", "metadata": {}, "source": ["在 SFT（监督微调）中，我们希望 只对 Assistant 的回答计算 Loss，而 User 的提问、历史的问答内容，不计算 Loss。所以，loss_mask 这个列表中，**0 表示不计算 Loss（mask 掉），1 表示计算 Loss（保留）**。在实现过程中，我们具体的掩码代码是 ↓\n", "\n", "```python\n", "loss_mask = [0] * answer_start + [1] * (len(input_ids) - answer_start - padding_len) + [0] * padding_len\n", "```\n", "\n", "也就是——\n", "\n", "| 部分 | 解释 |\n", "|------|------|\n", "| `[0] * answer_start` | assistant回复开始前的内容，都不参与loss |\n", "| `[1] * (有效长度 - answer_start)` | assistant的回复部分，才参与loss |\n", "| `[0] * padding_len` | 补上的pad部分，也不参与loss |"]}, {"cell_type": "markdown", "id": "8ce5d579-c89f-42a9-af9e-bd2908df597d", "metadata": {}, "source": ["举个具体例子，比如假设 `tokenizer` 编码后如下（用 token 编号举例）：\n", "\n", "```python\n", "input_ids = [\n", "  101,  # <|user|>\n", "  201, 202, 203, 204, 205,   # 用户问题1\n", "  102,  # <|assistant|>\n", "  301, 302, 303, 304,        # 助手回答1\n", "  101,  # <|user|>\n", "  206, 207, 208, 209,        # 用户问题2\n", "  102,  # <|assistant|>\n", "  305, 306, 307, 308, 309, 310  # 助手回答2\n", "]\n", "```\n", "\n", "现在我们找 `<|assistant|>` 的 token ID 序列（假设是 `[102]`），然后我们调用：\n", "```python\n", "answer_start = find_sublist_index(input_ids, [102])\n", "```\n", "\n", "会返回 **最后一个 `<|assistant|>` 出现的位置**，即：\n", "```python\n", "answer_start = 16\n", "```\n", "\n", "假设max_seq_len是1024，那此时我们就会有：\n", "```python\n", "len(input_ids) = 22\n", "padding_len = 1024 - 22 = 1002\n", "```\n", "\n", "此时我们会使用代码将整个input_ids补齐——\n", "\n", "```python\n", "# 填充input_ids到最大长度\n", "input_ids = input_ids + [self.pad_token_id] * padding_len\n", "```\n", "\n", "那么 `loss_mask` 构造为：\n", "```python\n", "loss_mask = [0]*16 + [1]*(1024-16-1002) + [0]*1002\n", "```\n", "\n", "最终：\n", "```\n", "loss_mask = \n", "[ 0, 0, 0, ..., 0,    # 前16个：用户+历史内容 → 不训练\n", "  1, 1, 1, 1, 1, 1,   # 最后一次<|assistant|>之后的回答 → 用来训练\n", "  0, 0, ..., 0 ]      # padding → 不训练\n", "```"]}, {"cell_type": "markdown", "id": "8d74cbc4-f884-4053-b532-154dc65ce838", "metadata": {}, "source": ["- 找寻最后一个assitant的函数解析\n", "\n", "```python\n", "def find_sublist_index(self, main_list, sub_list):\n", "    last_index = -1\n", "    for i in range(len(main_list) - len(sub_list) + 1):\n", "        if main_list[i:i + len(sub_list)] == sub_list:\n", "            last_index = i\n", "    return last_index\n", "```\n", "\n", "---\n", "\n", "```python\n", "def find_sublist_index(self, main_list, sub_list):\n", "```\n", "- 定义一个方法 `find_sublist_index`，它接受两个参数：\n", "  - `main_list`：主列表（比如完整的 `input_ids`）\n", "  - `sub_list`：要查找的子列表（比如 `<|assistant|>` 的 token ID 序列）\n", "\n", "---\n", "\n", "```python\n", "    last_index = -1\n", "```\n", "- 初始化一个变量 `last_index` 为 -1\n", "- 如果后面一直没找到匹配，就默认返回 -1（说明没找到）\n", "\n", "---\n", "\n", "```python\n", "    for i in range(len(main_list) - len(sub_list) + 1):\n", "```\n", "- 开始遍历 `main_list`，遍历的位置 `i` 是从 `0` 到 `len(main_list) - len(sub_list)`\n", "  - 这是因为我们每次都要从 `i` 取出 `len(sub_list)` 长度的切片去比对\n", "  - 例如：\n", "    - `main_list = [1, 2, 3, 4, 5]`\n", "    - `sub_list = [3, 4]`\n", "    - 我们需要检查 `main_list[0:2], [1:3], [2:4], [3:5]` 这些片段\n", "\n", "---\n", "\n", "```python\n", "        if main_list[i:i + len(sub_list)] == sub_list:\n", "```\n", "- 检查从位置 `i` 开始、长度为 `len(sub_list)` 的切片是否等于 `sub_list`\n", "- 如果相等，说明此处匹配成功\n", "\n", "---\n", "\n", "```python\n", "            last_index = i\n", "```\n", "- 如果匹配上了，就把当前的 `i` 存到 `last_index`\n", "- 注意：**即使前面匹配过了，也会继续遍历整个主列表**\n", "  - 所以 `last_index` 最后记录的是**最后一次匹配的位置**\n", "\n", "---\n", "\n", "```python\n", "    return last_index\n", "```\n", "- 返回 `last_index`，即 `sub_list` 最后一次出现在 `main_list` 中的位置\n", "- 如果完全没找到，就会返回 -1\n", "\n", "---\n", "\n", "举个例子——\n", "\n", "```python\n", "main_list = [1, 2, 3, 4, 2, 3, 4, 5]\n", "sub_list = [2, 3, 4]\n", "\n", "find_sublist_index(main_list, sub_list)  → 返回 4\n", "```\n", "\n", "解释：\n", "- `sub_list` `[2, 3, 4]` 出现在下标 1 和 4 处\n", "- 所以返回的是最后一次出现的位置 4 ✅"]}, {"cell_type": "markdown", "id": "a2cd8de8-3198-41d0-ad62-3497c1cfd1c2", "metadata": {}, "source": ["### 2.7 完整微调训练流程与代码"]}, {"cell_type": "markdown", "id": "4974d20c-45e3-4946-9583-04bd0f941cc4", "metadata": {}, "source": ["- 开始训练"]}, {"cell_type": "markdown", "id": "cddeb098-f936-4091-8e86-096a07b67e25", "metadata": {}, "source": ["```shell\n", "pip install wandb\n", "\n", "deepspeed --master_port 29500 --num_gpus=8 full_sft.py --out_dir out --epochs 5 --use_wandb --wandb_project \"MateConv-SFT\"\n", "```"]}, {"cell_type": "markdown", "id": "f6d520aa-f53f-4d45-9a0f-54d5b6c3ade1", "metadata": {}, "source": ["- 完整SFT代码解读"]}, {"cell_type": "markdown", "id": "a8f45d56-64f5-46d8-bc0c-600e131d93f7", "metadata": {}, "source": ["```shell\n", "# 引入常用库\n", "import os\n", "import platform\n", "import argparse\n", "import time\n", "import math\n", "import warnings\n", "\n", "# 引入深度学习与数据处理相关库\n", "import pandas as pd\n", "import torch\n", "import torch.nn.functional as F\n", "import torch.distributed as dist\n", "from contextlib import nullcontext\n", "\n", "from torch import optim\n", "from torch.nn.parallel import DistributedDataParallel\n", "from torch.utils.data import DataLoader, DistributedSampler\n", "from transformers import AutoTokenizer, AutoModel\n", "\n", "# 引入自定义的模型结构、配置与数据集\n", "from model.model import Transformer\n", "from model.LMConfig import LMConfig\n", "from model.dataset import SFTDataset  # ✅ SFT相关核心组件\n", "\n", "# 忽略PyTorch的一些非关键警告\n", "warnings.filterwarnings('ignore')\n", "\n", "# 分布式/非分布式日志打印\n", "def Logger(content):\n", "    if not ddp or dist.get_rank() == 0:\n", "        print(content)\n", "\n", "# 自定义学习率调度函数：余弦退火+warmup\n", "def get_lr(it, all):\n", "    warmup_iters = args.warmup_iters\n", "    lr_decay_iters = all\n", "    min_lr = args.learning_rate / 10\n", "\n", "    if it < warmup_iters:\n", "        return args.learning_rate * it / warmup_iters\n", "    if it > lr_decay_iters:\n", "        return min_lr\n", "    decay_ratio = (it - warmup_iters) / (lr_decay_iters - warmup_iters)\n", "    assert 0 <= decay_ratio <= 1\n", "    coeff = 0.5 * (1.0 + math.cos(math.pi * decay_ratio))\n", "    return min_lr + coeff * (args.learning_rate - min_lr)\n", "\n", "# ✅ SFT训练的每个epoch执行过程\n", "def train_epoch(epoch, wandb, start_step=0):\n", "    start_time = time.time()\n", "    for step, (X, Y, loss_mask) in enumerate(train_loader, start=start_step):\n", "        # 将数据加载到设备上\n", "        X = X.to(args.device)\n", "        Y = Y.to(args.device)\n", "        loss_mask = loss_mask.to(args.device)\n", "\n", "        # 设置当前步的学习率\n", "        lr = get_lr(epoch * iter_per_epoch + step, args.epochs * iter_per_epoch)\n", "        for param_group in optimizer.param_groups:\n", "            param_group['lr'] = lr\n", "\n", "        # 混合精度上下文\n", "        with ctx:\n", "            # 模型前向传播：预测token logits\n", "            logits = model(X, Y).logits\n", "\n", "            # ✅ 计算SFT Loss：\n", "            # 对模型输出和标签计算交叉熵，但只在loss_mask==1的位置计算\n", "            loss = F.cross_entropy(logits.view(-1, logits.size(-1)), Y.view(-1), ignore_index=0, reduction='none')\n", "            loss_mask = loss_mask.view(-1)\n", "            loss = torch.sum(loss * loss_mask) / loss_mask.sum()\n", "\n", "        # 反向传播 + 梯度缩放（混合精度）\n", "        scaler.scale(loss).backward()\n", "\n", "        # 梯度累积与梯度裁剪\n", "        if (step + 1) % args.accumulation_steps == 0:\n", "            scaler.unscale_(optimizer)\n", "            torch.nn.utils.clip_grad_norm_(model.parameters(), args.grad_clip)\n", "            scaler.step(optimizer)\n", "            scaler.update()\n", "            optimizer.zero_grad(set_to_none=True)\n", "\n", "        # 日志记录\n", "        if step % args.log_interval == 0:\n", "            spend_time = time.time() - start_time\n", "            <PERSON><PERSON>(\n", "                'Epoch:[{}/{}]({}/{}) loss:{:.3f} lr:{:.7f} epoch_Time:{}min:'.format(\n", "                    epoch,\n", "                    args.epochs,\n", "                    step,\n", "                    iter_per_epoch,\n", "                    loss.item(),\n", "                    optimizer.param_groups[-1]['lr'],\n", "                    spend_time / (step + 1) * iter_per_epoch // 60 - spend_time // 60))\n", "\n", "            # wandb日志记录\n", "            if (wandb is not None) and (not ddp or dist.get_rank() == 0):\n", "                wandb.log({\"loss\": loss,\n", "                           \"lr\": optimizer.param_groups[-1]['lr'],\n", "                           \"epoch_Time\": spend_time / (step + 1) * iter_per_epoch // 60 - spend_time // 60})\n", "\n", "        # 模型保存\n", "        if (step + 1) % args.save_interval == 0 and (not ddp or dist.get_rank() == 0):\n", "            model.eval()\n", "            moe_path = '_moe' if lm_config.use_moe else ''\n", "            ckp = f'{args.save_dir}/full_sft_{lm_config.dim}{moe_path}.pth'\n", "\n", "            checkpoint = {\n", "                'model_state': model.module.state_dict() if isinstance(model, torch.nn.parallel.DistributedDataParallel) else model.state_dict(),\n", "                'optimizer_state': optimizer.state_dict(),\n", "                'scaler_state': scaler.state_dict(),\n", "                'epoch': epoch,\n", "                'step': step,\n", "                'args': vars(args)\n", "            }\n", "\n", "            torch.save(checkpoint, ckp)\n", "            Logger(f\"Checkpoint saved at {ckp}\")\n", "            model.train()\n", "\n", "# 模型/优化器/混合精度加载检查点\n", "def load_checkpoint(checkpoint_path, model, optimizer, scaler):\n", "    if os.path.exists(checkpoint_path):\n", "        Logger(f\"Loading checkpoint from {checkpoint_path}\")\n", "        checkpoint = torch.load(checkpoint_path, map_location=args.device)\n", "\n", "        if 'model_state' in checkpoint:\n", "            state_dict = checkpoint[\"model_state\"]\n", "            model.load_state_dict(state_dict, strict=False)\n", "        else:\n", "            Logger(\"No model_state found, skipping model loading.\")\n", "\n", "        if 'optimizer_state' in checkpoint:\n", "            optimizer.load_state_dict(checkpoint['optimizer_state'])\n", "        if 'scaler_state' in checkpoint:\n", "            scaler.load_state_dict(checkpoint['scaler_state'])\n", "\n", "        start_epoch = checkpoint.get('epoch', 0) + 1\n", "        start_step = checkpoint.get('step', 0) + 1\n", "        return start_epoch, start_step\n", "    else:\n", "        Logger(f\"No checkpoint found at {checkpoint_path}, starting from scratch.\")\n", "        return 0, 0\n", "\n", "# ✅ 初始化 SFT 模型与分词器\n", "def init_model():\n", "    tokenizer = AutoTokenizer.from_pretrained('./model/mateconv_tokenizer')\n", "    model_from = 1  # 从预训练权重加载\n", "\n", "    def count_parameters(model):\n", "        return sum(p.numel() for p in model.parameters() if p.requires_grad)\n", "\n", "    if model_from == 1:\n", "        model = Transformer(lm_config)\n", "        moe_path = '_moe' if lm_config.use_moe else ''\n", "        ckp = f'./out/pretrain_{lm_config.dim}{moe_path}.pth'\n", "        state_dict = torch.load(ckp, map_location=args.device)\n", "        unwanted_prefix = '_orig_mod.'\n", "        for k, v in list(state_dict.items()):\n", "            if k.startswith(unwanted_prefix):\n", "                state_dict[k[len(unwanted_prefix):]] = state_dict.pop(k)\n", "        model.load_state_dict(state_dict, strict=False)\n", "    else:\n", "        model = AutoModel.from_pretrained('./MateConv/out', trust_remote_code=True)\n", "\n", "    Logger(f'LLM总参数量：{count_parameters(model) / 1e6:.3f} 百万')\n", "    model = model.to(args.device)\n", "    return model, tokenizer\n", "\n", "# 分布式训练初始化\n", "def init_distributed_mode():\n", "    if not ddp: return\n", "    global ddp_local_rank, DEVICE\n", "    dist.init_process_group(backend=\"nccl\")\n", "    ddp_rank = int(os.environ[\"RANK\"])\n", "    ddp_local_rank = int(os.environ[\"LOCAL_RANK\"])\n", "    ddp_world_size = int(os.environ[\"WORLD_SIZE\"])\n", "    DEVICE = f\"cuda:{ddp_local_rank}\"\n", "    torch.cuda.set_device(DEVICE)\n", "\n", "# ✅ 训练主程序入口（SFT训练执行入口）\n", "if __name__ == \"__main__\":\n", "    # 读取命令行参数\n", "    parser = argparse.ArgumentParser(description=\"MateConv Full SFT\")\n", "    parser.add_argument(\"--out_dir\", type=str, default=\"out\", help=\"Output directory\")\n", "    parser.add_argument(\"--epochs\", type=int, default=19, help=\"Number of epochs\")\n", "    parser.add_argument(\"--batch_size\", type=int, default=128, help=\"Batch size\")\n", "    parser.add_argument(\"--learning_rate\", type=float, default=2e-5, help=\"Learning rate\")\n", "    parser.add_argument(\"--device\", type=str, default=\"cuda:0\" if torch.cuda.is_available() else \"cpu\", help=\"Device to use\")\n", "    parser.add_argument(\"--dtype\", type=str, default=\"bfloat16\", help=\"Data type\")\n", "    parser.add_argument(\"--use_wandb\", action=\"store_true\", help=\"Use Weights & Biases\")\n", "    parser.add_argument(\"--wandb_project\", type=str, default=\"MateConv-Full-SFT\", help=\"Weights & Biases project name\")\n", "    parser.add_argument(\"--num_workers\", type=int, default=8, help=\"Number of workers for data loading\")\n", "    parser.add_argument(\"--ddp\", action=\"store_true\", help=\"Use DistributedDataParallel\")\n", "    parser.add_argument(\"--accumulation_steps\", type=int, default=1, help=\"Gradient accumulation steps\")\n", "    parser.add_argument(\"--grad_clip\", type=float, default=1.0, help=\"Gradient clipping threshold\")\n", "    parser.add_argument(\"--warmup_iters\", type=int, default=0, help=\"Number of warmup iterations\")\n", "    parser.add_argument(\"--log_interval\", type=int, default=100, help=\"Logging interval\")\n", "    parser.add_argument(\"--save_interval\", type=int, default=1000, help=\"Model saving interval\")\n", "    parser.add_argument('--local_rank', type=int, default=-1, help='local rank for distributed training')\n", "    args = parser.parse_args()\n", "\n", "    # 初始化模型配置\n", "    lm_config = LMConfig()\n", "    max_seq_len = lm_config.max_seq_len\n", "    args.save_dir = os.path.join(args.out_dir)\n", "    os.makedirs(args.save_dir, exist_ok=True)\n", "    os.makedirs(args.out_dir, exist_ok=True)\n", "    tokens_per_iter = args.batch_size * max_seq_len\n", "    torch.manual_seed(1337)\n", "    device_type = \"cuda\" if \"cuda\" in args.device else \"cpu\"\n", "    args.wandb_run_name = f\"MateConv-Full-SFT-Epoch-{args.epochs}-BatchSize-{args.batch_size}-LearningRate-{args.learning_rate}\"\n", "    ctx = nullcontext() if device_type == \"cpu\" else torch.cuda.amp.autocast()\n", "\n", "    # 分布式训练标志\n", "    ddp = int(os.environ.get(\"RANK\", -1)) != -1\n", "    ddp_local_rank, DEVICE = 0, \"cuda:0\"\n", "    if ddp:\n", "        init_distributed_mode()\n", "        args.device = torch.device(DEVICE)\n", "\n", "    # wandb 初始化\n", "    if args.use_wandb and (not ddp or ddp_local_rank == 0):\n", "        import wandb\n", "        wandb.init(project=args.wandb_project, name=args.wandb_run_name)\n", "    else:\n", "        wandb = None\n", "\n", "    # ✅ 初始化模型和tokenizer\n", "    model, tokenizer = init_model()\n", "\n", "    # ✅ 加载 SFT训练数据集\n", "    df = pd.read_csv('./data/sft_data_mixed_single.csv')\n", "    df = df.sample(frac=1.0)  # 打乱数据\n", "    train_ds = SFTDataset(df, tokenizer, max_length=max_seq_len)  # ✅ 使用自定义的SFT数据集\n", "    train_sampler = DistributedSampler(train_ds) if ddp else None\n", "    train_loader = DataLoader(\n", "        train_ds,\n", "        batch_size=args.batch_size,\n", "        pin_memory=True,\n", "        drop_last=False,\n", "        shuffle=False,\n", "        num_workers=args.num_workers,\n", "        sampler=train_sampler\n", "    )\n", "\n", "    # 初始化优化器与自动混合精度Scaler\n", "    scaler = torch.cuda.amp.GradScaler(enabled=(args.dtype in ['float16', 'bfloat16']))\n", "    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate)\n", "\n", "    # 模型保存路径\n", "    moe_path = '_moe' if lm_config.use_moe else ''\n", "    checkpoint_path = f'{args.save_dir}/full_sft_{lm_config.dim}{moe_path}.pth'\n", "\n", "    # 检查点加载\n", "    start_epoch, start_step = load_checkpoint(checkpoint_path, model, optimizer, scaler)\n", "\n", "    # 是否使用torch.compile加速（可选）\n", "    if False and not lm_config.use_moe and platform.system() != 'Windows' and float(torch.__version__.split('.')[0]) >= 2:\n", "        Logger(\"compiling the model... (takes a ~minute)\")\n", "        unoptimized_model = model\n", "        model = torch.compile(model)\n", "\n", "    # DDP包裹模型（分布式训练）\n", "    if ddp:\n", "        model._ddp_params_and_buffers_to_ignore = {\"pos_cis\"}\n", "        model = DistributedDataParallel(model, device_ids=[ddp_local_rank])\n", "\n", "    # 每个epoch的迭代次数\n", "    iter_per_epoch = len(train_loader)\n", "\n", "    # ✅ 正式开始SFT训练\n", "    for epoch in range(start_epoch, args.epochs):\n", "        train_epoch(epoch, wandb, start_step)\n", "        start_step = 0  # 从第二个 epoch 开始，step重新置零\n", "```"]}, {"cell_type": "code", "execution_count": null, "id": "a9d8af78-6c98-4aeb-bf18-d1dd87afedf3", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}