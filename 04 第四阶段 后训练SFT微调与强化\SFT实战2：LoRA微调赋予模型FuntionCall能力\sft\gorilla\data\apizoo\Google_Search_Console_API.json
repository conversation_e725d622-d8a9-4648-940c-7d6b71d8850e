[{"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Search Console API - searchanalytics.query", "api_call": "service.searchanalytics().query(siteUrl: str).execute()", "api_version": "v1", "api_arguments": {"siteUrl": "[REQUIRED] string: The site's URL, including protocol. For example: `http://www.example.com/`."}, "functionality": "Query your data with filters and parameters that you define. Returns zero or more rows grouped by the row keys that you define. You must define a date range of one or more days. When date is one of the group by values, any days without data are omitted from the result list. If you need to know which days have data, issue a broad date range query grouped by date for any metric, and see which day rows are returned.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/webmaster-tools/v1/api_reference_index"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Search Console API - sitemaps.delete", "api_call": "service.sitemaps().delete(siteUrl: str, feedpath: str).execute()", "api_version": "v1", "api_arguments": {"feedpath": "[REQUIRED] string: The URL of the actual sitemap. For example: `http://www.example.com/sitemap.xml`.", "siteUrl": "[REQUIRED] string: The site's URL, including protocol. For example: `http://www.example.com/`."}, "functionality": "Deletes a sitemap from the Sitemaps report. Does not stop Google from crawling this sitemap or the URLs that were previously crawled in the deleted sitemap.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/webmaster-tools/v1/api_reference_index"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Search Console API - sitemaps.get", "api_call": "service.sitemaps().get(siteUrl: str, feedpath: str).execute()", "api_version": "v1", "api_arguments": {"feedpath": "[REQUIRED] string: The URL of the actual sitemap. For example: `http://www.example.com/sitemap.xml`.", "siteUrl": "[REQUIRED] string: The site's URL, including protocol. For example: `http://www.example.com/`."}, "functionality": "Retrieves information about a specific sitemap.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/webmaster-tools/v1/api_reference_index"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Search Console API - sitemaps.list", "api_call": "service.sitemaps().list(siteUrl: str).execute()", "api_version": "v1", "api_arguments": {"siteUrl": "[REQUIRED] string: The site's URL, including protocol. For example: `http://www.example.com/`.", "sitemapIndex": "string:  A URL of a site's sitemap index. For example: `http://www.example.com/sitemapindex.xml`."}, "functionality": " Lists the [sitemaps-entries](/webmaster-tools/v3/sitemaps) submitted for this site, or included in the sitemap index file (if `sitemapIndex` is specified in the request).", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/webmaster-tools/v1/api_reference_index"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Search Console API - sitemaps.submit", "api_call": "service.sitemaps().submit(siteUrl: str, feedpath: str).execute()", "api_version": "v1", "api_arguments": {"feedpath": "[REQUIRED] string: The URL of the actual sitemap. For example: `http://www.example.com/sitemap.xml`.", "siteUrl": "[REQUIRED] string: The site's URL, including protocol. For example: `http://www.example.com/`."}, "functionality": "Submits a sitemap for a site.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/webmaster-tools/v1/api_reference_index"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Search Console API - sites.add", "api_call": "service.sites().add(siteUrl: str).execute()", "api_version": "v1", "api_arguments": {"siteUrl": "[REQUIRED] string: The URL of the site to add."}, "functionality": " Adds a site to the set of the user's sites in Search Console.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/webmaster-tools/v1/api_reference_index"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Search Console API - sites.delete", "api_call": "service.sites().delete(siteUrl: str).execute()", "api_version": "v1", "api_arguments": {"siteUrl": "[REQUIRED] string: The URI of the property as defined in Search Console. **Examples:** `http://www.example.com/` or `sc-domain:example.com`."}, "functionality": " Removes a site from the set of the user's Search Console sites.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/webmaster-tools/v1/api_reference_index"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Search Console API - sites.get", "api_call": "service.sites().get(siteUrl: str).execute()", "api_version": "v1", "api_arguments": {"siteUrl": "[REQUIRED] string: The URI of the property as defined in Search Console. **Examples:** `http://www.example.com/` or `sc-domain:example.com`."}, "functionality": " Retrieves information about specific site.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/webmaster-tools/v1/api_reference_index"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Search Console API - sites.list", "api_call": "service.sites().list().execute()", "api_version": "v1", "api_arguments": {}, "functionality": " Lists the user's Search Console sites.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/webmaster-tools/v1/api_reference_index"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Search Console API - urlInspection.index.inspect", "api_call": "service.urlInspection().index().inspect().execute()", "api_version": "v1", "api_arguments": {}, "functionality": "Index inspection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/webmaster-tools/v1/api_reference_index"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Search Console API - urlTestingTools.mobileFriendlyTest.run", "api_call": "service.urlTestingTools().mobileFriendlyTest().run().execute()", "api_version": "v1", "api_arguments": {}, "functionality": "Runs Mobile-Friendly Test for a given URL.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/webmaster-tools/v1/api_reference_index"}}]