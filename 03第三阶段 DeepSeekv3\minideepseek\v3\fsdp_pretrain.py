### 📦 1. 基础引入和路径设置 ###
import glob
import math
import sys
import time
from pathlib import Path
from typing import Optional, Tuple, Union
import os
import lightning as L
import torch

from lightning.fabric.strategies import FSDPStrategy  # ✅ 使用 FSDP 策略
from torch.utils.data import DataLoader
from functools import partial

# 添加工程目录到路径中，支持模块 import
wd = Path(__file__).parent.parent.resolve()
sys.path.append(str(wd))

from transformers import AutoConfig
from lit_gpt.packed_dataset import create_dataloader
from lit_gpt.speed_monitor import SpeedMonitorFabric as Monitor
from lit_gpt.speed_monitor import estimate_flops
from lit_gpt.utils import get_default_supported_precision, num_parameters, step_csv_logger
from pytorch_lightning.loggers import WandbLogger
from lit_gpt.triton_cross_entropy import TritonCrossEntropyLoss
from loguru import logger

# ✅ 加载自定义模型（例如 Steel 模型）
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(os.path.join(parent_dir, "model", "steel_modify_from_qwen_1_5"))
from modeling_steel import SteelForCausalLM, SteelDecoderLayer
from steel_llm_utils import compatible_tiny_llama_config

### 📁 2. 配置与训练参数设置 ###
name = "steel_llm"
out_dir = Path("/data/gu_data/ckpt") / name
TRAIN_DATA_DIR = Path("/data/gu_data/step3_input")
MODEL_PATH = "../model/steel_modify_from_qwen_1_5"
BLOCK_SIZE = 2048
RESUME = Path("/data/gu_data/ckpt/steel_llm/step-860000-iter-6880000-ckpt")
ONLY_RESUME_MODEL = False
ADD_NEW_DATA_DIR = None
IGNORE_INDEX = 151643
USE_FLASH_ATTN = True

### ✅ 多卡训练核心参数设置
num_of_devices = 8  # ✅ 设置 GPU 数量
micro_batch_size = 8
global_batch_size = 64 * num_of_devices  # ✅ 全局 batch 是所有卡的 batch 总和
gradient_accumulation_steps = global_batch_size // (micro_batch_size * num_of_devices)  # ✅ 梯度累积

### 学习率调度参数
learning_rate = 3e-4
max_step = 430000 * 2 + 220000
warmup_steps = 1000
log_step_interval = 20
eval_step_interval = 20000
save_step_interval = 20000

### 日志与监控工具
hparams = {k: v for k, v in locals().items() if isinstance(v, (int, float, str)) and not k.startswith("_")}
logger = step_csv_logger("out", name)
wandb_logger = WandbLogger()

### 🛠️ 3. FSDP 模型训练启动逻辑 ###
def setup(devices: int = num_of_devices, ...):
    precision = precision or get_default_supported_precision(training=True)

    config = AutoConfig.from_pretrained(model_path, trust_remote_code=True)

    # ✅ 多卡使用 FSDP 策略
    if devices > 1:
        strategy = FSDPStrategy(
            auto_wrap_policy={SteelDecoderLayer},  # ✅ 按 Transformer Block 自动 wrap
            activation_checkpointing_policy=None,
            state_dict_type="full",
            limit_all_gathers=True,
            cpu_offload=False,
        )
    else:
        strategy = "auto"

    # ✅ 使用 Lightning Fabric 构建训练环境
    fabric = L.Fabric(devices=devices, strategy=strategy, precision=precision, loggers=[logger, wandb_logger])

    config.model_path = model_path
    config.use_flash_attn = USE_FLASH_ATTN
    config = compatible_tiny_llama_config(config, block_size)

    if devices > 1:
        fabric.launch(main, train_data_dir, val_data_dir, resume, config, only_resume_model)  # ✅ 启动多进程训练
    else:
        main(fabric, train_data_dir, val_data_dir, resume, config, only_resume_model)


### 🚀 4. 主训练流程逻辑（包含 resume、数据加载、模型初始化） ###
def main(fabric, train_data_dir, val_data_dir, resume, config, only_resume_model):
    monitor = Monitor(fabric)
    if fabric.global_rank == 0:
        out_dir.mkdir(parents=True, exist_ok=True)

    # ✅ 创建多进程 dataloader
    train_dataloader, val_dataloader, train_datasets, val_datasets = create_dataloaders(...)

    fabric.seed_everything(3407)

    # ✅ 初始化模型结构
    with fabric.init_module(empty_init=False):
        model = SteelForCausalLM(config)
        model.apply(model._init_weights)

    # ✅ 参数统计与 FLOPs 估算
    fabric.print(f"Total parameters {num_parameters(model):,}")
    with torch.device("meta"):
        meta_model = SteelForCausalLM(config)
        config.estimated_flops = estimate_flops(meta_model) * micro_batch_size

    # ✅ 设置 optimizer 与 FSDP 组网
    model = fabric.setup(model)
    optimizer = torch.optim.AdamW(model.parameters(), lr=learning_rate, weight_decay=0.05)
    optimizer = fabric.setup_optimizers(optimizer)

    # ✅ 从 checkpoint 恢复模型状态
    state = {"model": model, "optimizer": optimizer, "iter_num": 0, "step_count": 0}
    if resume or only_resume_model:
        state_dir = resume / "state.pth" if resume else only_resume_model / "state.pth"
        fabric.load(state_dir, state)
        fabric.print(f"Resumed from {state_dir}")

    train(fabric, state, train_dataloader, val_dataloader, monitor, resume, config, train_datasets, model)


### 🧪 5. 训练循环核心代码（包含梯度同步与日志记录） ###
def train(fabric, state, train_dataloader, val_dataloader, monitor, resume, config, train_datasets, huggingface_format_model):
    model, optimizer = state["model"], state["optimizer"]
    loss_func = TritonCrossEntropyLoss(ignore_index=IGNORE_INDEX)

    for train_data in train_dataloader:
        if state["iter_num"] >= max_step:
            break

        input_ids = train_data[:, :config.block_size]
        targets = train_data[:, 1:config.block_size+1]

        is_accumulating = (state["iter_num"] + 1) % gradient_accumulation_steps != 0
        with fabric.no_backward_sync(model, enabled=is_accumulating):  # ✅ 支持多卡的梯度累积
            logits = model(input_ids).logits
            loss = loss_func(logits, targets)
            fabric.backward(loss / gradient_accumulation_steps)

        if not is_accumulating:
            fabric.clip_gradients(model, optimizer, max_norm=1.0)
            optimizer.step()
            optimizer.zero_grad()
            state["step_count"] += 1

        state["iter_num"] += 1
        monitor.on_train_batch_end(...)

        # ✅ 多卡保存 checkpoint，只主进程保存
        if not is_accumulating and state["step_count"] % save_step_interval == 0:
            if fabric.global_rank == 0:
                ckpt_path = out_dir / f"step-{state['step_count']:06d}-iter-{state['iter_num']:06d}-ckpt"
                os.makedirs(ckpt_path, exist_ok=True)
                fabric.save(ckpt_path / "state.pth", state)
            fabric.barrier()  # ✅ 多进程间同步


### 🔍 6. 验证函数（用于 eval loss） ###
@torch.no_grad()
def validate(fabric: L.Fabric, model: torch.nn.Module, val_dataloader: DataLoader) -> torch.Tensor:
    model.eval()
    losses = []
    for val_data in val_dataloader:
        input_ids = val_data[:, :model.config.block_size]
        targets = val_data[:, 1:model.config.block_size+1]
        logits = model(input_ids)
        loss = TritonCrossEntropyLoss()(logits, targets)
        losses.append(loss.item())
    model.train()
    return torch.tensor(losses).mean()


### 🧵 7. CLI 接口启动器 ###
if __name__ == "__main__":
    from jsonargparse import CLI
    CLI(setup)
