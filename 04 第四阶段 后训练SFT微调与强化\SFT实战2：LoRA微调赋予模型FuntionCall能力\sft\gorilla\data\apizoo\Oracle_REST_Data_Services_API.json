[{"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle APEX: Get instance overview", "api_call": "GET /apex/statistics/overview", "api_version": "2023.12.17", "api_arguments": {"number_of_days": "integer: Specifies the number of days into the past to aggregate data for."}, "functionality": "This service returns aggregated overview data for an Application Express instance. If number_of_days parameter is not specified the response is for upto 30 days of aggregated data. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle APEX: Get application statistics", "api_call": "GET /apex/statistics/application/{application_id}/", "api_version": "2023.12.17", "api_arguments": {"application_id": "[REQUIRED] string: Identifier of the application."}, "functionality": "This service returns usage statistics for a specific Oracle APEX application. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle APEX: Get instance statistics", "api_call": "GET /apex/statistics/instance/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "This service returns usage statistics for the whole Oracle APEX instance. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle APEX: Get application statistics", "api_call": "GET /apex/statistics/workspace/{workspace_name}/", "api_version": "2023.12.17", "api_arguments": {"workspace_name": "[REQUIRED] string: Display name of the workspace."}, "functionality": "This service returns usage statistics for a specific Oracle APEX workspace. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle APEX: Get all workspaces", "api_call": "GET /apex/workspaces/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns information about the Oracle APEX workspaces available. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle APEX: Get workspace", "api_call": "GET /apex/workspaces/{workspace_name}/", "api_version": "2023.12.17", "api_arguments": {"workspace_name": "[REQUIRED] string: Display name of the workspace."}, "functionality": "This service returns a specific Oracle APEX workspace. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle APEX: Get all applications in the specified workspace", "api_call": "GET /apex/workspaces/{workspace_name}/applications/", "api_version": "2023.12.17", "api_arguments": {"workspace_name": "[REQUIRED] string: Display name of the workspace."}, "functionality": "Returns information about the Oracle APEX applications available in the specified workspace. This endpoint is a convenience. The same information can be retreived using /apex/applications/?q={\"workspace_display_name\":\"EXAMPLE_WORKSPACE\"} endpoint. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle APEX: Get or export a specific APEX application", "api_call": "GET /apex/applications/{application_id}", "api_version": "2023.12.17", "api_arguments": {"application_id": "[REQUIRED] string: Application Primary Key, Unique over all workspaces.", "export_format": "string: Indicates the file format to return the entire application components. Valid values SQL_SCRIPT | SQL_ZIP", "export_type": "string: Parameter for APEX Export function. Comma-delimited list of export types to perform APPLICATION_SOURCE,EMBEDDED_CODE, CHECKSUM-SH1", "with_date": "boolean: Parameter for APEX Export function. If true include export date and time in the result. Only valid if export_format query parameter is also specified.", "with_ir_public_reports": "boolean: Parameter for APEX Export function. If true, include public reports that a user saved. Only valid if export_format query parameter is also specified.", "with_ir_private_reports": "boolean: Parameter for APEX Export function. If true, include private reports that a user saved. Only valid if export_format query parameter is also specified.", "with_ir_notifications": "boolean: Parameter for APEX Export function. If true, include report notifications. Only valid if export_format query parameter is also specified.", "with_translations": "boolean: Parameter for APEX Export function. If true, include application translation mappings and all text from the translation repository. Only valid if export_format query parameter is also specified.", "p_with_pkg_app_mapping": "boolean: If true, export installed packaged applications  with references to the packaged application definition. If false, export them as normal applications.", "with_original_ids": "boolean: Parameter for APEX Export function. If true, export with the IDs as they were when the application was imported. Only valid if export_format query parameter is also specified.", "with_no_subscriptions": "boolean: Parameter for APEX Export function. If true, components contain subscription references. Only valid if export_format query parameter is also specified.", "with_comments": "boolean: Parameter for APEX Export function. If true, include developer comments. Only valid if export_format query parameter is also specified.", "with_supporting_objects": "string: Parameter for APEX Export function. If 'Y', export supporting objects. If 'I',automatically install on import. If 'N', do not export supporting objects. If null, the application's include in export deployment value is used. Only valid if export_format query parameter is also specified.", "with_acl_assignments": "boolean: Parameter for APEX Export function. If true, export ACL user role assignments. Only valid if export_format query parameter is also specified."}, "functionality": "This service returns a representation of the specified APEX Application. The representation data will be a metadata about the APEX Application from APEX_APPLICATIONS view or an export of the entire application using APEX GET_APPLICATION.EXPORT database procedure. See APEX documentation for more information. A client requires SQL Developer or SQL Administrator role.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle APEX: Export APEX application components", "api_call": "POST /apex/applications/{application_id}", "api_version": "2023.12.17", "api_arguments": {"application_id": "[REQUIRED] string: Application Primary Key, Unique over all workspaces.", "export_format": "string: Indicates the file format to return the entire application components. Valid values SQL_SCRIPT | SQL_ZIP", "export_type": "string: Parameter for APEX Export function. Comma-delimited list of export types to perform APPLICATION_SOURCE,EMBEDDED_CODE, CHECKSUM-SH1", "with_date": "boolean: Parameter for APEX Export function. If true include export date and time in the result. Only valid if export_format query parameter is also specified.", "with_ir_public_reports": "boolean: Parameter for APEX Export function. If true, include public reports that a user saved. Only valid if export_format query parameter is also specified.", "with_ir_private_reports": "boolean: Parameter for APEX Export function. If true, include private reports that a user saved. Only valid if export_format query parameter is also specified.", "with_ir_notifications": "boolean: Parameter for APEX Export function. If true, include report notifications. Only valid if export_format query parameter is also specified.", "with_translations": "boolean: Parameter for APEX Export function. If true, include application translation mappings and all text from the translation repository. Only valid if export_format query parameter is also specified.", "p_with_pkg_app_mapping": "boolean: If true, export installed packaged applications  with references to the packaged application definition. If false, export them as normal applications.", "with_original_ids": "boolean: Parameter for APEX Export function. If true, export with the IDs as they were when the application was imported. Only valid if export_format query parameter is also specified.", "with_no_subscriptions": "boolean: Parameter for APEX Export function. If true, components contain subscription references. Only valid if export_format query parameter is also specified.", "with_comments": "boolean: Parameter for APEX Export function. If true, include developer comments. Only valid if export_format query parameter is also specified.", "with_supporting_objects": "string: Parameter for APEX Export function. If 'Y', export supporting objects. If 'I',automatically install on import. If 'N', do not export supporting objects. If null, the application's include in export deployment value is used. Only valid if export_format query parameter is also specified.", "with_acl_assignments": "boolean: Parameter for APEX Export function. If true, export ACL user role assignments. Only valid if export_format query parameter is also specified."}, "functionality": "Export a subset of the specified APEX application.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle APEX: Create or Update an APEX application, with a specific application id, in the specified workspace", "api_call": "PUT /apex/workspaces/{workspace_name}/applications/{application_id}", "api_version": "2023.12.17", "api_arguments": {"workspace_name": "[REQUIRED] string: Display name of the workspace.", "application_id": "[REQUIRED] string: Application Primary Key, Unique over all workspaces."}, "functionality": "This services creates a new application in the specified workspace, using the given application/sql or application/zip input content using APEX install application procedure. The Application ID in the input file is ignored. If an application already exists with the application_id in the URL then it is overwritten. Otherwise, a new application is created in the workspace with the specified application_id from the URL. A client requires SQL Developer or SQL Administrator role to access this endpoint.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle APEX: Delete the specified APEX application", "api_call": "DELETE /apex/workspaces/{workspace_name}/applications/{application_id}", "api_version": "2023.12.17", "api_arguments": {"workspace_name": "[REQUIRED] string: Display name of the workspace.", "application_id": "[REQUIRED] string: Application Primary Key, Unique over all workspaces."}, "functionality": "Permanently delete the APEX application from the database. A client requires SQL Developer or SQL Administrator role to access this endpoint.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get all database components", "api_call": "GET /database/components/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns information about the components loaded into the database from DBA_REGISTRY view. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get a database component", "api_call": "GET /database/components/{comp_id}", "api_version": "2023.12.17", "api_arguments": {"comp_id": "[REQUIRED] string: Identifier of the component."}, "functionality": "Returns information about the specified component loaded into the database from DBA_REGISTRY view. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get a description of all local listeners for this database", "api_call": "GET /database/connections/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns LOCAL LISTENER and SERVICE NAME records from GV$LISTENER_NETWORK for this database. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get a description of a specific local listener for this database", "api_call": "GET /database/connections/{host_name},{port},{service_name}", "api_version": "2023.12.17", "api_arguments": {"host_name": "[REQUIRED] string: Name of the host.", "port": "[REQUIRED] number: Port number being listened on.", "service_name": "[REQUIRED] string: Name of the service."}, "functionality": "Returns LOCAL LISTENER and SERVICE NAME information from GV$LISTENER_NETWORK for a specific host, port, and service combination. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Get the current broker configuration", "api_call": "GET /database/dataguard/configuration/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "All the broker configuration. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Add a configuration", "api_call": "POST /database/dataguard/configuration/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Add a configuration. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Enable or disable a configuration", "api_call": "PUT /database/dataguard/configuration/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Depending on the operation property, the endpoint will enable or disable a configuration. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Remove the configuration", "api_call": "DELETE /database/dataguard/configuration/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Remove the configuration. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Get the current broker configuration properties", "api_call": "GET /database/dataguard/configuration/properties/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "All the broker confguration properties. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Get the specified broker configuration property", "api_call": "GET /database/dataguard/configuration/properties/{property}", "api_version": "2023.12.17", "api_arguments": {"property": "[REQUIRED] string: Name of the property"}, "functionality": "Get the specified configuration property. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Alter a configuration property", "api_call": "PUT /database/dataguard/configuration/properties/{property}", "api_version": "2023.12.17", "api_arguments": {"property": "[REQUIRED] string: Name of the property"}, "functionality": "Alter a configuration property. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Get the current broker configuration databases", "api_call": "GET /database/dataguard/databases/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Broker confguration databases. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Add a database", "api_call": "POST /database/dataguard/databases/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Add a database. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Get the current broker configuration database", "api_call": "GET /database/dataguard/databases/{database}", "api_version": "2023.12.17", "api_arguments": {"database": "[REQUIRED] string: Database name"}, "functionality": "All the broker confguration database. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Convert, enable, disable, or set the role of a database", "api_call": "PUT /database/dataguard/databases/{database}", "api_version": "2023.12.17", "api_arguments": {"database": "[REQUIRED] string: Database name"}, "functionality": "Depending on the operation type, the endpoint will convert to physical, convert to snapshot, enable, disable, set as failover, set as switchover, or set as reinstate a database. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Delete the current database from the broker", "api_call": "DELETE /database/dataguard/databases/{database}", "api_version": "2023.12.17", "api_arguments": {"database": "[REQUIRED] string: Database name"}, "functionality": "Remove a database. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Get the current broker database instances information", "api_call": "GET /database/dataguard/databases/{database}/instances/", "api_version": "2023.12.17", "api_arguments": {"database": "[REQUIRED] string: Database Name"}, "functionality": "All the broker databases. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Delete the Data Guard instance", "api_call": "DELETE /database/dataguard/databases/{database}/instances/{instance}", "api_version": "2023.12.17", "api_arguments": {"database": "[REQUIRED] string: Database Name", "instance": "[REQUIRED] string: Instance name"}, "functionality": "Delete a Data Guard instance. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Get the database Data Guard properties", "api_call": "GET /database/dataguard/databases/{database}/properties/", "api_version": "2023.12.17", "api_arguments": {"database": "[REQUIRED] string: Database Name"}, "functionality": "Get database Data Guard properties. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Get a database Data Guard property", "api_call": "GET /database/dataguard/databases/{database}/properties/{property}", "api_version": "2023.12.17", "api_arguments": {"database": "[REQUIRED] string: Database Name", "property": "[REQUIRED] string: Property Name"}, "functionality": "Get a database Data Guard property. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Guard: Set the value of a database Data Guard property", "api_call": "PUT /database/dataguard/databases/{database}/properties/{property}", "api_version": "2023.12.17", "api_arguments": {"database": "[REQUIRED] string: Database Name", "property": "[REQUIRED] string: Property Nane"}, "functionality": "Sets the value of a database Data Guard property. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Pump: Create an export data pump job", "api_call": "POST /database/datapump/export", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Create a Data Pump export job with the specified parameters and start it. Data and object structures can be exported to a directory in the Oracle database server or to Oracle Object Store. Refer to Oracle Data Pump documentation for a more detailed explanation of parameters. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Pump: Create an import data pump job", "api_call": "POST /database/datapump/import", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Create a Data Pump import job with the specified parameters and start it. Data and object structures can be imported from a directory in the Oracle database server or from Oracle Object Store. Refer to Oracle Data Pump documentation for a more detailed explanation of parameters. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Pump: Get all data pump jobs", "api_call": "GET /database/datapump/jobs/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes all Data Pump jobs in the database. Uses DBA_DATAPUMP_JOBS view. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Create a data pump job", "api_call": "POST /database/datapump/jobs/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Create a Data Pump job with the specified parameters and start it. Refer to Oracle Data Pump documentation for a more detailed explanation of parameters. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Pump: Get a specific data pump job", "api_call": "GET /database/datapump/jobs/{owner_name},{job_name}/", "api_version": "2023.12.17", "api_arguments": {"owner_name": "[REQUIRED] string: Owner of the Data Pump job.", "job_name": "[REQUIRED] string: Name of the Data Pump job."}, "functionality": "Describes a specific Data Pump job in the database. Uses DBA_DATAPUMP_JOBS view. When the specified job is a Data Pump Export job, the response contains links to the log and export files. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Pump: Get a file for a specific data pump job", "api_call": "GET /database/datapump/jobs/{owner_name},{job_name}/{filename}", "api_version": "2023.12.17", "api_arguments": {"owner_name": "[REQUIRED] string: Owner of the Data Pump job.", "job_name": "[REQUIRED] string: Name of the Data Pump job.", "filename": "[REQUIRED] string: Name of the file associated with the Data Pump job."}, "functionality": "Returns the log or export file for the given data pump job. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get all database links", "api_call": "GET /database/db_links/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes all database links in the database. Uses DBA_DB_LINKS or ALL_DB_LINKS view depending on the role access at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get a database link", "api_call": "GET /database/db_links/{owner},{db_link}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the database link.", "db_link": "[REQUIRED] string: Name of the database link."}, "functionality": "Describes a database link in the database. Uses DBA_DB_LINKS or ALL_DB_LINKS view depending on the role access at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get all feature usage statistics", "api_call": "GET /database/feature_usage/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "See what features are used in the database. Returns records from DBA_FEATURE_USAGE_STATISTICS. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get usage statistic for a feature", "api_call": "GET /database/feature_usage/{dbid},{name}", "api_version": "2023.12.17", "api_arguments": {"dbid": "[REQUIRED] number: Database identifier as found in GV$DATABASE.", "name": "[REQUIRED] string: Name of the feature."}, "functionality": "Returns records from DBA_FEATURE_USAGE_STATISTICS for a specific feature. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get all alerts", "api_call": "GET /database/monitoring/alerts/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns records from V$DIAG_ALERT_EXT. In an Oracle RAC environment, data returned by this service may vary according to the instance to which a session is connected. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get an alert record", "api_call": "GET /database/monitoring/alerts/{record_id}", "api_version": "2023.12.17", "api_arguments": {"record_id": "[REQUIRED] number: Identifier for the alert record."}, "functionality": "Returns specific alert record from V$DIAG_ALERT_EXT. In an Oracle RAC environment, data returned by this service may vary according to the instance to which a session is connected. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get count of recent alerts by message level", "api_call": "GET /database/monitoring/alerts_recent_summaries/by_message_level", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Group by message level on V$DIAG_ALERT_EXT originating within the last 7 days. In an Oracle RAC environment, data returned by this service may vary according to the instance to which a session is connected. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get count of recent alerts by message type", "api_call": "GET /database/monitoring/alerts_recent_summaries/by_message_type", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Group by message type on V$DIAG_ALERT_EXT originating within the last 7 days. In an Oracle RAC environment, data returned by this service may vary according to the instance to which a session is connected. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get count of alerts by message level", "api_call": "GET /database/monitoring/alerts_summaries/by_message_level", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Group by message level on V$DIAG_ALERT_EXT. In an Oracle RAC environment, data returned by this service may vary according to the instance to which a session is connected. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get count of alerts by message type", "api_call": "GET /database/monitoring/alerts_summaries/by_message_type", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Group by message type on V$DIAG_ALERT_EXT. In an Oracle RAC environment, data returned by this service may vary according to the instance to which a session is connected. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get all session limits", "api_call": "GET /database/monitoring/session_limits", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Get all resource limits for sessions using GV$RESOURCE_LIMIT view. Since GV$RESOURCE_LIMIT exposes global level information, it can only be queried from CDB$ROOT or in a non-CDB database. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get all locks held in the database", "api_call": "GET /database/monitoring/session_locks/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Get all locks that are held in the database using DBA_LOCK view. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get a specific session lock record", "api_call": "GET /database/monitoring/session_locks/{session_id},{lock_id1},{lock_id2}", "api_version": "2023.12.17", "api_arguments": {"session_id": "[REQUIRED] number: Session holding or acquiring the lock.", "lock_id1": "[REQUIRED] string: Type-specific lock identifier, part 1.", "lock_id2": "[REQUIRED] string: Type-specific lock identifier, part 2."}, "functionality": "Returns the corresponding record from DBA_LOCK view. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get all locks that are blocking other sessions", "api_call": "GET /database/monitoring/session_locks/holding/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Using DBA_LOCKS and DBA_BLOCKERS views, this service returns a list of DBA_LOCKS records where the session is not waiting for a locked object but is holding a lock on an object for which another session is waiting. In an Oracle RAC environment, this only applies if the blocker is on the same instance. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get a session lock that is blocking other sessions", "api_call": "GET /database/monitoring/session_locks/holding/{session_id},{lock_id1},{lock_id2}", "api_version": "2023.12.17", "api_arguments": {"session_id": "[REQUIRED] number: Session identifier.", "lock_id1": "[REQUIRED] string: Type-specific lock identifier, part 1.", "lock_id2": "[REQUIRED] string: Type-specific lock identifier, part 2."}, "functionality": "Using DBA_LOCKS and DBA_BLOCKERS views, this service returns a specific DBA_LOCKS record where the session is not waiting for a locked object but is holding a lock on an object for which another session is waiting. In an Oracle RAC environment, this only applies if the blocker is on the same instance. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get all locks that sessions are waiting on", "api_call": "GET /database/monitoring/session_locks/waiting/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Using DBA_LOCKS and DBA_WAITERS views, this service returns a list of DBA_LOCKS records for sessions that are waiting on locks held by other sessions. In an Oracle RAC environment, this only applies if the waiter is on the same instance. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get a specific lock that a session is waiting on", "api_call": "GET /database/monitoring/session_locks/waiting/{session_id},{lock_id1},{lock_id2}", "api_version": "2023.12.17", "api_arguments": {"session_id": "[REQUIRED] number: Session identifier.", "lock_id1": "[REQUIRED] string: Type-specific lock identifier, part 1.", "lock_id2": "[REQUIRED] string: Type-specific lock identifier, part 2."}, "functionality": "Using DBA_LOCKS and DBA_WAITERS views, this service returns the corresponding DBA_LOCKS records for session that is waiting on locks held by other sessions. In an Oracle RAC environment, this only applies if the waiter is on the same instance. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get count of different session types", "api_call": "GET /database/monitoring/session_summaries/by_type", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Group by session type on GV$SESSION. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get all session wait class records", "api_call": "GET /database/monitoring/session_wait_classes/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all records from GV$SESSION_WAIT_CLASS. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get a specific session wait class record", "api_call": "GET /database/monitoring/session_wait_classes/{sid},{serial_number},{wait_class_number}", "api_version": "2023.12.17", "api_arguments": {"sid": "[REQUIRED] number: Session identifier.", "serial_number": "[REQUIRED] number: Session serial number.", "wait_class_number": "[REQUIRED] number: Number of the wait class."}, "functionality": "Returns the corresponding record from GV$SESSION_WAIT_CLASS. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get all session waits", "api_call": "GET /database/monitoring/session_waits/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all records from GV$SESSION_WAIT.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get a specific session wait", "api_call": "GET /database/monitoring/session_waits/{sid},{seq_number}", "api_version": "2023.12.17", "api_arguments": {"sid": "[REQUIRED] number: Session identifier.", "seq_number": "[REQUIRED] number: Wait sequence number."}, "functionality": "Returns the corresponding record from GV$SESSION_WAIT. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get all current sessions", "api_call": "GET /database/monitoring/sessions/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all records on GV$SESSION. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get a session", "api_call": "GET /database/monitoring/sessions/{sid},{serial_number}/", "api_version": "2023.12.17", "api_arguments": {"sid": "[REQUIRED] number: Session identifier.", "serial_number": "[REQUIRED] number: Session serial number."}, "functionality": "Returns the GV$SESSION for the specified Session Identifier and Session Serial Number. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get all long running operations for a session", "api_call": "GET /database/monitoring/sessions/{sid},{serial_number}/long_running_operations", "api_version": "2023.12.17", "api_arguments": {"sid": "[REQUIRED] number: Session identifier.", "serial_number": "[REQUIRED] number: Session serial number."}, "functionality": "Returns all records from GV$SESSION_LONGOPS for a specific session. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get current active process for a session", "api_call": "GET /database/monitoring/sessions/{sid},{serial_number}/process", "api_version": "2023.12.17", "api_arguments": {"sid": "[REQUIRED] number: Session identifier.", "serial_number": "[REQUIRED] number: Session serial number."}, "functionality": "Returns all records from GV$PROCESS for a specific session. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get time totals for all registered wait classes", "api_call": "GET /database/monitoring/wait_class_totals", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all the records from GV$SYSTEM_WAIT_CLASS, except 'Idle' wait class. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get wait time summary from recent active session history records", "api_call": "GET /database/monitoring/waits_recent", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Oracle Diagnostics Pack licence is required for this service. Group by sample_time and wait_class on GV$ACTIVE_SESSION_HISTORY for records with sample_time within the last 7 days. Note that time waited is in microseconds. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Monitoring: Get all wait class metrics", "api_call": "GET /database/monitoring/wait_class_metrics", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all records from GV$WAITCLASSMETRIC for all GV$SYSTEM_WAIT_CLASS records except 'Idle' wait class. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get all database objects", "api_call": "GET /database/objects/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all records from DBA_OBJECTS or ALL_OBJECTS depending on the role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get a specific object", "api_call": "GET /database/objects/{owner},{object_name},{object_type}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the index.", "object_name": "[REQUIRED] string: Name of the object.", "object_type": "[REQUIRED] string: Type of the object."}, "functionality": "Returns the corresponding record from DBA_OBJECTS or ALL_OBJECTS view depending on the role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get all arguments for functions and procedures", "api_call": "GET /database/objects/arguments/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all records from DBA_ARGUMENTS or ALL_ARGUMENTS view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get an argument", "api_call": "GET /database/objects/arguments/{object_id},{subprogram_id},{argument_name}", "api_version": "2023.12.17", "api_arguments": {"object_id": "[REQUIRED] number: Identifier of the object the argument belongs to.", "subprogram_id": "[REQUIRED] number: Identifier of the subprogram the argument belongs to.", "argument_name": "[REQUIRED] string: Name of the argument."}, "functionality": "Returns the specified argument information from DBA_ARGUMENTS or ALL_ARGUMENTS view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get all table columns", "api_call": "GET /database/objects/columns/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all records from DBA_TAB_COLUMNS or ALL_TAB_COLUMNS view depending on role at runtime. Records included in the response describes the columns of all tables, views, and clusters that the role has access to. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get a specific column", "api_call": "GET /database/objects/columns/{owner},{table_name},{column_name}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the index.", "table_name": "[REQUIRED] string: Name of the table the column belongs to.", "column_name": "[REQUIRED] string: Name of the column."}, "functionality": "Returns the corresponding record from DBA_TAB_COLUMNS or ALL_TAB_COLUMNS view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get all foreign keys", "api_call": "GET /database/objects/foreign_keys/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all records where the constraint type is 'R' from DBA_CONSTRAINTS and DBA_CONS_COLUMNS, or ALL_CONSTRAINTS and ALL_CONS_COLUMNS, views depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get a specific foreign keys", "api_call": "GET /database/objects/foreign_keys/{owner},{constraint_name}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the foreign key.", "constraint_name": "[REQUIRED] string: Name of the constraint for the foreign key."}, "functionality": "Returns the corresponding record from DBA_CONSTRAINTS and DBA_CONS_COLUMNS, or ALL_CONSTRAINTS and ALL_CONS_COLUMNS, views depending on role at runtime. Recorded included in the response are where the constraint type is 'R'. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get all functions", "api_call": "GET /database/objects/functions/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns records where OBJECT_TYPE = 'FUNCTION' from DBA_PROCEDURES or ALL_PROCEDURES view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get a specific function", "api_call": "GET /database/objects/functions/{object_id}", "api_version": "2023.12.17", "api_arguments": {"object_id": "[REQUIRED] string: Object Identifier for the function."}, "functionality": "Returns the corresponding record from DBA_PROCEDURES or ALL_PROCEDURES view depending on role at runtime. Records included in the response are where OBJECT_TYPE = 'FUNCTION'. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get all indexes", "api_call": "GET /database/objects/indexes/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes all the indexes in the database using DBA_INDEXES or ALL_INDEXES view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get a specific index", "api_call": "GET /database/objects/indexes/{owner},{index_name}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the index.", "index_name": "[REQUIRED] string: Name of the index."}, "functionality": "Returns the corresponding record from DBA_INDEXES or ALL_INDEXES view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get all materialized view logs", "api_call": "GET /database/objects/materialized_view_logs/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all records from DBA_MVIEW_LOGS or ALL_MVIEW_LOGS view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get information on a specific materialized view log", "api_call": "GET /database/objects/materialized_view_logs/{log_owner},{log_table}", "api_version": "2023.12.17", "api_arguments": {"log_owner": "[REQUIRED] string: Owner for the materialized view log.", "log_table": "[REQUIRED] string: Log table for the materialized view log."}, "functionality": "Returns the corresponding record from DBA_MVIEW_LOGS or ALL_MVIEW_LOGS view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get all procedures defined in package", "api_call": "GET /database/objects/packages/procedures/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all records from DBA_PROCEDURES or ALL_PROCEDURES view depending on role at runtime. Records included in the response are where OBJECT_TYPE = 'PACKAGE' and PROCEDURE_NAME is not null. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get a procedure that is defined in package", "api_call": "GET /database/objects/packages/procedures/{object_id},{subprogram_id}", "api_version": "2023.12.17", "api_arguments": {"object_id": "[REQUIRED] number: Object identifier for the package the procedure belongs to.", "subprogram_id": "[REQUIRED] number: Subprogram identifier for the procedure."}, "functionality": "Returns the corresponding record from DBA_PROCEDURES or ALL_PROCEDURES view depending on role at runtime. Records included in the response are where OBJECT_TYPE = 'PACKAGE' and PROCEDURE_NAME is not null. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get all partitions in the database", "api_call": "GET /database/objects/partitions/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all records from DBA_TAB_PARTITIONS or ALL_TAB_PARTITIONS view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get a partition", "api_call": "GET /database/objects/partitions/{table_owner},{table_name},{partition_name}", "api_version": "2023.12.17", "api_arguments": {"table_owner": "[REQUIRED] string: Owner for the index.", "table_name": "[REQUIRED] string: Name of the table.", "partition_name": "[REQUIRED] string: Name of the partition."}, "functionality": "Returns the specified partition information from DBA_TAB_PARTITIONS or ALL_TAB_PARTITIONS view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get all synonyms in the database", "api_call": "GET /database/objects/synonyms/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all records on DBA_SYNONYMS or ALL_SYNONYMS view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get a synonym", "api_call": "GET /database/objects/synonyms/{owner},{synonym_name}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner of the synonym.", "synonym_name": "[REQUIRED] string: Name of the synonym."}, "functionality": "Returns a record from DBA_SYNONYMS or ALL_SYNONYMS view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get all tables", "api_call": "GET /database/objects/tables/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes all the tables in the database using DBA_TABLES or ALL_TABLES view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get a specific table", "api_call": "GET /database/objects/tables/{owner},{table_name}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner of the table.", "table_name": "[REQUIRED] string: Name of the table."}, "functionality": "Describes a specific table in the database using DBA_TABLES or ALL_TABLES view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get all object types", "api_call": "GET /database/objects/types/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes all object types in the database. Uses DBA_TYPES or ALL_TYPES view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Dictionary: Get an object type", "api_call": "GET /database/objects/types/{owner},{type_name}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner of the type.", "type_name": "[REQUIRED] string: Name of the type."}, "functionality": "Describes a specific object type in the database. Uses DBA_TYPES or ALL_TYPES view depending on role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get all database parameters", "api_call": "GET /database/parameters/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Return records from GV$SYSTEM_PARAMETER. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get a specific database parameter", "api_call": "GET /database/parameters/{name}", "api_version": "2023.12.17", "api_arguments": {"name": "[REQUIRED] string: Name of the parameter."}, "functionality": "Returns the corresponding record from GV$SYSTEM_PARAMETER. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Lifecycle Management: Get all pluggable databases", "api_call": "GET /database/pdbs/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Oracle Multitenant license is required when there is more than one user-created pluggable database. Returns records from GV$PDBS in the corresponding Container Database. This service requires db.cdb.adminUser credentials to be set in the pool configuration. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Lifecycle Management: Create a PDB from PDB$SEED or XML", "api_call": "POST /database/pdbs/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Create a pluggable database from PDB$SEED or from an XML metadata file accessible to the database instance. The use of Oracle Transparent Data Encryption is only supported in topologies where the database and Oracle REST Data Services are on the same host. This service requires db.cdb.adminUser credentials to be set in the pool configuration. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Lifecycle Management: Get a pluggable database", "api_call": "GET /database/pdbs/{pdb_name}/", "api_version": "2023.12.17", "api_arguments": {"pdb_name": "[REQUIRED] string: Name of the Pluggable Database."}, "functionality": "Returns data from GV$PDBS for the specified database in the corresponding Container Database. This service requires db.cdb.adminUser credentials to be set in the pool configuration. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Lifecycle Management: Clone or Unplug operations for PDBs", "api_call": "POST /database/pdbs/{pdb_name}/", "api_version": "2023.12.17", "api_arguments": {"pdb_name": "[REQUIRED] string: Name of the Pluggable Database to clone from or unplug."}, "functionality": "Clone or Unplug a PDB. These operations have a significant impact on the specified pluggable databases. The unplug operation will delete the PDB. Note that when cloning a pluggable database, the state of the source database will temporarily be set to OPEN READ ONLY for the duration of the operation and reset to its original state. The use of of Oracle Transparent Data Encryption is only supported in topologies where the database and Oracle REST Data Services are on the same host. This service requires db.cdb.adminUser credentials to be set in the pool configuration. ORA- errors need to be reviewed by the user. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Lifecycle Management: Drop a PDB", "api_call": "DELETE /database/pdbs/{pdb_name}/", "api_version": "2023.12.17", "api_arguments": {"pdb_name": "[REQUIRED] string: Name of the Pluggable Database.", "action": "string: Specify if datafiles should be removed or not. The value can be INCLUDING or KEEP (default).", "getScript": "boolean: If defined, the response will contain a JSON object with the information of the script that was generated for execution. Script execution does not happen and the database is not dropped."}, "functionality": "Drop a pluggable database. This service requires db.cdb.adminUser credentials to be set in the pool configuration. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Lifecycle Management: Return status information of a PDB", "api_call": "GET /database/pdbs/{pdb_name}/status", "api_version": "2023.12.17", "api_arguments": {"pdb_name": "[REQUIRED] string: Name of the Pluggable Database."}, "functionality": "Returns status data from GV$PDBS for the specified database in the corresponding Container Database. This service requires db.cdb.adminUser credentials to be set in the pool configuration. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Lifecycle Management: Change the state of a PDB", "api_call": "POST /database/pdbs/{pdb_name}/status", "api_version": "2023.12.17", "api_arguments": {"pdb_name": "[REQUIRED] string: Name of the Pluggable Database."}, "functionality": "Changes the state of a pluggable database. The executed ALTER PLUGGABLE DATABASE command in the database includes the INSTANCES=ALL clause so that in an Oracle Real Application Clusters environment the pluggable database state is changed in all instances. This service requires db.cdb.adminUser credentials to be set in the pool configuration. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Lifecycle Management: The snapshots of all pluggable databases (PDBs) in the Container Database", "api_call": "GET /database/pdb_snapshots/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns records from dba_pdb_snapshots. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Lifecycle Management: The list of all snapshots taken for thee specified pluggable databases (PDBs)", "api_call": "GET /database/pdb_snapshots/{pdb_name}/", "api_version": "2023.12.17", "api_arguments": {"pdb_name": "[REQUIRED] string: The name of the specified PDB to retrieve the list of snapshots for."}, "functionality": "Returns records from dba_pdb_snapshots based on container name. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Lifecycle Management: The specific snapshot taken of the specific pluggable databases (PDBs)", "api_call": "GET /database/pdb_snapshots/{pdb_name}/{snapshot_name}", "api_version": "2023.12.17", "api_arguments": {"pdb_name": "[REQUIRED] string: The name of the specified PDB to retrieve the snapshot for.", "snapshot_name": "[REQUIRED] string: The name of the snapshot to retrieve."}, "functionality": "Returns records from dba_pdb_snapshots based on container name and snapshot name. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get all sampled session activity in the database", "api_call": "GET /database/performance/active_sessions_history/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Oracle Diagnostics Pack licence is required for this service. Returns all records from GV$ACTIVE_SESSION_HISTORY. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get a specified sampled session activity in the database", "api_call": "GET /database/performance/active_sessions_history/{sample_id},{session_id}", "api_version": "2023.12.17", "api_arguments": {"sample_id": "[REQUIRED] number: ID of the sample.", "session_id": "[REQUIRED] number: Session identifier for the sampled session."}, "functionality": "Oracle Diagnostics Pack licence is required for this service. Returns specified records from GV$ACTIVE_SESSION_HISTORY. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get the last 10 wait events for each active session", "api_call": "GET /database/performance/active_sessions_history_waits/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Return records from GV$SESSION_WAIT_HISTORY. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get the specified wait event for specific active session", "api_call": "GET /database/performance/active_sessions_history_waits/{sid},{seq_number}/", "api_version": "2023.12.17", "api_arguments": {"sid": "[REQUIRED] number: Session Identifier.", "seq_number": "[REQUIRED] number: Sequence number of wait event; 1 is the most recent."}, "functionality": "Return data from GV$SESSION_WAIT_HISTORY for the specified Session Identifier and Event Sequence Number. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get SQL statistics for a specific session", "api_call": "GET /database/performance/active_sessions_history_waits/{sid},{seq_number}/active_sql", "api_version": "2023.12.17", "api_arguments": {"sid": "[REQUIRED] number: Session Identifier.", "seq_number": "[REQUIRED] number: Sequence number of wait event; 1 is the most recent."}, "functionality": "Return records from GV$SQLAREA for a given session with current waits on GV$SESSION_WAIT_HISTORY. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get statistics for all SQL statements", "api_call": "GET /database/performance/sql_statements/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Oracle Tuning Pack licence is required for this service. Returns data from GV$SQL with GV$SQL_MONITOR timing data. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get statistics for a SQL statement", "api_call": "GET /database/performance/sql_statements/{sql_id}/", "api_version": "2023.12.17", "api_arguments": {"sql_id": "[REQUIRED] string: SQL identifier."}, "functionality": "Oracle Tuning Pack licence is required for this service. Returns data from GV$SQL with GV$SQL_MONITOR timing data for the specified SQL statement identifier. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get monitoring statistics for a SQL Statement", "api_call": "GET /database/performance/sql_statements/{sql_id}/monitor/", "api_version": "2023.12.17", "api_arguments": {"sql_id": "[REQUIRED] string: SQL identifier."}, "functionality": "Oracle Tuning Pack licence is required for this service. Returns all GV$SQL_MONITOR records for a specified SQL_ID. GV$SQL_MONITOR will contain statistics only for SQL statements whose execution have been (or are being) monitored by Oracle. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get information on a monitored statement", "api_call": "GET /database/performance/sql_statements/{sql_id}/monitor/{sql_exec_id},{sql_exec_start}/", "api_version": "2023.12.17", "api_arguments": {"sql_id": "[REQUIRED] string: SQL identifier.", "sql_exec_id": "[REQUIRED] number: Execution identifier.", "sql_exec_start": "[REQUIRED] string: Time when the execution started."}, "functionality": "Oracle Tuning Pack licence is required for this service. Returns information on a monitored statement from GV$SQL_MONITOR view. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get parallel execution information for specific statement execution", "api_call": "GET /database/performance/sql_statements/{sql_id}/monitor/{sql_exec_id},{sql_exec_start}/parallelism", "api_version": "2023.12.17", "api_arguments": {"sql_id": "[REQUIRED] string: SQL identifier.", "sql_exec_id": "[REQUIRED] number: Execution identifier.", "sql_exec_start": "[REQUIRED] string: Time when the execution started."}, "functionality": "Oracle Tuning Pack licence is required for this service. Returns information on a monitored statement that is executed in parallel. Using GV$SQL_MONITOR view, the information includes the Parallel Coordinator and the instance(s) where it was executed. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get execution plan for an SQL Statement", "api_call": "GET /database/performance/sql_statements/{sql_id}/plan", "api_version": "2023.12.17", "api_arguments": {"sql_id": "[REQUIRED] string: SQL identifier."}, "functionality": "Returns all GV$SQL_PLAN records for a specified SQL_ID. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get historical statistics for a SQL statement", "api_call": "GET /database/performance/sql_statements/{sql_id}/history", "api_version": "2023.12.17", "api_arguments": {"sql_id": "[REQUIRED] string: SQL identifier."}, "functionality": "Oracle Diagnostics Pack licence is required for this service. Returns DBA_HIST_SQLSTAT and DBA_HIST_SNAPSHOT records for a specified SQL_ID. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get SQL statement", "api_call": "GET /database/performance/sql_statements/{sql_id}/text", "api_version": "2023.12.17", "api_arguments": {"sql_id": "[REQUIRED] string: SQL identifier."}, "functionality": "Returns records from GV$SQL for specified SQL_ID. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get all SQL statements ordered by CPU time descending", "api_call": "GET /database/performance/top_sql_statements/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns records from GV$SQL ordered by CPU time descending, SQL Text ascending. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Performance: Get SQL statistics maximum values", "api_call": "GET /database/performance/top_sql_statements/maximums", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns maximum values for cpu time, elapsed time, disk reads, buffer gets and executions from GV$SQL. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: List RDF networks", "api_call": "GET /database/rdf/networks/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns a collection of RDF networks visible to the current user. A client requires SQL Developer, RDF Developer or RDF Reader role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: RDF network information", "api_call": "GET /database/rdf/networks/{network_owner},{network_name}", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns metadata describing the specified network. The exact attributes returned may vary depending on the Database version. A client requires SQL Developer, RDF Developer or RDF Reader role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Create RDF network", "api_call": "PUT /database/rdf/networks/{network_owner},{network_name}", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Creates an RDF network. A client requires SQL Developer or RDF Developer role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Drop RDF network", "api_call": "DELETE /database/rdf/networks/{network_owner},{network_name}", "api_version": "2023.12.17", "api_arguments": {"cascade": "string: true to drop all data in the RDF network."}, "functionality": "Drops an RDF network. A client requires SQL Developer or RDF Developer role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: List RDF models", "api_call": "GET /database/rdf/networks/{network_owner},{network_name}/models/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns metadata describing all RDF models in the network. The exact attributes returned may vary depending on the Database version. A client requires SQL Developer, RDF Developer or RDF Reader role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: RDF model information", "api_call": "GET /database/rdf/networks/{network_owner},{network_name}/models/{model_name}", "api_version": "2023.12.17", "api_arguments": {"model_name": "[REQUIRED] string: The name of the RDF model."}, "functionality": "Returns metadata describing the specified RDF model. The exact attributes returned may vary depending on the Database version. A client requires SQL Developer, RDF Developer or RDF Reader role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Create RDF model", "api_call": "PUT /database/rdf/networks/{network_owner},{network_name}/models/{model_name}", "api_version": "2023.12.17", "api_arguments": {"model_name": "[REQUIRED] string: The name of the RDF model."}, "functionality": "Creates an RDF model. A client requires SQL Developer or RDF Developer role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Drop RDF model", "api_call": "DELETE /database/rdf/networks/{network_owner},{network_name}/models/{model_name}", "api_version": "2023.12.17", "api_arguments": {"model_name": "[REQUIRED] string: The name of the RDF model."}, "functionality": "Drops an RDF model. A client requires SQL Developer or RDF Developer role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: List entailments", "api_call": "GET /database/rdf/networks/{network_owner},{network_name}/entailments/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns metadata describing all entailments in the network. The exact attributes returned may vary depending on the Database version. A client requires SQL Developer, RDF Developer or RDF Reader role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Entailment information", "api_call": "GET /database/rdf/networks/{network_owner},{network_name}/entailments/{entailment_name}", "api_version": "2023.12.17", "api_arguments": {"entailment_name": "[REQUIRED] string: The name of the entailment."}, "functionality": "Returns metadata describing the specified entailment. The exact attributes returned may vary depending on the Database version. A client requires SQL Developer, RDF Developer or RDF Reader role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Create entailment", "api_call": "PUT /database/rdf/networks/{network_owner},{network_name}/entailments/{entailment_name}", "api_version": "2023.12.17", "api_arguments": {"entailment_name": "[REQUIRED] string: The name of the entailment."}, "functionality": "Creates an entailment. A client requires SQL Developer or RDF Developer role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Drop entailment", "api_call": "DELETE /database/rdf/networks/{network_owner},{network_name}/entailments/{entailment_name}", "api_version": "2023.12.17", "api_arguments": {"entailment_name": "[REQUIRED] string: The name of the entailment."}, "functionality": "Drops an entailment. A client requires SQL Developer or RDF Developer role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: List virtual models", "api_call": "GET /database/rdf/networks/{network_owner},{network_name}/virtual_models/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns metadata describing all virtual models in the network. The exact attributes returned may vary depending on the Database version. A client requires SQL Developer, RDF Developer or RDF Reader role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Virtual model information", "api_call": "GET /database/rdf/networks/{network_owner},{network_name}/virtual_models/{virtual_model_name}", "api_version": "2023.12.17", "api_arguments": {"virtual_model_name": "[REQUIRED] string: The name of the virtual model."}, "functionality": "Returns metadata describing the specified virtual model. The exact attributes returned may vary depending on the Database version. A client requires SQL Developer, RDF Developer or RDF Reader role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Create virtual model", "api_call": "PUT /database/rdf/networks/{network_owner},{network_name}/virtual_models/{virtual_model_name}", "api_version": "2023.12.17", "api_arguments": {"virtual_model_name": "[REQUIRED] string: The name of the virtual model."}, "functionality": "Creates a virtual model. A client requires SQL Developer or RDF Developer role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Drop virtual model", "api_call": "DELETE /database/rdf/networks/{network_owner},{network_name}/virtual_models/{virtual_model_name}", "api_version": "2023.12.17", "api_arguments": {"virtual_model_name": "[REQUIRED] string: The name of the virtual model."}, "functionality": "Drops a virtual model. A client requires SQL Developer or RDF Developer role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Execute a SPARQL query", "api_call": "GET /database/rdf/networks/{network_owner},{network_name}/models/{model_name}/sparql/1.1", "api_version": "2023.12.17", "api_arguments": {"query": "[REQUIRED] string: A URL-encoded SPARQL query string."}, "functionality": "Executes a SPARQL query and returns the result in JSON format (application/sparql-results+json) or XML format (application/sparql-results+xml) for SPARQL SELECT and ASK queries, or N-Triples format (application/n-triples) for CONSTRUCT and DESCRIBE queries. A client requires SQL Developer, RDF Developer or RDF Reader role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Execute a SPARQL query or update", "api_call": "POST /database/rdf/networks/{network_owner},{network_name}/models/{model_name}/sparql/1.1", "api_version": "2023.12.17", "api_arguments": {"using-graph-uri": "string: A URL-encoded IRI identifying a named graph that should be included in the DEFAULT graph of the update dataset.", "using-named-graph-uri": "string: A URL-encoded IRI identifying a named graph that should be included in the set of named graphs for the update dataset.", "match_model": "string: The name of the model to evaluate the WHERE portion of a SPARQL update against. The default is the value of the modelName path parameter.", "match_options": "string: The options string to use when evaluating the WHERE portion of a SPARQL update."}, "functionality": "Executes a SPARQL update or executes a SPARQL query and returns the result in JSON format (application/sparql-results+json) or XML format (application/sparql-results+xml) for SPARQL SELECT and ASK queries, or N-Triples format (application/n-triples) for CONSTRUCT and DESCRIBE queries. A client requires SQL Developer or RDF Developer role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Translate SPARQL to SQL", "api_call": "GET /database/rdf/networks/{network_owner},{network_name}/models/{model_name}/sparql2sql/1.1", "api_version": "2023.12.17", "api_arguments": {"query": "[REQUIRED] string: A URL-encoded SPARQL query string."}, "functionality": "Returns the SQL translation for a SPARQL query. A client requires SQL Developer, RDF Developer or RDF Reader role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Translate SPARQL to SQL", "api_call": "POST /database/rdf/networks/{network_owner},{network_name}/models/{model_name}/sparql2sql/1.1", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns the SQL translation for a SPARQL query. A client requires SQL Developer or RDF Developer role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Get a graph", "api_call": "GET /database/rdf/networks/{network_owner},{network_name}/models/{model_name}/graph_store/1.1", "api_version": "2023.12.17", "api_arguments": {"model_name": "[REQUIRED] string: The name of the RDF model.", "graph": "string: A URL-encoded named graph URI", "default": "string: An empty-valued parameter that signifies the default graph."}, "functionality": "Retrieves an RDF payload that is the serialization of the requested named graph or default graph. A client requires SQL Developer, RDF Developer or RDF Reader role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Append a graph", "api_call": "POST /database/rdf/networks/{network_owner},{network_name}/models/{model_name}/graph_store/1.1", "api_version": "2023.12.17", "api_arguments": {"model_name": "[REQUIRED] string: The name of the RDF model.", "graph": "string: A URL-encoded named graph URI", "default": "string: An empty-valued parameter that signifies the default graph."}, "functionality": "Appends the RDF content in the request payload to the target named graph or default graph. A client requires SQL Developer or RDF Developer role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Replace a graph", "api_call": "PUT /database/rdf/networks/{network_owner},{network_name}/models/{model_name}/graph_store/1.1", "api_version": "2023.12.17", "api_arguments": {"model_name": "[REQUIRED] string: The name of the RDF model.", "graph": "string: A URL-encoded named graph URI", "default": "string: An empty-valued parameter that signifies the default graph."}, "functionality": "Deletes all triples in the target named graph or defaut graph and replaces its content with the RDF content in the request payload. A client requires SQL Developer or RDF Developer role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - RDF Graph: Delete a graph", "api_call": "DELETE /database/rdf/networks/{network_owner},{network_name}/models/{model_name}/graph_store/1.1", "api_version": "2023.12.17", "api_arguments": {"model_name": "[REQUIRED] string: The name of the RDF model.", "graph": "string: A URL-encoded named graph URI", "default": "string: An empty-valued parameter that signifies the default graph."}, "functionality": "Deletes all triples in the target named graph or defaut graph. A client requires SQL Developer or RDF Developer role to invoke this servce.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get all database users", "api_call": "GET /database/security/users/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns records from DBA_USERS or ALL_USERS view depending on the role at runtime.  A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get a specific database user", "api_call": "GET /database/security/users/{username}", "api_version": "2023.12.17", "api_arguments": {"username": "[REQUIRED] string: Identifier for the user."}, "functionality": "Returns the corresponding record from DBA_USERS or ALL_USERS view depending on the role at runtime. A client requires SQL Administrator or SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get database status", "api_call": "GET /database/status", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns records from GV$INSTANCE and GV$DATABASE. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get storage statistics", "api_call": "GET /database/storage/bytes", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns info on bytes allocated for storage and how much is used. Using DBA_DATA_FILES data to calculate the total bytes allocated for storage along with DBA_TABLESPACE_USAGE_METRICS and DBA_TABLESPACES data to calculate the used bytes. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get all tablespaces", "api_call": "GET /database/storage/tablespaces/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns records from DBA_TABLESPACES along with data usage information from DBA_TABLESPACE_USAGE_METRICS. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get a tablespace", "api_call": "GET /database/storage/tablespaces/{tablespace_name}/", "api_version": "2023.12.17", "api_arguments": {"tablespace_name": "[REQUIRED] string: Name of the tablespace."}, "functionality": "Returns data from DBA_TABLESPACES along with data usage information from DBA_TABLESPACE_USAGE_METRICS for a specific tablespace. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get data file usage summary for a specific tablespace", "api_call": "GET /database/storage/tablespaces/{tablespace_name}/datafiles_usage", "api_version": "2023.12.17", "api_arguments": {"tablespace_name": "[REQUIRED] string: Name of the tablespace."}, "functionality": "Returns GV$DATAFILE and DBA_DATA_FILES records for a specific tablespace. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get data files for a tablespace", "api_call": "GET /database/storage/tablespaces/{tablespace_name}/datafiles/", "api_version": "2023.12.17", "api_arguments": {"tablespace_name": "[REQUIRED] string: Name of the tablespace."}, "functionality": "Returns records from DBA_DATA_FILES for a specific tablespace. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get tablespace data file", "api_call": "GET /database/storage/tablespaces/{tablespace_name}/datafiles/{file_id}/", "api_version": "2023.12.17", "api_arguments": {"tablespace_name": "[REQUIRED] string: Name of the tablespace.", "file_id": "[REQUIRED] string: File identifier."}, "functionality": "Returns data from DBA_DATA_FILES for a specific tablespace and file. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get tablespace data file usage", "api_call": "GET /database/storage/tablespaces/{tablespace_name}/datafiles/{file_id}/usage", "api_version": "2023.12.17", "api_arguments": {"tablespace_name": "[REQUIRED] string: Name of the tablespace.", "file_id": "[REQUIRED] string: File identifier."}, "functionality": "Returns GV$DATAFILE and DBA_DATA_FILES records for a specific tablespace and file. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get tablespace space usage history", "api_call": "GET /database/storage/tablespaces/{tablespace_name}/history", "api_version": "2023.12.17", "api_arguments": {"tablespace_name": "[REQUIRED] string: Name of the tablespace."}, "functionality": "Oracle Diagnostic Pack licence is required for this service. Returns DBA_HIST_TBSPC_SPACE_USAGE records where tablespace size has changed. These records are historical tablespace usage statistics. Recent changes in tablespace storage or usage will not appear until a snapshot is created. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get tablespace segments", "api_call": "GET /database/storage/tablespaces/{tablespace_name}/segments/", "api_version": "2023.12.17", "api_arguments": {"tablespace_name": "[REQUIRED] string: Name of the tablespace."}, "functionality": "Returns records from DBA_SEGMENTS for a specific tablespace. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get a tablespace segment", "api_call": "GET /database/storage/tablespaces/{tablespace_name}/segments/{segment_name}", "api_version": "2023.12.17", "api_arguments": {"tablespace_name": "[REQUIRED] string: Name of the tablespace.", "segment_name": "[REQUIRED] string: Name of the segment."}, "functionality": "Returns data from DBA_SEGMENTS for a specific tablespace and segment. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get temporary file usage summary for a specific tablespace", "api_call": "GET /database/storage/tablespaces/{tablespace_name}/tempfiles_usage", "api_version": "2023.12.17", "api_arguments": {"tablespace_name": "[REQUIRED] string: Name of the tablespace."}, "functionality": "Returns records from GV$TEMPFILE and DBA_TEMP_FILES for a specific tablespace. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get temporary files for a tablespace", "api_call": "GET /database/storage/tablespaces/{tablespace_name}/tempfiles/", "api_version": "2023.12.17", "api_arguments": {"tablespace_name": "[REQUIRED] string: Name of the tablespace."}, "functionality": "Returns records from DBA_TEMP_FILES for a specific tablespace. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get tablespace temporary file", "api_call": "GET /database/storage/tablespaces/{tablespace_name}/tempfiles/{file_id}/", "api_version": "2023.12.17", "api_arguments": {"tablespace_name": "[REQUIRED] string: Name of the tablespace.", "file_id": "[REQUIRED] string: File identifier."}, "functionality": "Returns data from DBA_TEMP_FILES for a specific tablespace and file. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get tablespace temporary file usage", "api_call": "GET /database/storage/tablespaces/{tablespace_name}/tempfiles/{file_id}/usage", "api_version": "2023.12.17", "api_arguments": {"tablespace_name": "[REQUIRED] string: Name of the tablespace.", "file_id": "[REQUIRED] string: File identifier."}, "functionality": "Returns GV$DATAFILE and DBA_TEMP_FILES records for a specific tablespace and file. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Get all Transactional Event Queue Topics in the schema", "api_call": "GET /database/txeventq/topics", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "This API is compiant witht the Confluent Kafka API. Get a list of Transactional Event Queue Topics defined. A client requires SQL Developer role to invoke this service. Database user requires EXECUTE on DBMS_AQADM and OWA_UTIL packages.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Produce a message to the Transactional Event Queue Topic specified by topic_name", "api_call": "POST /database/txeventq/topics/{topic_name}", "api_version": "2023.12.17", "api_arguments": {"topic_name": "[REQUIRED] string"}, "functionality": "Produce a message to the topic specified by topic_name with a key and value. Optionally, specify a partition to produce the message in. If no partition is specified one is automatically chosen. A client requires SQL Developer role to invoke this service. Database user requires Execute privilege on DBMS_AQ, DBMS_AQADM and OWA_UTIL packages. Topic must belong to Database User's schema.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Get description of a particular Transactional Event Queue Topic.", "api_call": "GET /database/txeventq/topics/{topic_name}", "api_version": "2023.12.17", "api_arguments": {"topic_name": "[REQUIRED] string"}, "functionality": "Get description of a particular topic. A client requires SQL Developer role to invoke this service. Database user requires READ privilege on USER_QUEUE_SHARDS.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Get Consumer Groups in Cluster", "api_call": "GET /database/txeventq/clusters/{cluster_id}/consumer-groups", "api_version": "2023.12.17", "api_arguments": {"cluster_id": "[REQUIRED] string"}, "functionality": "Returns the list of consumer groups that belong to the specified cluster. A client requires SQL Developer role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Create a Consumer Group", "api_call": "POST /database/txeventq/clusters/{cluster_id}/consumer-groups/{consumer_group_id}", "api_version": "2023.12.17", "api_arguments": {"cluster_id": "[REQUIRED] string", "consumer_group_id": "[REQUIRED] string", "Topic": "[REQUIRED] string"}, "functionality": "Create a new consumer consumer group in the specified Cluster Id. A client requires SQL Developer role to invoke this service. Database user requires Execute privilege on DBMS_AQADM and OWA_UTIL packages and the Topic must be created in the Database User's' schema.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Get a Consumer Group", "api_call": "GET /database/txeventq/clusters/{cluster_id}/consumer-groups/{consumer_group_id}", "api_version": "2023.12.17", "api_arguments": {"cluster_id": "[REQUIRED] string", "consumer_group_id": "[REQUIRED] string"}, "functionality": "Get the consumer group specified by the consumer_group_id.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Delete a Consumer Group", "api_call": "DELETE /database/txeventq/clusters/{cluster_id}/consumer-groups/{consumer_group_id}", "api_version": "2023.12.17", "api_arguments": {"cluster_id": "[REQUIRED] string", "consumer_group_id": "[REQUIRED] string", "Topic": "string"}, "functionality": "Delete consumer consumer group in the specified Cluster Id. A client requires SQL Developer role to invoke this service. Database User requires Execute privilege on DBMS_AQADM and OWA_UTIL packages and the Topic must have been created in Database User's schema.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Get lag summary for Consumer Groups in Cluster", "api_call": "GET /database/txeventq/clusters/{cluster_id}/consumer-groups/{consumer_group_id}/lag-summary", "api_version": "2023.12.17", "api_arguments": {"cluster_id": "[REQUIRED] string", "consumer_group_id": "[REQUIRED] string"}, "functionality": "Gets the max and total lag of the consumers in the specified consumer group. A client requires SQL Developer role to invoke this service. Database User requires EXECUTE privilege on DBMS_AQADM and OWA_UTIL packages.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Lags/Offsets of consumers in the Consumer group ", "api_call": "GET /database/txeventq/clusters/{cluster_id}/consumer-groups/{consumer_group_id}/lags", "api_version": "2023.12.17", "api_arguments": {"cluster_id": "[REQUIRED] string", "consumer_group_id": "[REQUIRED] string"}, "functionality": "Gets a list of Current/Log-End Offsets and Lag of consumers belonging to the specified consumer group. A client requires SQL Developer role to invoke this service. Database User requires EXECUTE privilege on DBMS_AQADM and OWA_UTIL packages.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Create a new Transactional Event Queue Topic", "api_call": "POST /database/txeventq/clusters/{cluster_id}/topics", "api_version": "2023.12.17", "api_arguments": {"cluster_id": "[REQUIRED] string", "topic_name": "[REQUIRED] string", "partitions_count": "[REQUIRED] integer"}, "functionality": "Create a new Topic. A client requires SQL Developer role to invoke this service. Database User requires Execute privilege on DBMS_AQADM and OWA_UTIL packages as well as Resource and Unlimited Tablespace privileges.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: List Transactional Event Queue Topics that belong to the specified database cluster", "api_call": "GET /database/txeventq/clusters/{cluster_id}/topics", "api_version": "2023.12.17", "api_arguments": {"cluster_id": "[REQUIRED] string"}, "functionality": "List topics that belong to the specified database cluster. A client requires SQL Developer role to invoke this service. Database User requires Execute privilege on DBMS_AQADM and OWA_UTIL packages and READ privilege on USER_QUEUE_SHARDS.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Delete the Transactional Event Queue Topic with the specified topic_name", "api_call": "DELETE /database/txeventq/clusters/{cluster_id}/topics/{topic_name}", "api_version": "2023.12.17", "api_arguments": {"cluster_id": "[REQUIRED] string", "topic_name": "[REQUIRED] string"}, "functionality": "Delete the topic with the specified topic_name. A client requires SQL Developer role to invoke this service. Database User requires Execute privilege on DBMS_AQADM and OWA_UTIL packages and the Topic must have been created in the database user's schema.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Describes Transactional Event Queue Topic in Cluster", "api_call": "GET /database/txeventq/clusters/{cluster_id}/topics/{topic_name}", "api_version": "2023.12.17", "api_arguments": {"cluster_id": "[REQUIRED] string", "topic_name": "[REQUIRED] string"}, "functionality": "Get a full description of the topic specified by the topic_name. A client requires SQL Developer role to invoke this service. Database User requires to have READ privilege on USER_QUEUE_SHARDS.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: List Partitions for a Transactional Event Queue Topic in Cluster", "api_call": "GET /database/txeventq/clusters/{cluster_id}/topics/{topic_name}/partitions", "api_version": "2023.12.17", "api_arguments": {"cluster_id": "[REQUIRED] string", "topic_name": "[REQUIRED] string"}, "functionality": "Get all the partitions that belong to a specified topic. A client requires SQL Developer role to invoke this service. Database User requires READ privilege on USER_QUEUE_SHARDS.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Describe Transactional Event Queue Topic Partition specified in Cluster", "api_call": "GET /database/txeventq/clusters/{cluster_id}/topics/{topic_name}/partitions/{partition_id}", "api_version": "2023.12.17", "api_arguments": {"cluster_id": "[REQUIRED] string", "partition_id": "[REQUIRED] string", "topic_name": "[REQUIRED] string"}, "functionality": "Get the partition with a specified partition_id for the specified Topic in cluster. A client requires SQL Developer role to invoke this service. Database User requires READ privilege on USER_QUEUE_SHARDS.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Create Consumer Instance", "api_call": "POST /database/txeventq/consumers/{group_name}", "api_version": "2023.12.17", "api_arguments": {"group_name": "[REQUIRED] string"}, "functionality": "Create a new consumer instance in the consumer group. Database user requires SQL Developer role and EXECUTE privilege on OWA_UTIL package to invoke this service. The database user also requires SELECT privilege on V_$SESSION to perform this operation.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Delete Consumer Instance", "api_call": "DELETE /database/txeventq/consumers/{group_name}/instances/{instance}", "api_version": "2023.12.17", "api_arguments": {"group_name": "[REQUIRED] string", "instance": "[REQUIRED] string"}, "functionality": "Delete the consumer instance. A client requires SQL Developer role to invoke this service. Also EXECUTE privilege on OWA_UTIL package.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Get consumer lag for Transactional Event Queue Topic Partition", "api_call": "GET /database/txeventq/clusters/{cluster_id}/consumer-groups/{consumer_group_id}/lags/{topic_name}/partitions/{partition_id}", "api_version": "2023.12.17", "api_arguments": {"cluster_id": "[REQUIRED] string", "consumer_group_id": "[REQUIRED] string", "partition_id": "[REQUIRED] string", "topic_name": "[REQUIRED] string"}, "functionality": "Get the consumer lag on a specified partition in a topic. A client requires SQL Developer role to invoke this service. Database User requires EXECUTE privilege on DBMS_AQADM and OWA_UTIL packages.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Get Consumer last Commit Offsets", "api_call": "GET /database/txeventq/consumers/{group_name}/instances/{instance}/offsets", "api_version": "2023.12.17", "api_arguments": {"group_name": "[REQUIRED] string", "instance": "[REQUIRED] string", "List of Topic and Partition": "[REQUIRED] string"}, "functionality": "Get the last committed offsets for the given partition. A client requires SQL Developer role to invoke this service. Database User requires EXECUTE privilege on DBMS_AQADM and OWA_UTIL packages.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Seek to specified Transactional Event Queue Topic partition offsets for Consumer Group", "api_call": "POST /database/txeventq/consumers/{group_name}/instances/{instance}/positions", "api_version": "2023.12.17", "api_arguments": {"group_name": "[REQUIRED] string", "instance": "[REQUIRED] string"}, "functionality": "Seek to the offsets for each of the Topic partition. A client requires SQL Developer role to invoke this service. Database User requires EXECUTE privilege on DBMS_AQ, DBMS_AQADM and OWA_UTIL packages. Also needs READ privilege on V$DATABASE and V$TRANSPORTABLE_PLATFORM.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Seek to start of Partitions for Consumer Group", "api_call": "POST /database/txeventq/consumers/{group_name}/instances/{instance}/positions/beginning", "api_version": "2023.12.17", "api_arguments": {"group_name": "[REQUIRED] string", "instance": "[REQUIRED] string"}, "functionality": "Seek to the beginning offset for each of the given partitions. A client requires SQL Developer role to invoke this service. Database User requires EXECUTE privilege on DBMS_AQ and OWA_UTIL packages.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Seek to End of Partitions for Consumer Group", "api_call": "POST /database/txeventq/consumers/{group_name}/instances/{instance}/positions/end", "api_version": "2023.12.17", "api_arguments": {"group_name": "[REQUIRED] string", "instance": "[REQUIRED] string"}, "functionality": "Seek to the End offset for each of the given partitions. A client requires SQL Developer role to invoke this service. Database User requires EXECUTE privilege on DBMS_AQ and OWA_UTIL packages.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Fetch messages for the specified Consumer", "api_call": "GET /database/txeventq/consumers/{group_name}/instances/{instance}/records", "api_version": "2023.12.17", "api_arguments": {"group_name": "[REQUIRED] string", "instance": "[REQUIRED] string", "Maximum Bytes of Records": "integer", "Timeout in Milli Seconds": "integer"}, "functionality": "Fetch messages for the specified Consumer in Consumer Group from Topics its Subscribed to. A client requires SQL Developer role to invoke this service. Database User requires EXECUTE privilege on DBMS_AQ, DBMS_AQADM and OWA_UTIL packages.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Subscribed list of Transactional Event Queue Topics", "api_call": "GET /database/txeventq/consumers/{group_name}/instances/{instance}/subscription", "api_version": "2023.12.17", "api_arguments": {"group_name": "[REQUIRED] string", "instance": "[REQUIRED] string"}, "functionality": "Get the current subscribed list of topics for the consumer group instance. A client requires SQL Developer role to invoke this service. Database User requires EXECUTE privilege on DBMS_AQADM and OWA_UTIL packagse.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Describe Transactional Event Queue Topic Partitions", "api_call": "GET /database/txeventq/topics/{topic_name}/partitions", "api_version": "2023.12.17", "api_arguments": {"topic_name": "[REQUIRED] string"}, "functionality": "Get a list of partitions for the specified topic_name. A client requires SQL Developer role to invoke this service. Database User requires READ privilege on USER_QUEUE_SHARDS.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Produce messages in the specified Transactional Event Queue Topic Partition", "api_call": "POST /database/txeventq/topics/{topic_name}/partitions/{partition_id}", "api_version": "2023.12.17", "api_arguments": {"partition_id": "[REQUIRED] string", "topic_name": "[REQUIRED] string"}, "functionality": "Produce messages to the specified partition of the topic. A client requires SQL Developer role to invoke this service. Database User requires EXECUTE privilege on DBMS_AQ, DBMS_AQADM and OWA_UTIL packages.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Describe the specified Transactional Event Queue Topic Partition", "api_call": "GET /database/txeventq/topics/{topic_name}/partitions/{partition_id}", "api_version": "2023.12.17", "api_arguments": {"partition_id": "[REQUIRED] string", "topic_name": "[REQUIRED] string"}, "functionality": "Get metadata details of a specified partition in the topic. A client requires SQL Developer role to invoke this service. Database User requires READ privilege on USER_QUEUE_SHARDS.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Oracle Transactional Event Queues: Get Transactional Event Queue Topic Partition Offsets", "api_call": "GET /database/txeventq/topics/{topic_name}/partitions/{partition_id}/offsets", "api_version": "2023.12.17", "api_arguments": {"partition_id": "[REQUIRED] string", "topic_name": "[REQUIRED] string"}, "functionality": "Get the first and last offset in the specified partition. A client requires SQL Developer role to invoke this service. Database User requires EXECUTE privilege on DBMS_AQADM and OWA_UTIL packages.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get database version", "api_call": "GET /database/version", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns records from GV$INSTANCE and GV$VERSION. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get a list of the databases defined in the default Oracle Home", "api_call": "GET /environment/databases/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns a description of all databases specified in the Oracle Home specified by $ORACLE_HOME environment variable. This Oracle Home is expected to be on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Create a new database in the default Oracle Home", "api_call": "POST /environment/databases/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Create a new database in the Oracle Home specified by $ORACLE_HOME environment variable. This Oracle Home is expected to be on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get a specific database defined in the default Oracle Home", "api_call": "GET /environment/databases/{databaseName}", "api_version": "2023.12.17", "api_arguments": {"databaseName": "[REQUIRED] string: Name of the database."}, "functionality": "Returns a description of the database specified in the default Oracle Home. The default Oracle Home is on the same host as the Oracle REST Data Services server instance and is specified using the $ORACLE_HOME environment variable. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get a list of the DBCA jobs created through ORDS Database API in the default Oracle Home", "api_call": "GET /environment/dbca/jobs/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns a description of all DBCA jobs created through ORDS Database API in the default Oracle Home. This Oracle Home is specified by $ORACLE_HOME environment variable and is expected to be on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Create a new DBCA job in the default Oracle Home", "api_call": "POST /environment/dbca/jobs/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Submit a new Database Configuration Assistant (DBCA) job request to CREATE or DELETE a database in the Oracle Home specified by $ORACLE_HOME environment variable. This Oracle Home is expected to be on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get a specified DBCA jobs created through ORDS Database API in the default Oracle Home", "api_call": "GET /environment/dbca/jobs/{jobId}/", "api_version": "2023.12.17", "api_arguments": {"jobId": "[REQUIRED] string: Identifier of the DBCA job."}, "functionality": "Returns a description of the DBCA job created through ORDS Database API in the default Oracle Home. The default Oracle Home is specified by $ORACLE_HOME environment variable and is expected to be on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Delete the specified DBCA job in the default Oracle Home", "api_call": "DELETE /environment/dbca/jobs/{jobId}/", "api_version": "2023.12.17", "api_arguments": {"jobId": "[REQUIRED] string: Identifier of the DBCA job."}, "functionality": "Delete the DBCA job in the Oracle Home specified by $ORACLE_HOME environment variable. This Oracle Home is expected to be on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Snapshot Carousel: get all snapshots of the current pluggable databases", "api_call": "GET /database/snapshots/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all snapshot record. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Snapshot Carousel: Create snapshot of the current pluggable databases, the name will be generated", "api_call": "POST /database/snapshots/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns created snapshot record. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Snapshot Carousel: Configure automatic PDB snapshots.", "api_call": "PUT /database/snapshots/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Configure max snapshot and snapshot mode for the current PDB.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Snapshot Carousel: Get specific snapshot based on the name in current PDB", "api_call": "GET /database/snapshots/{snapshot_name}", "api_version": "2023.12.17", "api_arguments": {"snapshot_name": "[REQUIRED] string: Snapshot Name."}, "functionality": "Returns a specific snapshot record. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Snapshot Carousel: Create snapshot of the current pluggable databases, the name will be {snapshotName}", "api_call": "POST /database/snapshots/{snapshot_name}", "api_version": "2023.12.17", "api_arguments": {"snapshot_name": "[REQUIRED] string: Snapshot Name."}, "functionality": "Returns created snapshot record. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Snapshot Carousel: Delete the specified snapshot", "api_call": "DELETE /database/snapshots/{snapshot_name}", "api_version": "2023.12.17", "api_arguments": {"snapshot_name": "[REQUIRED] string: Snapshot Name."}, "functionality": "Delete specified snapshot by snapshot name, A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Pluggable Database Snapshot Carousel: Get snapshot mode in current PDB", "api_call": "GET /database/snapshot_mode", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns a snapshot mode. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get the DBCA log file content for a specified DBCA jobs created through ORDS Database API in the default Oracle Home", "api_call": "GET /environment/dbca/jobs/{jobId}/log", "api_version": "2023.12.17", "api_arguments": {"jobId": "[REQUIRED] string: Identifier of the DBCA job."}, "functionality": "Returns the DBCA log file content from the DBCA job created through ORDS Database API in the specified Oracle Home. The Oracle Home is specified by $ORACLE_HOME environment variable. This Oracle Home is expected to be on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get a list of the DBCA database templates defined in the default Oracle Home", "api_call": "GET /environment/dbca/templates/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns a description of all database templates specified in the default Oracle Home. The Oracle Home is on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get a specific database template file in the default Oracle Home", "api_call": "GET /environment/dbca/templates/{filename}", "api_version": "2023.12.17", "api_arguments": {"filename": "[REQUIRED] string: Name of the database template file."}, "functionality": "Returns the database template file in the default Oracle Home. The default Oracle Home is on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get database Oracle Homes", "api_call": "GET /environment/homes/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns all database Oracle Homes that are on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - General: Get specific database Oracle Home", "api_call": "GET /environment/homes/{homeName}/", "api_version": "2023.12.17", "api_arguments": {"homeName": "[REQUIRED] string: Name of the Oracle Home."}, "functionality": "Returns a description for a specific Oracle Home on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get a list of the databases defined in an Oracle Home", "api_call": "GET /environment/homes/{homeName}/databases/", "api_version": "2023.12.17", "api_arguments": {"homeName": "[REQUIRED] string: Name of the Oracle Home."}, "functionality": "Returns a description of all databases specified in the Oracle Home. The Oracle Home is on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Create a new database in the specified Oracle Home", "api_call": "POST /environment/homes/{homeName}/databases/", "api_version": "2023.12.17", "api_arguments": {"homeName": "[REQUIRED] string: Name of the Oracle Home."}, "functionality": "Create a new database in the Oracle Home. This Oracle Home is expected to be on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get a specific database defined in a specific Oracle Home", "api_call": "GET /environment/homes/{homeName}/databases/{databaseName}", "api_version": "2023.12.17", "api_arguments": {"homeName": "[REQUIRED] string: Name of the Oracle Home.", "databaseName": "[REQUIRED] string: Name of the database."}, "functionality": "Returns a description of the database specified in the Oracle Home. The Oracle Home is on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get a list of the DBCA jobs created through ORDS Database API in the Oracle Home", "api_call": "GET /environment/homes/{homeName}/dbca/jobs/", "api_version": "2023.12.17", "api_arguments": {"homeName": "[REQUIRED] string: Name of the Oracle Home."}, "functionality": "Returns a description of all DBCA jobs created through ORDS Database API in the specified Oracle Home. This Oracle Home is expected to be on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Create a new DBCA job in the Oracle Home", "api_call": "POST /environment/homes/{homeName}/dbca/jobs/", "api_version": "2023.12.17", "api_arguments": {"homeName": "[REQUIRED] string: Name of the Oracle Home."}, "functionality": "Submit a new DBCA job request to CREATE or DELETE a database in the Oracle Home specified. This Oracle Home is expected to be on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get a specified DBCA jobs created through ORDS Database API in the specified Oracle Home", "api_call": "GET /environment/homes/{homeName}/dbca/jobs/{jobId}/", "api_version": "2023.12.17", "api_arguments": {"homeName": "[REQUIRED] string: Name of the Oracle Home.", "jobId": "[REQUIRED] string: Identifier of the DBCA job."}, "functionality": "Returns a description of the DBCA job created through ORDS Database API in the specified Oracle Home. This Oracle Home is expected to be on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Delete the specified DBCA job in the default Oracle Home", "api_call": "DELETE /environment/homes/{homeName}/dbca/jobs/{jobId}/", "api_version": "2023.12.17", "api_arguments": {"homeName": "[REQUIRED] string: Name of the Oracle Home.", "jobId": "[REQUIRED] string: Identifier of the DBCA job."}, "functionality": "Delete the DBCA job in the Oracle Home specified by $ORACLE_HOME environment variable. This Oracle Home is expected to be on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get the DBCA log file content for a specified DBCA jobs created through ORDS Database API in the specified Oracle Home", "api_call": "GET /environment/homes/{homeName}/dbca/jobs/{jobId}/log", "api_version": "2023.12.17", "api_arguments": {"homeName": "[REQUIRED] string: Name of the Oracle Home.", "jobId": "[REQUIRED] string: Identifier of the DBCA job."}, "functionality": "Returns DBCA log file content from the DBCA job created through ORDS Database API in the specified Oracle Home. This Oracle Home is expected to be on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get a list of the DBCA database templates defined in an Oracle Home", "api_call": "GET /environment/homes/{homeName}/dbca/templates/", "api_version": "2023.12.17", "api_arguments": {"homeName": "[REQUIRED] string: Name of the Oracle Home."}, "functionality": "Returns a description of all database templates specified in the Oracle Home. The Oracle Home is on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Environment: Get a specific database template file in a specific Oracle Home", "api_call": "GET /environment/homes/{homeName}/dbca/templates/{filename}", "api_version": "2023.12.17", "api_arguments": {"homeName": "[REQUIRED] string: Name of the Oracle Home.", "filename": "[REQUIRED] string: Name of the database template file."}, "functionality": "Returns the database template file in the specified Oracle Home. The Oracle Home is on the same host as the Oracle REST Data Services server instance. A client requires System Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Open Service Broker: Get the catalog of services that the service broker offers", "api_call": "GET /openservicebroker/v2/catalog", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns the Open Service Broker catalog for the associated database pool.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Open Service Broker: Deprovision a service instance", "api_call": "DELETE /openservicebroker/v2/service_instances/{instance_id}", "api_version": "2023.12.17", "api_arguments": {"instance_id": "[REQUIRED] string: Instance id of instance to provision."}, "functionality": "This service exists to satisfy Open Service Broker orphan mitigation flows. Service instances are not persisted by ORDS. As a result, there are no objects to delete.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Open Service Broker: Provision a service instance", "api_call": "PUT /openservicebroker/v2/service_instances/{instance_id}", "api_version": "2023.12.17", "api_arguments": {"instance_id": "[REQUIRED] string: Instance id of instance to provision."}, "functionality": "When the Service Broker receives a provision request from the Platform, it MUST take whatever action is necessary to create a new resource, according to the Service Plan specified.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Creates data load jobs", "api_call": "POST /data-tools/data-loads/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Creates data load jobs", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Gets data load jobs", "api_call": "GET /data-tools/data-loads/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Gets data load jobs", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Gets specific data load job", "api_call": "GET /data-tools/data-loads/{owner},{data_load_job_id}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the data load.", "data_load_job_id": "[REQUIRED] string: name of data load job to get"}, "functionality": "Gets specific data load job", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Delete data load job", "api_call": "DELETE /data-tools/data-loads/{owner},{data_load_job_id}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the data load.", "data_load_job_id": "[REQUIRED] integer: Data load job ID to delete"}, "functionality": "Delete data load job", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Gets progress of data load job", "api_call": "GET /data-tools/data-loads/{owner},{data_load_job_id}/progress", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the data load.", "data_load_job_id": "[REQUIRED] string: name of data load job to get"}, "functionality": "Gets progress of data load job", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Surveys input data", "api_call": "POST /data-tools/surveys/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Surveys input data", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Creates Live Table Feed", "api_call": "POST /data-tools/live-table-feeds/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Creates Live Table Feed", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Gets Live Table Feeds", "api_call": "GET /data-tools/live-table-feeds/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Gets Live Table Feeds", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Gets specific Live Table Feed", "api_call": "GET /data-tools/live-table-feeds/{owner},{feed_name}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the Live Table Feed.", "feed_name": "[REQUIRED] "}, "functionality": "Gets specific Live Table Feed", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Modify Live Table Feed", "api_call": "PUT /data-tools/live-table-feeds/{owner},{feed_name}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the Live Table Feed.", "feed_name": "[REQUIRED] "}, "functionality": "Modify Live Table Feed", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Delete Live Table Feed", "api_call": "DELETE /data-tools/live-table-feeds/{owner},{feed_name}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the Live Table Feed.", "feed_name": "[REQUIRED] : Live Table Feed to delete"}, "functionality": "Delete Live Table Feed", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Gets Live Table Feed notification key", "api_call": "GET /data-tools/live-table-feeds/{owner},{feed_name}/key", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the Live Table Feed.", "feed_name": "[REQUIRED] "}, "functionality": "Gets Live Table Feed notification key", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Triggers data load for Live Table Feed", "api_call": "POST /data-tools/live-table-feeds/{owner},{feed_name}/load_new_data", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the Live Table Feed.", "feed_name": "[REQUIRED] "}, "functionality": "Triggers data load for Live Table Feed.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Creates Cloud Storage Link", "api_call": "POST /data-tools/cloud-storage-links/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Creates Cloud Storage Link", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Gets Cloud Storage Links", "api_call": "GET /data-tools/cloud-storage-links/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Gets Cloud Storage Links", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Gets specific Cloud Storage Link", "api_call": "GET /data-tools/cloud-storage-links/{owner},{storage_link_name}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the Cloud Storage Link.", "storage_link_name": "[REQUIRED] string: Name of Cloud Storage Link to get"}, "functionality": "Gets specific Cloud Storage Link", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Delete a Cloud Storage Link", "api_call": "DELETE /data-tools/cloud-storage-links/{owner},{storage_link_name}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the Cloud Storage Link.", "storage_link_name": "[REQUIRED] string: Name of Cloud Storage Link to get"}, "functionality": "Delete a Cloud Storage Link", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Creates credentials", "api_call": "POST /data-tools/credentials/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Creates credentials", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Gets credentials", "api_call": "GET /data-tools/credentials/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Gets credentials", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Gets specific credential", "api_call": "GET /data-tools/credentials/{owner},{credential_name}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the Credential.", "credential_name": "[REQUIRED] string: Name of credential to get"}, "functionality": "Gets specific credential", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - Data Tools: Delete a credential", "api_call": "DELETE /data-tools/credentials/{owner},{credential_name}", "api_version": "2023.12.17", "api_arguments": {"owner": "[REQUIRED] string: Owner for the Credential.", "credential_name": "[REQUIRED] string: Name of credential to delete"}, "functionality": "Delete a credential", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all ORDS properties values", "api_call": "GET /ords/properties/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Returns information about the ORDS properties that have values set in the database. Depending on the associated database account permissions the data will be returned from USER_ORDS_PROPERTIES view for that correspond REST Enabled schema or all REST Enabled schemas if the database account has access to them. A client requires SQL Developer or SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get an ORDS property value", "api_call": "GET /ords/properties/{key}", "api_version": "2023.12.17", "api_arguments": {"key": "[REQUIRED] string: The name of the ORDS property."}, "functionality": "Returns information about a specfic ORDS property that has a value set in the database. Depending on the associated database account permissions the data will be returned from USER_ORDS_PROPERTIES view for that correspond REST Enabled schema or all REST Enabled schemas if the database account has access to them. Users can view their own ORDS property values that are defined at SCHEMA scope through this endpoint. ORDS properties that are defined at DATABASE scope can be view through this endpoint by an administrator. A client requires SQL Developer or SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Set an ORDS property value", "api_call": "PUT /ords/properties/{key}", "api_version": "2023.12.17", "api_arguments": {"key": "[REQUIRED] string: The name of the ORDS property."}, "functionality": "Set a specfic ORDS property value in the database. Depending on the associated database account permissions the data will be returned from USER_ORDS_PROPERTIES view for that correspond REST Enabled schema or all REST Enabled schemas if the database account has access to them. Users can set their own ORDS property values that are defined at SCHEMA scope through this endpoint. ORDS properties that are defined at DATABASE scope can be set through this endpoint by an administrator. A client requires SQL Developer or SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Unset an ORDS property value", "api_call": "DELETE /ords/properties/{key}", "api_version": "2023.12.17", "api_arguments": {"key": "[REQUIRED] string: The name of the ORDS property."}, "functionality": "Removes a specfic ORDS property value in the database. Depending on the associated database account permissions the data will be returned from USER_ORDS_PROPERTIES view for that correspond REST Enabled schema or all REST Enabled schemas if the database account has access to them. Users can unset their own ORDS property values that are defined at SCHEMA scope through this endpoint. ORDS properties that are defined at DATABASE scope can be unset through this endpoint by an administrator. A client requires SQL Developer or SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get an ORDS property value for a schema", "api_call": "GET /ords/properties/{schema}/{key}", "api_version": "2023.12.17", "api_arguments": {"schema": "[REQUIRED] string: The name of the REST Enabled database schema.", "key": "[REQUIRED] string: The name of the ORDS property."}, "functionality": "Returns information about a specfic ORDS property that has a value set in the database for the specified schema. ORDS property values that are defined at SCHEMA scope can be view through this endpoint. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Set an ORDS property value for a schema", "api_call": "PUT /ords/properties/{schema}/{key}", "api_version": "2023.12.17", "api_arguments": {"schema": "[REQUIRED] string: The name of the REST Enabled database schema.", "key": "[REQUIRED] string: The name of the ORDS property."}, "functionality": "Set a specfic ORDS property value in the database for the specified schema. ORDS property values that are defined at SCHEMA scope can be managed through this endpoint. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Unset an ORDS property value for a schema", "api_call": "DELETE /ords/properties/{schema}/{key}", "api_version": "2023.12.17", "api_arguments": {"schema": "[REQUIRED] string: The name of the REST Enabled database schema.", "key": "[REQUIRED] string: The name of the ORDS property."}, "functionality": "Removes a specfic ORDS property value in the database for the specified schema. ORDS property values that are defined at SCHEMA scope can be unset through this endpoint. A client requires SQL Administrator role to invoke this service.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all REST modules", "api_call": "GET /ords/rest/modules/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes all the REST modules.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Create a REST module", "api_call": "POST /ords/rest/modules/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Creates a REST module.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get a REST module", "api_call": "GET /ords/rest/modules/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific module."}, "functionality": "Desribes a REST module.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Update a REST module", "api_call": "PUT /ords/rest/modules/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific module."}, "functionality": "Updates a REST module.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get a REST module source", "api_call": "GET /ords/rest/modules/{id}/source", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific module."}, "functionality": "Exports the REST module source code.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all REST module templates", "api_call": "GET /ords/rest/templates/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes all the REST templates.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Create a REST module template", "api_call": "POST /ords/rest/templates/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Creates a REST module template.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get a REST module template", "api_call": "GET /ords/rest/templates/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific template."}, "functionality": "Describes a REST module template.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Update a REST module template", "api_call": "PUT /ords/rest/templates/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific template."}, "functionality": "Updates a REST module template.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Delete a REST module template", "api_call": "DELETE /ords/rest/templates/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific template."}, "functionality": "Deletes a REST module template.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all REST template handlers", "api_call": "GET /ords/rest/handlers/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes all the REST template handlers.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Create a REST template handler", "api_call": "POST /ords/rest/handlers/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Creates a REST template handler.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get a REST template handler", "api_call": "GET /ords/rest/handlers/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific handler."}, "functionality": "Describes a REST template handler.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Update a REST template handler", "api_call": "PUT /ords/rest/handlers/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific handler."}, "functionality": "Updates a REST template handler.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Delete a REST template handler", "api_call": "DELETE /ords/rest/handlers/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific handler."}, "functionality": "Deletes a REST template handler.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get a REST handler source", "api_call": "GET /ords/rest/handlers/{id}/source", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific handler."}, "functionality": "Returns a REST handler source code.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all REST handler parameters", "api_call": "GET /ords/rest/parameters/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes all the REST handler parameters.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Create a REST handler parameter", "api_call": "POST /ords/rest/parameters/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Creates a REST handler parameter.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get a REST handler parameter", "api_call": "GET /ords/rest/parameters/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific parameter."}, "functionality": "Describes a REST handler parameter.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Update a REST handler parameter", "api_call": "PUT /ords/rest/parameters/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific parameter."}, "functionality": "Updates a REST handler parameter.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Delete a REST handler parameter", "api_call": "DELETE /ords/rest/parameters/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific parameter."}, "functionality": "Deletes a REST handler parameter.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all ORDS roles", "api_call": "GET /ords/rest/roles/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes all the ORDS roles.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Create an ORDS role", "api_call": "POST /ords/rest/roles/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Creates an ORDS role.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get an ORDS role", "api_call": "GET /ords/rest/roles/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific ORDS role."}, "functionality": "Describes an ORDS role.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Update an ORDS role", "api_call": "PUT /ords/rest/roles/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific ORDS role."}, "functionality": "Updates an ORDS role.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Delete an ORDS role", "api_call": "DELETE /ords/rest/roles/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific ORDS role."}, "functionality": "Deletes an ORDS role.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all ORDS privileges for a specific role", "api_call": "GET /ords/rest/roles/{id}/privileges/", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific ORDS role privilege."}, "functionality": "Lists all the privileges for the specified role.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all ORDS privileges", "api_call": "GET /ords/rest/privileges/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes all the ORDS privileges.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Create an ORDS privilege", "api_call": "POST /ords/rest/privileges/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Creates an ORDS privilege.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get an ORDS privilege", "api_call": "GET /ords/rest/privileges/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for ORDS specific privilege."}, "functionality": "Describes an ORDS privilege.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Update an ORDS privilege", "api_call": "PUT /ords/rest/privileges/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific ORDS privilege."}, "functionality": "Updates an ORDS privilege.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Delete an ORDS privilege", "api_call": "DELETE /ords/rest/privileges/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific ORDS privilege."}, "functionality": "Deletes an ORDS privilege.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all REST modules in a privilege", "api_call": "GET /ords/rest/privileges/{id}/modules/", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for ORDS specific privilege."}, "functionality": "Describes all the REST modules protected by a privilege.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all REST patterns in a privilege", "api_call": "GET /ords/rest/privileges/{id}/patterns/", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for ORDS specific privilege."}, "functionality": "Describes all the REST patterns protected by a privilege.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all ORDS roles in a privilege", "api_call": "GET /ords/rest/privileges/{id}/roles/", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for ORDS specific privilege."}, "functionality": "Describes all the ORDS roles assigned to a privilege.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all protected REST modules", "api_call": "GET /ords/rest/privilege/modules/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes all the protected REST modules.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all ORDS OAuth clients", "api_call": "GET /ords/rest/clients/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Description of the purpose of the client, displayed to the end user during the approval phase of three-legged OAuth. May be null if grant_type is client_credentials; otherwise, must not be null.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Create an ORDS OAuth client", "api_call": "POST /ords/rest/clients/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Creates an ORDS OAuth client.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get an ORDS OAuth client", "api_call": "GET /ords/rest/clients/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific OAuth client."}, "functionality": "Describes an ORDS OAuth client.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Update an ORDS OAuth client", "api_call": "PUT /ords/rest/clients/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific OAuth client."}, "functionality": "Updates an ORDS OAuth client.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Delete an ORDS OAuth client", "api_call": "DELETE /ords/rest/clients/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific OAuth client."}, "functionality": "Deletes an ORDS OAuth client.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Updates the ORDS OAuth client avatar image.", "api_call": "POST /ords/rest/clients/{id}/logo/", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific OAuth client."}, "functionality": "Updates the ORDS OAuth client avatar image.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all ORDS privileges in an OAuth client", "api_call": "GET /ords/rest/clients/{id}/privileges/", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID for specific OAuth client."}, "functionality": "Describes all the ORDS privileges in an OAuth client.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all ORDS roles in an OAuth client", "api_call": "GET /ords/rest/clients/{id}/roles/", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID of the OAuth client."}, "functionality": "Describes all the ORDS roles in an OAuth client.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all REST enabled objects", "api_call": "GET /ords/rest/autorest/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes all the REST enabled objects.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: REST enable an object", "api_call": "POST /ords/rest/autorest/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "ENABLE_OBJECT enables Oracle REST Data Services access to a specified table or view in a schema.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get a REST enabled object", "api_call": "GET /ords/rest/autorest/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID of the module for the REST Enabled Object."}, "functionality": "Describes a REST enabled object.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Update or disables a REST enabled object", "api_call": "PUT /ords/rest/autorest/{id}", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID of the module for the REST Enabled Object."}, "functionality": "Updates or disables a REST enabled object,", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get count of all the REST enabled objects", "api_call": "GET /ords/rest/autorest/summary", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Summarizes all the REST enabled objects.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get a REST enabled object metadata", "api_call": "GET /ords/rest/autorest/{id}/metadata", "api_version": "2023.12.17", "api_arguments": {"id": "[REQUIRED] integer: ID of specific REST enabled object."}, "functionality": "Describes a REST enabled object metadata.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get current ORDS schema and count of all current schema REST objects and count of all REST modules", "api_call": "GET /ords/rest/overview/", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes the current ORDS schema and summarizes all the REST objects in the current schema and Summary of all the published, unpublished, fully protected, partially protected and unique protected REST modules, get current ORDS schema auto-REST authorization", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Oracle REST Data Services API - ORDS REST Services: Get all current schema recent REST objects", "api_call": "GET /ords/rest/overview/objects_recent", "api_version": "2023.12.17", "api_arguments": {}, "functionality": "Describes the last 7 days manipulated REST objects in the current schema in UPDATED_ON descending order.", "metadata": {"documentation_link": "https://docs.oracle.com/en/database/oracle/oracle-rest-data-services/23.4/orrst/"}}]