{"domain": "Video Classification", "framework": "PyTorch", "functionality": "3D ResNet", "api_name": "slow_r50", "api_call": "torch.hub.load(repo_or_dir='facebookresearch/pytorchvideo', model='slow_r50', pretrained=True)", "api_arguments": {"pretrained": "True"}, "python_environment_requirements": ["torch", "json", "urllib", "pytorchvideo", "torchvision", "torchaudio", "torchtext", "torcharrow", "TorchData", "TorchRec", "TorchServe", "PyTorch on XLA Devices"], "example_code": ["import torch", "model = torch.hub.load('facebookresearch/pytorchvideo', 'slow_r50', pretrained=True)", "device = 'cpu'", "model = model.eval()", "model = model.to(device)"], "performance": {"dataset": "Kinetics 400", "accuracy": {"top_1": 74.58, "top_5": 91.63}, "Flops (G)": 54.52, "Params (M)": 32.45}, "description": "The 3D ResNet model is a Resnet-style video classification network pretrained on the Kinetics 400 dataset. It is based on the architecture from the paper 'SlowFast Networks for Video Recognition' by Christoph Feichtenhofer et al."} 
{"domain": "Object Detection", "framework": "PyTorch", "functionality": "Traffic Object Detection, Drivable Area Segmentation, Lane Detection", "api_name": "HybridNets", "api_call": "torch.hub.load(repo_or_dir='datvuthanh/hybridnets', model='hybridnets', pretrained=True)", "api_arguments": "pretrained", "python_environment_requirements": "Python>=3.7, PyTorch>=1.10", "example_code": "import torch\nmodel = torch.hub.load('datvuthanh/hybridnets', 'hybridnets', pretrained=True)\nimg = torch.randn(1,3,640,384)\nfeatures, regression, classification, anchors, segmentation = model(img)", "performance": {"dataset": [{"name": "BDD100K", "accuracy": {"Traffic Object Detection": {"Recall (%)": 92.8, "mAP@0.5 (%)": 77.3}, "Drivable Area Segmentation": {"Drivable mIoU (%)": 90.5}, "Lane Line Detection": {"Accuracy (%)": 85.4, "Lane Line IoU (%)": 31.6}}}]}, "description": "HybridNets is an end2end perception network for multi-tasks. Our work focused on traffic object detection, drivable area segmentation and lane detection. HybridNets can run real-time on embedded systems, and obtains SOTA Object Detection, Lane Detection on BDD100K Dataset."} 
{"domain": "Object Detection", "framework": "PyTorch", "functionality": "Object Detection, Drivable Area Segmentation, Lane Detection", "api_name": "YOLOP", "api_call": "torch.hub.load(repo_or_dir='hustvl/yolop', model='yolop', pretrained=True)", "api_arguments": "pretrained", "python_environment_requirements": "pip install -qr https://github.com/hustvl/YOLOP/blob/main/requirements.txt", "example_code": "import torch\nmodel = torch.hub.load('hustvl/yolop', 'yolop', pretrained=True)\nimg = torch.randn(1,3,640,640)\ndet_out, da_seg_out,ll_seg_out = model(img)", "performance": {"dataset": "BDD100K", "accuracy": {"Object Detection": {"Recall(%)": 89.2, "mAP50(%)": 76.5, "Speed(fps)": 41}, "Drivable Area Segmentation": {"mIOU(%)": 91.5, "Speed(fps)": 41}, "Lane Detection": {"mIOU(%)": 70.5, "IOU(%)": 26.2}}}, "description": "YOLOP is an efficient multi-task network that can jointly handle three crucial tasks in autonomous driving: object detection, drivable area segmentation and lane detection. And it is also the first to reach real-time on embedded devices while maintaining state-of-the-art level performance on the BDD100K dataset."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Computing relative depth from a single image", "api_name": "MiDaS", "api_call": "torch.hub.load(repo_or_dir='intel-isl/MiDaS', model='DPT_Large', pretrained=True)", "api_arguments": {"repo_or_dir": "intel-isl/MiDaS", "model": "model_type"}, "python_environment_requirements": "pip install timm", "example_code": ["import cv2", "import torch", "import urllib.request", "import matplotlib.pyplot as plt", "url, filename = ('https://github.com/pytorch/hub/raw/master/images/dog.jpg', 'dog.jpg')", "urllib.request.urlretrieve(url, filename)", "model_type = 'DPT_Large'", "midas = torch.hub.load('intel-isl/MiDaS', 'DPT_Large')", "device = torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')", "midas.to(device)", "midas.eval()", "midas_transforms = torch.hub.load('intel-isl/MiDaS', 'transforms')", "if model_type == 'DPT_Large' or model_type == 'DPT_Hybrid':", " transform = midas_transforms.dpt_transform", "else:", " transform = midas_transforms.small_transform", "img = cv2.imread(filename)", "img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)", "input_batch = transform(img).to(device)", "with torch.no_grad():", " prediction = midas(input_batch)", "prediction = torch.nn.functional.interpolate(", " prediction.unsqueeze(1),", " size=img.shape[:2],", " mode='bicubic',", " align_corners=False,", ").squeeze()", "output = prediction.cpu().numpy()", "plt.imshow(output)", "plt.show()"], "performance": {"dataset": "10 distinct datasets", "accuracy": "Multi-objective optimization"}, "description": "MiDaS computes relative inverse depth from a single image. The repository provides multiple models that cover different use cases ranging from a small, high-speed model to a very large model that provide the highest accuracy. The models have been trained on 10 distinct datasets using multi-objective optimization to ensure high quality on a wide range of inputs."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Computing relative depth from a single image", "api_name": "MiDaS", "api_call": "torch.hub.load(repo_or_dir='intel-isl/MiDaS', model='DPT_Hybrid', pretrained=True)", "api_arguments": {"repo_or_dir": "intel-isl/MiDaS", "model": "model_type"}, "python_environment_requirements": "pip install timm", "example_code": ["import cv2", "import torch", "import urllib.request", "import matplotlib.pyplot as plt", "url, filename = ('https://github.com/pytorch/hub/raw/master/images/dog.jpg', 'dog.jpg')", "urllib.request.urlretrieve(url, filename)", "model_type = 'DPT_Large'", "midas = torch.hub.load('intel-isl/MiDaS', 'DPT_Hybrid')", "device = torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')", "midas.to(device)", "midas.eval()", "midas_transforms = torch.hub.load('intel-isl/MiDaS', 'transforms')", "if model_type == 'DPT_Large' or model_type == 'DPT_Hybrid':", " transform = midas_transforms.dpt_transform", "else:", " transform = midas_transforms.small_transform", "img = cv2.imread(filename)", "img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)", "input_batch = transform(img).to(device)", "with torch.no_grad():", " prediction = midas(input_batch)", "prediction = torch.nn.functional.interpolate(", " prediction.unsqueeze(1),", " size=img.shape[:2],", " mode='bicubic',", " align_corners=False,", ").squeeze()", "output = prediction.cpu().numpy()", "plt.imshow(output)", "plt.show()"], "performance": {"dataset": "10 distinct datasets", "accuracy": "Multi-objective optimization"}, "description": "MiDaS computes relative inverse depth from a single image. The repository provides multiple models that cover different use cases ranging from a small, high-speed model to a very large model that provide the highest accuracy. The models have been trained on 10 distinct datasets using multi-objective optimization to ensure high quality on a wide range of inputs."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Computing relative depth from a single image", "api_name": "MiDaS", "api_call": "torch.hub.load(repo_or_dir='intel-isl/MiDaS', model='MiDaS_small', pretrained=True)", "api_arguments": {"repo_or_dir": "intel-isl/MiDaS", "model": "model_type"}, "python_environment_requirements": "pip install timm", "example_code": ["import cv2", "import torch", "import urllib.request", "import matplotlib.pyplot as plt", "url, filename = ('https://github.com/pytorch/hub/raw/master/images/dog.jpg', 'dog.jpg')", "urllib.request.urlretrieve(url, filename)", "model_type = 'DPT_Large'", "midas = torch.hub.load('intel-isl/MiDaS', 'MiDaS_small')", "device = torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')", "midas.to(device)", "midas.eval()", "midas_transforms = torch.hub.load('intel-isl/MiDaS', 'transforms')", "if model_type == 'DPT_Large' or model_type == 'DPT_Hybrid':", " transform = midas_transforms.dpt_transform", "else:", " transform = midas_transforms.small_transform", "img = cv2.imread(filename)", "img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)", "input_batch = transform(img).to(device)", "with torch.no_grad():", " prediction = midas(input_batch)", "prediction = torch.nn.functional.interpolate(", " prediction.unsqueeze(1),", " size=img.shape[:2],", " mode='bicubic',", " align_corners=False,", ").squeeze()", "output = prediction.cpu().numpy()", "plt.imshow(output)", "plt.show()"], "performance": {"dataset": "10 distinct datasets", "accuracy": "Multi-objective optimization"}, "description": "MiDaS computes relative inverse depth from a single image. The repository provides multiple models that cover different use cases ranging from a small, high-speed model to a very large model that provide the highest accuracy. The models have been trained on 10 distinct datasets using multi-objective optimization to ensure high quality on a wide range of inputs."} 
{"domain": "Audio Separation", "framework": "PyTorch", "functionality": "Music Source Separation", "api_name": "Open-Unmix", "api_call": "torch.hub.load(repo_or_dir='sigsep/open-unmix-pytorch', model='umxhq', pretrained=True)", "api_arguments": ["model_name"], "python_environment_requirements": ["PyTorch >=1.6.0", "torchaudio"], "example_code": ["import torch", "separator = torch.hub.load('sigsep/open-unmix-pytorch', 'umxhq')", "audio = torch.rand((1, 2, 100000))", "original_sample_rate = separator.sample_rate", "estimates = separator(audio)"], "performance": {"dataset": "MUSDB18", "accuracy": "N/A"}, "description": "Open-Unmix provides ready-to-use models that allow users to separate pop music into four stems: vocals, drums, bass and the remaining other instruments. The models were pre-trained on the freely available MUSDB18 dataset."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Fine-grained image classifier", "api_name": "ntsnet", "api_call": "torch.hub.load(repo_or_dir='nicolalandro/ntsnet-cub200', model='ntsnet', pretrained=True, **{'topN': 6, 'device':'cpu', 'num_classes': 200})", "api_arguments": {"pretrained": "True", "topN": "6", "device": "cpu", "num_classes": "200"}, "python_environment_requirements": ["torch", "torchvision", "PIL"], "example_code": "from torchvision import transforms\nimport torch\nimport urllib\nfrom PIL import Image\n\ntransform_test = transforms.Compose([\n transforms.Resize((600, 600), Image.BILINEAR),\n transforms.CenterCrop((448, 448)),\n transforms.ToTensor(),\n transforms.Normalize((0.485, 0.456, 0.406), (0.229, 0.224, 0.225)),\n])\n\nmodel = torch.hub.load('nicolalandro/ntsnet-cub200', 'ntsnet', pretrained=True, **{'topN': 6, 'device':'cpu', 'num_classes': 200})\nmodel.eval()\n\nurl = 'https://raw.githubusercontent.com/nicolalandro/ntsnet-cub200/master/images/nts-net.png'\nimg = Image.open(urllib.request.urlopen(url))\nscaled_img = transform_test(img)\ntorch_images = scaled_img.unsqueeze(0)\n\nwith torch.no_grad():\n top_n_coordinates, concat_out, raw_logits, concat_logits, part_logits, top_n_index, top_n_prob = model(torch_images)\n\n_, predict = torch.max(concat_logits, 1)\npred_id = predict.item()\nprint('bird class:', model.bird_classes[pred_id])", "performance": {"dataset": "CUB200 2011", "accuracy": "Not provided"}, "description": "This is an nts-net pretrained with CUB200 2011 dataset, which is a fine-grained dataset of birds species."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Speech-To-Text", "api_name": "Silero Models", "api_call": "torch.hub.load(repo_or_dir='snakers4/silero-models', model='silero_stt', pretrained=True)", "api_arguments": {"repo_or_dir": "snakers4/silero-models", "model": "silero_stt", "language": ["en", "de", "es"], "device": "device"}, "python_environment_requirements": ["pip install -q torchaudio omegaconf soundfile"], "example_code": ["import torch", "import zipfile", "import torchaudio", "from glob import glob", "device = torch.device('cpu')", "model, decoder, utils = torch.hub.load(repo_or_dir='snakers4/silero-models', model='silero_stt', language='en', device=device)", "(read_batch, split_into_batches, read_audio, prepare_model_input) = utils", "torch.hub.download_url_to_file('https://opus-codec.org/static/examples/samples/speech_orig.wav', dst ='speech_orig.wav', progress=True)", "test_files = glob('speech_orig.wav')", "batches = split_into_batches(test_files, batch_size=10)", "input = prepare_model_input(read_batch(batches[0]), device=device)", "output = model(input)", "for example in output:", " print(decoder(example.cpu()))"], "performance": {"dataset": "Open-STT", "accuracy": "See the wiki for quality and performance benchmarks"}, "description": "Silero Speech-To-Text models provide enterprise grade STT in a compact form-factor for several commonly spoken languages. The models are robust to a variety of dialects, codecs, domains, noises, and lower sampling rates. They consume a normalized audio in the form of samples and output frames with token probabilities. A decoder utility is provided for simplicity."} 
{"domain": "Text-To-Speech", "framework": "PyTorch", "functionality": "Text-To-Speech", "api_name": "Silero Text-To-Speech Models", "api_call": "torch.hub.load(repo_or_dir='snakers4/silero-models', model='silero_tts', pretrained=True)", "api_arguments": {"repo_or_dir": "snakers4/silero-models", "model": "silero_tts", "language": "language", "speaker": "speaker"}, "python_environment_requirements": ["pip install -q torchaudio omegaconf"], "example_code": "import torch\nlanguage = 'en'\nspeaker = 'lj_16khz'\ndevice = torch.device('cpu')\nmodel, symbols, sample_rate, example_text, apply_tts = torch.hub.load(repo_or_dir='snakers4/silero-models', model='silero_tts', language=language, speaker=speaker)\nmodel = model.to(device)\naudio = apply_tts(texts=[example_text], model=model, sample_rate=sample_rate, symbols=symbols, device=device)", "performance": {"dataset": [{"language": "Russian", "speakers": 6}, {"language": "English", "speakers": 1}, {"language": "German", "speakers": 1}, {"language": "Spanish", "speakers": 1}, {"language": "French", "speakers": 1}], "accuracy": "High throughput on slow hardware. Decent performance on one CPU thread"}, "description": "Silero Text-To-Speech models provide enterprise grade TTS in a compact form-factor for several commonly spoken languages. They offer one-line usage, naturally sounding speech, no GPU or training required, minimalism and lack of dependencies, a library of voices in many languages, support for 16kHz and 8kHz out of the box."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Voice Activity Detection", "api_name": "Silero Voice Activity Detector", "api_call": "torch.hub.load(repo_or_dir='snakers4/silero-vad', model='silero_vad', force_reload=True)", "api_arguments": {"repo_or_dir": "snakers4/silero-vad", "model": "silero_vad", "force_reload": "True"}, "python_environment_requirements": {"torchaudio": "pip install -q torchaudio"}, "example_code": {"import": ["import torch", "torch.set_num_threads(1)", "from IPython.display import Audio", "from pprint import pprint"], "download_example": "torch.hub.download_url_to_file('https://models.silero.ai/vad_models/en.wav', 'en_example.wav')", "load_model": "model, utils = torch.hub.load(repo_or_dir='snakers4/silero-vad', model='silero_vad', force_reload=True)", "load_utils": "(get_speech_timestamps, _, read_audio, _) = utils", "set_sampling_rate": "sampling_rate = 16000", "read_audio": "wav = read_audio('en_example.wav', sampling_rate=sampling_rate)", "get_speech_timestamps": "speech_timestamps = get_speech_timestamps(wav, model, sampling_rate=sampling_rate)", "print_speech_timestamps": "pprint(speech_timestamps)"}, "performance": {"dataset": "", "accuracy": ""}, "description": "Silero VAD is a pre-trained enterprise-grade Voice Activity Detector (VAD) that aims to provide a high-quality and modern alternative to the WebRTC Voice Activity Detector. The model is optimized for performance on 1 CPU thread and is quantized."} 
{"domain": "Semantic Segmentation", "framework": "PyTorch", "functionality": "DeepLabV3", "api_name": "torch.hub.load", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='deeplabv3_resnet50', pretrained=True)", "api_arguments": {"pretrained": "True"}, "python_environment_requirements": {"torch": "latest", "torchvision": "latest", "PIL": "latest", "matplotlib": "latest"}, "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'deeplabv3_resnet50', pretrained=True)", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "input_image = input_image.convert('RGB')", "preprocess = transforms.Compose([", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)['out'][0]", "output_predictions = output.argmax(0)"], "performance": {"dataset": "COCO val2017", "accuracy": [{"model": "deeplabv3_resnet50", "Mean IOU": 66.4, "Global Pixelwise Accuracy": 92.4}]}, "description": "DeepLabV3 models with ResNet-50, ResNet-101 and MobileNet-V3 backbones for semantic segmentation. The pre-trained models have been trained on a subset of COCO train2017, on the 20 categories that are present in the Pascal VOC dataset."} 
{"domain": "Semantic Segmentation", "framework": "PyTorch", "functionality": "DeepLabV3", "api_name": "torch.hub.load", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='deeplabv3_resnet101', pretrained=True)", "api_arguments": {"pretrained": "True"}, "python_environment_requirements": {"torch": "latest", "torchvision": "latest", "PIL": "latest", "matplotlib": "latest"}, "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'deeplabv3_resnet101', pretrained=True)", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "input_image = input_image.convert('RGB')", "preprocess = transforms.Compose([", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)['out'][0]", "output_predictions = output.argmax(0)"], "performance": {"dataset": "COCO val2017", "accuracy": [{"model": "deeplabv3_resnet101", "Mean IOU": 67.4, "Global Pixelwise Accuracy": 92.4}]}, "description": "DeepLabV3 models with ResNet-50, ResNet-101 and MobileNet-V3 backbones for semantic segmentation. The pre-trained models have been trained on a subset of COCO train2017, on the 20 categories that are present in the Pascal VOC dataset."} 
{"domain": "Semantic Segmentation", "framework": "PyTorch", "functionality": "DeepLabV3", "api_name": "torch.hub.load", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='deeplabv3_mobilenet_v3_large', pretrained=True)", "api_arguments": {"pretrained": "True"}, "python_environment_requirements": {"torch": "latest", "torchvision": "latest", "PIL": "latest", "matplotlib": "latest"}, "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'deeplabv3_mobilenet_v3_large', pretrained=True)", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "input_image = input_image.convert('RGB')", "preprocess = transforms.Compose([", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)['out'][0]", "output_predictions = output.argmax(0)"], "performance": {"dataset": "COCO val2017", "accuracy": [{"model": "deeplabv3_mobilenet_v3_large", "Mean IOU": 60.3, "Global Pixelwise Accuracy": 91.4}]}, "description": "DeepLabV3 models with ResNet-50, ResNet-101 and MobileNet-V3 backbones for semantic segmentation. The pre-trained models have been trained on a subset of COCO train2017, on the 20 categories that are present in the Pascal VOC dataset."} 
{"domain": "Object Detection", "framework": "PyTorch", "functionality": "YOLOv5", "api_name": "torch.hub.load", "api_call": "torch.hub.load(repo_or_dir='ultralytics/yolov5', model='yolov5s', pretrained=True)", "api_arguments": ["'ultralytics/yolov5'", "'yolov5s'", "pretrained=True"], "python_environment_requirements": "Python>=3.8, PyTorch>=1.7", "example_code": ["import torch", "model = torch.hub.load('ultralytics/yolov5', 'yolov5s', pretrained=True)", "imgs = ['https://ultralytics.com/images/zidane.jpg']", "results = model(imgs)", "results.print()", "results.save()", "results.xyxy[0]", "results.pandas().xyxy[0]"], "performance": {"dataset": "COCO", "accuracy": {"YOLOv5s6": {"mAPval0.5:0.95": 43.3, "mAPtest0.5:0.95": 43.3, "mAPval0.5": 61.9}, "YOLOv5m6": {"mAPval0.5:0.95": 50.5, "mAPtest0.5:0.95": 50.5, "mAPval0.5": 68.7}, "YOLOv5l6": {"mAPval0.5:0.95": 53.4, "mAPtest0.5:0.95": 53.4, "mAPval0.5": 71.1}, "YOLOv5x6": {"mAPval0.5:0.95": 54.4, "mAPtest0.5:0.95": 54.4, "mAPval0.5": 72.0}, "YOLOv5x6 TTA": {"mAPval0.5:0.95": 55.0, "mAPtest0.5:0.95": 55.0, "mAPval0.5": 72.0}}}, "description": "YOLOv5 is a family of compound-scaled object detection models trained on the COCO dataset, and includes simple functionality for Test Time Augmentation (TTA), model ensembling, hyperparameter evolution, and export to ONNX, CoreML and TFLite."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Generative Adversarial Networks", "api_name": "DCGAN", "api_call": "torch.hub.load(repo_or_dir='facebookresearch/pytorch_GAN_zoo:hub', model='DCGAN', pretrained=True, useGPU=use_gpu)", "api_arguments": {"pretrained": "True", "useGPU": "use_gpu"}, "python_environment_requirements": "Python 3", "example_code": {"import": ["import torch", "import matplotlib.pyplot as plt", "import torchvision"], "use_gpu": "use_gpu = True if torch.cuda.is_available() else False", "load_model": "model = torch.hub.load('facebookresearch/pytorch_GAN_zoo:hub', 'DCGAN', pretrained=True, useGPU=use_gpu)", "build_noise_data": "noise, _ = model.buildNoiseData(num_images)", "generate_images": "with torch.no_grad(): generated_images = model.test(noise)", "plot_images": ["plt.imshow(torchvision.utils.make_grid(generated_images).permute(1, 2, 0).cpu().numpy())", "plt.show()"]}, "performance": {"dataset": "FashionGen", "accuracy": "N/A"}, "description": "DCGAN is a model designed in 2015 by Radford et. al. in the paper Unsupervised Representation Learning with Deep Convolutional Generative Adversarial Networks. It is a GAN architecture both very simple and efficient for low resolution image generation (up to 64x64)."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "ResNext WSL", "api_call": "torch.hub.load(repo_or_dir='facebookresearch/WSL-Images', model='resnext101_32x8d_wsl', pretrained=True)", "api_arguments": [{"name": "resnext101_32x8d_wsl", "type": "str", "description": "ResNeXt-101 32x8d WSL model"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('facebookresearch/WSL-Images', 'resnext101_32x8d_wsl')", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "preprocess = transforms.Compose([", " transforms.Resize(256),", " transforms.CenterCrop(224),", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)", "print(output[0])", "print(torch.nn.functional.softmax(output[0], dim=0))"], "performance": {"dataset": "ImageNet", "accuracy": {"ResNeXt-101 32x8d": {"Top-1 Acc.": "82.2", "Top-5 Acc.": "96.4"}}}, "description": "The provided ResNeXt models are pre-trained in weakly-supervised fashion on 940 million public images with 1.5K hashtags matching with 1000 ImageNet1K synsets, followed by fine-tuning on ImageNet1K dataset. The models significantly improve the training accuracy on ImageNet compared to training from scratch. They achieve state-of-the-art accuracy of 85.4% on ImageNet with the ResNext-101 32x48d model."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "ResNext WSL", "api_call": "torch.hub.load(repo_or_dir='facebookresearch/WSL-Images', model='resnext101_32x16d_wsl', pretrained=True)", "api_arguments": [{"name": "resnext101_32x16d_wsl", "type": "str", "description": "ResNeXt-101 32x16d WSL model"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('facebookresearch/WSL-Images', 'resnext101_32x16d_wsl')", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "preprocess = transforms.Compose([", " transforms.Resize(256),", " transforms.CenterCrop(224),", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)", "print(output[0])", "print(torch.nn.functional.softmax(output[0], dim=0))"], "performance": {"dataset": "ImageNet", "accuracy": {"ResNeXt-101 32x16d": {"Top-1 Acc.": "84.2", "Top-5 Acc.": "97.2"}}}, "description": "The provided ResNeXt models are pre-trained in weakly-supervised fashion on 940 million public images with 1.5K hashtags matching with 1000 ImageNet1K synsets, followed by fine-tuning on ImageNet1K dataset. The models significantly improve the training accuracy on ImageNet compared to training from scratch. They achieve state-of-the-art accuracy of 85.4% on ImageNet with the ResNext-101 32x48d model."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "ResNext WSL", "api_call": "torch.hub.load(repo_or_dir='facebookresearch/WSL-Images', model='resnext101_32x32d_wsl', pretrained=True)", "api_arguments": [{"name": "resnext101_32x32d_wsl", "type": "str", "description": "ResNeXt-101 32x32d WSL model"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('facebookresearch/WSL-Images', 'resnext101_32x32d_wsl')", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "preprocess = transforms.Compose([", " transforms.Resize(256),", " transforms.CenterCrop(224),", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)", "print(output[0])", "print(torch.nn.functional.softmax(output[0], dim=0))"], "performance": {"dataset": "ImageNet", "accuracy": {"ResNeXt-101 32x32d": {"Top-1 Acc.": "85.1", "Top-5 Acc.": "97.5"}}}, "description": "The provided ResNeXt models are pre-trained in weakly-supervised fashion on 940 million public images with 1.5K hashtags matching with 1000 ImageNet1K synsets, followed by fine-tuning on ImageNet1K dataset. The models significantly improve the training accuracy on ImageNet compared to training from scratch. They achieve state-of-the-art accuracy of 85.4% on ImageNet with the ResNext-101 32x48d model."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "ResNext WSL", "api_call": "torch.hub.load(repo_or_dir='facebookresearch/WSL-Images', model='resnext101_32x48d_wsl', pretrained=True)", "api_arguments": [{"name": "resnext101_32x48d_wsl", "type": "str", "description": "ResNeXt-101 32x48d WSL model"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('facebookresearch/WSL-Images', 'resnext101_32x48d_wsl')", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "preprocess = transforms.Compose([", " transforms.Resize(256),", " transforms.CenterCrop(224),", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)", "print(output[0])", "print(torch.nn.functional.softmax(output[0], dim=0))"], "performance": {"dataset": "ImageNet", "accuracy": {"ResNeXt-101 32x48d": {"Top-1 Acc.": "85.4", "Top-5 Acc.": "97.6"}}}, "description": "The provided ResNeXt models are pre-trained in weakly-supervised fashion on 940 million public images with 1.5K hashtags matching with 1000 ImageNet1K synsets, followed by fine-tuning on ImageNet1K dataset. The models significantly improve the training accuracy on ImageNet compared to training from scratch. They achieve state-of-the-art accuracy of 85.4% on ImageNet with the ResNext-101 32x48d model."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Generative Adversarial Networks (GANs)", "api_name": "PGAN", "api_call": "torch.hub.load(repo_or_dir='facebookresearch/pytorch_GAN_zoo:hub', model='PGAN', pretrained=True)", "api_arguments": {"repo_or_dir": "facebookresearch/pytorch_GAN_zoo:hub", "model": "PGAN", "model_name": "celebAHQ-512", "pretrained": "True", "useGPU": "use_gpu"}, "python_environment_requirements": "Python 3", "example_code": {"import": "import torch", "use_gpu": "use_gpu = True if torch.cuda.is_available() else False", "load_model": "model = torch.hub.load('facebookresearch/pytorch_GAN_zoo:hub', 'PGAN', model_name='celebAHQ-512', pretrained=True, useGPU=use_gpu)", "build_noise_data": "noise, _ = model.buildNoiseData(num_images)", "test": "generated_images = model.test(noise)", "plot_images": {"import_matplotlib": "import matplotlib.pyplot as plt", "import_torchvision": "import torchvision", "make_grid": "grid = torchvision.utils.make_grid(generated_images.clamp(min=-1, max=1), scale_each=True, normalize=True)", "imshow": "plt.imshow(grid.permute(1, 2, 0).cpu().numpy())", "show": "plt.show()"}}, "performance": {"dataset": "celebA", "accuracy": "High-quality celebrity faces"}, "description": "Progressive Growing of GANs (PGAN) is a method for generating high-resolution images using generative adversarial networks. The model is trained progressively, starting with low-resolution images and gradually increasing the resolution until the desired output is achieved. This implementation is based on the paper by Tero Karras et al., 'Progressive Growing of GANs for Improved Quality, Stability, and Variation'."} 
{"domain": "Semantic Segmentation", "framework": "PyTorch", "functionality": "Biomedical Image Segmentation", "api_name": "U-Net for brain MRI", "api_call": "torch.hub.load(repo_or_dir='mateuszbuda/brain-segmentation-pytorch', model='unet', in_channels=3, out_channels=1, init_features=32, pretrained=True)", "api_arguments": {"in_channels": 3, "out_channels": 1, "init_features": 32, "pretrained": true}, "python_environment_requirements": ["torch", "torchvision", "numpy", "PIL"], "example_code": ["import torch", "model = torch.hub.load('mateuszbuda/brain-segmentation-pytorch', 'unet', in_channels=3, out_channels=1, init_features=32, pretrained=True)", "import numpy as np", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "m, s = np.mean(input_image, axis=(0, 1)), np.std(input_image, axis=(0, 1))", "preprocess = transforms.Compose([transforms.ToTensor(),transforms.Normalize(mean=m, std=s)])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model = model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)", "print(torch.round(output[0]))"], "performance": {"dataset": "kaggle.com/mateuszbuda/lgg-mri-segmentation"}, "description": "U-Net with batch normalization for biomedical image segmentation with pretrained weights for abnormality segmentation in brain MRI. The model comprises four levels of blocks containing two convolutional layers with batch normalization and ReLU activation function, and one max pooling layer in the encoding part and up-convolutional layers instead in the decoding part. The number of convolutional filters in each block is 32, 64, 128, and 256. The bottleneck layer has 512 convolutional filters. From the encoding layers, skip connections are used to the corresponding layers in the decoding part. Input image is a 3-channel brain MRI slice from pre-contrast, FLAIR, and post-contrast sequences, respectively. Output is a one-channel probability map of abnormality regions with the same size as the input image."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "ResNet50", "api_call": "torch.hub.load(repo_or_dir='NVIDIA/DeepLearningExamples:torchhub', model='nvidia_resnet50', pretrained=True)", "api_arguments": {"pretrained": "True"}, "python_environment_requirements": ["pip install validators matplotlib"], "example_code": ["import torch", "from PIL import Image", "import torchvision.transforms as transforms", "import numpy as np", "import json", "import requests", "import matplotlib.pyplot as plt", "import warnings", "warnings.filterwarnings('ignore')", "%matplotlib inline", "device = torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')", "print(f'Using {device} for inference')", "resnet50 = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_resnet50', pretrained=True)", "utils = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_convnets_processing_utils')", "resnet50.eval().to(device)", "uris = [...]", "batch = torch.cat([utils.prepare_input_from_uri(uri) for uri in uris]).to(device)", "with torch.no_grad():", " output = torch.nn.functional.softmax(resnet50(batch), dim=1)", "results = utils.pick_n_best(predictions=output, n=5)", "for uri, result in zip(uris, results):", " img = Image.open(requests.get(uri, stream=True).raw)", " img.thumbnail((256,256), Image.ANTIALIAS)", " plt.imshow(img)", " plt.show()", " print(result)"], "performance": {"dataset": "ImageNet", "accuracy": "~0.5% top1 improvement over ResNet50 v1"}, "description": "The ResNet50 v1.5 model is a modified version of the original ResNet50 v1 model. The difference between v1 and v1.5 is that, in the bottleneck blocks which requires downsampling, v1 has stride = 2 in the first 1x1 convolution, whereas v1.5 has stride = 2 in the 3x3 convolution. This difference makes ResNet50 v1.5 slightly more accurate (~0.5% top1) than v1, but comes with a small performance drawback (~5% imgs/sec). The model is initialized as described in Delving deep into rectifiers: Surpassing human-level performance on ImageNet classification. This model is trained with mixed precision using Tensor Cores on Volta, Turing, and the NVIDIA Ampere GPU architectures."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "ResNeXt101-32x4d", "api_call": "torch.hub.load(repo_or_dir='NVIDIA/DeepLearningExamples:torchhub', model='nvidia_resneXt', pretrained=True)", "api_arguments": {"pretrained": "True"}, "python_environment_requirements": ["torch", "torchvision", "validators", "matplotlib"], "example_code": ["import torch", "from PIL import Image", "import torchvision.transforms as transforms", "import numpy as np", "import json", "import requests", "import matplotlib.pyplot as plt", "import warnings", "warnings.filterwarnings('ignore')", "%matplotlib inline", "device = torch.device(\"cuda\") if torch.cuda.is_available() else torch.device(\"cpu\")", "print(f'Using {device} for inference')", "resneXt = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_resneXt')", "utils = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_convnets_processing_utils')", "resneXt.eval().to(device)", "uris = [", " 'http://images.cocodataset.org/test-stuff2017/000000024309.jpg',", " 'http://images.cocodataset.org/test-stuff2017/000000028117.jpg',", " 'http://images.cocodataset.org/test-stuff2017/000000006149.jpg',", " 'http://images.cocodataset.org/test-stuff2017/000000004954.jpg',", "]", "batch = torch.cat(", " [utils.prepare_input_from_uri(uri) for uri in uris]", ").to(device)", "with torch.no_grad():", " output = torch.nn.functional.softmax(resneXt(batch), dim=1)", "results = utils.pick_n_best(predictions=output, n=5)", "for uri, result in zip(uris, results):", " img = Image.open(requests.get(uri, stream=True).raw)", " img.thumbnail((256,256), Image.ANTIALIAS)", " plt.imshow(img)", " plt.show()", " print(result)"], "performance": {"dataset": "IMAGENET", "accuracy": "Not provided"}, "description": "ResNeXt101-32x4d is a model introduced in the Aggregated Residual Transformations for Deep Neural Networks paper. It is based on regular ResNet model, substituting 3x3 convolutions inside the bottleneck block for 3x3 grouped convolutions. This model is trained with mixed precision using Tensor Cores on Volta, Turing, and the NVIDIA Ampere GPU architectures. It can be deployed for inference on the NVIDIA Triton Inference Server using TorchScript, ONNX Runtime or TensorRT as an execution backend."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "SE-ResNeXt101", "api_call": "torch.hub.load(repo_or_dir='NVIDIA/DeepLearningExamples:torchhub', model='nvidia_se_resnext101_32x4d', pretrained=True)", "api_arguments": "N/A", "python_environment_requirements": "validators, matplotlib", "example_code": "import torch\nfrom PIL import Image\nimport torchvision.transforms as transforms\nimport numpy as np\nimport json\nimport requests\nimport matplotlib.pyplot as plt\nimport warnings\nwarnings.filterwarnings('ignore')\n%matplotlib inline\ndevice = torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')\nprint(f'Using {device} for inference')\nresneXt = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_se_resnext101_32x4d')\nutils = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_convnets_processing_utils')\nresneXt.eval().to(device)\nuris = ['http://images.cocodataset.org/test-stuff2017/000000024309.jpg','http://images.cocodataset.org/test-stuff2017/000000028117.jpg','http://images.cocodataset.org/test-stuff2017/000000006149.jpg','http://images.cocodataset.org/test-stuff2017/000000004954.jpg']\nbatch = torch.cat([utils.prepare_input_from_uri(uri) for uri in uris]).to(device)\nwith torch.no_grad():\n output = torch.nn.functional.softmax(resneXt(batch), dim=1)\nresults = utils.pick_n_best(predictions=output, n=5)\nfor uri, result in zip(uris, results):\n img = Image.open(requests.get(uri, stream=True).raw)\n img.thumbnail((256,256), Image.ANTIALIAS)\n plt.imshow(img)\n plt.show()\n print(result)", "performance": {"dataset": "IMAGENET", "accuracy": "N/A"}, "description": "The SE-ResNeXt101-32x4d is a ResNeXt101-32x4d model with added Squeeze-and-Excitation module. This model is trained with mixed precision using Tensor Cores on Volta, Turing, and the NVIDIA Ampere GPU architectures, which allows researchers to get results 3x faster than training without Tensor Cores while experiencing the benefits of mixed precision training."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "AlexNet", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='alexnet', pretrained=True)", "api_arguments": {"pretrained": "True"}, "python_environment_requirements": {"torch": ">=1.9.0", "torchvision": ">=0.10.0"}, "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'alexnet', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"top-1_error": 43.45, "top-5_error": 20.91}}, "description": "AlexNet is a deep convolutional neural network that achieved a top-5 error of 15.3% in the 2012 ImageNet Large Scale Visual Recognition Challenge. The main contribution of the original paper was the depth of the model, which was computationally expensive but made feasible through the use of GPUs during training. The pretrained AlexNet model in PyTorch can be used for image classification tasks."} 
{"domain": "Text-to-Speech", "framework": "PyTorch", "functionality": "Speech Synthesis", "api_name": "WaveGlow", "api_call": "torch.hub.load(repo_or_dir='NVIDIA/DeepLearningExamples:torchhub', model='nvidia_waveglow', pretrained=True)", "api_arguments": {"repo_or_dir": "NVIDIA/DeepLearningExamples:torchhub", "model": "nvidia_waveglow", "model_math": "fp32"}, "python_environment_requirements": ["numpy", "scipy", "librosa", "unidecode", "inflect", "libsndfile1"], "example_code": {"load_waveglow_model": "waveglow = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_waveglow', model_math='fp32')", "prepare_waveglow_model": ["waveglow = waveglow.remove_weightnorm(waveglow)", "waveglow = waveglow.to('cuda')", "waveglow.eval()"], "load_tacotron2_model": "tacotron2 = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_tacotron2', model_math='fp32')", "prepare_tacotron2_model": ["tacotron2 = tacotron2.to('cuda')", "tacotron2.eval()"], "synthesize_speech": ["text = \"hello world, I missed you so much\"", "utils = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_tts_utils')", "sequences, lengths = utils.prepare_input_sequence([text])", "with torch.no_grad():", " mel, _, _ = tacotron2.infer(sequences, lengths)", " audio = waveglow.infer(mel)", "audio_numpy = audio[0].data.cpu().numpy()", "rate = 22050"], "save_audio": "write(\"audio.wav\", rate, audio_numpy)", "play_audio": "Audio(audio_numpy, rate=rate)"}, "performance": {"dataset": "LJ Speech", "accuracy": null}, "description": "The Tacotron 2 and WaveGlow model form a text-to-speech system that enables users to synthesize natural-sounding speech from raw transcripts without any additional prosody information. The Tacotron 2 model produces mel spectrograms from input text using encoder-decoder architecture. WaveGlow is a flow-based model that consumes the mel spectrograms to generate speech."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "RoBERTa", "api_name": "torch.hub.load", "api_call": "torch.hub.load(repo_or_dir='pytorch/fairseq', model='roberta.large', pretrained=True)", "api_arguments": ["'pytorch/fairseq'", "'roberta.large'"], "python_environment_requirements": ["regex", "requests", "hydra-core", "omegaconf"], "example_code": ["import torch", "roberta = torch.hub.load('pytorch/fairseq', 'roberta.large')", "roberta.eval()", "tokens = roberta.encode('Hello world!')", "last_layer_features = roberta.extract_features(tokens)", "all_layers = roberta.extract_features(tokens, return_all_hiddens=True)", "roberta = torch.hub.load('pytorch/fairseq', 'roberta.large.mnli')", "roberta.eval()", "tokens = roberta.encode('Roberta is a heavily optimized version of BERT.', 'Roberta is not very optimized.')", "prediction = roberta.predict('mnli', tokens).argmax().item()", "tokens = roberta.encode('Roberta is a heavily optimized version of BERT.', 'Roberta is based on BERT.')", "prediction = roberta.predict('mnli', tokens).argmax().item()", "roberta.register_classification_head('new_task', num_classes=3)", "logprobs = roberta.predict('new_task', tokens)"], "performance": {"dataset": "MNLI", "accuracy": "N/A"}, "description": "RoBERTa is a robustly optimized version of BERT, a revolutionary self-supervised pretraining technique that learns to predict intentionally hidden (masked) sections of text. RoBERTa builds on BERT's language masking strategy and modifies key hyperparameters, including removing BERT's next-sentence pretraining objective, and training with much larger mini-batches and learning rates. RoBERTa was also trained on an order of magnitude more data than BERT, for a longer amount of time, allowing it to generalize even better to downstream tasks."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Dense Convolutional Network", "api_name": "Densenet", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='densenet121', pretrained=True)", "api_arguments": [{"name": "densenet121", "type": "str", "description": "Densenet-121 model"}], "python_environment_requirements": {"torch": "latest", "torchvision": "latest"}, "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'densenet121', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"densenet121": {"Top-1 error": 25.35, "Top-5 error": 7.83}}}, "description": "Dense Convolutional Network (DenseNet) connects each layer to every other layer in a feed-forward fashion. It alleviates the vanishing-gradient problem, strengthens feature propagation, encourages feature reuse, and substantially reduces the number of parameters."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Dense Convolutional Network", "api_name": "Densenet", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='densenet169', pretrained=True)", "api_arguments": [{"name": "densenet169", "type": "str", "description": "Densenet-169 model"}], "python_environment_requirements": {"torch": "latest", "torchvision": "latest"}, "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'densenet169', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"densenet169": {"Top-1 error": 24.0, "Top-5 error": 7.0}}}, "description": "Dense Convolutional Network (DenseNet) connects each layer to every other layer in a feed-forward fashion. It alleviates the vanishing-gradient problem, strengthens feature propagation, encourages feature reuse, and substantially reduces the number of parameters."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Dense Convolutional Network", "api_name": "Densenet", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='densenet201', pretrained=True)", "api_arguments": [{"name": "densenet201", "type": "str", "description": "Densenet-201 model"}], "python_environment_requirements": {"torch": "latest", "torchvision": "latest"}, "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'densenet201', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"densenet201": {"Top-1 error": 22.8, "Top-5 error": 6.43}}}, "description": "Dense Convolutional Network (DenseNet) connects each layer to every other layer in a feed-forward fashion. It alleviates the vanishing-gradient problem, strengthens feature propagation, encourages feature reuse, and substantially reduces the number of parameters."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Dense Convolutional Network", "api_name": "Densenet", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='densenet161', pretrained=True)", "api_arguments": [{"name": "densenet161", "type": "str", "description": "Densenet-161 model"}], "python_environment_requirements": {"torch": "latest", "torchvision": "latest"}, "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'densenet161', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"densenet161": {"Top-1 error": 22.35, "Top-5 error": 6.2}}}, "description": "Dense Convolutional Network (DenseNet) connects each layer to every other layer in a feed-forward fashion. It alleviates the vanishing-gradient problem, strengthens feature propagation, encourages feature reuse, and substantially reduces the number of parameters."}
{"domain": "Semantic Segmentation", "framework": "PyTorch", "functionality": "Fully-Convolutional Network", "api_name": "fcn_resnet50", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='fcn_resnet50', pretrained=True)", "api_arguments": [{"name": "pretrained", "type": "boolean", "description": "If True, returns a model pre-trained on COCO train2017"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "matplotlib"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'fcn_resnet50', pretrained=True)", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "input_image = input_image.convert(\"RGB\")", "preprocess = transforms.Compose([", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)['out'][0]", "output_predictions = output.argmax(0)"], "performance": {"dataset": "COCO val2017", "accuracy": {"fcn_resnet50": {"Mean IOU": 60.5, "Global Pixelwise Accuracy": 91.4}}}, "description": "FCN-ResNet is a Fully-Convolutional Network model using a ResNet-50 or a ResNet-101 backbone. The pre-trained models have been trained on a subset of COCO train2017, on the 20 categories that are present in the Pascal VOC dataset."} 
{"domain": "Semantic Segmentation", "framework": "PyTorch", "functionality": "Fully-Convolutional Network", "api_name": "fcn_resnet101", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='fcn_resnet101', pretrained=True)", "api_arguments": [{"name": "pretrained", "type": "boolean", "description": "If True, returns a model pre-trained on COCO train2017"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "matplotlib"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'fcn_resnet101', pretrained=True)", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "input_image = input_image.convert(\"RGB\")", "preprocess = transforms.Compose([", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)['out'][0]", "output_predictions = output.argmax(0)"], "performance": {"dataset": "COCO val2017", "accuracy": {"fcn_resnet101": {"Mean IOU": 63.7, "Global Pixelwise Accuracy": 91.9}}}, "description": "FCN-ResNet is a Fully-Convolutional Network model using a ResNet-50 or a ResNet-101 backbone. The pre-trained models have been trained on a subset of COCO train2017, on the 20 categories that are present in the Pascal VOC dataset."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "HarDNet", "api_call": "torch.hub.load(repo_or_dir='PingoLH/Pytorch-HarDNet', model='hardnet39ds', pretrained=True)", "api_arguments": [{"name": "hardnet39ds", "type": "str", "description": "HarDNet-39DS model"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('PingoLH/Pytorch-HarDNet', 'hardnet39ds', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"hardnet39ds": {"Top-1 error": 27.92, "Top-5 error": 9.57}}}, "description": "Harmonic DenseNet (HarDNet) is a low memory traffic CNN model, which is fast and efficient. The basic concept is to minimize both computational cost and memory access cost at the same time, such that the HarDNet models are 35% faster than ResNet running on GPU comparing to models with the same accuracy (except the two DS models that were designed for comparing with MobileNet)."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "HarDNet", "api_call": "torch.hub.load(repo_or_dir='PingoLH/Pytorch-HarDNet', model='hardnet68ds', pretrained=True)", "api_arguments": [{"name": "hardnet68ds", "type": "str", "description": "HarDNet-68DS model"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('PingoLH/Pytorch-HarDNet', 'hardnet68ds', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"hardnet68ds": {"Top-1 error": 25.71, "Top-5 error": 8.13}}}, "description": "Harmonic DenseNet (HarDNet) is a low memory traffic CNN model, which is fast and efficient. The basic concept is to minimize both computational cost and memory access cost at the same time, such that the HarDNet models are 35% faster than ResNet running on GPU comparing to models with the same accuracy (except the two DS models that were designed for comparing with MobileNet)."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "HarDNet", "api_call": "torch.hub.load(repo_or_dir='PingoLH/Pytorch-HarDNet', model='hardnet68', pretrained=True)", "api_arguments": [{"name": "hardnet68", "type": "str", "description": "HarDNet-68 model"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('PingoLH/Pytorch-HarDNet', 'hardnet68', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"hardnet68": {"Top-1 error": 23.52, "Top-5 error": 6.99}}}, "description": "Harmonic DenseNet (HarDNet) is a low memory trafficCNN model, which is fast and efficient. The basic concept is to minimize both computational cost and memory access cost at the same time, such that the HarDNet models are 35% faster than ResNet running on GPU comparing to models with the same accuracy (except the two DS models that were designed for comparing with MobileNet)."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "HarDNet", "api_call": "torch.hub.load(repo_or_dir='PingoLH/Pytorch-HarDNet', model='hardnet85', pretrained=True)", "api_arguments": [{"name": "hardnet85", "type": "str", "description": "HarDNet-85 model"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('PingoLH/Pytorch-HarDNet', 'hardnet85', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"hardnet85": {"Top-1 error": 21.96, "Top-5 error": 6.11}}}, "description": "Harmonic DenseNet (HarDNet) is a low memory traffic CNN model, which is fast and efficient. The basic concept is to minimize both computational cost and memory access cost at the same time, such that the HarDNet models are 35% faster than ResNet running on GPU comparing to models with the same accuracy (except the two DS models that were designed for comparing with MobileNet)."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Efficient networks by generating more features from cheap operations", "api_name": "GhostNet", "api_call": "torch.hub.load(repo_or_dir='huawei-noah/ghostnet', model='ghostnet_1x', pretrained=True)", "api_arguments": ["pretrained"], "python_environment_requirements": ["torch", "torchvision", "PIL"], "example_code": ["import torch", "model = torch.hub.load('huawei-noah/ghostnet', 'ghostnet_1x', pretrained=True)", "model.eval()", "input_image = Image.open(filename)", "preprocess = transforms.Compose([", " transforms.Resize(256),", " transforms.CenterCrop(224),", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)", "probabilities = torch.nn.functional.softmax(output[0], dim=0)", "print(probabilities)"], "performance": {"dataset": "ImageNet", "accuracy": {"Top-1 acc": "73.98", "Top-5 acc": "91.46"}}, "description": "The GhostNet architecture is based on an Ghost module structure which generates more features from cheap operations. Based on a set of intrinsic feature maps, a series of cheap operations are applied to generate many ghost feature maps that could fully reveal information underlying intrinsic features. Experiments conducted on benchmarks demonstrate the superiority of GhostNet in terms of speed and accuracy tradeoff."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "GoogLeNet", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='googlenet', pretrained=True)", "api_arguments": {"pretrained": "True"}, "python_environment_requirements": {"torch": ">=1.0.0", "torchvision": ">=0.2.2"}, "example_code": {"import": ["import torch", "import urllib", "from PIL import Image", "from torchvision import transforms"], "load_model": "model = torch.hub.load('pytorch/vision:v0.10.0', 'googlenet', pretrained=True)", "model_eval": "model.eval()", "image_preprocessing": ["input_image = Image.open(filename)", "preprocess = transforms.Compose([transforms.Resize(256), transforms.CenterCrop(224), transforms.ToTensor(), transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)"], "model_execution": ["if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)"], "output_processing": ["probabilities = torch.nn.functional.softmax(output[0], dim=0)", "top5_prob, top5_catid = torch.topk(probabilities, 5)"]}, "performance": {"dataset": "ImageNet", "accuracy": {"Top-1 error": "30.22", "Top-5 error": "10.47"}}, "description": "GoogLeNet is based on a deep convolutional neural network architecture codenamed 'Inception', which was responsible for setting the new state of the art for classification and detection in the ImageNet Large-Scale Visual Recognition Challenge 2014 (ILSVRC 2014)."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "IBN-Net", "api_name": "torch.hub.load", "api_call": "torch.hub.load(repo_or_dir='XingangPan/IBN-Net', model='resnet50_ibn_a', pretrained=True)", "api_arguments": [{"name": "resnet50_ibn_a", "type": "str", "description": "ResNet-50-IBN-a model"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "model = torch.hub.load('XingangPan/IBN-Net', 'resnet50_ibn_a', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"resnet50_ibn_a": {"Top-1 acc": 77.46, "Top-5 acc": 93.68}}}, "description": "IBN-Net is a CNN model with domain/appearance invariance. Motivated by style transfer works, IBN-Net carefully unifies instance normalization and batch normalization in a single deep network. It provides a simple way to increase both modeling and generalization capacities without adding model complexity. IBN-Net is especially suitable for cross domain or person/vehicle re-identification tasks."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "IBN-Net", "api_name": "torch.hub.load", "api_call": "torch.hub.load(repo_or_dir='XingangPan/IBN-Net', model='resnet101_ibn_a', pretrained=True)", "api_arguments": [{"name": "resnet101_ibn_a", "type": "str", "description": "ResNet-101-IBN-a model"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "model = torch.hub.load('XingangPan/IBN-Net', 'resnet101_ibn_a', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"resnet101_ibn_a": {"Top-1 acc": 78.61, "Top-5 acc": 94.41}}}, "description": "IBN-Net is a CNN model with domain/appearance invariance. Motivated by style transfer works, IBN-Net carefully unifies instance normalization and batch normalization in a single deep network. It provides a simple way to increase both modeling and generalization capacities without adding model complexity. IBN-Net is especially suitable for cross domain or person/vehicle re-identification tasks."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "IBN-Net", "api_name": "torch.hub.load", "api_call": "torch.hub.load(repo_or_dir='XingangPan/IBN-Net', model='resnext101_ibn_a', pretrained=True)", "api_arguments": [{"name": "resnext101_ibn_a", "type": "str", "description": "ResNeXt-101-IBN-a model"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "model = torch.hub.load('XingangPan/IBN-Net', 'resnext101_ibn_a', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet","accuracy": {"resnext101_ibn_a": {"Top-1 acc": 79.12, "Top-5 acc": 94.58}}}, "description": "IBN-Net is a CNN model with domain/appearance invariance. Motivated by style transfer works, IBN-Net carefully unifies instance normalization and batch normalization in a single deep network. It provides a simple way to increase both modeling and generalization capacities without adding model complexity. IBN-Net is especially suitable for cross domain or person/vehicle re-identification tasks."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "IBN-Net", "api_name": "torch.hub.load", "api_call": "torch.hub.load(repo_or_dir='XingangPan/IBN-Net', model='se_resnet101_ibn_a', pretrained=True)", "api_arguments": [{"name": "se_resnet101_ibn_a", "type": "str", "description": "SE-ResNet-101-IBN-a model"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "model = torch.hub.load('XingangPan/IBN-Net', 'se_resnet101_ibn_a', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"se_resnet101_ibn_a": {"Top-1 acc": 78.75, "Top-5 acc": 94.49}}}, "description": "IBN-Net is a CNN model with domain/appearance invariance. Motivated by style transfer works, IBN-Net carefully unifies instance normalization and batch normalization in a single deep network. It provides a simple way to increase both modeling and generalization capacities without adding model complexity. IBN-Net is especially suitable for cross domain or person/vehicle re-identification tasks."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "Inception_v3", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='inception_v3', pretrained=True)", "api_arguments": {"pretrained": "True"}, "python_environment_requirements": {"torch": "1.9.0", "torchvision": "0.10.0"}, "example_code": {"import_libraries": "import torch", "load_model": "model = torch.hub.load('pytorch/vision:v0.10.0', 'inception_v3', pretrained=True)", "model_evaluation": "model.eval()"}, "performance": {"dataset": "imagenet", "accuracy": {"top-1_error": 22.55, "top-5_error": 6.44}}, "description": "Inception v3, also called GoogleNetv3, is a famous Convolutional Neural Network trained on the ImageNet dataset from 2015. It is based on the exploration of ways to scale up networks to utilize the added computation as efficiently as possible by using suitably factorized convolutions and aggressive regularization. The model achieves a top-1 error of 22.55% and a top-5 error of 6.44% on the ImageNet dataset."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "ResNeSt", "api_call": "torch.hub.load(repo_or_dir='zhanghang1989/ResNeSt', model='resnest50', pretrained=True)", "api_arguments": "resnest50", "python_environment_requirements": {"torch": "1.0.0", "torchvision": "0.2.2"}, "example_code": ["import torch", "model = torch.hub.load('zhanghang1989/ResNeSt', 'resnest50', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"ResNeSt-50": 81.03}}, "description": "ResNeSt models are from the ResNeSt: Split-Attention Networks paper. They are a new ResNet variant that enables attention across feature-map groups. By stacking Split-Attention blocks ResNet-style, ResNeSt models outperform other networks with similar model complexities, and also help downstream tasks including object detection, instance segmentation, and semantic segmentation."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "ResNeSt", "api_call": "torch.hub.load(repo_or_dir='zhanghang1989/ResNeSt', model='resnest101', pretrained=True)", "api_arguments": "resnest101", "python_environment_requirements": {"torch": "1.0.0", "torchvision": "0.2.2"}, "example_code": ["import torch", "model = torch.hub.load('zhanghang1989/ResNeSt', 'resnest101', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"ResNeSt-101": 82.83}}, "description": "ResNeSt models are from the ResNeSt: Split-Attention Networks paper. They are a new ResNet variant that enables attention across feature-map groups. By stacking Split-Attention blocks ResNet-style, ResNeSt models outperform other networks with similar model complexities, and also help downstream tasks including object detection, instance segmentation, and semantic segmentation."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "ResNeSt", "api_call": "torch.hub.load(repo_or_dir='zhanghang1989/ResNeSt', model='resnest200', pretrained=True)", "api_arguments": "resnest200", "python_environment_requirements": {"torch": "1.0.0", "torchvision": "0.2.2"}, "example_code": ["import torch", "model = torch.hub.load('zhanghang1989/ResNeSt', 'resnest200', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"ResNeSt-200": 83.84}}, "description": "ResNeSt models are from the ResNeSt: Split-Attention Networks paper. They are a new ResNet variant that enables attention across feature-map groups. By stacking Split-Attention blocks ResNet-style, ResNeSt models outperform other networks with similar model complexities, and also help downstream tasks including object detection, instance segmentation, and semantic segmentation."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "ResNeSt", "api_call": "torch.hub.load(repo_or_dir='zhanghang1989/ResNeSt', model='resnest269', pretrained=True)", "api_arguments": "resnest269", "python_environment_requirements": {"torch": "1.0.0", "torchvision": "0.2.2"}, "example_code": ["import torch", "model = torch.hub.load('zhanghang1989/ResNeSt', 'resnest269', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"ResNeSt-269": 84.54}}, "description": "ResNeSt models are from the ResNeSt: Split-Attention Networks paper. They are a new ResNet variant that enables attention across feature-map groups. By stacking Split-Attention blocks ResNet-style, ResNeSt models outperform other networks with similar model complexities, and also help downstream tasks including object detection, instance segmentation, and semantic segmentation."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "ProxylessNAS", "api_name": "mit-han-lab/ProxylessNAS", "api_call": "torch.hub.load(repo_or_dir='mit-han-lab/ProxylessNAS', model='proxylessnas_cpu', pretrained=True)", "api_arguments": [{"name": "proxylessnas_cpu", "type": "str", "description": "ProxylessNAS optimized for CPU"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "target_platform = 'proxyless_cpu'", "model = torch.hub.load('mit-han-lab/ProxylessNAS', target_platform, pretrained=True)", "model.eval()"], "performance": {"dataset": [{"model_structure": "proxylessnas_cpu", "accuracy": 75.3}]}, "description": "ProxylessNAS models are from the ProxylessNAS: Direct Neural Architecture Search on Target Task and Hardware paper. They specialize CNN architectures for different hardware platforms, offering free yet significant performance boost on all three platforms (CPU, GPU, and Mobile) with similar accuracy."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "ProxylessNAS", "api_name": "mit-han-lab/ProxylessNAS", "api_call": "torch.hub.load(repo_or_dir='mit-han-lab/ProxylessNAS', model='proxylessnas_gpu', pretrained=True)", "api_arguments": [{"name": "proxylessnas_gpu", "type": "str", "description": "ProxylessNAS optimized for GPU"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "target_platform = 'proxyless_gpu'", "model = torch.hub.load('mit-han-lab/ProxylessNAS', target_platform, pretrained=True)", "model.eval()"], "performance": {"dataset": [{"model_structure": "proxylessnas_gpu", "accuracy": 75.1}]}, "description": "ProxylessNAS models are from the ProxylessNAS: Direct Neural Architecture Search on Target Task and Hardware paper. They specialize CNN architectures for different hardware platforms, offering free yet significant performance boost on all three platforms (CPU, GPU, and Mobile) with similar accuracy."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "ProxylessNAS", "api_name": "mit-han-lab/ProxylessNAS", "api_call": "torch.hub.load(repo_or_dir='mit-han-lab/ProxylessNAS', model='proxylessnas_mobile', pretrained=True)", "api_arguments": [{"name": "proxylessnas_mobile", "type": "str", "description": "ProxylessNAS optimized for Mobile"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "target_platform = 'proxyless_mobile'", "model = torch.hub.load('mit-han-lab/ProxylessNAS', target_platform, pretrained=True)", "model.eval()"], "performance": {"dataset": [{"model_structure": "proxylessnas_mobile", "accuracy": 74.6}]}, "description": "ProxylessNAS models are from the ProxylessNAS: Direct Neural Architecture Search on Target Task and Hardware paper. They specialize CNN architectures for different hardware platforms, offering free yet significant performance boost on all three platforms (CPU, GPU, and Mobile) with similar accuracy."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "MobileNet v2", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='mobilenet_v2', pretrained=True)", "api_arguments": {"pretrained": "True"}, "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision', 'mobilenet_v2', pretrained=True)", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "preprocess = transforms.Compose([", " transforms.Resize(256),", " transforms.CenterCrop(224),", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)", "probabilities = torch.nn.functional.softmax(output[0], dim=0)"], "performance": {"dataset": "ImageNet", "accuracy": {"top-1_error": 28.12, "top-5_error": 9.71}}, "description": "The MobileNet v2 architecture is based on an inverted residual structure where the input and output of the residual block are thin bottleneck layers opposite to traditional residual models which use expanded representations in the input. MobileNet v2 uses lightweight depthwise convolutions to filter features in the intermediate expansion layer. Additionally, non-linearities in the narrow layers were removed in order to maintain representational power."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Deep Residual Networks", "api_name": "ResNet", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='resnet18', pretrained=True)", "api_arguments": [{"name": "pretrained", "type": "bool", "default": "False", "description": "If True, returns a model pre-trained on ImageNet"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'resnet18', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"model": "resnet18", "top-1_error": 30.24, "top-5_error": 10.92}}, "description": "ResNet models are deep residual networks pre-trained on ImageNet. They were proposed in the paper 'Deep Residual Learning for Image Recognition'. Available model variants include ResNet18, ResNet34, ResNet50, ResNet101, and ResNet152."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Deep Residual Networks", "api_name": "ResNet", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='resnet34', pretrained=True)", "api_arguments": [{"name": "pretrained", "type": "bool", "default": "False", "description": "If True, returns a model pre-trained on ImageNet"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'resnet34', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"model": "resnet34", "top-1_error": 26.7, "top-5_error": 8.58}}, "description": "ResNet models are deep residual networks pre-trained on ImageNet. They were proposed in the paper 'Deep Residual Learning for Image Recognition'. Available model variants include ResNet18, ResNet34, ResNet50, ResNet101, and ResNet152."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Deep Residual Networks", "api_name": "ResNet", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='resnet50', pretrained=True)", "api_arguments": [{"name": "pretrained", "type": "bool", "default": "False", "description": "If True, returns a model pre-trained on ImageNet"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'resnet50', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"model": "resnet50", "top-1_error": 23.85, "top-5_error": 7.13}}, "description": "ResNet models are deep residual networks pre-trained on ImageNet. They were proposed in the paper 'Deep Residual Learning for Image Recognition'. Available model variants include ResNet18, ResNet34, ResNet50, ResNet101, and ResNet152."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Deep Residual Networks", "api_name": "ResNet", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='resnet101', pretrained=True)", "api_arguments": [{"name": "pretrained", "type": "bool", "default": "False", "description": "If True, returns a model pre-trained on ImageNet"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'resnet101', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"model": "resnet101", "top-1_error": 22.63, "top-5_error": 6.44}}, "description": "ResNet models are deep residual networks pre-trained on ImageNet. They were proposed in the paper 'Deep Residual Learning for Image Recognition'. Available model variants include ResNet18, ResNet34, ResNet50, ResNet101, and ResNet152."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Deep Residual Networks", "api_name": "ResNet", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='resnet152', pretrained=True)", "api_arguments": [{"name": "pretrained", "type": "bool", "default": "False", "description": "If True, returns a model pre-trained on ImageNet"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'resnet152', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"model": "resnet152", "top-1_error": 21.69, "top-5_error": 5.94}}, "description": "ResNet models are deep residual networks pre-trained on ImageNet. They were proposed in the paper 'Deep Residual Learning for Image Recognition'. Available model variants include ResNet18, ResNet34, ResNet50, ResNet101, and ResNet152."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "ResNext", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='resnext50_32x4d', pretrained=True)", "api_arguments": {"pretrained": "True"}, "python_environment_requirements": ["torch", "torchvision", "PIL"], "example_code": ["import torch", "from PIL import Image", "from torchvision import transforms", "model = torch.hub.load('pytorch/vision:v0.10.0', 'resnext50_32x4d', pretrained=True)", "model.eval()", "input_image = Image.open('dog.jpg')", "preprocess = transforms.Compose([", " transforms.Resize(256),", " transforms.CenterCrop(224),", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)", "probabilities = torch.nn.functional.softmax(output[0], dim=0)"], "performance": {"dataset": "ImageNet", "accuracy": {"resnext50_32x4d": {"top-1": 22.38, "top-5": 6.3}}}, "description": "ResNext is a next-generation ResNet architecture for image classification. It is more efficient and accurate than the original ResNet. This implementation includes two versions of the model, resnext50_32x4d and resnext101_32x8d, with 50 and 101 layers respectively."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "ResNext", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='resnext101_32x4d', pretrained=True)", "api_arguments": {"pretrained": "True"}, "python_environment_requirements": ["torch", "torchvision", "PIL"], "example_code": ["import torch", "from PIL import Image", "from torchvision import transforms", "model = torch.hub.load('pytorch/vision:v0.10.0', 'resnext101_32x4d', pretrained=True)", "model.eval()", "input_image = Image.open('dog.jpg')", "preprocess = transforms.Compose([", " transforms.Resize(256),", " transforms.CenterCrop(224),", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)", "probabilities = torch.nn.functional.softmax(output[0], dim=0)"], "performance": {"dataset": "ImageNet", "resnext101_32x8d": {"top-1": 20.69, "top-5": 5.47}}, "description": "ResNext is a next-generation ResNet architecture for image classification. It is more efficient and accurate than the original ResNet. This implementation includes two versions of the model, resnext50_32x4d and resnext101_32x8d, with 50 and 101 layers respectively."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "SNNMLP", "api_call": "torch.hub.load(repo_or_dir='huawei-noah/Efficient-AI-Backbones', model='snnmlp_t', pretrained=True)", "api_arguments": [{"name": "snnmlp_t", "type": "str", "description": "SNNMLP Tiny model"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "model = torch.hub.load('huawei-noah/Efficient-AI-Backbones', 'snnmlp_t', pretrained=True)", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "preprocess = transforms.Compose([", " transforms.Resize(256),", " transforms.CenterCrop(224),", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)", "print(torch.nn.functional.softmax(output[0], dim=0))"], "performance": {"dataset": "ImageNet", "accuracy": {"model": "SNNMLP Tiny", "top-1": 81.88}}, "description": "SNNMLP incorporates the mechanism of LIF neurons into the MLP models, to achieve better accuracy without extra FLOPs. We propose a full-precision LIF operation to communicate between patches, including horizontal LIF and vertical LIF in different directions. We also propose to use group LIF to extract better local features. With LIF modules, our SNNMLP model achieves 81.9%, 83.3% and 83.6% top-1 accuracy on ImageNet dataset with only 4.4G, 8.5G and 15.2G FLOPs, respectively."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "SNNMLP", "api_call": "torch.hub.load(repo_or_dir='huawei-noah/Efficient-AI-Backbones', model='snnmlp_s', pretrained=True)", "api_arguments": [{"name": "snnmlp_s", "type": "str", "description": "SNNMLP Small model"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "model = torch.hub.load('huawei-noah/Efficient-AI-Backbones', 'snnmlp_s', pretrained=True)", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "preprocess = transforms.Compose([", " transforms.Resize(256),", " transforms.CenterCrop(224),", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)", "print(torch.nn.functional.softmax(output[0], dim=0))"], "performance": {"dataset": "ImageNet", "accuracy": {"model": "SNNMLP Small", "top-1": 83.3}}, "description": "SNNMLP incorporates the mechanism of LIF neurons into the MLP models, to achieve better accuracy without extra FLOPs. We propose a full-precision LIF operation to communicate between patches, including horizontal LIF and vertical LIF in different directions. We also propose to use group LIF to extract better local features. With LIF modules, our SNNMLP model achieves 81.9%, 83.3% and 83.6% top-1 accuracy on ImageNet dataset with only 4.4G, 8.5G and 15.2G FLOPs, respectively."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "SNNMLP", "api_call": "torch.hub.load(repo_or_dir='huawei-noah/Efficient-AI-Backbones', model='snnmlp_b', pretrained=True)", "api_arguments": [{"name": "snnmlp_b", "type": "str", "description": "SNNMLP Base model"}], "python_environment_requirements": ["torch", "torchvision", "PIL", "urllib"], "example_code": ["import torch", "model = torch.hub.load('huawei-noah/Efficient-AI-Backbones', 'snnmlp_b', pretrained=True)", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "preprocess = transforms.Compose([", " transforms.Resize(256),", " transforms.CenterCrop(224),", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)", "print(torch.nn.functional.softmax(output[0], dim=0))"], "performance": {"dataset": "ImageNet", "accuracy": {"model": "SNNMLP Base", "top-1": 85.59}}, "description": "SNNMLP incorporates the mechanism of LIF neurons into the MLP models, to achieve better accuracy without extra FLOPs. We propose a full-precision LIF operation to communicate between patches, including horizontal LIF and vertical LIF in different directions. We also propose to use group LIF to extract better local features. With LIF modules, our SNNMLP model achieves 81.9%, 83.3% and 83.6% top-1 accuracy on ImageNet dataset with only 4.4G, 8.5G and 15.2G FLOPs, respectively."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "SqueezeNet", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='squeezenet1_0', pretrained=True)", "api_arguments": {"version": "v0.10.0", "model": ["squeezenet1_0"], "pretrained": "True"}, "python_environment_requirements": {"torch": ">=1.9.0", "torchvision": ">=0.10.0"}, "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'squeezenet1_0', pretrained=True)", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "preprocess = transforms.Compose([", " transforms.Resize(256),", " transforms.CenterCrop(224),", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)", "probabilities = torch.nn.functional.softmax(output[0], dim=0)", "print(probabilities)"], "performance": {"dataset": "ImageNet", "accuracy": {"squeezenet1_0": {"Top-1 error": 41.9, "Top-5 error": 19.58}}}, "description": "SqueezeNet is an image classification model that achieves AlexNet-level accuracy with 50x fewer parameters. It has two versions: squeezenet1_0 and squeezenet1_1, with squeezenet1_1 having 2.4x less computation and slightly fewer parameters than squeezenet1_0, without sacrificing accuracy."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "SqueezeNet", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='squeezenet1_1', pretrained=True)", "api_arguments": {"version": "v0.10.0", "model": ["squeezenet1_1"], "pretrained": "True"}, "python_environment_requirements": {"torch": ">=1.9.0", "torchvision": ">=0.10.0"}, "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'squeezenet1_1', pretrained=True)", "model.eval()", "from PIL import Image", "from torchvision import transforms", "input_image = Image.open(filename)", "preprocess = transforms.Compose([", " transforms.Resize(256),", " transforms.CenterCrop(224),", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)", "if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)", "probabilities = torch.nn.functional.softmax(output[0], dim=0)", "print(probabilities)"], "performance": {"dataset": "ImageNet", "accuracy": {"squeezenet1_1": {"Top-1 error": 41.81, "Top-5 error": 19.38}}}, "description": "SqueezeNet is an image classification model that achieves AlexNet-level accuracy with 50x fewer parameters. It has two versions: squeezenet1_0 and squeezenet1_1, with squeezenet1_1 having 2.4x less computation and slightly fewer parameters than squeezenet1_0, without sacrificing accuracy."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "ShuffleNet v2", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='shufflenet_v2_x1_0', pretrained=True)", "api_arguments": {"pretrained": "True"}, "python_environment_requirements": {"torch": "torch", "torchvision": "torchvision", "PIL": "Image", "urllib": "urllib"}, "example_code": {"import_libraries": ["import torch", "from PIL import Image", "from torchvision import transforms", "import urllib"], "load_model": ["model = torch.hub.load('pytorch/vision:v0.10.0', 'shufflenet_v2_x1_0', pretrained=True)", "model.eval()"], "load_image": ["url, filename = ('https://github.com/pytorch/hub/raw/master/images/dog.jpg', 'dog.jpg')", "try: urllib.URLopener().retrieve(url, filename)", "except: urllib.request.urlretrieve(url, filename)", "input_image = Image.open(filename)"], "preprocess_image": ["preprocess = transforms.Compose([", " transforms.Resize(256),", " transforms.CenterCrop(224),", " transforms.ToTensor(),", " transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),", "])", "input_tensor = preprocess(input_image)", "input_batch = input_tensor.unsqueeze(0)"], "run_inference": ["if torch.cuda.is_available():", " input_batch = input_batch.to('cuda')", " model.to('cuda')", "with torch.no_grad():", " output = model(input_batch)"], "get_probabilities": ["probabilities = torch.nn.functional.softmax(output[0], dim=0)"], "top_categories": ["top5_prob, top5_catid = torch.topk(probabilities, 5)", "for i in range(top5_prob.size(0)):", " print(categories[top5_catid[i]], top5_prob[i].item())"]}, "performance": {"dataset": "Imagenet", "accuracy": {"top-1_error": 30.64, "top-5_error": 11.68}}, "description": "ShuffleNet V2 is an efficient ConvNet optimized for speed and memory, pre-trained on Imagenet. It is designed based on practical guidelines for efficient network design, including speed and accuracy tradeoff."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Recognition", "api_name": "vgg-nets", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='vgg11', pretrained=True)", "api_arguments": [{"name": "vgg11", "type": "str", "description": "VGG11 model"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'vgg11', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"vgg11": {"Top-1 error": 30.98, "Top-5 error": 11.37}}}, "description": "vgg-nets are award-winning ConvNets from the 2014 Imagenet ILSVRC challenge. They are used for large-scale image recognition tasks. The available models are vgg11, vgg11_bn, vgg13, vgg13_bn, vgg16, vgg16_bn, vgg19, and vgg19_bn."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Recognition", "api_name": "vgg-nets", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='vgg11_bn', pretrained=True)", "api_arguments": [{"name": "vgg11_bn", "type": "str", "description": "VGG11 model with batch normalization"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'vgg11_bn', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"vgg11_bn": {"Top-1 error": 26.7, "Top-5 error": 8.58}}}, "description": "vgg-nets are award-winning ConvNets from the 2014 Imagenet ILSVRC challenge. They are used for large-scale image recognition tasks. The available models are vgg11, vgg11_bn, vgg13, vgg13_bn, vgg16, vgg16_bn, vgg19, and vgg19_bn."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Recognition", "api_name": "vgg-nets", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='vgg13', pretrained=True)", "api_arguments": [{"name": "vgg13", "type": "str", "description": "VGG13 model"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'vgg13', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"vgg13": {"Top-1 error": 30.07, "Top-5 error": 10.75}}}, "description": "vgg-nets are award-winning ConvNets from the 2014 Imagenet ILSVRC challenge. They are used for large-scale image recognition tasks. The available models are vgg11, vgg11_bn, vgg13, vgg13_bn,vgg16, vgg16_bn, vgg19, and vgg19_bn."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Recognition", "api_name": "vgg-nets", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='vgg13_bn', pretrained=True)", "api_arguments": [{"name": "vgg13_bn", "type": "str", "description": "VGG13 model with batch normalization"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'vgg13_bn', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"vgg13_bn": {"Top-1 error": 28.45, "Top-5 error": 9.63}}}, "description": "vgg-nets are award-winning ConvNets from the 2014 Imagenet ILSVRC challenge. They are used for large-scale image recognition tasks. The available models are vgg11, vgg11_bn, vgg13, vgg13_bn, vgg16, vgg16_bn, vgg19, and vgg19_bn."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Recognition", "api_name": "vgg-nets", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='vgg16', pretrained=True)", "api_arguments": [{"name": "vgg16", "type": "str", "description": "VGG16 model"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'vgg16', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"vgg16": {"Top-1 error": 28.41, "Top-5 error": 9.62}}}, "description": "vgg-nets are award-winning ConvNets from the 2014 Imagenet ILSVRC challenge. They are used for large-scale image recognition tasks. The available models are vgg11, vgg11_bn, vgg13, vgg13_bn, vgg16, vgg16_bn, vgg19, and vgg19_bn."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Recognition", "api_name": "vgg-nets", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='vgg16_bn', pretrained=True)", "api_arguments": [{"name": "vgg16_bn", "type": "str", "description": "VGG16 model with batch normalization"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'vgg16_bn', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"vgg16_bn": {"Top-1 error": 26.63, "Top-5 error": 8.5}}}, "description": "vgg-nets are award-winning ConvNets from the 2014 Imagenet ILSVRC challenge. They are used for large-scale image recognition tasks. The available models are vgg11, vgg11_bn, vgg13, vgg13_bn, vgg16, vgg16_bn, vgg19, and vgg19_bn."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Recognition", "api_name": "vgg-nets", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='vgg19', pretrained=True)", "api_arguments": [{"name": "vgg19", "type": "str", "description": "VGG19 model"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'vgg19', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"vgg19": {"Top-1 error": 27.62, "Top-5 error": 9.12}}}, "description": "vgg-nets are award-winning ConvNets from the 2014 Imagenet ILSVRC challenge. They are used for large-scale image recognition tasks. The available models are vgg11, vgg11_bn, vgg13, vgg13_bn, vgg16, vgg16_bn, vgg19, and vgg19_bn."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Image Recognition", "api_name": "vgg-nets", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='vgg19_bn', pretrained=True)", "api_arguments": [{"name": "vgg19_bn", "type": "str", "description": "VGG19 model with batch normalization"}], "python_environment_requirements": ["torch", "torchvision"], "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'vgg19_bn', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"vgg19_bn": {"Top-1 error": 25.76, "Top-5 error": 8.15}}}, "description": "vgg-nets are award-winning ConvNets from the 2014 Imagenet ILSVRC challenge. They are used for large-scale image recognition tasks. The available models are vgg11, vgg11_bn, vgg13, vgg13_bn, vgg16, vgg16_bn, vgg19, and vgg19_bn."}
{"domain": "Classification", "framework": "PyTorch", "functionality": "Wide Residual Networks", "api_name": "wide_resnet50_2", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='wide_resnet50_2', pretrained=True)", "api_arguments": "pretrained", "python_environment_requirements": "torch, torchvision", "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'wide_resnet50_2', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"wide_resnet50_2": {"Top-1 error": 21.49, "Top-5 error": 5.91}}}, "description": "Wide Residual networks simply have increased number of channels compared to ResNet. Otherwise the architecture is the same. Deeper ImageNet models with bottleneck block have increased number of channels in the inner 3x3 convolution. The wide_resnet50_2 and wide_resnet101_2 models were trained in FP16 with mixed precision training using SGD with warm restarts. Checkpoints have weights in half precision (except batch norm) for smaller size, and can be used in FP32 models too."} 
{"domain": "Classification", "framework": "PyTorch", "functionality": "Wide Residual Networks", "api_name": "wide_resnet101_2", "api_call": "torch.hub.load(repo_or_dir='pytorch/vision', model='wide_resnet101_2', pretrained=True)", "api_arguments": "pretrained", "python_environment_requirements": "torch, torchvision", "example_code": ["import torch", "model = torch.hub.load('pytorch/vision:v0.10.0', 'wide_resnet101_2', pretrained=True)", "model.eval()"], "performance": {"dataset": "ImageNet", "accuracy": {"wide_resnet101_2": {"Top-1 error": 21.16, "Top-5 error": 5.72}}}, "description": "Wide Residual networks simply have increased number of channels compared to ResNet. Otherwise the architecture is the same. Deeper ImageNet models with bottleneck block have increased number of channels in the inner 3x3 convolution. The wide_resnet50_2 and wide_resnet101_2 models were trained in FP16 with mixed precision training using SGD with warm restarts. Checkpoints have weights in half precision (except batch norm) for smaller size, and can be used in FP32 models too."} 
{"domain": "Video Classification", "framework": "PyTorchVideo", "functionality": "SlowFast Networks", "api_name": "torch.hub.load", "api_call": "torch.hub.load(repo_or_dir='facebookresearch/pytorchvideo', model='slowfast_r50', pretrained=True)", "api_arguments": {"repository": "facebookresearch/pytorchvideo", "model": "slowfast_r50", "pretrained": "True"}, "python_environment_requirements": ["torch", "torchvision", "pytorchvideo"], "example_code": ["import torch", "model = torch.hub.load('facebookresearch/pytorchvideo', 'slowfast_r50', pretrained=True)", "device = 'cpu'", "model = model.eval()", "model = model.to(device)"], "performance": {"dataset": "Kinetics 400", "accuracy": {"top1": 76.94, "top5": 92.69}, "flops": 65.71, "params": 34.57}, "description": "Slow Fast model architectures are based on the paper 'SlowFast Networks for Video Recognition' by Christoph Feichtenhofer et al. They are pretrained on the Kinetics 400 dataset using the 8x8 setting. This model is capable of classifying video clips into different action categories. It is provided by the FAIR PyTorchVideo library."} 
{ "domain": "Video Classification", "framework": "PyTorchVideo", "functionality": "X3D Networks", "api_name": "torch.hub.load", "api_call": "torch.hub.load(repo_or_dir='facebookresearch/pytorchvideo', model='x3d_s', pretrained=True)", "api_arguments": {"repository": "facebookresearch/pytorchvideo", "model": "x3d_s", "pretrained": "True"}, "python_environment_requirements": ["torch", "torchvision", "pytorchvideo"], "example_code": [ "import torch", "model = torch.hub.load('facebookresearch/pytorchvideo', 'x3d_s', pretrained=True)", "device = 'cpu'", "model = model.eval()", "model = model.to(device)" ], "performance": {"dataset": "Kinetics 400", "accuracy": {"top1": 73.33, "top5": 91.27}, "flops": 2.96, "params": 3.79}, "description": "X3D model architectures are based on the paper 'X3D: Expanding Architectures for Efficient Video Recognition' by Christoph Feichtenhofer. They are pretrained on the Kinetics 400 dataset. This model is capable of classifying video clips into different action categories. It is provided by the FAIR PyTorchVideo library." } 
{ "domain": "Classification", "framework": "PyTorch", "functionality": "GPUNet Networks", "api_name": "torch.hub.load", "api_call": "torch.hub.load(repo_or_dir='NVIDIA/DeepLearningExamples:torchhub', model='nvidia_gpunet', pretrained=True, model_type=model_type, model_math=precision)", "api_arguments": { "repository": "NVIDIA/DeepLearningExamples:torchhub", "model": "nvidia_gpunet", "pretrained": "True", "model_type": "GPUNet-0", "model_math": "fp32" }, "python_environment_requirements": ["torch", "validators", "matplotlib", "timm==0.5.4"], "example_code": [ "import torch", "model_type = 'GPUNet-0'", "precision = 'fp32'", "gpunet = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_gpunet', pretrained=True, model_type=model_type, model_math=precision)", "device = torch.device('cuda') if torch.cuda.is_available() else torch.device('cpu')", "gpunet.to(device)", "gpunet.eval()" ], "performance": { "dataset": "IMAGENET", "description": "GPUNet demonstrates state-of-the-art inference performance up to 2x faster than EfficientNet-X and FBNet-V3." }, "description": "GPUNet is a family of Convolutional Neural Networks designed by NVIDIA using novel Neural Architecture Search (NAS) methods. They are optimized for NVIDIA GPU and TensorRT performance. GPUNet models are pretrained on the IMAGENET dataset and are capable of classifying images into different categories. The models are provided by the NVIDIA Deep Learning Examples library." } 
{ "domain": "Classification", "framework": "PyTorch", "functionality": "Once-for-all (OFA) Networks", "api_name": "torch.hub.load", "api_call": "torch.hub.load(repo_or_dir='mit-han-lab/once-for-all', model='ofa_supernet_mbv3_w10', pretrained=True)", "api_arguments": {"repository": "mit-han-lab/once-for-all", "model": "ofa_supernet_mbv3_w10", "pretrained": "True"}, "python_environment_requirements": ["torch", "torchvision"], "example_code": [ "import torch", "super_net_name = 'ofa_supernet_mbv3_w10'", "super_net = torch.hub.load('mit-han-lab/once-for-all', super_net_name, pretrained=True).eval()" ], "performance": { "description": "OFA networks outperform state-of-the-art NAS methods (up to 4.0% ImageNet top1 accuracy improvement over MobileNetV3, or same accuracy but 1.5x faster than MobileNetV3, 2.6x faster than EfficientNet w.r.t measured latency) while reducing many orders of magnitude GPU hours and CO2 emission." }, "description": "Once-for-all (OFA) networks are a family of neural networks designed by MIT Han Lab. They decouple training and search, achieving efficient inference across various edge devices and resource constraints. OFA networks are pretrained on the IMAGENET dataset and are capable of classifying images into different categories." } 
{ "domain": "Classification", "framework": "PyTorch", "functionality": "Semi-supervised and semi-weakly supervised ImageNet Models", "api_name": "torch.hub.load", "api_call": "torch.hub.load(repo_or_dir='facebookresearch/semi-supervised-ImageNet1K-models', model='resnet18_swsl', pretrained=True)", "api_arguments": {"repository": "facebookresearch/semi-supervised-ImageNet1K-models", "model": "resnet18_swsl", "pretrained": "True"}, "python_environment_requirements": ["torch", "torchvision"], "example_code": [ "import torch", "model = torch.hub.load('facebookresearch/semi-supervised-ImageNet1K-models', 'resnet18_swsl', pretrained=True)", "model.eval()" ], "performance": { "description": "Semi-supervised and semi-weakly supervised ImageNet models achieve state-of-the-art accuracy of 81.2% on ImageNet for the widely used/adopted ResNet-50 model architecture." }, "description": "Semi-supervised and semi-weakly supervised ImageNet Models are introduced in the 'Billion scale semi-supervised learning for image classification' paper. These models are pretrained on a subset of unlabeled YFCC100M public image dataset and fine-tuned with the ImageNet1K training dataset. They are capable of classifying images into different categories and are provided by the Facebook Research library." } 
{ "domain": "Classification", "framework": "PyTorch", "functionality": "Natural Language Processing", "api_name": "PyTorch-Transformers", "api_call": "torch.hub.load(repo_or_dir='huggingface/pytorch-transformers')", "api_arguments": ["pretrained_model_or_path", "output_attention", "output_hidden_states", "config", "from_tf"], "python_environment_requirements": ["tqdm", "boto3", "requests", "regex", "sentencepiece", "sacremoses"], "example_code": "import torch\ntokenizer = torch.hub.load('huggingface/pytorch-transformers', 'tokenizer', 'bert-base-cased')\n\ntext_1 = \"Jim Henson was a puppeteer\"\ntext_2 = \"Who was Jim Henson ?\"\n\nindexed_tokens = tokenizer.encode(text_1, text_2, add_special_tokens=True)\n\nmodel = torch.hub.load('huggingface/pytorch-transformers', 'model', 'bert-base-cased')\n\nwith torch.no_grad():\n encoded_layers, _ = model(tokens_tensor, token_type_ids=segments_tensors)", "performance": {"dataset": [{"name": "MRPC", "accuracy": "Not provided"}]}, "description": "PyTorch-Transformers is a library of state-of-the-art pre-trained models for Natural Language Processing (NLP) including BERT, GPT, GPT-2, Transformer-XL, XLNet, XLM, RoBERTa, and DistilBERT. The library provides functionality for tokenization, configuration, and various model architectures for different tasks such as causal language modeling, sequence classification, question answering, and masked language modeling." } 
{ "domain": "Semantic Segmentation", "framework": "PyTorch", "functionality": "Neural Machine Translation", "api_name": "Transformer (NMT)", "api_call": "torch.hub.load(repo_or_dir='pytorch/fairseq')", "api_arguments": ["model_name", "tokenizer", "bpe", "beam", "sampling", "sampling_topk"], "python_environment_requirements": ["bitarray", "fastBPE", "hydra-core", "omegaconf", "regex", "requests", "sacremoses", "subword_nmt"], "example_code": "import torch\n\nen2fr = torch.hub.load('pytorch/fairseq', 'transformer.wmt14.en-fr', tokenizer='moses', bpe='subword_nmt')\n\nen2fr.cuda()\n\nfr = en2fr.translate('Hello world!', beam=5)\nassert fr == 'Bonjour à tous !'", "performance": { "dataset": [ {"name": "WMT'14", "accuracy": "Not provided"}, {"name": "WMT'18", "accuracy": "Not provided"}, {"name": "WMT'19", "accuracy": "Not provided"} ] }, "description": "Transformer (NMT) is a powerful sequence-to-sequence modeling architecture that produces state-of-the-art neural machine translation systems. It is based on the paper 'Attention Is All You Need' and has been improved using techniques such as large-scale semi-supervised training, back-translation, and noisy-channel reranking. It supports English-French and English-German translation as well as round-trip translation for paraphrasing."} 
{ "domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "EfficientNet", "api_call": "torch.hub.load(repo_or_dir='NVIDIA/DeepLearningExamples:torchhub', model='nvidia_efficientnet_b0', pretrained=True)", "api_arguments": ["model_name", "pretrained"], "python_environment_requirements": ["validators", "matplotlib"], "example_code": "import torch\n\nefficientnet = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_efficientnet_b0', pretrained=True)\nutils = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_convnets_processing_utils')\n\nefficientnet.eval().to(device)\n\nbatch = torch.cat([utils.prepare_input_from_uri(uri) for uri in uris]).to(device)\n\nwith torch.no_grad():\n output = torch.nn.functional.softmax(efficientnet(batch), dim=1)\n \nresults = utils.pick_n_best(predictions=output, n=5)", "performance": {"dataset": {"name": "IMAGENET", "accuracy": "Not provided"}}, "description": "EfficientNet is a family of image classification models that achieve state-of-the-art accuracy while being smaller and faster. The models are trained with mixed precision using Tensor Cores on the NVIDIA Volta and Ampere GPU architectures. The EfficientNet models include EfficientNet-B0, EfficientNet-B4, EfficientNet-WideSE-B0, and EfficientNet-WideSE-B4. The WideSE models use wider Squeeze-and-Excitation layers than the original EfficientNet models, resulting in slightly better accuracy." } 
{ "domain": "Object Detection", "framework": "PyTorch", "functionality": "Single Shot MultiBox Detector", "api_name": "SSD", "api_call": "torch.hub.load(repo_or_dir='NVIDIA/DeepLearningExamples:torchhub', model='nvidia_ssd', pretrained=True)", "api_arguments": ["model_name"], "python_environment_requirements": ["numpy", "scipy", "scikit-image", "matplotlib"], "example_code": "import torch\n\nssd_model = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_ssd')\nutils = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_ssd_processing_utils')\n\nssd_model.to('cuda')\nssd_model.eval()\n\ninputs = [utils.prepare_input(uri) for uri in uris]\ntensor = utils.prepare_tensor(inputs)\n\nwith torch.no_grad():\n detections_batch = ssd_model(tensor)\n\nresults_per_input = utils.decode_results(detections_batch)\nbest_results_per_input = [utils.pick_best(results, 0.40) for results in results_per_input]", "performance": {"dataset": {"name": "COCO", "accuracy": "Not provided"}}, "description": "The SSD (Single Shot MultiBox Detector) model is an object detection model based on the paper 'SSD: Single Shot MultiBox Detector'. It uses a deep neural network for detecting objects in images. This implementation replaces the obsolete VGG model backbone with the more modern ResNet-50 model. The SSD model is trained on the COCO dataset and can be used to detect objects in images with high accuracy and efficiency." } 
{ "domain": "Classification", "framework": "PyTorch", "functionality": "Text-to-Speech", "api_name": "Tacotron 2", "api_call": "torch.hub.load(repo_or_dir='NVIDIA/DeepLearningExamples:torchhub', model='nvidia_tacotron2', model_math='fp16')", "api_arguments": {"model_math": "fp16"}, "python_environment_requirements": ["numpy", "scipy", "librosa", "unidecode", "inflect", "libsndfile1"], "example_code": [ "import torch", "tacotron2 = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_tacotron2', model_math='fp16')", "tacotron2 = tacotron2.to('cuda')", "tacotron2.eval()", "text = 'Hello world, I missed you so much.'", "utils = torch.hub.load('NVIDIA/DeepLearningExamples:torchhub', 'nvidia_tts_utils')", "sequences, lengths = utils.prepare_input_sequence([text])", "with torch.no_grad():", " mel, _, _ = tacotron2.infer(sequences, lengths)", " audio = waveglow.infer(mel)", "audio_numpy = audio[0].data.cpu().numpy()", "rate = 22050" ], "performance": {"dataset": "LJ Speech", "accuracy": "Not specified"}, "description": "The Tacotron 2 model generates mel spectrograms from input text using an encoder-decoder architecture, and it is designed for generating natural-sounding speech from raw transcripts without any additional prosody information. This implementation uses Dropout instead of Zoneout to regularize the LSTM layers. The WaveGlow model (also available via torch.hub) is a flow-based model that consumes the mel spectrograms to generate speech." } 
{ "domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "MEAL_V2", "api_call": "torch.hub.load(repo_or_dir='szq0214/MEAL-V1', model='mealv1_resnest50', pretrained=True)", "api_arguments": { "model_name": "mealv1_resnest50" }, "python_environment_requirements": "!pip install timm", "example_code": "import torch\nfrom PIL import Image\nfrom torchvision import transforms\n\nmodel = torch.hub.load('szq0214/MEAL-V2','meal_v2', 'mealv2_resnest50_cutmix', pretrained=True)\nmodel.eval()\n\ninput_image = Image.open('dog.jpg')\npreprocess = transforms.Compose([\n transforms.Resize(256),\n transforms.CenterCrop(224),\n transforms.ToTensor(),\n transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\n])\ninput_tensor = preprocess(input_image)\ninput_batch = input_tensor.unsqueeze(0)\n\nif torch.cuda.is_available():\n input_batch = input_batch.to('cuda')\n model.to('cuda')\n\nwith torch.no_grad():\n output = model(input_batch)\nprobabilities = torch.nn.functional.softmax(output[0], dim=0)\nprint(probabilities)", "performance": [ { "dataset": "ImageNet", "accuracy": { "model": "MEAL-V1 w/ ResNet50", "resolution": "224", "parameters": "25.6M", "top1": "78.21", "top5": "94.01" } } ], "description": "MEAL V2 models are from the MEAL V2: Boosting Vanilla ResNet-50 to 80%+ Top-1 Accuracy on ImageNet without Tricks paper. The method is based on ensemble knowledge distillation via discriminators, and it achieves state-of-the-art results without using common tricks such as architecture modification, outside training data, autoaug/randaug, cosine learning rate, mixup/cutmix training, or label smoothing." }
{ "domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "MEAL_V2", "api_call": "torch.hub.load(repo_or_dir='szq0214/MEAL-V2', 'meal_v2', model='mealv2_resnest50', pretrained=True)", "api_arguments": { "model_name": "mealv2_resnest50" }, "python_environment_requirements": "!pip install timm", "example_code": "import torch\nfrom PIL import Image\nfrom torchvision import transforms\n\nmodel = torch.hub.load('szq0214/MEAL-V2','meal_v2', 'mealv2_resnest50_cutmix', pretrained=True)\nmodel.eval()\n\ninput_image = Image.open('dog.jpg')\npreprocess = transforms.Compose([\n transforms.Resize(256),\n transforms.CenterCrop(224),\n transforms.ToTensor(),\n transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\n])\ninput_tensor = preprocess(input_image)\ninput_batch = input_tensor.unsqueeze(0)\n\nif torch.cuda.is_available():\n input_batch = input_batch.to('cuda')\n model.to('cuda')\n\nwith torch.no_grad():\n output = model(input_batch)\nprobabilities = torch.nn.functional.softmax(output[0], dim=0)\nprint(probabilities)", "performance": [ { "dataset": "ImageNet", "accuracy": { "model": "MEAL-V2 w/ ResNet50", "resolution": "224", "parameters": "25.6M", "top1": "80.67", "top5": "95.09" } } ], "description": "MEAL V2 models are from the MEAL V2: Boosting Vanilla ResNet-50 to 80%+ Top-1 Accuracy on ImageNet without Tricks paper. The method is based on ensemble knowledge distillation via discriminators, and it achieves state-of-the-art results without using common tricks such as architecture modification, outside training data, autoaug/randaug, cosine learning rate, mixup/cutmix training, or label smoothing." }
{ "domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "MEAL_V2", "api_call": "torch.hub.load(repo_or_dir='szq0214/MEAL-V2', 'meal_v2', model='mealv2_resnest50_cutmix', pretrained=True)", "api_arguments": { "model_name": "mealv2_resnest50_cutmix" }, "python_environment_requirements": "!pip install timm", "example_code": "import torch\nfrom PIL import Image\nfrom torchvision import transforms\n\nmodel = torch.hub.load('szq0214/MEAL-V2','meal_v2', 'mealv2_resnest50_cutmix', pretrained=True)\nmodel.eval()\n\ninput_image = Image.open('dog.jpg')\npreprocess = transforms.Compose([\n transforms.Resize(256),\n transforms.CenterCrop(224),\n transforms.ToTensor(),\n transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\n])\ninput_tensor = preprocess(input_image)\ninput_batch = input_tensor.unsqueeze(0)\n\nif torch.cuda.is_available():\n input_batch = input_batch.to('cuda')\n model.to('cuda')\n\nwith torch.no_grad():\n output = model(input_batch)\nprobabilities = torch.nn.functional.softmax(output[0], dim=0)\nprint(probabilities)", "performance": [ { "dataset": "ImageNet", "accuracy": { "model": "MEAL-V2 + CutMix w/ ResNet50", "resolution": "224", "parameters": "25.6M", "top1": "80.98", "top5": "95.35" } } ], "description": "MEAL V2 models are from the MEAL V2: Boosting Vanilla ResNet-50 to 80%+ Top-1 Accuracy on ImageNet without Tricks paper. The method is based on ensemble knowledge distillation via discriminators, and it achieves state-of-the-art results without using common tricks such as architecture modification, outside training data, autoaug/randaug, cosine learning rate, mixup/cutmix training, or label smoothing." }
{ "domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "MEAL_V2", "api_call": "torch.hub.load(repo_or_dir='szq0214/MEAL-V2', 'meal_v2', model='mealv2_resnest50_380x380', pretrained=True)", "api_arguments": { "model_name": "mealv2_resnest50_380x380" }, "python_environment_requirements": "!pip install timm", "example_code": "import torch\nfrom PIL import Image\nfrom torchvision import transforms\n\nmodel = torch.hub.load('szq0214/MEAL-V2','meal_v2', 'mealv2_resnest50_cutmix', pretrained=True)\nmodel.eval()\n\ninput_image = Image.open('dog.jpg')\npreprocess = transforms.Compose([\n transforms.Resize(256),\n transforms.CenterCrop(224),\n transforms.ToTensor(),\n transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\n])\ninput_tensor = preprocess(input_image)\ninput_batch = input_tensor.unsqueeze(0)\n\nif torch.cuda.is_available():\n input_batch = input_batch.to('cuda')\n model.to('cuda')\n\nwith torch.no_grad():\n output = model(input_batch)\nprobabilities = torch.nn.functional.softmax(output[0], dim=0)\nprint(probabilities)", "performance": [ { "dataset": "ImageNet", "accuracy": { "model": "MEAL-V2 w/ ResNet50", "resolution": "380", "parameters": "25.6M", "top1": "81.72", "top5": "95.81" } } ], "description": "MEAL V2 models are from the MEAL V2: Boosting Vanilla ResNet-50 to 80%+ Top-1 Accuracy on ImageNet without Tricks paper. The method is based on ensemble knowledge distillation via discriminators, and it achieves state-of-the-art results without using common tricks such as architecture modification, outside training data, autoaug/randaug, cosine learning rate, mixup/cutmix training, or label smoothing." }
{ "domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "MEAL_V2", "api_call": "torch.hub.load(repo_or_dir='szq0214/MEAL-V2', 'meal_v2', model='mealv2_mobilenetv3_small_075', pretrained=True)", "api_arguments": { "model_name": "mealv2_mobilenetv3_small_075" }, "python_environment_requirements": "!pip install timm", "example_code": "import torch\nfrom PIL import Image\nfrom torchvision import transforms\n\nmodel = torch.hub.load('szq0214/MEAL-V2','meal_v2', 'mealv2_resnest50_cutmix', pretrained=True)\nmodel.eval()\n\ninput_image = Image.open('dog.jpg')\npreprocess = transforms.Compose([\n transforms.Resize(256),\n transforms.CenterCrop(224),\n transforms.ToTensor(),\n transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\n])\ninput_tensor = preprocess(input_image)\ninput_batch = input_tensor.unsqueeze(0)\n\nif torch.cuda.is_available():\n input_batch = input_batch.to('cuda')\n model.to('cuda')\n\nwith torch.no_grad():\n output = model(input_batch)\nprobabilities = torch.nn.functional.softmax(output[0], dim=0)\nprint(probabilities)", "performance": [ { "dataset": "ImageNet", "accuracy": { "model": "MEAL-V2 w/ MobileNet V3-Small 0.75", "resolution": "224", "parameters": "2.04M", "top1": "67.60", "top5": "87.23" } } ], "description": "MEAL V2 models are from the MEAL V2: Boosting Vanilla ResNet-50 to 80%+ Top-1 Accuracy on ImageNet without Tricks paper. The method is based on ensemble knowledge distillation via discriminators, and it achieves state-of-the-art results without using common tricks such as architecture modification, outside training data, autoaug/randaug, cosine learning rate, mixup/cutmix training, or label smoothing." }
{ "domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "MEAL_V2", "api_call": "torch.hub.load(repo_or_dir='szq0214/MEAL-V2', 'meal_v2', model='mealv2_mobilenetv3_small_100', pretrained=True)", "api_arguments": { "model_name": "mealv2_mobilenetv3_small_100" }, "python_environment_requirements": "!pip install timm", "example_code": "import torch\nfrom PIL import Image\nfrom torchvision import transforms\n\nmodel = torch.hub.load('szq0214/MEAL-V2','meal_v2', 'mealv2_resnest50_cutmix', pretrained=True)\nmodel.eval()\n\ninput_image = Image.open('dog.jpg')\npreprocess = transforms.Compose([\n transforms.Resize(256),\n transforms.CenterCrop(224),\n transforms.ToTensor(),\n transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\n])\ninput_tensor = preprocess(input_image)\ninput_batch = input_tensor.unsqueeze(0)\n\nif torch.cuda.is_available():\n input_batch = input_batch.to('cuda')\n model.to('cuda')\n\nwith torch.no_grad():\n output = model(input_batch)\nprobabilities = torch.nn.functional.softmax(output[0], dim=0)\nprint(probabilities)", "performance": [ { "dataset": "ImageNet", "accuracy": { "model": "MEAL-V2 w/ MobileNet V3-Small 1.0", "resolution": "224", "parameters": "2.54M", "top1": "69.65", "top5": "88.71" } } ], "description": "MEAL V2 models are from the MEAL V2: Boosting Vanilla ResNet-50 to 80%+ Top-1 Accuracy on ImageNet without Tricks paper. The method is based on ensemble knowledge distillation via discriminators, and it achieves state-of-the-art results without using common tricks such as architecture modification, outside training data, autoaug/randaug, cosine learning rate, mixup/cutmix training, or label smoothing." }
{ "domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "MEAL_V2", "api_call": "torch.hub.load(repo_or_dir='szq0214/MEAL-V2', 'meal_v2', model='mealv2_mobilenet_v3_large_100', pretrained=True)", "api_arguments": { "model_name": "mealv2_mobilenet_v3_large_100" }, "python_environment_requirements": "!pip install timm", "example_code": "import torch\nfrom PIL import Image\nfrom torchvision import transforms\n\nmodel = torch.hub.load('szq0214/MEAL-V2','meal_v2', 'mealv2_resnest50_cutmix', pretrained=True)\nmodel.eval()\n\ninput_image = Image.open('dog.jpg')\npreprocess = transforms.Compose([\n transforms.Resize(256),\n transforms.CenterCrop(224),\n transforms.ToTensor(),\n transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\n])\ninput_tensor = preprocess(input_image)\ninput_batch = input_tensor.unsqueeze(0)\n\nif torch.cuda.is_available():\n input_batch = input_batch.to('cuda')\n model.to('cuda')\n\nwith torch.no_grad():\n output = model(input_batch)\nprobabilities = torch.nn.functional.softmax(output[0], dim=0)\nprint(probabilities)", "performance": [ { "dataset": "ImageNet", "accuracy": { "model": "MEAL-V2 w/ MobileNet V3-Large 1.0", "resolution": "224", "parameters": "5.48M", "top1": "76.92", "top5": "93.32" } } ], "description": "MEAL V2 models are from the MEAL V2: Boosting Vanilla ResNet-50 to 80%+ Top-1 Accuracy on ImageNet without Tricks paper. The method is based on ensemble knowledge distillation via discriminators, and it achieves state-of-the-art results without using common tricks such as architecture modification, outside training data, autoaug/randaug, cosine learning rate, mixup/cutmix training, or label smoothing." }
{ "domain": "Classification", "framework": "PyTorch", "functionality": "Image Classification", "api_name": "MEAL_V2", "api_call": "torch.hub.load(repo_or_dir='szq0214/MEAL-V2', 'meal_v2', model='mealv2_efficientnet_b0', pretrained=True)", "api_arguments": { "model_name": "mealv2_efficientnet_b0" }, "python_environment_requirements": "!pip install timm", "example_code": "import torch\nfrom PIL import Image\nfrom torchvision import transforms\n\nmodel = torch.hub.load('szq0214/MEAL-V2','meal_v2', 'mealv2_resnest50_cutmix', pretrained=True)\nmodel.eval()\n\ninput_image = Image.open('dog.jpg')\npreprocess = transforms.Compose([\n transforms.Resize(256),\n transforms.CenterCrop(224),\n transforms.ToTensor(),\n transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),\n])\ninput_tensor = preprocess(input_image)\ninput_batch = input_tensor.unsqueeze(0)\n\nif torch.cuda.is_available():\n input_batch = input_batch.to('cuda')\n model.to('cuda')\n\nwith torch.no_grad():\n output = model(input_batch)\nprobabilities = torch.nn.functional.softmax(output[0], dim=0)\nprint(probabilities)", "performance": [ { "dataset": "ImageNet", "accuracy": { "model": "MEAL-V2 w/ EfficientNet-B0", "resolution": "224", "parameters": "5.29M", "top1": "78.29", "top5": "93.95" } } ], "description": "MEAL V2 models are from the MEAL V2: Boosting Vanilla ResNet-50 to 80%+ Top-1 Accuracy on ImageNet without Tricks paper. The method is based on ensemble knowledge distillation via discriminators, and it achieves state-of-the-art results without using common tricks such as architecture modification, outside training data, autoaug/randaug, cosine learning rate, mixup/cutmix training, or label smoothing." }
