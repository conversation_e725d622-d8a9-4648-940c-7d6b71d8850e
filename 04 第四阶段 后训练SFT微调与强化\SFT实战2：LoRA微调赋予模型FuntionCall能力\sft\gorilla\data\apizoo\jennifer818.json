[{"user_name": "jennifer818", "api_name": "Notion - Append block children", "api_call": "notion.blocks.children.append({block_id={blockId}, children={[children], after={after}}})", "api_version": "2022-06-28", "api_arguments": {"blockID": " string (Identifier for a block. Also accepts a page ID.)", "children": " list (Child content to append to a container block as an array of block objects.)", "after": " string (Optional. The ID of the existing block that the new block should be appended after.)"}, "functionality": "Creates and appends new children blocks to the parent block_id specified.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const blockId = 'b55c9c91-384d-452b-81db-d1ef79372b75'; \n const response = await notion.blocks.children.append({ \n block_id: blockId, \n children: [{\"heading_2\": {\"rich_text\": [{\"text\": {\"content\": \"Lacinato kale\"}}]}},{\"paragraph\": {\"rich_text\": [{ \n \"text\": { \n \"content\": \"Lacinato kale is a variety of kale with a long tradition in Italian cuisine, especially that of Tuscany. It is also known as Tuscan kale, Italian kale, dinosaur kale, kale, flat back kale, palm tree kale, or black Tuscan palm.\", \n \"link\": { \n \"url\": \"https://en.wikipedia.org/wiki/Lacinato_kale\" \n }}}]}}],}); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/patch-block-children"}, "questions": ["I want to add a bolded paragraph on the importance of recycling in my paper."]}, {"user_name": "jennifer818", "api_name": "Notion - Retrieve a block", "api_call": "notion.blocks.retrieve({block_id={blockID}})", "api_version": "2022-06-28", "api_arguments": {"block_id": " string (Identifier for a Notion block.)"}, "functionality": "Retrieves a Block object using the ID specified.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const blockId = 'c02fc1d3-db8b-45c5-a222-27595b15aea7'; \n const response = await notion.blocks.retrieve({ \n block_id: blockId, \n }); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/retrieve-a-block"}, "questions": ["I want to retrieve my to-do checklist under today's date."]}, {"user_name": "jennifer818", "api_name": "Notion - Retrieve block children", "api_call": "notion.blocks.children.list({block_id={blockId}, start_cursor={startCursor}, page_size={pageSize}})", "api_version": "2022-06-28", "api_arguments": {"blockID": " string (Identifier for a block)", "startCursor": " string (Optional. Page of results starts after the cursor provided)", "pageSize": " int32 (Optional. The number of items from the full list desired in the response. Maximum: 100)"}, "functionality": "Returns a paginated array of child block objects contained in the block using the ID specified.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const blockId = '59833787-2cf9-4fdf-8782-e53db20768a5'; \n const response = await notion.blocks.children.list({ \n block_id: blockId, \n page_size: 50, \n }); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/get-block-children"}, "questions": ["I want to retrieve all the fruit in my grocery shopping list."]}, {"user_name": "jennifer818", "api_name": "Notion - Update a block", "api_call": "notion.blocks.update({block_id={blockID}, block={updatedBlock}, archived={archive}})", "api_version": "2022-06-28", "api_arguments": {"blockID": " string (Identifier for a Notion block)", "updatedBlock": " block (Block that will replace old block)", "archive": " boolean (Optional. Set to true to archive (delete) a block. Set to false to un-archive (restore) a block.)"}, "functionality": "Updates the content for the specified block_id based on the block type. Supported fields based on the block object type.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const blockId = '9bc30ad4-9373-46a5-84ab-0a7845ee52e6'; \n const response = await notion.blocks.update({ \n \"block_id\": blockId, \"heading_2\": {\"rich_text\": [{\"text\": {\"content\": \"Lacinato kale\"},\"annotations\": {\"color\": \"green\"}}]}}); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/update-a-block"}, "questions": ["I want to update my plans for today to include more self care."]}, {"user_name": "jennifer818", "api_name": "Notion - Delete a block", "api_call": "await notion.blocks.delete({block_id={blockId},});", "api_version": "2022-06-28", "api_arguments": {"blockID": " string (Identifier for a Notion block)"}, "functionality": "Sets a Block object, including page blocks, to archived: true using the ID specified.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const blockId = '7985540b-2e77-4ac6-8615-c3047e36f872'; \n const response = await notion.blocks.delete({ \n block_id: blockId, \n }); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/delete-a-block"}, "questions": ["Some of the notes are outdated and they should be deleted."]}, {"user_name": "jennifer818", "api_name": "Notion - Create a page", "api_call": "notion.pages.create({parent={parent}, properties={properties}, children={children}, icon={icon}, cover={cover}})", "api_version": "2022-06-28", "api_arguments": {"parent": " json (the parent page or database where the new page is inserted)", " properties": " json (The values of the page’s properties.)", " children": " array of strings (Optional. The content to be rendered on the new Page.)", " icon": " json (The icon of the new page. Either an emoji object or an external file object.)", " cover": " json (The cover image of the new page.)"}, "functionality": "Creates a new page that is a child of an existing page or database.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const response = await notion.pages.create({ \n \"cover\": { \n \"type\": \"external\", \n \"external\": { \n \"url\": \"https://upload.wikimedia.org/wikipedia/commons/6/62/Tuscankale.jpg\"}}, \n \"icon\": { \n \"type\": \"emoji\", \n \"emoji\": \"🥬\"}, \n \"parent\": { \n \"type\": \"database_id\", \n \"database_id\": \"d9824bdc-8445-4327-be8b-5b47500af6ce\"}, \n \"properties\": { \n \"Name\": { \n \"title\": [ \n { \n \"text\": { \n \"content\": \"Tuscan kale\"}}]}, \n \"Description\": { \n \"rich_text\": [ \n { \n \"text\": { \n \"content\": \"A dark green leafy vegetable\"}}]}, \n \"Food group\": { \n \"select\": { \n \"name\": \"🥬 Vegetable\"}}}, \n \"children\": [ \n { \n \"object\": \"block\", \n \"heading_2\": { \n \"rich_text\": [ \n { \n \"text\": { \n \"content\": \"Lacinato kale\"}}]}}, \n { \n \"object\": \"block\", \n \"paragraph\": { \n \"rich_text\": [ \n { \n \"text\": { \n \"content\": \"Lacinato kale is a variety of kale with a long tradition in Italian cuisine, especially that of Tuscany. It is also known as Tuscan kale, Italian kale, dinosaur kale, kale, flat back kale, palm tree kale, or black Tuscan palm.\", \n \"link\": { \n \"url\": \"https://en.wikipedia.org/wiki/Lacinato_kale\" \n } \n }, \n \"href\": \"https://en.wikipedia.org/wiki/Lacinato_kale\"}], \n \"color\": \"default\"}}] \n }); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/post-page"}, "questions": ["There should be a page on the rules for Capture the Flag in the game book for field day."]}, {"user_name": "jennifer818", "api_name": "Notion - Retrieve a page", "api_call": "notion.pages.retrieve({page_id={pageId}, filter_properties={filterProperties}});", "api_version": "2022-06-28", "api_arguments": {"pageID": " string (Identifier for a Notion page)"}, "functionality": "filterProperties, string (Optional. A list of page property value IDs associated with the page. Use this param to limit the response to a specific page property value or values.)", "env_requirements": ["@notionhq/client"], "example_code": "Retrieves a Page object (page properties) using the ID specified. \n const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const pageId = '59833787-2cf9-4fdf-8782-e53db20768a5'; \n const response = await notion.pages.retrieve({ page_id: pageId }); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/retrieve-a-page"}, "questions": ["Select the list of groceries for tomorrow."]}, {"user_name": "jennifer818", "api_name": "Notion - Retrieve a page property item", "api_call": "notion.pages.properties.retrieve({page_id={pageId}, property_id={propertyId}, page_size={pageSize}, start_cursor={startCursor}});", "api_version": "2022-06-28", "api_arguments": {"pageID": " string (Identifier for a Notion page)", " propertyID": " string (Identifier for a page property)", " pageSize": " int32 (Optional. For paginated properties. The max number of property item objects on a page. The default size is 100)", " startCursor": " string (Optional. For paginated properties.)"}, "functionality": "Retrieves a property_item object for a given page_id and property_id.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const pageId = 'b55c9c91-384d-452b-81db-d1ef79372b75'; \n const propertyId = \"aBcD123 \n const response = await notion.pages.properties.retrieve({ page_id: pageId, property_id: propertyId }); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/retrieve-a-page-property"}, "questions": ["I want a list of titles for all the stories in the database about Fairytales."]}, {"user_name": "jennifer818", "api_name": "Notion - Update page properties", "api_call": "notion.pages.update({page_id={papgeID}, properties={properties}, archived={archived}, icon={icon}, cover={cover}})", "api_version": "2022-06-28", "api_arguments": {"pageID": " string (The identifier for the Notion page to be updated.)", " properties": " json (Optional. The property values to update for the page. The keys are the names or IDs of the property and the values are property values.)", " archived": " boolean (Optional. Whether the page is archived (deleted). Set to true to archive a page. Set to false to un-archive (restore) a page.)", " icon": " json (Optional. A page icon for the page.)", " cover": " json (Optional. A cover image for the page. Only external file objects are supported.)"}, "functionality": "Updates the properties of a page in a database.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const pageId = '59833787-2cf9-4fdf-8782-e53db20768a5'; \n const response = await notion.pages.update({ \n page_id: pageId, \n properties: { \n 'In stock': { \n checkbox: true, \n }, \n }, \n }); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/patch-page"}, "questions": ["I want to retitle my notes for tomorrow's meeting."]}, {"user_name": "jennifer818", "api_name": "Notion - Create a database", "api_call": "notion.databases.create({parent={parent}, title={title}, properties={properties}})", "api_version": "2022-06-28", "api_arguments": {"parent": " json (A page parent)", " title": " array (Optional. Title of database as it appears in Notion.)", " properties": " json (Property schema of database.)"}, "functionality": "Creates a database as a subpage in the specified parent page, with the specified properties schema.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const response = await notion.databases.create({ \n parent: { \n type: \"page_id\", \n page_id: \"98ad959b-2b6a-4774-80ee-00246fb0ea9b\", \n }, \n icon: { \n type: \"emoji\", \n emoji: \"📝\", \n }, \n title: [ \n { \n type: \"text\", \n text: { \n content: \"Grocery List\", \n link: null, \n }, \n }, \n ], \n properties: { \n Name: { \n title: {}, \n }, \n Description: { \n rich_text: {}, \n }, \n \"In stock\": { \n checkbox: {}, \n }, \n \"Food group\": { \n select: { \n options: [ \n { \n name: \"Vegetable\", \n color: \"green\", \n }, \n { \n name: \"Fruit\", \n color: \"red\", \n }, \n { \n name: \"Protein\", \n color: \"yellow\",},],},},}, \n }); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/create-a-database"}, "questions": ["I want to keep track of all the fun events that were put on last year with attendance, budget, and ratings."]}, {"user_name": "jennifer818", "api_name": "Notion - Filter database entries", "api_call": "notion.databases.query({database_id={databaseID}, filer={filter}})", "api_version": "2022-06-28", "api_arguments": {"databaseID": " string (The name of the property as it appears in the database or propertyID)", " filter": " object (The type-specific filter condition for the query.)"}, "functionality": "When you query a database, you can send a filter object in the body of the request that limits the returned entries based on the specified criteria.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n const databaseId = 'd9824bdc-8445-4327-be8b-5b47500af6ce'; \n const filteredRows = async () => { \n const response = await notion.databases.query({ \n database_id: databaseId, \n filter: { \n property: \"Task completed\", \n checkbox: { \n equals: true \n } \n }, \n }); \n return response; \n } \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/post-database-query-filter"}, "questions": ["I only want to look at projects due before 2025"]}, {"user_name": "jennifer818", "api_name": "Notion - Sort database entries", "api_call": "notion.databases.query({database_id={databaseID}, sorts={sortObject}})", "api_version": "2022-06-28", "api_arguments": {"databaseID": " string (The name of the property as it appears in the database or propertyID)", " sortObject": " (object) (Property or timestamp to sort by)"}, "functionality": "A sort is a condition used to order the entries returned from a database query.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n const databaseId = 'd9824bdc-8445-4327-be8b-5b47500af6ce'; \n const sortedRows = async () => { \n const response = await notion.databases.query({ \n database_id: databaseId, \n sorts: [ \n { \n property: \"Name\", \n direction: \"ascending\" \n } \n ], \n }); \n return response; \n } \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/post-database-query-sort"}, "questions": ["I want to read all the information on the attendees of the events from year in alphabetical order."]}, {"user_name": "jennifer818", "api_name": "Notion - Query a database", "api_call": "await notion.databases.query({database_id={databaseID}, filter_properties={filterProperties}}, filter={filter}, sorts={sortCriteria}, page_size={pageSize})", "api_version": "2022-06-28", "api_arguments": {"databaseID": " string (The name of the property as it appears in the database or propertyID)", " filterProperties": " array of strings (Optional. A list of page property value IDs associated with the database.)", " filter": " json (Optional. Limits which pages are returned based on the filter conditions.)", " sortCriteria": " array (Optional. Orders the results based on the provided sort criteria.)", " startCursor": " string (Optional. Returns a page of results starting after the cursor provided)", " pageSize": " int32 (The number of items from the full list desired in the response. Maximum: 100)"}, "functionality": "Gets a list of Pages and/or Databases contained in the database, filtered and ordered according to the filter conditions and sort criteria provided in the request.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const databaseId = 'd9824bdc-8445-4327-be8b-5b47500af6ce'; \n const response = await notion.databases.query({ \n database_id: databaseId, \n filter: { \n or: [ \n { \n property: 'In stock', \n checkbox: { \n equals: true, \n }, \n }, \n { \n property: 'Cost of next trip', \n number: { \n greater_than_or_equal_to: 2, \n }, \n }, \n ], \n }, \n sorts: [ \n { \n property: 'Last ordered', \n direction: 'ascending', \n }, \n ], \n }); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/post-database-query"}, "questions": ["I want to find all the items in the store that have a stock greater than 30 items."]}, {"user_name": "jennifer818", "api_name": "Notion - Retrieve a database", "api_call": "notion.databases.retrieve({database_id={databaseId}})", "api_version": "2022-06-28", "api_arguments": {"databaseID": " string (The name of the property as it appears in the database or propertyID)"}, "functionality": "Retrieves a database object — information that describes the structure and columns of a database — for a provided database ID.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const databaseId = '668d797c-76fa-4934-9b05-ad288df2d136'; \n const response = await notion.databases.retrieve({ database_id: databaseId }); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/retrieve-a-database"}, "questions": ["I want to know what information the database on our inventory stores."]}, {"user_name": "jennifer818", "api_name": "Notion - Update a database", "api_call": "notion.databases.update({database_id={databaseID}, title={title}, description={description}, properties={json}})", "api_version": "2022-06-28", "api_arguments": {"databaseID": " string (identifier for a Notion database)", " title": " array (Optional. An array of rich text objects that represents the title of the database that is displayed in the Notion UI.)", " description": " array (Optional. An array of rich text objects that represents the description of the database that is displayed in the Notion UI.)", " properties": " json (Optional. The properties of a database to be changed in the request in the form of a JSON object.)"}, "functionality": "Updates the database object — the title, description, or properties — of a specified database.", "env_requirements": ["@notionhq/client"], "example_code": "const options = { \n method: 'PATCH', \n headers: { \n accept: 'application/json', \n 'Notion-Version': '2022-06-28', \n 'content-type': 'application/json' \n } \n }; \n fetch('https://api.notion.com/v1/databases/database_id', options) \n .then(response => response.json()) \n .then(response => console.log(response)) \n .catch(err => console.error(err)); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/update-a-database"}, "questions": ["I want to change the documented creator of the inventory database"]}, {"user_name": "jennifer818", "api_name": "Notion - List all users", "api_call": "notion.users.list();", "api_version": "2022-06-28", "api_arguments": {"start_cursor": " string (Optional. Returns a page of results starting after the cursor provided. Default: this endpoint will return the first page of results.)", " page_size": " (Optional. The number of items from the full list desired in the response. Maximum: 100.)"}, "functionality": "Returns a paginated list of Users for the workspace. The response may contain fewer than page_size of results.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const response = await notion.users.list(); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/get-users"}, "questions": ["I want to see everyone who is using the Notion workspace."]}, {"user_name": "jennifer818", "api_name": "Notion - Retrieve a user", "api_call": "notion.users.retrieve({ user_id: {userId}});", "api_version": "2022-06-28", "api_arguments": {"userID": " string (Identifier for a Notion user)"}, "functionality": "Retrieves a User using the ID specified.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const userId = 'd40e767c-d7af-4b18-a86d-55c61f1e39a4'; \n const response = await notion.users.retrieve({ user_id: userId }); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/get-user"}, "questions": ["I need all the information of <PERSON>, the user who creates the schedules for all the workers."]}, {"user_name": "jennifer818", "api_name": "Notion - Retrieve your token's bot user", "api_call": "notion.users.me();", "api_version": "2022-06-28", "api_arguments": {}, "functionality": "Retrieves the bot User associated with the API token provided in the authorization header. The bot will have an owner field with information about the person who authorized the integration.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const response = await notion.users.me(); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/get-self"}, "questions": ["Give me all the information on the bot that signs documents."]}, {"user_name": "jennifer818", "api_name": "Notion - Create comment", "api_call": "notion.comments.create({parent = {pageParent}, discussion_id = {discID}, rich_text = {comment}});", "api_version": "2022-06-28", "api_arguments": {"pageParent": " json (A page parent. Either this or a discussion_id is required but not both.)", " discID": " string (A UUID identifier for a discussion thread. Either this or a parent object is required (not both))", " comment": " json (A rich text object)"}, "functionality": "Creates a comment in a page or existing discussion thread. Returns a comment object for the created comment.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const response = await notion.comments.create({ \n \"parent\": { \n \"page_id\": \"5c6a28216bb14a7eb6e1c50111515c3d\" \n }, \n \"rich_text\": [ \n { \n \"text\": { \n \"content\": \"Hello world\" \n } \n } \n ] \n }); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/create-a-comment"}, "questions": ["Add a comment to next week's schedule about pushing back the 1pm meeting back by an hour."]}, {"user_name": "jennifer818", "api_name": "Notion - Retrieve comments", "api_call": "notion.comments.list({ block_id={blockId}, start_cursor={startCursor}, page_size={pageSize} })", "api_version": "2022-06-28", "api_arguments": {"blockID": " string (Identifier for a Notion block or page)", " startCursor": " string (Optional. Returns a page of results starting after the cursor provided. If not supplied this endpoint will return the first page of results.)", " pageSize": " int32 (Optional. The number of items from the full list desired in the response. Maximum: 100)"}, "functionality": "Retrieves a list of un-resolved Comment objects from a page or block.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const blockId = 'd40e767c-d7af-4b18-a86d-55c61f1e39a4'; \n const response = await notion.comments.list({ block_id: blockId }); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/retrieve-a-comment"}, "questions": ["I want to look through all the comments about the grocery inventory."]}, {"user_name": "jennifer818", "api_name": "Notion - Search by title", "api_call": "notion.search({query = {query}, filter = {filter}, sort = {sort}, start_cursor = {startCursor}, page_size = {pageSize}})", "api_version": "2022-06-28", "api_arguments": {"query": " string (The text that the API compares page and database titles against.)", " sort": " object (Optional. A set of criteria (direction and timestamp keys) that orders the results. The only supported timestamp value is \"last_edited_time\". Supported direction values are \"ascending\" and \"descending\".)", " filter": " object (Optional. A set of criteria (value and property keys) that limits the results to either only pages or only databases. Possible value values are \"page\" or \"database\". The only supported property value is \"object\".)", " startCursor": " string (Optional. Limits the response to results starting after the cursor)", " pageSize": " int32 (Optional. The number of items from the full list to include in the response. Maximum: 100.)"}, "functionality": "Searches all parent or child pages and databases that have been shared with an integration. Returns all pages or databases, excluding duplicated linked databases, that have titles that include the query param. If no query param is provided, then the response contains all pages or databases that have been shared with the integration.", "env_requirements": ["@notionhq/client"], "example_code": "const { Client } = require('@notionhq/client'); \n const notion = new Client({ auth: process.env.NOTION_API_KEY }); \n (async () => { \n const response = await notion.search({ \n query: 'External tasks', \n filter: { \n value: 'database', \n property: 'object' \n }, \n sort: { \n direction: 'ascending', \n timestamp: 'last_edited_time' \n }, \n }); \n console.log(response); \n })(); \n ", "meta_data": {"documentation_link": " https://developers.notion.com/reference/post-search"}, "questions": ["I need to look at all relevant documents and databases related to worker schedules on the 21st."]}]