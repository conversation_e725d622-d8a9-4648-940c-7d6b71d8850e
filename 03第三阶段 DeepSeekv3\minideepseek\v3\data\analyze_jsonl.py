import json
import re
import numpy as np
import os
import sys
from collections import Counter
from tqdm import tqdm
from multiprocessing import Pool, cpu_count

# **获取用户输入的 JSONL 目录**
if len(sys.argv) < 2:
    print("❌ 请输入 JSONL 文件所在的目录，如：")
    print("   python analyze_jsonl.py /path/to/jsonl/files")
    sys.exit(1)

jsonl_dir = sys.argv[1]

# **检查目录是否存在**
if not os.path.isdir(jsonl_dir):
    print(f"❌ 目录 {jsonl_dir} 不存在！请检查路径是否正确。")
    sys.exit(1)

# **获取 JSONL 文件列表**
jsonl_files = [os.path.join(jsonl_dir, f) for f in os.listdir(jsonl_dir) if f.endswith(".jsonl")]

if not jsonl_files:
    print(f"❌ 目录 {jsonl_dir} 中没有找到 JSONL 文件！")
    sys.exit(1)

print(f"📂 发现 {len(jsonl_files)} 个 JSONL 文件，正在分析...\n")

# **统计函数**
def process_jsonl_file(file_path):
    """处理一个 JSONL 文件，返回统计数据"""
    char_lengths = []
    word_counts = []
    avg_line_lengths = []
    max_line_lengths = []
    char_repetition_rates = []
    word_repetition_rates = []
    special_char_ratios = []

    file_name = os.path.basename(file_path)
    print(f"\n🚀 开始处理: {file_name}")

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # **使用 tqdm 在文件内部显示进度条**
        for line in tqdm(lines, desc=f"📊 处理中: {file_name}", unit="行"):
            try:
                data = json.loads(line)
                text = data.get("text", "").strip()

                if not text:
                    continue  # 跳过空文本

                # **文本总字符数**
                char_length = len(text)
                char_lengths.append(char_length)

                # **单词数**
                words = re.findall(r'\w+', text)
                word_count = len(words)
                word_counts.append(word_count)

                # **行级统计**
                lines = text.split("\n")
                line_lengths = [len(line) for line in lines if line.strip()]
                
                if line_lengths:
                    avg_line_lengths.append(np.mean(line_lengths))  # **平均行长**
                    max_line_lengths.append(np.max(line_lengths))  # **最长行**

                # **字符重复率**
                char_counter = Counter(text)
                total_chars = sum(char_counter.values())
                if total_chars > 0:
                    most_common_char_freq = max(char_counter.values()) / total_chars
                    char_repetition_rates.append(most_common_char_freq)

                # **单词重复率**
                word_counter = Counter(words)
                total_words = sum(word_counter.values())
                if total_words > 0:
                    most_common_word_freq = max(word_counter.values()) / total_words
                    word_repetition_rates.append(most_common_word_freq)

                # **特殊字符比例**
                special_chars = re.findall(r'[^\w\s]', text)
                special_char_ratio = len(special_chars) / char_length if char_length > 0 else 0
                special_char_ratios.append(special_char_ratio)

            except json.JSONDecodeError:
                print(f"⚠️  {file_name} 中有无法解析的 JSON 行，已跳过。")

    except Exception as e:
        print(f"❌  处理 {file_name} 时发生错误: {e}")

    print(f"✅  处理完成: {file_name}")

    # **统计信息**
    def get_stats(name, data):
        if data:
            return {
                "平均值": round(np.mean(data), 2),
                "中位数": round(np.median(data), 2),
                "最小值": round(np.min(data), 2),
                "最大值": round(np.max(data), 2),
                "95% 分位数": round(np.percentile(data, 95), 2),
            }
        return None

    file_stats = {
        "文件名": file_name,
        "文本字符数 (text_length_filter)": get_stats("文本字符数", char_lengths),
        "单词数 (words_num_filter)": get_stats("单词数", word_counts),
        "平均行长度 (average_line_length_filter)": get_stats("平均行长", avg_line_lengths),
        "最长行长度 (maximum_line_length_filter)": get_stats("最长行长", max_line_lengths),
        "字符重复率 (character_repetition_filter)": get_stats("字符重复率", char_repetition_rates),
        "单词重复率 (word_repetition_filter)": get_stats("单词重复率", word_repetition_rates),
        "特殊字符比例 (special_characters_filter)": get_stats("特殊字符比例", special_char_ratios),
    }

    # **立即打印当前文件的统计结果**
    print(f"\n📊 **{file_stats['文件名']}** 统计结果:")
    for key, value in file_stats.items():
        if key != "文件名" and value:
            print(f"  {key}: {value}")

    return file_stats

# **并行处理 JSONL 文件**
cpu_cores = max(1, cpu_count() - 1)  # 留一个 CPU 核心空闲
print(f"🚀 使用 {cpu_cores} 核心并行处理数据...\n")

with Pool(cpu_cores) as pool:
    for result in pool.imap_unordered(process_jsonl_file, jsonl_files):
        pass  # 处理结果已经在 `process_jsonl_file` 内部打印，不需要额外存储或处理

print("\n✅ 所有文件统计完成！🎉")