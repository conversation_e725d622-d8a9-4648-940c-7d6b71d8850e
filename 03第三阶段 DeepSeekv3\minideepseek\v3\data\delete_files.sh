# 设置目标目录路径
TARGET_DIR="/root/autodl-tmp/minideepseek/v3/data/deep_clean/"

# 定义不同数据集的删除比例
declare -A DELETE_PERCENTAGE
DELETE_PERCENTAGE["djed_starcoder"]=75  # 只保留 25%
DELETE_PERCENTAGE["djed_slimpajama"]=75 # 只保留 25%
DELETE_PERCENTAGE["djed_skypile"]=66    # 只保留 33%

# 遍历目标目录下的所有子目录
for dir in "$TARGET_DIR"/*; do
  # 提取目录名
  dataset_name=$(basename "$dir")

  # 如果数据集不在需要删除的列表中，跳过
  if [[ ! -v DELETE_PERCENTAGE["$dataset_name"] ]]; then
    echo "Skipping $dataset_name (no deletion needed)"
    continue
  fi

  # 计算要删除的比例
  PERCENTAGE=${DELETE_PERCENTAGE["$dataset_name"]}
  echo "Processing dataset: $dataset_name (deleting $PERCENTAGE% of files)"

  # 计算要删除的文件数
  total_files=$(find "$dir" -type f -name "*.jsonl" | wc -l)
  num_to_delete=$((total_files * PERCENTAGE / 100))

  # 确保有文件需要删除
  if [ "$num_to_delete" -gt 0 ]; then
    echo "Deleting $num_to_delete files from $dataset_name..."
    
    # 获取所有文件并随机选取需要删除的文件
    find "$dir" -type f -name "*.jsonl" | shuf | head -n "$num_to_delete" | while read -r file; do
      echo "Deleting file: $file"
      rm "$file"
    done
  else
    echo "No files to delete for $dataset_name"
  fi
done

echo "File reduction process complete!"
