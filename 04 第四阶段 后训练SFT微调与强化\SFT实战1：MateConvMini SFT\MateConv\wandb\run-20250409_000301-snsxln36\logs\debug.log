2025-04-09 00:03:01,186 INFO    MainThread:459432 [wandb_setup.py:_flush():67] Current SDK version is 0.19.9
2025-04-09 00:03:01,186 INFO    MainThread:459432 [wandb_setup.py:_flush():67] Configure stats pid to 459432
2025-04-09 00:03:01,186 INFO    MainThread:459432 [wandb_setup.py:_flush():67] Loading settings from /root/.config/wandb/settings
2025-04-09 00:03:01,186 INFO    MainThread:459432 [wandb_setup.py:_flush():67] Loading settings from /root/autodl-tmp/MateConv/wandb/settings
2025-04-09 00:03:01,186 INFO    MainThread:459432 [wandb_setup.py:_flush():67] Loading settings from environment variables
2025-04-09 00:03:01,186 INFO    MainThread:459432 [wandb_init.py:setup_run_log_directory():662] Logging user logs to /root/autodl-tmp/MateConv/wandb/run-20250409_000301-snsxln36/logs/debug.log
2025-04-09 00:03:01,186 INFO    MainThread:459432 [wandb_init.py:setup_run_log_directory():663] Logging internal logs to /root/autodl-tmp/MateConv/wandb/run-20250409_000301-snsxln36/logs/debug-internal.log
2025-04-09 00:03:01,186 INFO    MainThread:459432 [wandb_init.py:init():781] calling init triggers
2025-04-09 00:03:01,186 INFO    MainThread:459432 [wandb_init.py:init():786] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-04-09 00:03:01,187 INFO    MainThread:459432 [wandb_init.py:init():809] starting backend
2025-04-09 00:03:01,187 INFO    MainThread:459432 [wandb_init.py:init():813] sending inform_init request
2025-04-09 00:03:01,192 INFO    MainThread:459432 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-04-09 00:03:01,192 INFO    MainThread:459432 [wandb_init.py:init():823] backend started and connected
2025-04-09 00:03:01,193 INFO    MainThread:459432 [wandb_init.py:init():915] updated telemetry
2025-04-09 00:03:01,194 INFO    MainThread:459432 [wandb_init.py:init():939] communicating run to backend with 90.0 second timeout
2025-04-09 00:03:02,710 INFO    MainThread:459432 [wandb_init.py:init():1014] starting run threads in backend
2025-04-09 00:03:03,026 INFO    MainThread:459432 [wandb_run.py:_console_start():2454] atexit reg
2025-04-09 00:03:03,027 INFO    MainThread:459432 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-04-09 00:03:03,027 INFO    MainThread:459432 [wandb_run.py:_redirect():2371] Wrapping output streams.
2025-04-09 00:03:03,027 INFO    MainThread:459432 [wandb_run.py:_redirect():2394] Redirects installed.
2025-04-09 00:03:03,029 INFO    MainThread:459432 [wandb_init.py:init():1056] run started, returning control to user process
