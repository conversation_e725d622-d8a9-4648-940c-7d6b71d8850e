[{"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - abuseReports.insert", "api_call": "youtube.abuseReports().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - activities.list", "api_call": "youtube.activities().list(part: str).execute()", "api_version": "v3", "api_arguments": {"channelId": "string", "home": "boolean", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "mine": "boolean", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more activity resource properties that the API response will include. If the parameter identifies a property that contains child properties, the child properties will be included in the response. For example, in an activity resource, the snippet property contains other properties that identify the type of activity, a display title for the activity, and so forth. If you set *part=snippet*, the API response will also contain all of those nested properties.", "publishedAfter": "string", "publishedBefore": "string", "regionCode": "string"}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - captions.delete", "api_call": "youtube.captions().delete(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string", "onBehalfOf": "string: ID of the Google+ Page for the channel that the request is be on behalf of", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The actual CMS account that the user authenticates with must be linked to the specified YouTube content owner."}, "functionality": "Deletes a resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - captions.download", "api_call": "youtube.captions().download(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string: The ID of the caption track to download, required for One Platform.", "onBehalfOf": "string: ID of the Google+ Page for the channel that the request is be on behalf of", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The actual CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "tfmt": "string: Convert the captions into this format. Supported options are sbv, srt, and vtt.", "tlang": "string: tlang is the language code; machine translate the captions into this language."}, "functionality": "Downloads a caption track.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - captions.insert", "api_call": "youtube.captions().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOf": "string: ID of the Google+ Page for the channel that the request is be on behalf of", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The actual CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "part": "[REQUIRED] string: The *part* parameter specifies the caption resource parts that the API response will include. Set the parameter value to snippet.", "sync": "boolean: Extra parameter to allow automatically syncing the uploaded caption/transcript with the audio."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - captions.list", "api_call": "youtube.captions().list(part: str, videoId: str).execute()", "api_version": "v3", "api_arguments": {"id": "string: Returns the captions with the given IDs for Stubby or Apiary.", "onBehalfOf": "string: ID of the Google+ Page for the channel that the request is on behalf of.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The actual CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more caption resource parts that the API response will include. The part names that you can include in the parameter value are id and snippet.", "videoId": "[REQUIRED] string: Returns the captions for the specified video."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - captions.update", "api_call": "youtube.captions().update(part: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOf": "string: ID of the Google+ Page for the channel that the request is on behalf of.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The actual CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more caption resource parts that the API response will include. The part names that you can include in the parameter value are id and snippet.", "sync": "boolean: Extra parameter to allow automatically syncing the uploaded caption/transcript with the audio."}, "functionality": "Updates an existing resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - channelBanners.insert", "api_call": "youtube.channelBanners().insert().execute()", "api_version": "v3", "api_arguments": {"channelId": "string: Unused, channel_id is currently derived from the security context of the requestor.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The actual CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - channelSections.delete", "api_call": "youtube.channelSections().delete(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner."}, "functionality": "Deletes a resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - channelSections.insert", "api_call": "youtube.channelSections().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include. The part names that you can include in the parameter value are snippet and contentDetails."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - channelSections.list", "api_call": "youtube.channelSections().list(part: str).execute()", "api_version": "v3", "api_arguments": {"channelId": "string: Return the ChannelSections owned by the specified channel ID.", "hl": "string: Return content in specified language", "id": "string: Return the ChannelSections with the given IDs for Stubby or Apiary.", "mine": "boolean: Return the ChannelSections owned by the authenticated user.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more channelSection resource properties that the API response will include. The part names that you can include in the parameter value are id, snippet, and contentDetails. If the parameter identifies a property that contains child properties, the child properties will be included in the response. For example, in a channelSection resource, the snippet property contains other properties, such as a display title for the channelSection. If you set *part=snippet*, the API response will also contain all of those nested properties."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - channelSections.update", "api_call": "youtube.channelSections().update(part: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include. The part names that you can include in the parameter value are snippet and contentDetails."}, "functionality": "Updates an existing resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - channels.list", "api_call": "youtube.channels().list(part: str).execute()", "api_version": "v3", "api_arguments": {"categoryId": "string: Return the channels within the specified guide category ID.", "forHandle": "string: Return the channel associated with a YouTube handle.", "forUsername": "string: Return the channel associated with a YouTube username.", "hl": "string: Stands for \"host language\". Specifies the localization language of the metadata to be filled into snippet.localized. The field is filled with the default metadata if there is no localization in the specified language. The parameter value must be a language code included in the list returned by the i18nLanguages.list method (e.g. en_US, es_MX).", "id": "string: Return the channels with the specified IDs.", "managedByMe": "boolean: Return the channels managed by the authenticated user.", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "mine": "boolean: Return the ids of channels owned by the authenticated user.", "mySubscribers": "boolean: Return the channels subscribed to the authenticated user", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more channel resource properties that the API response will include. If the parameter identifies a property that contains child properties, the child properties will be included in the response. For example, in a channel resource, the contentDetails property contains other properties, such as the uploads properties. As such, if you set *part=contentDetails*, the API response will also contain all of those nested properties."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - channels.update", "api_call": "youtube.channels().update(part: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: The *onBehalfOfContentOwner* parameter indicates that the authenticated user is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The actual CMS account that the user authenticates with needs to be linked to the specified YouTube content owner.", "part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include. The API currently only allows the parameter value to be set to either brandingSettings or invideoPromotion. (You cannot update both of those parts with a single request.) Note that this method overrides the existing values for all of the mutable properties that are contained in any parts that the parameter value specifies."}, "functionality": "Updates an existing resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - commentThreads.insert", "api_call": "youtube.commentThreads().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"part": "[REQUIRED] string: The *part* parameter identifies the properties that the API response will include. Set the parameter value to snippet. The snippet part has a quota cost of 2 units."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - commentThreads.list", "api_call": "youtube.commentThreads().list(part: str).execute()", "api_version": "v3", "api_arguments": {"allThreadsRelatedToChannelId": "string: Returns the comment threads of all videos of the channel and the channel comments as well.", "channelId": "string: Returns the comment threads for all the channel comments (ie does not include comments left on videos).", "id": "string: Returns the comment threads with the given IDs for Stubby or Apiary.", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "moderationStatus": "string: Limits the returned comment threads to those with the specified moderation status. Not compatible with the 'id' filter. Valid values: published, heldForReview, likelySpam.", "order": "string", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more commentThread resource properties that the API response will include.", "searchTerms": "string: Limits the returned comment threads to those matching the specified key words. Not compatible with the 'id' filter.", "textFormat": "string: The requested text format for the returned comments.", "videoId": "string: Returns the comment threads of the specified video."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - comments.delete", "api_call": "youtube.comments().delete(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string"}, "functionality": "Deletes a resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - comments.insert", "api_call": "youtube.comments().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"part": "[REQUIRED] string: The *part* parameter identifies the properties that the API response will include. Set the parameter value to snippet. The snippet part has a quota cost of 2 units."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - comments.list", "api_call": "youtube.comments().list(part: str).execute()", "api_version": "v3", "api_arguments": {"id": "string: Returns the comments with the given IDs for One Platform.", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved.", "parentId": "string: Returns replies to the specified comment. Note, currently YouTube features only one level of replies (ie replies to top level comments). However replies to replies may be supported in the future.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more comment resource properties that the API response will include.", "textFormat": "string: The requested text format for the returned comments."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - comments.markAsSpam", "api_call": "youtube.comments().markAsSpam(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string: Flags the comments with the given IDs as spam in the caller's opinion."}, "functionality": "Expresses the caller's opinion that one or more comments should be flagged as spam.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - comments.setModerationStatus", "api_call": "youtube.comments().setModerationStatus(id: str, moderationStatus: str).execute()", "api_version": "v3", "api_arguments": {"banAuthor": "boolean: If set to true the author of the comment gets added to the ban list. This means all future comments of the author will autmomatically be rejected. Only valid in combination with STATUS_REJECTED.", "id": "[REQUIRED] string: Modifies the moderation status of the comments with the given IDs", "moderationStatus": "[REQUIRED] string: Specifies the requested moderation status. Note, comments can be in statuses, which are not available through this call. For example, this call does not allow to mark a comment as 'likely spam'. Valid values: 'heldForReview', 'published' or 'rejected'."}, "functionality": "Sets the moderation status of one or more comments.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - comments.update", "api_call": "youtube.comments().update(part: str).execute()", "api_version": "v3", "api_arguments": {"part": "[REQUIRED] string: The *part* parameter identifies the properties that the API response will include. You must at least include the snippet part in the parameter value since that part contains all of the properties that the API request can update."}, "functionality": "Updates an existing resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - i18nLanguages.list", "api_call": "youtube.i18nLanguages().list(part: str).execute()", "api_version": "v3", "api_arguments": {"hl": "string", "part": "[REQUIRED] string: The *part* parameter specifies the i18nLanguage resource properties that the API response will include. Set the parameter value to snippet."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - i18nRegions.list", "api_call": "youtube.i18nRegions().list(part: str).execute()", "api_version": "v3", "api_arguments": {"hl": "string", "part": "[REQUIRED] string: The *part* parameter specifies the i18nRegion resource properties that the API response will include. Set the parameter value to snippet."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveBroadcasts.bind", "api_call": "youtube.liveBroadcasts().bind(id: str, part: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string: Broadcast to bind to the stream", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more liveBroadcast resource properties that the API response will include. The part names that you can include in the parameter value are id, snippet, contentDetails, and status.", "streamId": "string: Stream to bind, if not set unbind the current one."}, "functionality": "Bind a broadcast to a stream.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveBroadcasts.delete", "api_call": "youtube.liveBroadcasts().delete(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string: Broadcast to delete.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel."}, "functionality": "Delete a given broadcast.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveBroadcasts.insert", "api_call": "youtube.liveBroadcasts().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include. The part properties that you can include in the parameter value are id, snippet, contentDetails, and status."}, "functionality": "Inserts a new stream for the authenticated user.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveBroadcasts.insertCuepoint", "api_call": "youtube.liveBroadcasts().insertCuepoint().execute()", "api_version": "v3", "api_arguments": {"id": "string: Broadcast to insert ads to, or equivalently `external_video_id` for internal use.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "part": "string: The *part* parameter specifies a comma-separated list of one or more liveBroadcast resource properties that the API response will include. The part names that you can include in the parameter value are id, snippet, contentDetails, and status."}, "functionality": "Insert cuepoints in a broadcast", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveBroadcasts.list", "api_call": "youtube.liveBroadcasts().list(part: str).execute()", "api_version": "v3", "api_arguments": {"broadcastStatus": "string: Return broadcasts with a certain status, e.g. active broadcasts.", "broadcastType": "string: Return only broadcasts with the selected type.", "id": "string: Return broadcasts with the given ids from Stubby or Apiary.", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "mine": "boolean", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more liveBroadcast resource properties that the API response will include. The part names that you can include in the parameter value are id, snippet, contentDetails, status and statistics."}, "functionality": "Retrieve the list of broadcasts associated with the given channel.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveBroadcasts.transition", "api_call": "youtube.liveBroadcasts().transition(broadcastStatus: str, id: str, part: str).execute()", "api_version": "v3", "api_arguments": {"broadcastStatus": "[REQUIRED] string: The status to which the broadcast is going to transition.", "id": "[REQUIRED] string: Broadcast to transition.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more liveBroadcast resource properties that the API response will include. The part names that you can include in the parameter value are id, snippet, contentDetails, and status."}, "functionality": "Transition a broadcast to a given status.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveBroadcasts.update", "api_call": "youtube.liveBroadcasts().update(part: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include. The part properties that you can include in the parameter value are id, snippet, contentDetails, and status. Note that this method will override the existing values for all of the mutable properties that are contained in any parts that the parameter value specifies. For example, a broadcast's privacy status is defined in the status part. As such, if your request is updating a private or unlisted broadcast, and the request's part parameter value includes the status part, the broadcast's privacy setting will be updated to whatever value the request body specifies. If the request body does not specify a value, the existing privacy setting will be removed and the broadcast will revert to the default privacy setting."}, "functionality": "Updates an existing broadcast for the authenticated user.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveChatBans.delete", "api_call": "youtube.liveChatBans().delete(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string"}, "functionality": "Deletes a chat ban.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveChatBans.insert", "api_call": "youtube.liveChatBans().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response returns. Set the parameter value to snippet."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveChatMessages.delete", "api_call": "youtube.liveChatMessages().delete(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string"}, "functionality": "Deletes a chat message.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveChatMessages.insert", "api_call": "youtube.liveChatMessages().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"part": "[REQUIRED] string: The *part* parameter serves two purposes. It identifies the properties that the write operation will set as well as the properties that the API response will include. Set the parameter value to snippet."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveChatMessages.list", "api_call": "youtube.liveChatMessages().list(liveChatId: str, part: str).execute()", "api_version": "v3", "api_arguments": {"hl": "string: Specifies the localization language in which the system messages should be returned.", "liveChatId": "[REQUIRED] string: The id of the live chat for which comments should be returned.", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken property identify other pages that could be retrieved.", "part": "[REQUIRED] string: The *part* parameter specifies the liveChatComment resource parts that the API response will include. Supported values are id and snippet.", "profileImageSize": "integer: Specifies the size of the profile image that should be returned for each user."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveChatModerators.delete", "api_call": "youtube.liveChatModerators().delete(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string"}, "functionality": "Deletes a chat moderator.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveChatModerators.insert", "api_call": "youtube.liveChatModerators().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response returns. Set the parameter value to snippet."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveChatModerators.list", "api_call": "youtube.liveChatModerators().list(liveChatId: str, part: str).execute()", "api_version": "v3", "api_arguments": {"liveChatId": "[REQUIRED] string: The id of the live chat for which moderators should be returned.", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved.", "part": "[REQUIRED] string: The *part* parameter specifies the liveChatModerator resource parts that the API response will include. Supported values are id and snippet."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveStreams.delete", "api_call": "youtube.liveStreams().delete(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel."}, "functionality": "Deletes an existing stream for the authenticated user.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveStreams.insert", "api_call": "youtube.liveStreams().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include. The part properties that you can include in the parameter value are id, snippet, cdn, content_details, and status."}, "functionality": "Inserts a new stream for the authenticated user.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveStreams.list", "api_call": "youtube.liveStreams().list(part: str).execute()", "api_version": "v3", "api_arguments": {"id": "string: Return LiveStreams with the given ids from Stubby or Apiary.", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "mine": "boolean", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more liveStream resource properties that the API response will include. The part names that you can include in the parameter value are id, snippet, cdn, and status."}, "functionality": "Retrieve the list of streams associated with the given channel. --", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - liveStreams.update", "api_call": "youtube.liveStreams().update(part: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include. The part properties that you can include in the parameter value are id, snippet, cdn, and status. Note that this method will override the existing values for all of the mutable properties that are contained in any parts that the parameter value specifies. If the request body does not specify a value for a mutable property, the existing value for that property will be removed."}, "functionality": "Updates an existing stream for the authenticated user.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - members.list", "api_call": "youtube.members().list(part: str).execute()", "api_version": "v3", "api_arguments": {"filterByMemberChannelId": "string: Comma separated list of channel IDs. Only data about members that are part of this list will be included in the response.", "hasAccessToLevel": "string: Filter members in the results set to the ones that have access to a level.", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "mode": "string: Parameter that specifies which channel members to return.", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved.", "part": "[REQUIRED] string: The *part* parameter specifies the member resource parts that the API response will include. Set the parameter value to snippet."}, "functionality": "Retrieves a list of members that match the request criteria for a channel.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - membershipsLevels.list", "api_call": "youtube.membershipsLevels().list(part: str).execute()", "api_version": "v3", "api_arguments": {"part": "[REQUIRED] string: The *part* parameter specifies the membershipsLevel resource parts that the API response will include. Supported values are id and snippet."}, "functionality": "Retrieves a list of all pricing levels offered by a creator to the fans.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - playlistImages.delete", "api_call": "youtube.playlistImages().delete().execute()", "api_version": "v3", "api_arguments": {"id": "string: Id to identify this image. This is returned from by the List method.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner."}, "functionality": "Deletes a resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - playlistImages.insert", "api_call": "youtube.playlistImages().insert().execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "part": "string: The *part* parameter specifies the properties that the API response will include."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - playlistImages.list", "api_call": "youtube.playlistImages().list().execute()", "api_version": "v3", "api_arguments": {"maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved.", "parent": "string: Return PlaylistImages for this playlist id.", "part": "string: The *part* parameter specifies a comma-separated list of one or more playlistImage resource properties that the API response will include. If the parameter identifies a property that contains child properties, the child properties will be included in the response."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - playlistImages.update", "api_call": "youtube.playlistImages().update().execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "part": "string: The *part* parameter specifies the properties that the API response will include."}, "functionality": "Updates an existing resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - playlistItems.delete", "api_call": "youtube.playlistItems().delete(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner."}, "functionality": "Deletes a resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - playlistItems.insert", "api_call": "youtube.playlistItems().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - playlistItems.list", "api_call": "youtube.playlistItems().list(part: str).execute()", "api_version": "v3", "api_arguments": {"id": "string", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more playlistItem resource properties that the API response will include. If the parameter identifies a property that contains child properties, the child properties will be included in the response. For example, in a playlistItem resource, the snippet property contains numerous fields, including the title, description, position, and resourceId properties. As such, if you set *part=snippet*, the API response will contain all of those properties.", "playlistId": "string: Return the playlist items within the given playlist.", "videoId": "string: Return the playlist items associated with the given video ID."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - playlistItems.update", "api_call": "youtube.playlistItems().update(part: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include. Note that this method will override the existing values for all of the mutable properties that are contained in any parts that the parameter value specifies. For example, a playlist item can specify a start time and end time, which identify the times portion of the video that should play when users watch the video in the playlist. If your request is updating a playlist item that sets these values, and the request's part parameter value includes the contentDetails part, the playlist item's start and end times will be updated to whatever value the request body specifies. If the request body does not specify values, the existing start and end times will be removed and replaced with the default settings."}, "functionality": "Updates an existing resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - playlists.delete", "api_call": "youtube.playlists().delete(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner."}, "functionality": "Deletes a resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - playlists.insert", "api_call": "youtube.playlists().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - playlists.list", "api_call": "youtube.playlists().list(part: str).execute()", "api_version": "v3", "api_arguments": {"channelId": "string: Return the playlists owned by the specified channel ID.", "hl": "string: Return content in specified language", "id": "string: Return the playlists with the given IDs for Stubby or Apiary.", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "mine": "boolean: Return the playlists owned by the authenticated user.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more playlist resource properties that the API response will include. If the parameter identifies a property that contains child properties, the child properties will be included in the response. For example, in a playlist resource, the snippet property contains properties like author, title, description, tags, and timeCreated. As such, if you set *part=snippet*, the API response will contain all of those properties."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - playlists.update", "api_call": "youtube.playlists().update(part: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include. Note that this method will override the existing values for mutable properties that are contained in any parts that the request body specifies. For example, a playlist's description is contained in the snippet part, which must be included in the request body. If the request does not specify a value for the snippet.description property, the playlist's existing description will be deleted."}, "functionality": "Updates an existing resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - search.list", "api_call": "youtube.search().list(part: str).execute()", "api_version": "v3", "api_arguments": {"channelId": "string: Filter on resources belonging to this channelId.", "channelType": "string: Add a filter on the channel search.", "eventType": "string: Filter on the livestream status of the videos.", "forContentOwner": "boolean: Search owned by a content owner.", "forDeveloper": "boolean: Restrict the search to only retrieve videos uploaded using the project id of the authenticated user.", "forMine": "boolean: Search for the private videos of the authenticated user.", "location": "string: Filter on location of the video", "locationRadius": "string: Filter on distance from the location (specified above).", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "order": "string: Sort order of the results.", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more search resource properties that the API response will include. Set the parameter value to snippet.", "publishedAfter": "string: Filter on resources published after this date.", "publishedBefore": "string: Filter on resources published before this date.", "q": "string: Textual search terms to match.", "regionCode": "string: Display the content as seen by viewers in this country.", "relevanceLanguage": "string: Return results relevant to this language.", "safeSearch": "string: Indicates whether the search results should include restricted content as well as standard content.", "topicId": "string: Restrict results to a particular topic.", "type": "string: Restrict results to a particular set of resource types from One Platform.", "videoCaption": "string: Filter on the presence of captions on the videos.", "videoCategoryId": "string: Filter on videos in a specific category.", "videoDefinition": "string: Filter on the definition of the videos.", "videoDimension": "string: Filter on 3d videos.", "videoDuration": "string: Filter on the duration of the videos.", "videoEmbeddable": "string: Filter on embeddable videos.", "videoLicense": "string: Filter on the license of the videos.", "videoPaidProductPlacement": "string", "videoSyndicated": "string: Filter on syndicated videos.", "videoType": "string: Filter on videos of a specific type."}, "functionality": "Retrieves a list of search resources", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - subscriptions.delete", "api_call": "youtube.subscriptions().delete(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string"}, "functionality": "Deletes a resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - subscriptions.insert", "api_call": "youtube.subscriptions().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - subscriptions.list", "api_call": "youtube.subscriptions().list(part: str).execute()", "api_version": "v3", "api_arguments": {"channelId": "string: Return the subscriptions of the given channel owner.", "forChannelId": "string: Return the subscriptions to the subset of these channels that the authenticated user is subscribed to.", "id": "string: Return the subscriptions with the given IDs for Stubby or Apiary.", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "mine": "boolean: Flag for returning the subscriptions of the authenticated user.", "myRecentSubscribers": "boolean", "mySubscribers": "boolean: Return the subscribers of the given channel owner.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "order": "string: The order of the returned subscriptions", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more subscription resource properties that the API response will include. If the parameter identifies a property that contains child properties, the child properties will be included in the response. For example, in a subscription resource, the snippet property contains other properties, such as a display title for the subscription. If you set *part=snippet*, the API response will also contain all of those nested properties."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - superChatEvents.list", "api_call": "youtube.superChatEvents().list(part: str).execute()", "api_version": "v3", "api_arguments": {"hl": "string: Return rendered funding amounts in specified language.", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set.", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved.", "part": "[REQUIRED] string: The *part* parameter specifies the superChatEvent resource parts that the API response will include. This parameter is currently not supported."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - tests.insert", "api_call": "youtube.tests().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"externalChannelId": "string", "part": "[REQUIRED] string"}, "functionality": "POST method.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - thirdPartyLinks.delete", "api_call": "youtube.thirdPartyLinks().delete(linkingToken: str, type: str).execute()", "api_version": "v3", "api_arguments": {"externalChannelId": "string: Channel ID to which changes should be applied, for delegation.", "linkingToken": "[REQUIRED] string: Delete the partner links with the given linking token.", "part": "string: Do not use. Required for compatibility.", "type": "[REQUIRED] string: Type of the link to be deleted."}, "functionality": "Deletes a resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - thirdPartyLinks.insert", "api_call": "youtube.thirdPartyLinks().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"externalChannelId": "string: Channel ID to which changes should be applied, for delegation.", "part": "[REQUIRED] string: The *part* parameter specifies the thirdPartyLink resource parts that the API request and response will include. Supported values are linkingToken, status, and snippet."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - thirdPartyLinks.list", "api_call": "youtube.thirdPartyLinks().list(part: str).execute()", "api_version": "v3", "api_arguments": {"externalChannelId": "string: Channel ID to which changes should be applied, for delegation.", "linkingToken": "string: Get a third party link with the given linking token.", "part": "[REQUIRED] string: The *part* parameter specifies the thirdPartyLink resource parts that the API response will include. Supported values are linkingToken, status, and snippet.", "type": "string: Get a third party link of the given type."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - thirdPartyLinks.update", "api_call": "youtube.thirdPartyLinks().update(part: str).execute()", "api_version": "v3", "api_arguments": {"externalChannelId": "string: Channel ID to which changes should be applied, for delegation.", "part": "[REQUIRED] string: The *part* parameter specifies the thirdPartyLink resource parts that the API request and response will include. Supported values are linkingToken, status, and snippet."}, "functionality": "Updates an existing resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - thumbnails.set", "api_call": "youtube.thumbnails().set(videoId: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The actual CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "videoId": "[REQUIRED] string: Returns the Thumbnail with the given video IDs for Stubby or Apiary."}, "functionality": "As this is not an insert in a strict sense (it supports uploading/setting of a thumbnail for multiple videos, which doesn't result in creation of a single resource), I use a custom verb here.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - videoAbuseReportReasons.list", "api_call": "youtube.videoAbuseReportReasons().list(part: str).execute()", "api_version": "v3", "api_arguments": {"hl": "string", "part": "[REQUIRED] string: The *part* parameter specifies the videoCategory resource parts that the API response will include. Supported values are id and snippet."}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - videoCategories.list", "api_call": "youtube.videoCategories().list(part: str).execute()", "api_version": "v3", "api_arguments": {"hl": "string", "id": "string: Returns the video categories with the given IDs for Stubby or Apiary.", "part": "[REQUIRED] string: The *part* parameter specifies the videoCategory resource properties that the API response will include. Set the parameter value to snippet.", "regionCode": "string"}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - videos.delete", "api_call": "youtube.videos().delete(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The actual CMS account that the user authenticates with must be linked to the specified YouTube content owner."}, "functionality": "Deletes a resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - videos.getRating", "api_call": "youtube.videos().getRating(id: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner."}, "functionality": "Retrieves the ratings that the authorized user gave to a list of specified videos.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - videos.insert", "api_call": "youtube.videos().insert(part: str).execute()", "api_version": "v3", "api_arguments": {"autoLevels": "boolean: Should auto-levels be applied to the upload.", "notifySubscribers": "boolean: Notify the channel subscribers about the new video. As default, the notification is enabled.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "onBehalfOfContentOwnerChannel": "string: This parameter can only be used in a properly authorized request. *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwnerChannel* parameter specifies the YouTube channel ID of the channel to which a video is being added. This parameter is required when a request specifies a value for the onBehalfOfContentOwner parameter, and it can only be used in conjunction with that parameter. In addition, the request must be authorized using a CMS account that is linked to the content owner that the onBehalfOfContentOwner parameter specifies. Finally, the channel that the onBehalfOfContentOwnerChannel parameter value specifies must be linked to the content owner that the onBehalfOfContentOwner parameter specifies. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and perform actions on behalf of the channel specified in the parameter value, without having to provide authentication credentials for each separate channel.", "part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include. Note that not all parts contain properties that can be set when inserting or updating a video. For example, the statistics object encapsulates statistics that YouTube calculates for a video and does not contain values that you can set or modify. If the parameter value specifies a part that does not contain mutable values, that part will still be included in the API response.", "stabilize": "boolean: Should stabilize be applied to the upload."}, "functionality": "Inserts a new resource into this collection.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - videos.list", "api_call": "youtube.videos().list(part: str).execute()", "api_version": "v3", "api_arguments": {"chart": "string: Return the videos that are in the specified chart.", "hl": "string: Stands for \"host language\". Specifies the localization language of the metadata to be filled into snippet.localized. The field is filled with the default metadata if there is no localization in the specified language. The parameter value must be a language code included in the list returned by the i18nLanguages.list method (e.g. en_US, es_MX).", "id": "string: Return videos with the given ids.", "locale": "string", "maxHeight": "integer", "maxResults": "integer: The *maxResults* parameter specifies the maximum number of items that should be returned in the result set. *Note:* This parameter is supported for use in conjunction with the myRating and chart parameters, but it is not supported for use in conjunction with the id parameter.", "maxWidth": "integer: Return the player with maximum height specified in", "myRating": "string: Return videos liked/disliked by the authenticated user. Does not support RateType.RATED_TYPE_NONE.", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "pageToken": "string: The *pageToken* parameter identifies a specific page in the result set that should be returned. In an API response, the nextPageToken and prevPageToken properties identify other pages that could be retrieved. *Note:* This parameter is supported for use in conjunction with the myRating and chart parameters, but it is not supported for use in conjunction with the id parameter.", "part": "[REQUIRED] string: The *part* parameter specifies a comma-separated list of one or more video resource properties that the API response will include. If the parameter identifies a property that contains child properties, the child properties will be included in the response. For example, in a video resource, the snippet property contains the channelId, title, description, tags, and categoryId properties. As such, if you set *part=snippet*, the API response will contain all of those properties.", "regionCode": "string: Use a chart that is specific to the specified region", "videoCategoryId": "string: Use chart that is specific to the specified video category"}, "functionality": "Retrieves a list of resources, possibly filtered.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - videos.rate", "api_call": "youtube.videos().rate(id: str, rating: str).execute()", "api_version": "v3", "api_arguments": {"id": "[REQUIRED] string", "rating": "[REQUIRED] string"}, "functionality": "Adds a like or dislike rating to a video or removes a rating from a video.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - videos.reportAbuse", "api_call": "youtube.videos().reportAbuse().execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner."}, "functionality": "Report abuse for a video.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - videos.update", "api_call": "youtube.videos().update(part: str).execute()", "api_version": "v3", "api_arguments": {"onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The actual CMS account that the user authenticates with must be linked to the specified YouTube content owner.", "part": "[REQUIRED] string: The *part* parameter serves two purposes in this operation. It identifies the properties that the write operation will set as well as the properties that the API response will include. Note that this method will override the existing values for all of the mutable properties that are contained in any parts that the parameter value specifies. For example, a video's privacy setting is contained in the status part. As such, if your request is updating a private video, and the request's part parameter value includes the status part, the video's privacy setting will be updated to whatever value the request body specifies. If the request body does not specify a value, the existing privacy setting will be removed and the video will revert to the default privacy setting. In addition, not all parts contain properties that can be set when inserting or updating a video. For example, the statistics object encapsulates statistics that YouTube calculates for a video and does not contain values that you can set or modify. If the parameter value specifies a part that does not contain mutable values, that part will still be included in the API response."}, "functionality": "Updates an existing resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - watermarks.set", "api_call": "youtube.watermarks().set(channelId: str).execute()", "api_version": "v3", "api_arguments": {"channelId": "[REQUIRED] string", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner."}, "functionality": "Allows upload of watermark image and setting it for a channel.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - watermarks.unset", "api_call": "youtube.watermarks().unset(channelId: str).execute()", "api_version": "v3", "api_arguments": {"channelId": "[REQUIRED] string", "onBehalfOfContentOwner": "string: *Note:* This parameter is intended exclusively for YouTube content partners. The *onBehalfOfContentOwner* parameter indicates that the request's authorization credentials identify a YouTube CMS user who is acting on behalf of the content owner specified in the parameter value. This parameter is intended for YouTube content partners that own and manage many different YouTube channels. It allows content owners to authenticate once and get access to all their video and channel data, without having to provide authentication credentials for each individual channel. The CMS account that the user authenticates with must be linked to the specified YouTube content owner."}, "functionality": "Allows removal of channel watermark.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Youtube API - youtube.v3.updateCommentThreads", "api_call": "youtube.youtube().v3().updateCommentThreads().execute()", "api_version": "v3", "api_arguments": {"part": "string: The *part* parameter specifies a comma-separated list of commentThread resource properties that the API response will include. You must at least include the snippet part in the parameter value since that part contains all of the properties that the API request can update."}, "functionality": "Updates an existing resource.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://developers.google.com/youtube/v3/docs"}}]