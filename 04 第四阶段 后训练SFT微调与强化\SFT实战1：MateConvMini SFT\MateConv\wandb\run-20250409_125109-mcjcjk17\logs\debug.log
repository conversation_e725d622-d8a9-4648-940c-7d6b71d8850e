2025-04-09 12:51:09,129 INFO    MainThread:436765 [wandb_setup.py:_flush():67] Current SDK version is 0.19.9
2025-04-09 12:51:09,130 INFO    MainThread:436765 [wandb_setup.py:_flush():67] Configure stats pid to 436765
2025-04-09 12:51:09,130 INFO    MainThread:436765 [wandb_setup.py:_flush():67] Loading settings from /root/.config/wandb/settings
2025-04-09 12:51:09,130 INFO    MainThread:436765 [wandb_setup.py:_flush():67] Loading settings from /root/autodl-tmp/MateConv/wandb/settings
2025-04-09 12:51:09,130 INFO    MainThread:436765 [wandb_setup.py:_flush():67] Loading settings from environment variables
2025-04-09 12:51:09,130 INFO    MainThread:436765 [wandb_init.py:setup_run_log_directory():662] Logging user logs to /root/autodl-tmp/MateConv/wandb/run-20250409_125109-mcjcjk17/logs/debug.log
2025-04-09 12:51:09,130 INFO    MainThread:436765 [wandb_init.py:setup_run_log_directory():663] Logging internal logs to /root/autodl-tmp/MateConv/wandb/run-20250409_125109-mcjcjk17/logs/debug-internal.log
2025-04-09 12:51:09,130 INFO    MainThread:436765 [wandb_init.py:init():781] calling init triggers
2025-04-09 12:51:09,130 INFO    MainThread:436765 [wandb_init.py:init():786] wandb.init called with sweep_config: {}
config: {'_wandb': {}}
2025-04-09 12:51:09,130 INFO    MainThread:436765 [wandb_init.py:init():809] starting backend
2025-04-09 12:51:09,130 INFO    MainThread:436765 [wandb_init.py:init():813] sending inform_init request
2025-04-09 12:51:09,136 INFO    MainThread:436765 [backend.py:_multiprocessing_setup():101] multiprocessing start_methods=fork,spawn,forkserver, using: spawn
2025-04-09 12:51:09,136 INFO    MainThread:436765 [wandb_init.py:init():823] backend started and connected
2025-04-09 12:51:09,139 INFO    MainThread:436765 [wandb_init.py:init():915] updated telemetry
2025-04-09 12:51:09,140 INFO    MainThread:436765 [wandb_init.py:init():939] communicating run to backend with 90.0 second timeout
2025-04-09 12:51:10,634 INFO    MainThread:436765 [wandb_init.py:init():1014] starting run threads in backend
2025-04-09 12:51:10,905 INFO    MainThread:436765 [wandb_run.py:_console_start():2454] atexit reg
2025-04-09 12:51:10,906 INFO    MainThread:436765 [wandb_run.py:_redirect():2306] redirect: wrap_raw
2025-04-09 12:51:10,906 INFO    MainThread:436765 [wandb_run.py:_redirect():2371] Wrapping output streams.
2025-04-09 12:51:10,906 INFO    MainThread:436765 [wandb_run.py:_redirect():2394] Redirects installed.
2025-04-09 12:51:10,908 INFO    MainThread:436765 [wandb_init.py:init():1056] run started, returning control to user process
2025-04-09 12:51:39,778 INFO    MsgRouterThr:436765 [mailbox.py:close():129] [no run ID] Closing mailbox, abandoning 1 handles.
