{"name": "authenticate_travel", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Authenticate the user with the travel API", "parameters": {"type": "dict", "properties": {"client_id": {"type": "string", "description": "The client applications client_id supplied by App Management"}, "client_secret": {"type": "string", "description": "The client applications client_secret supplied by App Management"}, "refresh_token": {"type": "string", "description": "The refresh token obtained from the initial authentication"}, "grant_type": {"type": "string", "description": "The grant type of the authentication request. Here are the options: read_write, read, write"}, "user_first_name": {"type": "string", "description": "The first name of the user"}, "user_last_name": {"type": "string", "description": "The last name of the user"}}, "required": ["client_id", "client_secret", "refresh_token", "grant_type", "user_first_name", "user_last_name"]}, "response": {"type": "dict", "properties": {"expires_in": {"type": "integer", "description": "The number of time it can use until the access token expires"}, "access_token": {"type": "string", "description": "The access token to be used in the Authorization header of future requests"}, "token_type": {"type": "string", "description": "The type of token"}, "scope": {"type": "string", "description": "The scope of the token"}}}}
{"name": "book_flight", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Book a flight given the travel information. From and To should be the airport codes in the IATA format.", "parameters": {"type": "dict", "properties": {"access_token": {"type": "string", "description": "The access token obtained from the authenticate"}, "card_id": {"type": "string", "description": "The ID of the credit card to use for the booking"}, "travel_date": {"type": "string", "description": "The date of the travel in the format YYYY-MM-DD"}, "travel_from": {"type": "string", "description": "The location the travel is from"}, "travel_to": {"type": "string", "description": "The location the travel is to"}, "travel_class": {"type": "string", "description": "The class of the travel"}, "travel_cost": {"type": "float", "description": "The cost of the travel"}}, "required": ["access_token", "card_id", "travel_date", "travel_from", "travel_to", "travel_class", "travel_cost"]}, "response": {"type": "dict", "properties": {"booking_id": {"type": "string", "description": "The ID of the booking"}, "transaction_id": {"type": "string", "description": "The ID of the transaction"}, "booking_status": {"type": "boolean", "description": "The status of the booking, True if successful, False if failed"}, "booking_history": {"type": "dict", "description": "The booking history if long context is enabled", "properties": {"booking_id": {"type": "string", "description": "The ID of the booking"}, "transaction_id": {"type": "string", "description": "The ID of the transaction"}, "travel_date": {"type": "string", "description": "The date of the travel"}, "travel_from": {"type": "string", "description": "The location the travel is from"}, "travel_to": {"type": "string", "description": "The location the travel is to"}, "travel_class": {"type": "string", "description": "The class of the travel"}, "travel_cost": {"type": "float", "description": "The cost of the travel"}}}}}}
{"name": "cancel_booking", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Cancel a booking", "parameters": {"type": "dict", "properties": {"access_token": {"type": "string", "description": "The access token obtained from the authenticate"}, "booking_id": {"type": "string", "description": "The ID of the booking"}}, "required": ["access_token", "booking_id"]}, "response": {"type": "dict", "properties": {"cancel_status": {"type": "boolean", "description": "The status of the cancellation, True if successful, False if failed"}}}}
{"name": "compute_exchange_rate", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Compute the exchange rate between two currencies", "parameters": {"type": "dict", "properties": {"base_currency": {"type": "string", "description": "The base currency. [Enum]: USD, RMB, EUR, JPY, GBP, CAD, AUD, INR, RUB, BRL, MXN"}, "target_currency": {"type": "string", "description": "The target currency. [Enum]: USD, RMB, EUR, JPY, GBP, CAD, AUD, INR, RUB, BRL, MXN"}, "value": {"type": "float", "description": "The value to convert"}}, "required": ["base_currency", "target_currency", "value"]}, "response": {"type": "dict", "properties": {"exchanged_value": {"type": "float", "description": "The value after the exchange"}}}}
{"name": "contact_customer_support", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Contact travel booking customer support, get immediate support on an issue with an online call.", "parameters": {"type": "dict", "properties": {"booking_id": {"type": "string", "description": "The ID of the booking"}, "message": {"type": "string", "description": "The message to send to customer support"}}, "required": ["booking_id", "message"]}, "response": {"type": "dict", "properties": {"customer_support_message": {"type": "string", "description": "The message from customer support"}}}}
{"name": "get_all_credit_cards", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Get all registered credit cards", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"credit_card_list": {"type": "dict", "description": "A dictionary containing all registered credit cards", "properties": {"card_number": {"type": "string", "description": "The number of the credit card"}, "expiration_date": {"type": "string", "description": "The expiration date of the credit card in the format YYYY-MM-DD"}, "cardholder_name": {"type": "string", "description": "The name of the cardholder"}, "card_verification_value": {"type": "integer", "description": "The verification value of the credit card"}, "balance": {"type": "float", "description": "The balance of the credit card"}}}}}}
{"name": "get_budget_fiscal_year", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Get the budget fiscal year", "parameters": {"type": "dict", "properties": {"lastModifiedAfter": {"type": "string", "description": "Use this field if you only want Fiscal Years that were changed after the supplied date. The supplied date will be interpreted in the UTC time zone. If lastModifiedAfter is not supplied, the service will return all Fiscal Years, regardless of modified date. Example: 2016-03-29T16:12:20. Return in the format of YYYY-MM-DDTHH:MM:SS.", "default": "None"}, "includeRemoved": {"type": "string", "description": "If true, the service will return all Fiscal Years, including those that were previously removed. If not supplied, this field defaults to false.", "default": "None"}}, "required": []}, "response": {"type": "dict", "properties": {"budget_fiscal_year": {"type": "string", "description": "The budget fiscal year"}}}}
{"name": "get_credit_card_balance", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Get the balance of a credit card", "parameters": {"type": "dict", "properties": {"access_token": {"type": "string", "description": "The access token obtained from the authenticate"}, "card_id": {"type": "string", "description": "The ID of the credit card"}}, "required": ["access_token", "card_id"]}, "response": {"type": "dict", "properties": {"card_balance": {"type": "float", "description": "The balance of the credit card"}}}}
{"name": "get_flight_cost", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Get the list of cost of a flight in USD based on location, date, and class", "parameters": {"type": "dict", "properties": {"travel_from": {"type": "string", "description": "The 3 letter code of the departing airport"}, "travel_to": {"type": "string", "description": "The 3 letter code of the arriving airport"}, "travel_date": {"type": "string", "description": "The date of the travel in the format 'YYYY-MM-DD'"}, "travel_class": {"type": "string", "description": "The class of the travel. Options are: economy, business, first."}}, "required": ["travel_from", "travel_to", "travel_date", "travel_class"]}, "response": {"type": "dict", "properties": {"travel_cost_list": {"type": "array", "description": "The list of cost of the travel", "items": {"type": "float"}}}}}
{"name": "get_nearest_airport_by_city", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Get the nearest airport to the given location", "parameters": {"type": "dict", "properties": {"location": {"type": "string", "description": "The name of the location. [Enum]: Rivermist, Stonebrook, Maplecrest, Silverpine, Shadowridge, London, Paris, Sunset Valley, Oakendale, Willowbend, Crescent Hollow, Autumnville, Pinehaven, Greenfield, San Francisco, Los Angeles, New York, Chicago, Boston, Beijing, Hong Kong, Rome, Tokyo"}}, "required": ["location"]}, "response": {"type": "dict", "properties": {"nearest_airport": {"type": "string", "description": "The nearest airport to the given location"}}}}
{"name": "list_all_airports", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: List all available airports", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"airports": {"type": "array", "description": "A list of all available airports", "items": {"type": "string"}}}}}
{"name": "purchase_insurance", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Purchase insurance", "parameters": {"type": "dict", "properties": {"access_token": {"type": "string", "description": "The access token obtained from the authenticate"}, "insurance_type": {"type": "string", "description": "The type of insurance to purchase"}, "insurance_cost": {"type": "float", "description": "The cost of the insurance"}, "booking_id": {"type": "string", "description": "The ID of the booking"}, "card_id": {"type": "string", "description": "The ID of the credit card to use for the"}}, "required": ["access_token", "insurance_type", "booking_id", "insurance_cost", "card_id"]}, "response": {"type": "dict", "properties": {"insurance_id": {"type": "string", "description": "The ID of the insurance"}, "insurance_status": {"type": "boolean", "description": "The status of the insurance purchase, True if successful, False if failed"}}}}
{"name": "register_credit_card", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Register a credit card", "parameters": {"type": "dict", "properties": {"access_token": {"type": "string", "description": "The access token obtained from the authenticate method"}, "card_number": {"type": "string", "description": "The credit card number"}, "expiration_date": {"type": "string", "description": "The expiration date of the credit card in the format MM/YYYY"}, "cardholder_name": {"type": "string", "description": "The name of the cardholder"}, "card_verification_number": {"type": "integer", "description": "The card verification number"}}, "required": ["access_token", "card_number", "expiration_date", "cardholder_name", "card_verification_number"]}, "response": {"type": "dict", "properties": {"card_id": {"type": "string", "description": "The ID of the registered credit card"}}}}
{"name": "retrieve_invoice", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Retrieve the invoice for a booking", "parameters": {"type": "dict", "properties": {"access_token": {"type": "string", "description": "The access token obtained from the authenticate"}, "booking_id": {"type": "string", "description": "The ID of the booking", "default": "None"}, "insurance_id": {"type": "string", "description": "The ID of the insurance", "default": "None"}}, "required": ["access_token"]}, "response": {"type": "dict", "properties": {"invoice": {"type": "dict", "description": "The invoice for the booking", "properties": {"booking_id": {"type": "string", "description": "The ID of the booking"}, "travel_date": {"type": "string", "description": "The date of the travel"}, "travel_from": {"type": "string", "description": "The location the travel is from"}, "travel_to": {"type": "string", "description": "The location the travel is to"}, "travel_class": {"type": "string", "description": "The class of the travel"}, "travel_cost": {"type": "float", "description": "The cost of the travel"}, "transaction_id": {"type": "string", "description": "The ID of the transaction"}}}}}}
{"name": "set_budget_limit", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Set the budget limit for the user", "parameters": {"type": "dict", "properties": {"access_token": {"type": "string", "description": "The access token obtained from the authentication process or initial configuration."}, "budget_limit": {"type": "float", "description": "The budget limit to set in USD"}}, "required": ["access_token", "budget_limit"]}, "response": {"type": "dict", "properties": {"budget_limit": {"type": "float", "description": "The budget limit set in USD"}}}}
{"name": "travel_get_login_status", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Get the status of the login", "parameters": {"type": "dict", "properties": {}, "required": []}, "response": {"type": "dict", "properties": {"status": {"type": "boolean", "description": "The status of the login"}}}}
{"name": "verify_traveler_information", "description": "This tool belongs to the travel system, which allows users to book flights, manage credit cards, and view budget information. Tool description: Verify the traveler information", "parameters": {"type": "dict", "properties": {"first_name": {"type": "string", "description": "The first name of the traveler"}, "last_name": {"type": "string", "description": "The last name of the traveler"}, "date_of_birth": {"type": "string", "description": "The date of birth of the traveler in the format YYYY-MM-DD"}, "passport_number": {"type": "string", "description": "The passport number of the traveler"}}, "required": ["first_name", "last_name", "date_of_birth", "passport_number"]}, "response": {"type": "dict", "properties": {"verification_status": {"type": "boolean", "description": "The status of the verification, True if successful, False if failed"}, "verification_failure": {"type": "string", "description": "The reason for the verification failure"}}}}
