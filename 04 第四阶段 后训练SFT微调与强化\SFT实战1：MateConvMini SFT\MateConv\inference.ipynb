{"cells": [{"cell_type": "code", "execution_count": 1, "id": "ce631b51-600e-41be-9c20-a0885e013265", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import torch\n", "import random\n", "import numpy as np\n", "from transformers import AutoTokenizer\n", "from model.model import Transformer\n", "from model.LMConfig import LMConfig"]}, {"cell_type": "code", "execution_count": 2, "id": "ac8d9853-3c3b-44c1-9ab4-9eda36697515", "metadata": {}, "outputs": [], "source": ["# 1. 设置设备和随机种子\n", "device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")\n", "\n", "def setup_seed(seed):\n", "    random.seed(seed)\n", "    np.random.seed(seed)\n", "    torch.manual_seed(seed)\n", "    torch.cuda.manual_seed(seed)\n", "    torch.cuda.manual_seed_all(seed)\n", "    torch.backends.cudnn.deterministic = True\n", "    torch.backends.cudnn.benchmark = False\n", "\n", "setup_seed(1337)"]}, {"cell_type": "code", "execution_count": 3, "id": "f30d7d73-624d-4c70-a71f-43dc98626488", "metadata": {}, "outputs": [], "source": ["# 2. 初始化模型和分词器\n", "lm_config = LMConfig()"]}, {"cell_type": "code", "execution_count": 5, "id": "3c5850de-0926-41b2-9607-bb8575dddb5b", "metadata": {}, "outputs": [{"data": {"text/plain": ["LMConfig {\n", "  \"aux_loss_alpha\": 0.01,\n", "  \"dim\": 512,\n", "  \"dropout\": 0.0,\n", "  \"flash_attn\": true,\n", "  \"hidden_dim\": null,\n", "  \"max_seq_len\": 512,\n", "  \"model_type\": \"MateConv Mini\",\n", "  \"multiple_of\": 64,\n", "  \"n_heads\": 16,\n", "  \"n_kv_heads\": 8,\n", "  \"n_layers\": 8,\n", "  \"n_routed_experts\": 4,\n", "  \"n_shared_experts\": true,\n", "  \"norm_eps\": 1e-05,\n", "  \"norm_topk_prob\": true,\n", "  \"num_experts_per_tok\": 2,\n", "  \"scoring_func\": \"softmax\",\n", "  \"seq_aux\": true,\n", "  \"transformers_version\": \"4.44.0\",\n", "  \"use_moe\": false,\n", "  \"vocab_size\": 6400\n", "}"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["lm_config"]}, {"cell_type": "code", "execution_count": 8, "id": "09b91061-14cd-4f28-8efc-e5ab55a1a580", "metadata": {}, "outputs": [{"data": {"text/plain": ["Transformer(\n", "  (tok_embeddings): Embedding(6400, 512)\n", "  (dropout): Dropout(p=0.0, inplace=False)\n", "  (layers): ModuleList(\n", "    (0-7): 8 x TransformerBlock(\n", "      (attention): Attention(\n", "        (wq): Linear(in_features=512, out_features=512, bias=False)\n", "        (wk): Linear(in_features=512, out_features=256, bias=False)\n", "        (wv): Linear(in_features=512, out_features=256, bias=False)\n", "        (wo): Linear(in_features=512, out_features=512, bias=False)\n", "        (attn_dropout): Dropout(p=0.0, inplace=False)\n", "        (resid_dropout): Dropout(p=0.0, inplace=False)\n", "      )\n", "      (attention_norm): RMSNorm()\n", "      (ffn_norm): RMSNorm()\n", "      (feed_forward): FeedForward(\n", "        (w1): Linear(in_features=512, out_features=1408, bias=False)\n", "        (w2): Linear(in_features=1408, out_features=512, bias=False)\n", "        (w3): Linear(in_features=512, out_features=1408, bias=False)\n", "        (dropout): Dropout(p=0.0, inplace=False)\n", "      )\n", "    )\n", "  )\n", "  (norm): RMSNorm()\n", "  (output): Linear(in_features=512, out_features=6400, bias=False)\n", ")"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["max_seq_len = 1024  # 可以根据需要调整\n", "lm_config.max_seq_len = max_seq_len\n", "\n", "model = Transformer(lm_config).to(device)\n", "model_path = './out/full_sft_512.pth'  # 替换为你的模型路径\n", "checkpoint = torch.load(model_path, map_location=device)\n", "model.load_state_dict(checkpoint[\"model_state\"])\n", "model.eval()"]}, {"cell_type": "code", "execution_count": 9, "id": "60c6b967-87ff-415c-8629-5487b4a3be9d", "metadata": {}, "outputs": [], "source": ["tokenizer = AutoTokenizer.from_pretrained('./model/mateconv_tokenizer') "]}, {"cell_type": "code", "execution_count": 10, "id": "04f13ccb-4f9c-4a56-850c-e46d86805a9b", "metadata": {}, "outputs": [], "source": ["# 3. 对话函数：生成完整回复\n", "def generate_reply(prompt, temperature=0.5, top_k=16, stream=True):\n", "    messages = [{\"role\": \"user\", \"content\": prompt}]\n", "    \n", "    # 使用自定义的 prompt 模板 (根据你的应用逻辑)\n", "    new_prompt = tokenizer.apply_chat_template(\n", "        messages, \n", "        tokenize=False, \n", "        add_generation_prompt=True\n", "    )[-(max_seq_len - 1):]\n", "\n", "    input_ids = tokenizer(new_prompt).data['input_ids']\n", "    input_ids = torch.tensor(input_ids, dtype=torch.long, device=device).unsqueeze(0)\n", "\n", "    generated_text = \"\"\n", "    with torch.no_grad():\n", "        # 生成器返回的生成结果\n", "        res_y = model.generate(input_ids, \n", "                               tokenizer.eos_token_id, \n", "                               max_new_tokens=max_seq_len, \n", "                               temperature=temperature, \n", "                               top_k=top_k, \n", "                               stream=stream)\n", "\n", "        # 从生成器逐步获取生成结果\n", "        try:\n", "            y = next(res_y)\n", "        except StopIteration:\n", "            print(\"No answer\")\n", "            return \"\"\n", "\n", "        history_idx = 0\n", "        while y is not None:\n", "            answer = tokenizer.decode(y[0].tolist())\n", "            if answer and answer[-1] == '�':\n", "                try:\n", "                    y = next(res_y)\n", "                except StopIteration:\n", "                    break\n", "                continue\n", "\n", "            if len(answer):\n", "                generated_text += answer[history_idx:]\n", "            \n", "            try:\n", "                y = next(res_y)\n", "            except StopIteration:\n", "                break\n", "            history_idx = len(answer)\n", "\n", "    return generated_text"]}, {"cell_type": "code", "execution_count": 11, "id": "60d00d28-1340-491e-9539-34acd36c5fb5", "metadata": {}, "outputs": [{"data": {"text/plain": ["'- 长江是中国的著名旅游胜地，也是中国最著名的旅游目的地之一。\\n- 长江是中国最大的城市之一，也是世界上最长的河流之一，流经中国东北地区，全长超过700公里。\\n- 长江以其独特的自然风光、丰富的文化遗产和独特的文化遗产而闻名于世。\\n- 长江是中国最重要的旅游目的地之一，也是世界上最大的河流之一，有着悠久的历史和丰富的文化遗产。\\n- 长江是中国最重要的经济和文化中心之一，也是世界上最大的河流之一，也是世界上最大的河流之一。\\n- 长江以其独特的自然风光和独特的文化遗产而闻名于世，是中国文化和历史的象征。'"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["response = generate_reply(\"长江、\")\n", "response"]}, {"cell_type": "code", "execution_count": 15, "id": "531d1875-100e-44e5-9cb4-e285fa86ca25", "metadata": {}, "outputs": [{"data": {"text/plain": ["'您好，很高兴为您提供帮助。请问您有什么问题需要咨询吗？'"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["response = generate_reply(\"你好，好久不见呀老朋友！\")\n", "response"]}, {"cell_type": "code", "execution_count": 16, "id": "be1b872b-1350-4e33-b1c2-30d8090d38f0", "metadata": {}, "outputs": [{"data": {"text/plain": ["'机器学习是一种人工智能领域的技术，它使计算机能够从数据中学习，并通过不断地优化和改进性能。在机器学习中，数据是用来训练模型的，而无需明确编程。它是一种人工智能技术，通过数据来训练模型，以便在没有明确编程的情况下进行预测和决策。机器学习的目的是让计算机系统能够自动地从数据中学习，并利用这些数据来做出预测或决策。'"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["response = generate_reply(\"什么是机器学习？\")\n", "response"]}, {"cell_type": "code", "execution_count": 17, "id": "9d431802-b4a6-428e-bdbf-6aeaba8f74c2", "metadata": {}, "outputs": [{"data": {"text/plain": ["'你好！我是一个AI模型，我可以回答各种问题。请问你是谁？'"]}, "execution_count": 17, "metadata": {}, "output_type": "execute_result"}], "source": ["response = generate_reply(\"你好，请问你是谁？\")\n", "response"]}, {"cell_type": "code", "execution_count": null, "id": "5e27340e-d33d-4807-8098-56270994c486", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}