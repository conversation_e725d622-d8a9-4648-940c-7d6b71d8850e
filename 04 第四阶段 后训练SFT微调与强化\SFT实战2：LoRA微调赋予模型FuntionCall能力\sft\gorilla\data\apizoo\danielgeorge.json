[{"user_name": "dangeo773", "api_name": "Stripe API - Charges", "api_call": "stripe.Charge.create(amount={amount}, currency={currency}, source={source}, description={description})", "api_version": "2023-10-16", "api_arguments": {"amount": "integer", "currency": "string", "source": "string", "description": "string"}, "functionality": "Create, retrieve, update, and delete charges.", "env_requirements": ["stripe"], "example_code": "stripe.Charge.create(amount=2000, currency='usd', source='tok_amex', description='<NAME_EMAIL>')", "meta_data": {"description": "The Stripe Charge API allows you to create, retrieve, update, and delete charges. This API is part of Stripe's suite for handling payments."}, "questions": ["Charge Jenny Rosen $2000 in USD.", "Can you check how much I charged <PERSON>?", "Undo my charge to <PERSON>"]}, {"user_name": "dangeo773", "api_name": "Stripe - Customers", "api_call": "stripe.Customer.create(email='email', description='description')", "api_version": "2023-10-16", "api_arguments": {"amount": "integer", "currency": "string", "source": "string", "description": "string"}, "functionality": "Create, retrieve, update, and delete customers.", "env_requirements": ["stripe"], "example_code": "stripe.Customer.create(email='<EMAIL>', description='<NAME_EMAIL>')", "meta_data": {"description": "The Stripe Customer API allows you to create, retrieve, update, and delete customers. This API is part of Stripe's suite for handling customers."}, "questions": ["How do I create a new customer on Stripe?", "Create a customer named <PERSON>, <NAME_EMAIL>", "Delete the customer <PERSON>."]}, {"user_name": "dangeo773", "api_name": "Stripe - Payment Intents", "api_call": "stripe.PaymentIntent.create(amount={amount}, currency={usd}, payment_method_types={payment_method_types})", "api_version": "2023-10-16", "api_arguments": {"amount": "integer", "currency": "string", "payment_method_types": "list"}, "functionality": "Create, retrieve, update, and handle payment intents.", "env_requirements": ["stripe"], "example_code": "stripe.PaymentIntent.create(amount=2000, currency='usd', payment_method_types=['card'])", "meta_data": {"description": "Stripe's Payment Intents API is used to handle complex payment flows. It is designed to manage the entire payment lifecycle, especially for businesses that need to handle complex payment scenarios."}, "questions": ["How can I create a PaymentIntent?", "What's the process to confirm a PaymentIntent?", "Create a payment intent of amount $3000 USD"]}, {"user_name": "dangeo773", "api_name": "Stripe - Balance", "api_call": "stripe.Balance.retrieve(id={id}, object={object}, amount={amount}, balance_transactions={balance_transactions}, charge={charge}, created={created}, currency={currency})", "api_version": "2023-10-16", "api_arguments": {"id": "string", "object": "string", "amount": "integer", "balance_transactions": "list", "charge": "string", "created": "integer", "currency": "string"}, "functionality": "Retrieve your Stripe account's current balance.", "env_requirements": ["stripe"], "example_code": "stripe.Balance.retrieve()", "meta_data": {"description": "The Balance API is used to retrieve your account's current balance, showing both pending and available amounts."}, "questions": ["How can I check my current Stripe account balance?", "What is the difference between pending and available amounts in my Stripe balance?"]}, {"user_name": "dangeo773", "api_name": "Stripe - Refunds", "api_call": "stripe.Refund.create(charge={charge})", "api_version": "2023-10-16", "api_arguments": {"charge": "string", "amount": "integer", "reason": "string"}, "functionality": "Create, retrieve, update, and delete refunds.", "env_requirements": ["stripe"], "example_code": "stripe.Refund.create(charge='ch_1Iq5Pf2eZvKYlo2C2pZOdF4Q')", "meta_data": {"description": "The Refunds API allows you to refund charges made to your account, either in whole or in part, and to retrieve details of previous refunds."}, "questions": ["Refund charge 2891823.", "Can I partially refund a transaction?", "Refund my previous transaction for $1,000."]}, {"user_name": "dangeo773", "api_name": "Stripe - Payouts", "api_call": "stripe.Payout.create(amount={amount}, currency={currency}, destination={destination})", "api_version": "2023-10-16", "api_arguments": {"amount": "integer", "currency": "string", "destination": "string"}, "functionality": "Create, retrieve, update, and delete payouts.", "env_requirements": ["stripe"], "example_code": "stripe.Payout.create(amount=1100, currency='usd', destination='bank_account')", "meta_data": {"description": "The Payouts API is used to manage the transfer of funds from your Stripe account balance to your bank account or debit card."}, "questions": ["Initiate a payout to my bank account.", "Commit a payout of $2041 to my bank account.", "How can I retrieve a specific payout transaction?"]}, {"user_name": "dangeo773", "api_name": "DocuSign - A<PERSON>unt<PERSON><PERSON><PERSON>", "api_call": "DocuSign.AccountBrands.{methodName}({parameters})", "api_version": "2023-10-16", "api_arguments": {"brandId": "string", "brandName": "string", "logo": "file", "colors": "object", "text": "object"}, "functionality": "Customize account-level brands for recipient experience.", "env_requirements": ["DocuSign"], "example_code": "DocuSign.AccountBrands.create({brandName: 'My Brand', colors: {...}, text: {...}})", "meta_data": {"description": "Manage account-level brands to customize styles and text for recipients."}, "questions": ["How do I create a new account brand?", "How can I delete a specific account brand?", "Update the logo for my account brand."]}, {"user_name": "dangeo773", "api_name": "DocuSign - Account<PERSON>ustom<PERSON><PERSON>s", "api_call": "DocuSign.AccountCustomFields.{methodName}({parameters})", "api_version": "2023-10-16", "api_arguments": {"fieldId": "string", "fieldName": "string", "fieldValue": "string"}, "functionality": "Manage envelope custom fields at account level.", "env_requirements": ["DocuSign"], "example_code": "DocuSign.AccountCustomFields.create({fieldName: 'Department', fieldValue: 'Sales'})", "meta_data": {"description": "Use accountCustomField for grouping and managing envelopes."}, "questions": ["Create a new custom field for my account.", "How can I list all account custom fields?", "Delete a specific custom field from my account."]}, {"user_name": "dangeo773", "api_name": "DocuSign - Accounts", "api_call": "DocuSign.Accounts.{methodName}({parameters})", "api_version": "2023-10-16", "api_arguments": {"accountId": "string", "setting": "object", "language": "string"}, "functionality": "Comprehensive account management tasks.", "env_requirements": ["DocuSign"], "example_code": "DocuSign.Accounts.get({accountId: '12345'})", "meta_data": {"description": "Perform various account management tasks including information retrieval and customization."}, "questions": ["How do I retrieve details about my account?", "Update settings for my DocuSign account.", "List all recipient names associated with my account's email."]}, {"user_name": "dangeo773", "api_name": "DocuSign - AccountConsumerDisclosures", "api_call": "DocuSign.AccountConsumerDisclosures.{methodName}({parameters})", "api_version": "2023-10-16", "api_arguments": {"accountId": "string", "langCode": "string", "esignAgreementText": "string", "esignText": "string"}, "functionality": "Manage ERSD options for your account.", "env_requirements": ["DocuSign"], "example_code": "DocuSign.AccountConsumerDisclosures.get({accountId: '12345', langCode: 'en'})", "meta_data": {"description": "Enable, retrieve, and manage the ERSD options for your account, supporting custom disclosures per language."}, "questions": ["How do I retrieve the default ERSD for my account?", "Update the ERSD for a specific language in my account.", "Retrieve a custom ERSD in Spanish for my account."]}, {"user_name": "dangeo773", "api_name": "DocuSign - AccountPasswordRules", "api_call": "DocuSign.AccountPasswordRules.{methodName}({parameters})", "api_version": "2023-10-16", "api_arguments": {"accountId": "string", "passwordRules": "object"}, "functionality": "Obtain and update account password rules.", "env_requirements": ["DocuSign"], "example_code": "DocuSign.AccountPasswordRules.get({accountId: '12345'})", "meta_data": {"description": "Provides methods to obtain and update password and membership rules for an account."}, "questions": ["How do I get the password rules for my account?", "Retrieve the membership account password rules.", "Update the password rules for my account."]}, {"user_name": "dangeo773", "api_name": "DocuSign - AccountPermissionProfiles", "api_call": "DocuSign.AccountPermissionProfiles.{methodName}({parameters})", "api_version": "2023-10-16", "api_arguments": {"accountId": "string", "permissionProfileId": "string", "profileName": "string", "permissions": "object"}, "functionality": "Manage user permissions via profiles.", "env_requirements": ["DocuSign"], "example_code": "DocuSign.AccountPermissionProfiles.create({accountId: '12345', profileName: 'Custom Profile', permissions: {...}})", "meta_data": {"description": "Set and manage permissions for groups of users with permission profiles."}, "questions": ["How do I create a new permission profile for my account?", "Delete a permission profile from my account.", "Retrieve details of a specific permission profile.", "List all permission profiles for my account.", "Update a permission profile for my account."]}, {"user_name": "dangeo773", "api_name": "DocuSign - AccountSealProviders", "api_call": "DocuSign.AccountSealProviders.list({accountId: '12345'})", "api_version": "2023-10-16", "api_arguments": {"accountId": "string"}, "functionality": "Retrieve available seals for an account.", "env_requirements": ["DocuSign"], "example_code": "DocuSign.AccountSealProviders.list({accountId: '12345'})", "meta_data": {"description": "List available seals that can be used by an account for digital signatures."}, "questions": ["List all available seals for my account."]}, {"user_name": "dangeo773", "api_name": "DocuSign - AccountSignatureProviders", "api_call": "DocuSign.AccountSignatureProviders.list({accountId: '12345'})", "api_version": "2023-10-16", "api_arguments": {"accountId": "string"}, "functionality": "Retrieve available signature providers for compliance.", "env_requirements": ["DocuSign"], "example_code": "DocuSign.AccountSignatureProviders.list({accountId: '12345'})", "meta_data": {"description": "Get Standards-Based Signatures providers that comply with regulations."}, "questions": ["List all available signature providers for my account."]}, {"user_name": "dangeo773", "api_name": "DocuSign - AccountSignatures", "api_call": "DocuSign.AccountSignatures.{methodName}({parameters})", "api_version": "2023-10-16", "api_arguments": {"accountId": "string", "signatureId": "string", "imageType": "string"}, "functionality": "Manage stamps and signature images.", "env_requirements": ["DocuSign"], "example_code": "DocuSign.AccountSignatures.getAccountSignature({accountId: '12345', signatureId: '67890'})", "meta_data": {"description": "Methods for managing stamps and signature images for an account."}, "questions": ["How do I add a new stamp to my account?", "Delete an account stamp.", "Update a signature image for an account stamp."]}, {"user_name": "dangeo773", "api_name": "DocuSign - AccountTabSettings", "api_call": "DocuSign.AccountTabSettings.{methodName}({accountId: '12345'})", "api_version": "2023-10-16", "api_arguments": {"accountId": "string"}, "functionality": "Manage account tab settings.", "env_requirements": ["DocuSign"], "example_code": "DocuSign.AccountTabSettings.get({accountId: '12345'})", "meta_data": {"description": "Allows management of tab settings for an account."}, "questions": ["Retrieve my account's tab settings.", "Modify tab settings for my account."]}, {"user_name": "dangeo773", "api_name": "DocuSign - AccountWatermarks", "api_call": "DocuSign.AccountWatermarks.{methodName}({accountId: '12345'})", "api_version": "2023-10-16", "api_arguments": {"accountId": "string"}, "functionality": "Manage watermark settings for documents.", "env_requirements": ["DocuSign"], "example_code": "DocuSign.AccountWatermarks.get({accountId: '12345'})", "meta_data": {"description": "Methods to obtain, preview, and update watermark information for an account."}, "questions": ["How can I get watermark information for my account?", "Preview watermark changes.", "Update watermark information for my account."]}, {"user_name": "dangeo773", "api_name": "DocuSign - Authorizations", "api_call": "DocuSign.Authorizations.{methodName}({accountId: '12345', userId: '67890'})", "api_version": "2023-10-16", "api_arguments": {"accountId": "string", "userId": "string", "authorizationId": "string"}, "functionality": "Manage user authorizations for shared access.", "env_requirements": ["DocuSign"], "example_code": "DocuSign.Authorizations.createUserAuthorization({accountId: '12345', userId: '67890'})", "meta_data": {"description": "Create, delete, and manage authorizations for users to share account access."}, "questions": ["Create a user authorization for shared account access.", "Delete a specific user authorization.", "Update the user authorization for managing account access."]}]