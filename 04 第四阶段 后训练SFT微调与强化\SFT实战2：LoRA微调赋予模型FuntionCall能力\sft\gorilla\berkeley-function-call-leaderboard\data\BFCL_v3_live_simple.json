{"id": "live_simple_0-0-0", "question": [[{"role": "user", "content": "Can you retrieve the details for the user with the ID 7890, who has black as their special request?"}]], "function": [{"name": "get_user_info", "description": "Retrieve details for a specific user by their unique identifier.", "parameters": {"type": "dict", "required": ["user_id"], "properties": {"user_id": {"type": "integer", "description": "The unique identifier of the user. It is used to fetch the specific user details from the database."}, "special": {"type": "string", "description": "Any special information or parameters that need to be considered while fetching user details.", "default": "none"}}}}]}
{"id": "live_simple_1-1-0", "question": [[{"role": "user", "content": "I want to see the star history of ShishirPatil/gorilla and gorilla-llm/gorilla-cli, with the timelines aligned, so that I can more clearly observe the rate of change from their initial releases."}]], "function": [{"name": "github_star", "description": "Generates a URL for tracking the star history of specified GitHub repositories, with the option to align them on the same timeline.", "parameters": {"type": "dict", "required": ["repos"], "properties": {"repos": {"type": "string", "description": "A comma-separated list of GitHub repositories to track, each in the 'owner/repo' format, such as 'octocat/Hello-World,octo-org/octo-repo'."}, "aligned": {"type": "boolean", "description": "Whether to align the repositories on the same timeline for comparison. If true, the star history of all repositories will start from the same point.", "default": false}}}}]}
{"id": "live_simple_2-2-0", "question": [[{"role": "user", "content": "I need a Comfort Uber ride from 2020 Addison Street, Berkeley, CA, USA, and I can wait up to 600 seconds for it."}]], "function": [{"name": "uber.ride", "description": "Finds a suitable Uber ride for customers based on their location, desired ride type, and maximum wait time.", "parameters": {"type": "dict", "required": ["loc", "type", "time"], "properties": {"loc": {"type": "string", "description": "The starting location for the Uber ride, in the format of 'Street Address, City, State (abbr), Country'."}, "type": {"type": "string", "description": "The type of Uber ride the user is requesting.", "enum": ["plus", "comfort", "black"]}, "time": {"type": "integer", "description": "The maximum amount of time the customer is willing to wait for the ride, specified in seconds."}}}}]}
{"id": "live_simple_3-2-1", "question": [[{"role": "user", "content": "I need a 'Plus' type Uber from 221B Baker Street, Berkeley, CA, USA, and I can wait up to 600 seconds for it. "}]], "function": [{"name": "uber.ride", "description": "Finds a suitable Uber ride for customers based on their location, desired ride type, and maximum wait time.", "parameters": {"type": "dict", "required": ["loc", "type", "time"], "properties": {"loc": {"type": "string", "description": "The starting location for the Uber ride, in the format of 'Street Address, City, State (abbr), Country'."}, "type": {"type": "string", "description": "The type of Uber ride the user is requesting.", "enum": ["plus", "comfort", "black"]}, "time": {"type": "integer", "description": "The maximum amount of time the customer is willing to wait for the ride, specified in seconds."}}}}]}
{"id": "live_simple_4-3-0", "question": [[{"role": "user", "content": "What are the current weather conditions in Tel Aviv, and could you provide that in Fahrenheit, please?"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_5-3-1", "question": [[{"role": "user", "content": "Qual a temperatura atual em Divin\u00f3polis, MG? fahrenheit"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_6-3-2", "question": [[{"role": "user", "content": "Can you tell me the weather in fahrenheit so that I decide if I need to wear a jacket today? San Francisco"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_7-3-3", "question": [[{"role": "user", "content": "Im at Riga, Latvia, Can you tell me the current temperature?"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_8-3-4", "question": [[{"role": "user", "content": "I want to know weather conditions? I'm at London, UK in fahr"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_9-3-5", "question": [[{"role": "user", "content": "What's weather in Hyderabad in Fahrenheit"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_10-3-6", "question": [[{"role": "user", "content": "Could you tell me the current weather in Yosemite National Park which locates at Mariposa, CA, and make sure to give me the temperature in Celsius?"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)' or 'Location, State', such as 'San Francisco, CA' if State exists. 'City, Country' if State for the city doesn't exist. "}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_11-3-7", "question": [[{"role": "user", "content": "Could you tell me the current weather conditions in Naples, Florida?"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist. If using state, then use short form like CA."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_12-3-8", "question": [[{"role": "user", "content": "I'm planning a trip to New York, NY in December and will be staying for 5 days. Could you tell me the current weather conditions, and provide the temperature in Celsius?"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist. If using state, then use short form like CA."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_13-3-9", "question": [[{"role": "user", "content": "\u6211\u60f3\u77e5\u9053\u4e0a\u6d77\u76ee\u524d\u7684\u5929\u6c14\u72b6\u51b5\uff0c\u53ef\u4ee5\u5e2e\u6211\u67e5\u8be2\u5417\uff1f\u987a\u4fbf\u4f7f\u7528\u6444\u6c0f\u5ea6\u6765\u663e\u793a\u6e29\u5ea6\u3002"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_14-3-10", "question": [[{"role": "user", "content": "I have a busy day today. Can you tell me what the temperature is going to be in Bangkok, Thailand. I prefer the fahrenheit unit."}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_15-3-11", "question": [[{"role": "user", "content": "I'm at Bengaluru. Tell me the current weather conditions in Chennai, Tamil Nadu in fahrenheit?"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_16-3-12", "question": [[{"role": "user", "content": "Could you tell me the current weather in Lang Son in fahrenheit?"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_17-3-13", "question": [[{"role": "user", "content": "weather today in Boston in fahrenheit?"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_18-3-14", "question": [[{"role": "user", "content": "Can you tell me the current weather in Moscow, specifying the temperature in Celsius?"}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)', such as 'San Francisco, CA' if State for the city exists. 'City, Country' if State for the city doesn't exist."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_19-3-15", "question": [[{"role": "user", "content": "What's the weather in Quintana Roo, Mexico. Please tell me the answer in the superior units of temperature, by that I mean Celsius."}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather conditions for a specified city and state. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)' or 'Location, State', such as 'San Francisco, CA' or 'Yosemite National Park, CA' if State exists. 'City, Country' if State for the city doesn't exist."}, "unit": {"type": "string", "description": "The unit of temperature for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_20-4-0", "question": [[{"role": "user", "content": "I would like to order a burger with the following modification verbatim: no onions, extra cheese"}]], "function": [{"name": "change_food", "description": "Modifies the food item based on the customer's request, such as changing ingredients or preparation methods.", "parameters": {"type": "dict", "required": ["food_item", "modification_request"], "properties": {"food_item": {"type": "string", "description": "The name of the food item to be modified."}, "modification_request": {"type": "string", "description": "Specific request detailing the changes to be made to the food item, such as 'no peanuts' or 'extra spicy'. If there are multiple requests, separate using comma with no space between comma"}}}}]}
{"id": "live_simple_21-4-1", "question": [[{"role": "user", "content": "I'd like to modify my order by making the dish called 'chicken dish' to a new spice level 'extra spicy', please."}]], "function": [{"name": "change_food", "description": "Modifies the food item based on the customer's request, such as changing ingredients or preparation methods.", "parameters": {"type": "dict", "required": ["food_item", "modification_request"], "properties": {"food_item": {"type": "string", "description": "The name of the food item to be modified."}, "modification_request": {"type": "string", "description": "Specific request detailing the changes to be made to the food item, such as 'no peanuts' or 'extra spicy'. If there are multiple requests, separate using comma with no space between comma"}}}}]}
{"id": "live_simple_22-5-0", "question": [[{"role": "user", "content": "I need Whopper also known old folks as the burger."}]], "function": [{"name": "ChaFod", "description": "Changes the selection of food based on the customer's request, ensuring the food name provided is in uppercase as per the requirement.", "parameters": {"type": "dict", "required": ["TheFod"], "properties": {"TheFod": {"type": "string", "description": "The name of the food to be changed, provided in uppercase letters only (e.g., 'PIZZA', 'BURGER').", "enum": ["PIZZA", "BURGER", "SALAD", "SOUP", "STEAK"]}}}}]}
{"id": "live_simple_23-5-1", "question": [[{"role": "user", "content": "Order me pizza"}]], "function": [{"name": "ChaFod", "description": "Changes the selection of food based on the customer's request, ensuring the food name provided is in uppercase as per the requirement.", "parameters": {"type": "dict", "required": ["TheFod"], "properties": {"TheFod": {"type": "string", "description": "The name of the food to be changed, provided in uppercase letters only (e.g., 'PIZZA', 'BURGER').", "enum": ["PIZZA", "BURGER", "SALAD", "SOUP", "STEAK"]}}}}]}
{"id": "live_simple_24-5-2", "question": [[{"role": "user", "content": "I would like to switch my order from pizza to a BURGER."}]], "function": [{"name": "ChaFod", "description": "Changes the selection of food based on the customer's request, ensuring the food name provided is in uppercase as per the requirement.", "parameters": {"type": "dict", "required": ["TheFod"], "properties": {"TheFod": {"type": "string", "description": "The name of the food to be changed, provided in uppercase letters only (e.g., 'PIZZA', 'BURGER').", "enum": ["PIZZA", "BURGER", "SALAD", "SOUP", "STEAK"]}}}}]}
{"id": "live_simple_25-5-3", "question": [[{"role": "user", "content": "I've changed my mind, can I get a greens instead of what I previously ordered?"}]], "function": [{"name": "ChaFod", "description": "Changes the selection of food based on the customer's request, ensuring the food name provided is in uppercase as per the requirement.", "parameters": {"type": "dict", "required": ["TheFod"], "properties": {"TheFod": {"type": "string", "description": "The name of the food to be changed, provided in uppercase letters only (e.g., 'PIZZA', 'BURGER').", "enum": ["PIZZA", "BURGER", "SALAD", "SOUP", "STEAK"]}}}}]}
{"id": "live_simple_26-6-0", "question": [[{"role": "user", "content": "T\u00f4i c\u1ea7n m\u1ed9t chuy\u1ebfn Uber lo\u1ea1i 'Plus' t\u1eeb \u0111\u1ecba ch\u1ec9 123 \u0110\u01b0\u1eddng \u0110\u1ea1i h\u1ecdc, Berkeley, CA 94704 v\u00e0 t\u00f4i kh\u00f4ng mu\u1ed1n ch\u1edd qu\u00e1 10 ph\u00fat."}]], "function": [{"name": "uber.ride", "description": "T\u00ecm chuy\u1ebfn \u0111i ph\u00f9 h\u1ee3p cho kh\u00e1ch h\u00e0ng d\u1ef1a tr\u00ean v\u1ecb tr\u00ed, lo\u1ea1i chuy\u1ebfn \u0111i v\u00e0 kho\u1ea3ng th\u1eddi gian kh\u00e1ch h\u00e0ng s\u1eb5n s\u00e0ng ch\u1edd \u0111\u1ee3i l\u00e0m th\u00f4ng s\u1ed1'", "parameters": {"type": "dict", "required": ["loc", "type", "time"], "properties": {"loc": {"type": "string", "description": "The starting location of the Uber ride in the format of 'Address, City, State'. For example, '123 Main St, Springfield, IL'."}, "type": {"type": "string", "description": "The type of Uber ride the user is requesting.", "enum": ["plus", "comfort", "black"]}, "time": {"type": "integer", "description": "The maximum amount of time the customer is willing to wait for the ride, in minutes."}}}}]}
{"id": "live_simple_27-7-0", "question": [[{"role": "user", "content": "I want to order five 'burgers' and six 'chicken wings' from uber pitada"}]], "function": [{"name": "uber.eat.order", "description": "Place an order for food delivery on Uber Eats by specifying the restaurant and the items with their respective quantities.", "parameters": {"type": "dict", "required": ["restaurant", "items", "quantities"], "properties": {"restaurant": {"type": "string", "description": "The name of the restaurant from which to order food."}, "items": {"type": "array", "items": {"type": "string"}, "description": "A list of food item names selected for the order."}, "quantities": {"type": "array", "items": {"type": "integer"}, "description": "A list of quantities for each food item, corresponding by index to the items array."}}}}]}
{"id": "live_simple_28-7-1", "question": [[{"role": "user", "content": "\u6211\u60f3\u5728\u80af\u5fb7\u57fa\u4e7010\u4e2a\u9ea6\u8fa3\u9e21\u817f\u5821\uff0c50\u676f\u53ef\u53e3\u53ef\u4e50\uff0c30\u4e2a\u6cb9\u70b8\u9e21\u7fc5\uff0c90\u6839\u85af\u6761"}]], "function": [{"name": "uber.eat.order", "description": "Place an order for food delivery on Uber Eats by specifying the restaurant and the items with their respective quantities.", "parameters": {"type": "dict", "required": ["restaurant", "items", "quantities"], "properties": {"restaurant": {"type": "string", "description": "The name of the restaurant from which to order food."}, "items": {"type": "array", "items": {"type": "string"}, "description": "A list of food item names selected for the order."}, "quantities": {"type": "array", "items": {"type": "integer"}, "description": "A list of quantities for each food item, corresponding by index to the items array."}}}}]}
{"id": "live_simple_29-7-2", "question": [[{"role": "user", "content": "I want to go to McDonald's and buy a pizza."}]], "function": [{"name": "uber.eat.order", "description": "Place an order for food delivery on Uber Eats by specifying the restaurant and the items with their respective quantities.", "parameters": {"type": "dict", "required": ["restaurant", "items", "quantities"], "properties": {"restaurant": {"type": "string", "description": "The name of the restaurant from which to order food."}, "items": {"type": "array", "items": {"type": "string"}, "description": "A list of food item names selected for the order."}, "quantities": {"type": "array", "items": {"type": "integer"}, "description": "A list of quantities for each food item, corresponding by index to the items array."}}}}]}
{"id": "live_simple_30-8-0", "question": [[{"role": "user", "content": "Could you help me retrieve the list of exports for my bot using the identifier 'my-bot-id' and focusing on version 'v2' sort in ascending? I want max 50 results"}]], "function": [{"name": "aws.lexv2_models.list_exports", "description": "Lists the exports for a bot, bot locale, or custom vocabulary within Amazon Lex. This data is retained for a duration of 7 days.", "parameters": {"type": "dict", "required": ["botId", "botVersion"], "properties": {"botId": {"type": "string", "description": "The unique identifier for the bot as assigned by Amazon Lex."}, "botVersion": {"type": "string", "description": "The specific version of the bot for which to list the exports."}, "sortBy": {"type": "string", "description": "Determines the field to sort the list of exports by. Can be sorted by 'LastUpdatedDateTime' in ascending or descending order.", "enum": ["ASC", "DESC"], "default": "ASC"}, "filterName": {"type": "string", "description": "The name of the field to filter the exports by. Possible values are 'Bot', 'BotLocale', or 'CustomVocabulary'.", "default": null}, "filterOperator": {"type": "string", "description": "The operator to use for filtering. Use 'EQ' for equality or 'CO' for containing the specified value.", "enum": ["EQ", "CO"], "default": "EQ"}, "filterValue": {"type": "string", "description": "The value to use for filtering the exports based on the filterName.", "default": null}, "maxResults": {"type": "integer", "description": "The maximum number of exports to return in each page of results. If there are fewer results than the max page size, only the actual number of results are returned.", "default": 50}, "nextToken": {"type": "string", "description": "A token to retrieve the next page of results if the response from the ListExports operation contains more results than specified in the maxResults parameter.", "default": null}, "localeId": {"type": "string", "description": "Specifies the locale of the resources that should be exported. If not specified, both bot locales and custom vocabularies are exported.", "default": null}}}}]}
{"id": "live_simple_31-8-1", "question": [[{"role": "user", "content": "Could you retrieve the list of exports for my bot with ID 'B12345' and for version 'v1'? I'm interested in seeing them in descending order by the last updated date."}]], "function": [{"name": "aws.lexv2_models.list_exports", "description": "Lists the exports for a bot, bot locale, or custom vocabulary within Amazon Lex. This data is retained for a duration of 7 days.", "parameters": {"type": "dict", "required": ["botId", "botVersion"], "properties": {"botId": {"type": "string", "description": "The unique identifier for the bot as assigned by Amazon Lex."}, "botVersion": {"type": "string", "description": "The specific version of the bot for which to list the exports."}, "sortBy": {"type": "string", "description": "Determines the field to sort the list of exports by. Can be sorted by 'LastUpdatedDateTime' in ascending or descending order.", "enum": ["ASC", "DESC"], "default": "ASC"}, "filterName": {"type": "string", "description": "The name of the field to filter the exports by. Possible values are 'Bot', 'BotLocale', or 'CustomVocabulary'.", "default": null}, "filterOperator": {"type": "string", "description": "The operator to use for filtering. Use 'EQ' for equality or 'CO' for containing the specified value.", "enum": ["EQ", "CO"], "default": "EQ"}, "filterValue": {"type": "string", "description": "The value to use for filtering the exports based on the filterName.", "default": null}, "maxResults": {"type": "integer", "description": "The maximum number of exports to return in each page of results. If there are fewer results than the max page size, only the actual number of results are returned.", "default": 50}, "nextToken": {"type": "string", "description": "A token to retrieve the next page of results if the response from the ListExports operation contains more results than specified in the maxResults parameter.", "default": null}, "localeId": {"type": "string", "description": "Specifies the locale of the resources that should be exported. If not specified, both bot locales and custom vocabularies are exported.", "default": null}}}}]}
{"id": "live_simple_32-9-0", "question": [[{"role": "user", "content": "Sure, here is the answer to the question:\\n\\n**Logistic regression is not present in the text, therefore I cannot answer this question.**"}]], "function": [{"name": "answer.string", "description": "Parses the response provided by a Large Language Model (LLM) and returns the answer. If the LLM does not provide an answer, it returns an empty string.", "parameters": {"type": "dict", "required": ["answer"], "properties": {"answer": {"type": "string", "description": "The answer generated by the Large Language Model in plain text. If the model does not generate an answer, this parameter should be an empty string."}}}}]}
{"id": "live_simple_33-10-0", "question": [[{"role": "user", "content": "Sure, here is the answer to the question:\\n\\n**Logistic regression is not present in the text, therefore I cannot answer this question.**"}]], "function": [{"name": "answer.string", "description": "Parses the generated answer from a Large Language Model (LLM) and returns an empty string if the answer indicates that no answer was found.", "parameters": {"type": "dict", "required": ["answer"], "properties": {"answer": {"type": "string", "description": "The response provided by the LLM in plain text. If the response contains the phrase 'answer not found', this function will return an empty string instead."}}}}]}
{"id": "live_simple_34-11-0", "question": [[{"role": "user", "content": "I asked my language model about logistic regression, and it replied with '**Logistic regression is not present in the text, therefore I cannot answer this question.**' Valid?"}]], "function": [{"name": "answer.string", "description": "Parses the answer generated from a Language Learning Model (LLM) and checks for its validity. Returns an empty string if the LLM output is 'answer not found'.", "parameters": {"type": "dict", "required": ["answer"], "properties": {"answer": {"type": "string", "description": "The text response from the LLM in plain text. This should be the raw output provided by the model."}}}}]}
{"id": "live_simple_35-12-0", "question": [[{"role": "user", "content": "What is logistic regression? \\n Sure, here is the answer to the question:\\n\\n**Logistic regression is not present in the text, therefore I cannot answer this question.**"}]], "function": [{"name": "answer.string", "description": "Analyzes the string output from a language model. It returns the string directly if an answer is found within it. Should the output indicate that no answer was found, an empty string is returned instead.", "parameters": {"type": "dict", "required": ["answer"], "properties": {"answer": {"type": "string", "description": "The text response from a language model in plain text. An empty string is returned if the language model's response suggests that an answer could not be found."}}}}]}
{"id": "live_simple_36-13-0", "question": [[{"role": "user", "content": "Sure, here is the answer to the question:\\n\\nThe text does not define logistic regression, therefore I cannot answer this question."}]], "function": [{"name": "parseAnswer", "description": "Analyzes an input string and determines if it can be interpreted as a meaningful answer. If the input string indicates that an answer cannot be found, the output is an empty string.", "parameters": {"type": "dict", "required": ["answer"], "properties": {"answer": {"type": "string", "description": "The input string to be parsed in plain text. If the string indicates 'answer not found', the result will be an empty string ('')."}}}}]}
{"id": "live_simple_37-14-0", "question": [[{"role": "user", "content": "Sure, here is the answer to the question:\\n\\nThe text does not define logistic regression, therefore I cannot answer this question."}]], "function": [{"name": "parseAnswer", "description": "Parses a given string to determine if a valid answer can be formulated. Returns a default response if a valid answer is not identified.", "parameters": {"type": "dict", "required": ["answer"], "properties": {"answer": {"type": "string", "description": "The answer generated by the language model that is to be parsed for validity in plain text."}}}}]}
{"id": "live_simple_38-15-0", "question": [[{"role": "user", "content": "Provide the current weather information for Yosemite National Park at Mariposa, CA, using the coordinates 37.8651 N, 119.5383 W, and tell me the temperature, wind speed, and precipitation? I would like the temperature in Fahrenheit."}]], "function": [{"name": "get_current_weather", "description": "Retrieves the current weather information for a specified location using the Open-Meteo API. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"url": {"type": "string", "description": "The API endpoint for fetching weather data, with a default value pointing to the Open-Meteo API service.", "default": "https://api.open-meteo.com/v1/forecast"}, "location": {"type": "string", "description": "The geographical location for which to retrieve weather data, in the format of 'City, State (abbr)' or 'Location, State (abbr)', such as 'San Francisco, CA'."}, "unit": {"type": "string", "description": "The unit of measurement for temperature values.", "enum": ["celsius", "fahrenheit"], "default": "celsius"}}}}]}
{"id": "live_simple_39-16-0", "question": [[{"role": "user", "content": "Could you provide me with the hourly forecast for temperature, wind speed, and precipitation for the next 10 days at the coordinates 37.8651 N, 119.5383 W?"}]], "function": [{"name": "fetch_weather_data", "description": "Retrieves weather forecast data for a specific location using the Open-Meteo API.", "parameters": {"type": "dict", "required": ["latitude", "longitude"], "properties": {"url": {"type": "string", "description": "The API endpoint for fetching weather data. This should be the full URL, including protocol and domain, without parameters.", "default": "https://api.open-meteo.com/v1/forecast"}, "latitude": {"type": "float", "description": "The latitude of the location for which weather data is to be fetched."}, "longitude": {"type": "float", "description": "The longitude of the location for which weather data is to be fetched."}, "units": {"type": "string", "description": "The units for temperature and wind speed.", "enum": ["metric", "imperial"], "default": "metric"}, "language": {"type": "string", "description": "The language for weather condition texts.", "enum": ["en", "es", "de", "fr"], "default": "en"}}}}]}
{"id": "live_simple_40-17-0", "question": [[{"role": "user", "content": "I'd like to start the air purifying function in my living room, please set the air conditioner to 'AIR_CLEAN' mode with strongest wind strength, with monitoring."}]], "function": [{"name": "ThinQ_Connect", "description": "Send a command to control an LG ThinQ appliance, such as an air conditioner, by setting various operation modes and target settings.", "parameters": {"type": "dict", "required": ["body"], "properties": {"body": {"type": "dict", "description": "A dictionary containing the settings and modes to control the LG ThinQ appliance.", "properties": {"airConJobMode": {"type": "string", "description": "The current job mode of the air conditioner.", "enum": ["AIR_CLEAN", "COOL", "AIR_DRY"], "default": "COOL"}, "windStrength": {"type": "string", "description": "The strength of the air flow.", "enum": ["LOW", "HIGH", "MID"], "default": "MID"}, "monitoringEnabled": {"type": "boolean", "description": "Flag to enable or disable air quality monitoring.", "default": false}, "airCleanOperationMode": {"type": "string", "description": "The operation mode for air cleaning.", "enum": ["POWER_ON", "POWER_OFF"], "default": "POWER_OFF"}, "powerSaveEnabled": {"type": "boolean", "description": "Flag to enable or disable power-saving mode.", "default": false}, "coolTargetTemperature": {"type": "integer", "description": "The target temperature for cooling in degrees Celsius. Valid values range from 18 to 30.", "default": 24}, "targetTemperature": {"type": "integer", "description": "The general target temperature in degrees Celsius. Valid values range from 18 to 30.", "default": 22}}}}}}]}
{"id": "live_simple_41-17-1", "question": [[{"role": "user", "content": "Switch air conditioner to air dry mode with a medium wind strength"}]], "function": [{"name": "ThinQ_Connect", "description": "Send a command to control an LG ThinQ appliance, such as an air conditioner, by setting various operation modes and target settings.", "parameters": {"type": "dict", "required": ["body"], "properties": {"body": {"type": "dict", "description": "A dictionary containing the settings and modes to control the LG ThinQ appliance.", "properties": {"airConJobMode": {"type": "string", "description": "The current job mode of the air conditioner.", "enum": ["AIR_CLEAN", "COOL", "AIR_DRY"], "default": "COOL"}, "windStrength": {"type": "string", "description": "The strength of the air flow.", "enum": ["LOW", "HIGH", "MID"], "default": "MID"}, "monitoringEnabled": {"type": "boolean", "description": "Flag to enable or disable air quality monitoring.", "default": false}, "airCleanOperationMode": {"type": "string", "description": "The operation mode for air cleaning.", "enum": ["POWER_ON", "POWER_OFF"], "default": "POWER_OFF"}, "powerSaveEnabled": {"type": "boolean", "description": "Flag to enable or disable power-saving mode.", "default": false}, "coolTargetTemperature": {"type": "integer", "description": "The target temperature for cooling in degrees Celsius. Valid values range from 18 to 30.", "default": 24}, "targetTemperature": {"type": "integer", "description": "The general target temperature in degrees Celsius. Valid values range from 18 to 30.", "default": 22}}}}}}]}
{"id": "live_simple_42-17-2", "question": [[{"role": "user", "content": "\uc9d1\uc5d0 \uc788\ub294 LG ThinQ \uc5d0\uc5b4\ucee8\uc744 \uc81c\uc2b5 \ubaa8\ub4dc\ub85c \uc124\uc815\ud558\uace0 \uc2f6\uc5b4\uc694. \ubc14\ub78c \uc138\uae30\ub294 \uc911\uac04\uc73c\ub85c \ud558\uace0, \ubaa9\ud45c \uc628\ub3c4\ub294 22\ub3c4\ub85c \uc124\uc815\ud574 \uc8fc\uc138\uc694."}]], "function": [{"name": "ThinQ_Connect", "description": "Send a command to control an LG ThinQ appliance, such as an air conditioner, by setting various operation modes and target settings.", "parameters": {"type": "dict", "required": ["body"], "properties": {"body": {"type": "dict", "description": "A dictionary containing the settings and modes to control the LG ThinQ appliance.", "properties": {"airConJobMode": {"type": "string", "description": "The current job mode of the air conditioner.", "enum": ["AIR_CLEAN", "COOL", "AIR_DRY"], "default": "COOL"}, "windStrength": {"type": "string", "description": "The strength of the air flow.", "enum": ["LOW", "HIGH", "MID"], "default": "MID"}, "monitoringEnabled": {"type": "boolean", "description": "Flag to enable or disable air quality monitoring.", "default": false}, "airCleanOperationMode": {"type": "string", "description": "The operation mode for air cleaning.", "enum": ["POWER_ON", "POWER_OFF"], "default": "POWER_OFF"}, "powerSaveEnabled": {"type": "boolean", "description": "Flag to enable or disable power-saving mode.", "default": false}, "coolTargetTemperature": {"type": "integer", "description": "The target temperature for cooling in degrees Celsius. Valid values range from 18 to 30.", "default": 24}, "targetTemperature": {"type": "integer", "description": "The general target temperature in degrees Celsius. Valid values range from 18 to 30.", "default": 22}}}}}}]}
{"id": "live_simple_43-17-3", "question": [[{"role": "user", "content": "set cool mode with a temp of 24 oC and the high wind strength."}]], "function": [{"name": "ThinQ_Connect", "description": "Send a command to control an LG ThinQ appliance, such as an air conditioner, by setting various operation modes and target settings.", "parameters": {"type": "dict", "required": ["body"], "properties": {"body": {"type": "dict", "description": "A dictionary containing the settings and modes to control the LG ThinQ appliance.", "properties": {"airConJobMode": {"type": "string", "description": "The current job mode of the air conditioner.", "enum": ["AIR_CLEAN", "COOL", "AIR_DRY"], "default": "COOL"}, "windStrength": {"type": "string", "description": "The strength of the air flow.", "enum": ["LOW", "HIGH", "MID"], "default": "MID"}, "monitoringEnabled": {"type": "boolean", "description": "Flag to enable or disable air quality monitoring.", "default": false}, "airCleanOperationMode": {"type": "string", "description": "The operation mode for air cleaning.", "enum": ["POWER_ON", "POWER_OFF"], "default": "POWER_OFF"}, "powerSaveEnabled": {"type": "boolean", "description": "Flag to enable or disable power-saving mode.", "default": false}, "coolTargetTemperature": {"type": "integer", "description": "The target temperature for cooling in degrees Celsius. Valid values range from 18 to 30.", "default": 24}, "targetTemperature": {"type": "integer", "description": "The general target temperature in degrees Celsius. Valid values range from 18 to 30.", "default": 22}}}}}}]}
{"id": "live_simple_44-18-0", "question": [[{"role": "user", "content": "the air conditioner turned on with cool mode."}]], "function": [{"name": "ThinQ_Connect", "description": "Send a command to control an appliance, such as setting operation modes, air flow strength, and target temperatures.", "parameters": {"type": "dict", "required": ["body"], "properties": {"body": {"type": "dict", "description": "A dictionary containing various control parameters for the appliance.", "properties": {"currentJobMode": {"type": "string", "description": "The current mode of operation for the air conditioner.", "enum": ["AIR_CLEAN", "COOL", "AIR_DRY"], "default": "COOL"}, "windStrength": {"type": "string", "description": "The strength level of the airflow.", "enum": ["LOW", "HIGH", "MID"], "default": "MID"}, "monitoringEnabled": {"type": "boolean", "description": "Flag to enable or disable air quality sensor monitoring.", "default": false}, "airCleanOperationMode": {"type": "string", "description": "The operation mode for the air cleaning process.", "enum": ["START", "STOP"], "default": "STOP"}, "airConOperationMode": {"type": "string", "description": "The operation mode for turning the air conditioner on or off.", "enum": ["POWER_ON", "POWER_OFF"], "default": "POWER_OFF"}, "powerSaveEnabled": {"type": "boolean", "description": "Flag to enable or disable power-saving mode.", "default": false}, "coolTargetTemperature": {"type": "integer", "description": "The target temperature for cooling mode in degrees Celsius. Value must be between 18 and 30.", "default": 24}, "targetTemperature": {"type": "integer", "description": "The general target temperature in degrees Celsius. Value must be between 18 and 30.", "default": 22}}}}}}]}
{"id": "live_simple_45-18-1", "question": [[{"role": "user", "content": "It's sooo hot. set the air conditioner to 'COOL' mode, temp to 20 degrees oC, while air cleaning on?"}]], "function": [{"name": "ThinQ_Connect", "description": "Send a command to control an appliance, such as setting operation modes, air flow strength, and target temperatures.", "parameters": {"type": "dict", "required": ["body"], "properties": {"body": {"type": "dict", "description": "A dictionary containing various control parameters for the appliance.", "properties": {"currentJobMode": {"type": "string", "description": "The current mode of operation for the air conditioner.", "enum": ["AIR_CLEAN", "COOL", "AIR_DRY"], "default": "COOL"}, "windStrength": {"type": "string", "description": "The strength level of the airflow.", "enum": ["LOW", "HIGH", "MID"], "default": "MID"}, "monitoringEnabled": {"type": "boolean", "description": "Flag to enable or disable air quality sensor monitoring.", "default": false}, "airCleanOperationMode": {"type": "string", "description": "The operation mode for the air cleaning process.", "enum": ["START", "STOP"], "default": "STOP"}, "airConOperationMode": {"type": "string", "description": "The operation mode for turning the air conditioner on or off.", "enum": ["POWER_ON", "POWER_OFF"], "default": "POWER_OFF"}, "powerSaveEnabled": {"type": "boolean", "description": "Flag to enable or disable power-saving mode.", "default": false}, "coolTargetTemperature": {"type": "integer", "description": "The target temperature for cooling mode in degrees Celsius. Value must be between 18 and 30.", "default": 24}, "targetTemperature": {"type": "integer", "description": "The general target temperature in degrees Celsius. Value must be between 18 and 30.", "default": 22}}}}}}]}
{"id": "live_simple_46-19-0", "question": [[{"role": "user", "content": "I've noticed that the current job mode of the air conditioner is set to clean, but I'd like to change it to cool mod, set the wind strength to medium, with quality monitoring. Now, start air cleaning, disable the power-saving mode, and adjust the temperature to 24 Celsius?"}]], "function": [{"name": "ThinQ_Connect", "description": "Sends a command to control an appliance, allowing the adjustment of various settings such as job modes, airflow, and temperature.", "parameters": {"type": "dict", "required": ["airConJobMode", "windStrength", "monitoringEnabled", "airCleanOperationMode", "airConOperationMode", "powerSaveEnabled", "targetTemperature"], "properties": {"airConJobMode": {"type": "string", "description": "The current job mode of the air conditioner.", "enum": ["AIR_CLEAN", "COOL", "AIR_DRY"]}, "windStrength": {"type": "string", "description": "The strength of the air flow.", "enum": ["LOW", "HIGH", "MID"]}, "monitoringEnabled": {"type": "boolean", "description": "A flag to enable or disable air quality sensor monitoring."}, "airCleanOperationMode": {"type": "string", "description": "The operation mode for the air cleaning process.", "enum": ["START", "STOP"]}, "airConOperationMode": {"type": "string", "description": "The operation mode of the air conditioner itself.", "enum": ["POWER_ON", "POWER_OFF"]}, "powerSaveEnabled": {"type": "boolean", "description": "A flag to enable or disable power-saving mode."}, "targetTemperature": {"type": "integer", "description": "The target temperature to set for the air conditioner, in degrees Celsius."}}}}]}
{"id": "live_simple_47-20-0", "question": [[{"role": "user", "content": "What is the multiplication of 3 and 2"}]], "function": [{"name": "multiply", "description": "Multiplies two integers and returns the result.", "parameters": {"type": "dict", "properties": {"a": {"type": "integer", "description": "The first integer to be multiplied."}, "b": {"type": "integer", "description": "The second integer to be multiplied."}}, "required": ["a", "b"]}}]}
{"id": "live_simple_48-21-0", "question": [[{"role": "user", "content": "Could you recommend a lager that's bitter, has a hoppy aroma, and presents a pale color from Sierra Nevada brewery?"}]], "function": [{"name": "find_beer", "description": "Recommend a beer based on specified attributes such as brewery, taste, aroma, color, style, and more.", "parameters": {"type": "dict", "required": ["brewery", "taste", "aroma", "color", "style"], "properties": {"brewery": {"type": "string", "description": "The brewery name to find similar beers."}, "taste": {"type": "string", "description": "The desired taste profile in the beer, such as 'bitter', 'sweet', 'sour'."}, "aroma": {"type": "string", "description": "The desired aroma profile in the beer, such as 'fruity', 'hoppy', 'malty'."}, "color": {"type": "string", "description": "The desired color of the beer, such as 'pale', 'amber', 'dark'."}, "style": {"type": "string", "description": "The style of beer being searched for, such as 'IPA', 'stout', 'lager'."}, "abv_min": {"type": "float", "description": "The minimum alcohol by volume (ABV) percentage, typically a value under 12.5%.", "default": 0.0}, "abv_max": {"type": "float", "description": "The maximum alcohol by volume (ABV) percentage, a value logically above 0%.", "default": 12.5}, "ibu_min": {"type": "integer", "description": "The minimum International Bitterness Units (IBU) score, typically a value under 120.", "default": 0}, "ibu_max": {"type": "integer", "description": "The maximum International Bitterness Units (IBU) score, logically above 0.", "default": 120}, "pairings": {"type": "array", "items": {"type": "string"}, "description": "A list of food items to pair with the beer, such as 'burger', 'cheese', 'chocolate'.", "default": []}}}}]}
{"id": "live_simple_49-21-1", "question": [[{"role": "user", "content": "Do you have any porters in stock? with a rich, chocolatey aroma and a slightly bitter taste. It should be dark in color, preferably from Creek brewery. All other preferences are the standard ones. Any recommendations?"}]], "function": [{"name": "find_beer", "description": "Recommend a beer based on specified attributes such as brewery, taste, aroma, color, style, and more.", "parameters": {"type": "dict", "required": ["brewery", "taste", "aroma", "color", "style"], "properties": {"brewery": {"type": "string", "description": "The brewery name to find similar beers."}, "taste": {"type": "string", "description": "The desired taste profile in the beer, such as 'bitter', 'sweet', 'sour'."}, "aroma": {"type": "string", "description": "The desired aroma profile in the beer, such as 'fruity', 'hoppy', 'malty'."}, "color": {"type": "string", "description": "The desired color of the beer, such as 'pale', 'amber', 'dark'."}, "style": {"type": "string", "description": "The style of beer being searched for, such as 'IPA', 'stout', 'lager'."}, "abv_min": {"type": "float", "description": "The minimum alcohol by volume (ABV) percentage, typically a value under 12.5%.", "default": 0.0}, "abv_max": {"type": "float", "description": "The maximum alcohol by volume (ABV) percentage, a value logically above 0%.", "default": 12.5}, "ibu_min": {"type": "integer", "description": "The minimum International Bitterness Units (IBU) score, typically a value under 120.", "default": 0}, "ibu_max": {"type": "integer", "description": "The maximum International Bitterness Units (IBU) score, logically above 0.", "default": 120}, "pairings": {"type": "array", "items": {"type": "string"}, "description": "A list of food items to pair with the beer, such as 'burger', 'cheese', 'chocolate'.", "default": []}}}}]}
{"id": "live_simple_50-22-0", "question": [[{"role": "user", "content": "what is the live carbon intensity in Great Britain?"}]], "function": [{"name": "get_latest_carbon_intensity", "description": "Retrieve the most recent carbon intensity data for a specified zone, which helps to understand the environmental impact of electricity consumption in that area.", "parameters": {"type": "dict", "required": ["zone"], "properties": {"zone": {"type": "string", "description": "The specific geographic zone for which the carbon intensity data is requested, such as 'California' or 'New York'."}}}}]}
{"id": "live_simple_51-23-0", "question": [[{"role": "user", "content": "Change the settings of my air conditioner. Set it to cool mode with a target temperature of 22 degrees Celsius. Enable the power save mode and set the high wind strength. Start in 1 hour and 30 minutes from now? For everything else"}]], "function": [{"name": "ThinQ_Connect", "description": "Sends a command to control an appliance, allowing only a single value to be sent in one call.", "parameters": {"type": "dict", "required": ["body"], "properties": {"body": {"type": "dict", "description": "A dictionary containing the parameters for the appliance command.", "properties": {"airConJobMode": {"type": "string", "description": "The current job mode of the air conditioner.", "enum": ["AIR_CLEAN", "COOL", "AIR_DRY"], "default": "COOL"}, "windStrength": {"type": "string", "description": "The strength of the air flow.", "enum": ["LOW", "HIGH", "MID"], "default": "MID"}, "monitoringEnabled": {"type": "boolean", "description": "Whether air quality sensor monitoring is enabled.", "default": false}, "airCleanOperationMode": {"type": "string", "description": "The operation mode for the air cleaning function.", "enum": ["START", "STOP"], "default": "STOP"}, "airConOperationMode": {"type": "string", "description": "The operation mode for the air conditioner, indicating if it is turned on or off.", "enum": ["POWER_ON", "POWER_OFF"], "default": "POWER_OFF"}, "powerSaveEnabled": {"type": "boolean", "description": "Whether power save mode is enabled to conserve energy.", "default": false}, "targetTemperature": {"type": "integer", "description": "The target temperature to set for the appliance, in degrees Celsius.", "default": 20}, "relativeHourToStop": {"type": "integer", "description": "The number of hours from the current time when the appliance should stop. If set to null, the appliance will not be scheduled to stop.", "default": null}, "relativeMinuteToStop": {"type": "integer", "description": "The number of minutes from the current time when the appliance should stop. If set to null, the appliance will not be scheduled to stop.", "default": null}, "relativeHourToStart": {"type": "integer", "description": "The number of hours from the current time when the appliance should start. If set to null, the appliance will not be scheduled to start.", "default": null}, "relativeMinuteToStart": {"type": "integer", "description": "The number of minutes from the current time when the appliance should start. If set to null, the appliance will not be scheduled to start.", "default": null}}}}}}]}
{"id": "live_simple_52-23-1", "question": [[{"role": "user", "content": "It's extremely hot today and my air conditioner is currently turned off. Set it to start cooling at 20 degrees Celsius mid wind strength. power off after an hour"}]], "function": [{"name": "ThinQ_Connect", "description": "Sends a command to control an appliance, allowing only a single value to be sent in one call.", "parameters": {"type": "dict", "required": ["body"], "properties": {"body": {"type": "dict", "description": "A dictionary containing the parameters for the appliance command.", "properties": {"airConJobMode": {"type": "string", "description": "The current job mode of the air conditioner.", "enum": ["AIR_CLEAN", "COOL", "AIR_DRY"], "default": "COOL"}, "windStrength": {"type": "string", "description": "The strength of the air flow.", "enum": ["LOW", "HIGH", "MID"], "default": "MID"}, "monitoringEnabled": {"type": "boolean", "description": "Whether air quality sensor monitoring is enabled.", "default": false}, "airCleanOperationMode": {"type": "string", "description": "The operation mode for the air cleaning function.", "enum": ["START", "STOP"], "default": "STOP"}, "airConOperationMode": {"type": "string", "description": "The operation mode for the air conditioner, indicating if it is turned on or off.", "enum": ["POWER_ON", "POWER_OFF"], "default": "POWER_OFF"}, "powerSaveEnabled": {"type": "boolean", "description": "Whether power save mode is enabled to conserve energy.", "default": false}, "targetTemperature": {"type": "integer", "description": "The target temperature to set for the appliance, in degrees Celsius.", "default": 20}, "relativeHourToStop": {"type": "integer", "description": "The number of hours from the current time when the appliance should stop. If set to null, the appliance will not be scheduled to stop.", "default": null}, "relativeMinuteToStop": {"type": "integer", "description": "The number of minutes from the current time when the appliance should stop. If set to null, the appliance will not be scheduled to stop.", "default": null}, "relativeHourToStart": {"type": "integer", "description": "The number of hours from the current time when the appliance should start. If set to null, the appliance will not be scheduled to start.", "default": null}, "relativeMinuteToStart": {"type": "integer", "description": "The number of minutes from the current time when the appliance should start. If set to null, the appliance will not be scheduled to start.", "default": null}}}}}}]}
{"id": "live_simple_53-24-0", "question": [[{"role": "user", "content": "can you create todo with the following words verbatim: go for shopping at 9 pm"}]], "function": [{"name": "todo_add", "description": "Adds a new item to the to-do list for tracking and further processing.", "parameters": {"type": "dict", "required": ["content"], "properties": {"content": {"type": "string", "description": "The text description of the to-do item."}}}}]}
{"id": "live_simple_54-25-0", "question": [[{"role": "user", "content": "Hi, my name is Charlie. Remove the 'todo random' item from my todo list"}]], "function": [{"name": "todo", "description": "Manages a todo list allowing the user to add, delete, or update items.", "parameters": {"type": "dict", "required": ["type", "content"], "properties": {"type": {"type": "string", "description": "The action to be performed on the todo list.", "enum": ["add", "delete", "update"]}, "content": {"type": "string", "description": "The details of the todo item relevant to the action being performed."}}}}]}
{"id": "live_simple_55-25-1", "question": [[{"role": "user", "content": "can you create todo for 'go for shopping at 9 pm'"}]], "function": [{"name": "todo", "description": "Manages a todo list allowing the user to add, delete, or update items.", "parameters": {"type": "dict", "required": ["type", "content"], "properties": {"type": {"type": "string", "description": "The action to be performed on the todo list.", "enum": ["add", "delete", "update"]}, "content": {"type": "string", "description": "The details of the todo item relevant to the action being performed."}}}}]}
{"id": "live_simple_56-26-0", "question": [[{"role": "user", "content": "delete todo with content - go to gym"}]], "function": [{"name": "todo_manager.handle_action", "description": "Manages a to-do list by allowing the user to add, delete, update, or complete to-do items.", "parameters": {"type": "dict", "required": ["type", "content"], "properties": {"type": {"type": "string", "description": "The action to be performed on the to-do list.", "enum": ["add", "delete", "update", "complete"]}, "content": {"type": "string", "description": "The content of the to-do item relevant to the action. For 'add' and 'update', it represents the new text of the item. For 'delete', it represents the identifier of the item to remove. For 'complete', it represents the identifier of the item to mark as completed."}}}}]}
{"id": "live_simple_57-26-1", "question": [[{"role": "user", "content": "add todo with content go to sleep at 9 pm"}]], "function": [{"name": "todo_manager.handle_action", "description": "Manages a to-do list by allowing the user to add, delete, update, or complete to-do items.", "parameters": {"type": "dict", "required": ["type", "content"], "properties": {"type": {"type": "string", "description": "The action to be performed on the to-do list.", "enum": ["add", "delete", "update", "complete"]}, "content": {"type": "string", "description": "The content of the to-do item relevant to the action. For 'add' and 'update', it represents the new text of the item. For 'delete', it represents the identifier of the item to remove. For 'complete', it represents the identifier of the item to mark as completed."}}}}]}
{"id": "live_simple_58-27-0", "question": [[{"role": "system", "content": "\nYou are an AI chatbot who helps users in providing information related to movies, cinema halls and booking movie tickets for them.  \nAs a system bot, consider / calculate / default the movie date to current date (today's date) in India. \n"}, {"role": "user", "content": "list movies in Mumbai?"}]], "function": [{"name": "get_movies", "description": "Retrieves a list of movies playing in a specified city and cinema hall, filtered by date, language, and format.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The city where the cinema is located, in the format 'City Name', such as 'Noida', 'Amritsar', or 'Indore'."}, "cinema_hall": {"type": "string", "description": "The name of the cinema hall, for example, 'PVR Logix', 'SARV Cinemas', 'PVR SCT City Centre', or 'Miraj Cinemas'.", "default": "All"}, "movie_date": {"type": "string", "description": "The date for which to retrieve movies, in the format 'DD MMM, YYYY', such as '24th Feb, 2023'. Defaults to the current date in India if not specified.", "default": null}, "movie_language": {"type": "string", "description": "The language of the movie, such as 'Hindi', 'English', 'Malayalam', 'Telugu', or 'Punjabi'.", "default": "All"}, "movie_format": {"type": "string", "description": "The format in which the movie is available, such as '2D', '3D', '4DX', '2D Screen X', 'ICE', or 'IMAX 2D'.", "enum": ["2D", "3D", "4DX", "2D Screen X", "ICE", "IMAX 2D"], "default": "2D"}}}}]}
{"id": "live_simple_59-28-0", "question": [[{"role": "system", "content": "\nYou are an AI chatbot who helps users in providing information related to movies, cinema halls and booking movie tickets for them.  \nAs a system bot, consider / calculate / default the movie date to current date (today's date) in India. \n"}, {"role": "user", "content": "What movies are playing today in Mumbai?"}]], "function": [{"name": "get_movies", "description": "Retrieve a list of movies playing in a specified city, optionally filtered by cinema hall, date, language, and format.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The city where the movies are being searched for, such as 'Los Angeles', 'New York'."}, "cinema_hall": {"type": "string", "description": "The name of the cinema hall to filter the movies by. Optional parameter.", "default": null}, "movie_date": {"type": "string", "description": "The date when the movies are playing, in the format 'YYYY-MM-DD'. Optional parameter. Default is the current date.", "default": null}, "movie_language": {"type": "string", "description": "The language of the movies to filter by, such as 'English' or 'Spanish'. Optional parameter.", "default": null}, "movie_format": {"type": "string", "description": "The format of the movie, such as '2D', '3D', 'IMAX'. Optional parameter.", "enum": ["2D", "3D", "IMAX"], "default": "2D"}}}}]}
{"id": "live_simple_60-29-0", "question": [[{"role": "user", "content": "I've completed the task 'Submit monthly financial report'. Mark it as completed on my to-do list?"}]], "function": [{"name": "todo", "description": "Manages a todo list by allowing the user to add, delete, or mark tasks as completed.", "parameters": {"type": "dict", "required": ["type", "content"], "properties": {"type": {"type": "string", "description": "The type of action to be performed on the todo list. 'add' to add a new task, 'delete' to remove an existing task, or 'complete' to mark a task as completed.", "enum": ["add", "delete", "complete"]}, "content": {"type": "string", "description": "The content or description of the task for the specified action."}}}}]}
{"id": "live_simple_61-29-1", "question": [[{"role": "user", "content": "add a todo 'go to gym tomorrow.'"}]], "function": [{"name": "todo", "description": "Manages a todo list by allowing the user to add, delete, or mark tasks as completed.", "parameters": {"type": "dict", "required": ["type", "content"], "properties": {"type": {"type": "string", "description": "The type of action to be performed on the todo list. 'add' to add a new task, 'delete' to remove an existing task, or 'complete' to mark a task as completed.", "enum": ["add", "delete", "complete"]}, "content": {"type": "string", "description": "The content or description of the task for the specified action."}}}}]}
{"id": "live_simple_62-29-2", "question": [[{"role": "user", "content": "I need to remove the task with the content 'ravi' from my todo list."}]], "function": [{"name": "todo", "description": "Manages a todo list by allowing the user to add, delete, or mark tasks as completed.", "parameters": {"type": "dict", "required": ["type", "content"], "properties": {"type": {"type": "string", "description": "The type of action to be performed on the todo list. 'add' to add a new task, 'delete' to remove an existing task, or 'complete' to mark a task as completed.", "enum": ["add", "delete", "complete"]}, "content": {"type": "string", "description": "The content or description of the task for the specified action."}}}}]}
{"id": "live_simple_63-29-3", "question": [[{"role": "user", "content": "add todo with content go to sleep at 9 pm"}]], "function": [{"name": "todo", "description": "Manages a todo list by allowing the user to add, delete, or mark tasks as completed.", "parameters": {"type": "dict", "required": ["type", "content"], "properties": {"type": {"type": "string", "description": "The type of action to be performed on the todo list. 'add' to add a new task, 'delete' to remove an existing task, or 'complete' to mark a task as completed.", "enum": ["add", "delete", "complete"]}, "content": {"type": "string", "description": "The content or description of the task for the specified action."}}}}]}
{"id": "live_simple_64-29-4", "question": [[{"role": "user", "content": "I need to remember to 'go to Goa'"}]], "function": [{"name": "todo", "description": "Manages a todo list by allowing the user to add, delete, or mark tasks as completed.", "parameters": {"type": "dict", "required": ["type", "content"], "properties": {"type": {"type": "string", "description": "The type of action to be performed on the todo list. 'add' to add a new task, 'delete' to remove an existing task, or 'complete' to mark a task as completed.", "enum": ["add", "delete", "complete"]}, "content": {"type": "string", "description": "The content or description of the task for the specified action."}}}}]}
{"id": "live_simple_65-29-5", "question": [[{"role": "user", "content": "I need to add 'Hi charlie' to my list of tasks."}]], "function": [{"name": "todo", "description": "Manages a todo list by allowing the user to add, delete, or mark tasks as completed.", "parameters": {"type": "dict", "required": ["type", "content"], "properties": {"type": {"type": "string", "description": "The type of action to be performed on the todo list. 'add' to add a new task, 'delete' to remove an existing task, or 'complete' to mark a task as completed.", "enum": ["add", "delete", "complete"]}, "content": {"type": "string", "description": "The content or description of the task for the specified action."}}}}]}
{"id": "live_simple_66-30-0", "question": [[{"role": "system", "content": "The Requirement Extractor is designed to interpret and process multiple user queries in a single input, especially in the context of inventory management and product information."}, {"role": "user", "content": "I'm wondering inventory levels for item IDs 102 for size L and 103 for size M are below the minimum threshold of 20 units and let me know if they need to be restocked?"}]], "function": [{"name": "inventory.restock_check", "description": "Checks the inventory levels for specified items and determines if restocking is required based on minimum threshold levels.", "parameters": {"type": "dict", "required": ["item_ids", "threshold"], "properties": {"item_ids": {"type": "array", "items": {"type": "integer"}, "description": "A list of unique integer identifiers for items to check in the inventory."}, "threshold": {"type": "integer", "description": "The minimum inventory level before restocking is triggered."}, "include_discontinued": {"type": "boolean", "description": "Whether to include discontinued items in the restock check.", "default": false}}}}]}
{"id": "live_simple_67-31-0", "question": [[{"role": "user", "content": "Quiero calcular el pago mensual para un cr\u00e9dito de auto de 1,000,000 de pesos a 12 meses, para un veh\u00edculo del a\u00f1o 2024, con un enganche del 20%."}]], "function": [{"name": "obtener_cotizacion_de_creditos", "description": "Calcula el pago mensual que un cliente debe realizar para un cr\u00e9dito, basado en el monto total del cr\u00e9dito, el plazo en meses, la tasa de inter\u00e9s y el enganche m\u00ednimo (si aplica).", "parameters": {"type": "dict", "required": ["monto_del_credito", "plazo_del_credito_mensual", "producto"], "properties": {"monto_del_credito": {"type": "float", "description": "El monto total del pr\u00e9stamo o cr\u00e9dito en pesos. Por ejemplo, 1000000."}, "plazo_del_credito_mensual": {"type": "integer", "description": "El plazo en meses para pagar el cr\u00e9dito."}, "tasa_interes_minima": {"type": "float", "description": "La tasa de inter\u00e9s m\u00ednima anual fija en porcentaje. Por ejemplo, 9.60% para un cr\u00e9dito hipotecario. Si el usuario no especifica una tasa, se utiliza la tasa predeterminada del producto.", "default": 5.0}, "producto": {"type": "string", "description": "El tipo de cr\u00e9dito que solicita el cliente, escrito en min\u00fasculas.", "enum": ["hipotecario", "auto", "personal", "negocios"]}, "a\u00f1o_vehiculo": {"type": "integer", "description": "El a\u00f1o del veh\u00edculo, necesario en caso de ser un cr\u00e9dito de auto. Ejemplo: 2023.", "default": null}, "enganche": {"type": "float", "description": "El porcentaje de enganche proporcionado por el cliente o el enganche m\u00ednimo en la base de datos, si es aplicable. Ejemplo: 0.1 para un enganche del 10%. Si no hay enganche, el valor por defecto es 0.", "default": 0.0}}}}]}
{"id": "live_simple_68-32-0", "question": [[{"role": "user", "content": "Could you tell me what I get if I add 5.0 to 3.0?"}]], "function": [{"name": "sum", "description": "Calculate the sum of two numeric values.", "parameters": {"type": "dict", "required": ["a", "b"], "properties": {"a": {"type": "float", "description": "The first operand in the summation; a floating-point number."}, "b": {"type": "float", "description": "The second operand in the summation; a floating-point number."}}}}]}
{"id": "live_simple_69-33-0", "question": [[{"role": "user", "content": "I need to review the sequence of events for a vulnerability in our system. Can you retrieve the analysis for project with UUID SUPERFANCY, component AB1010CD, and vulnerability ef903ac-893-f00?"}]], "function": [{"name": "analysis_api.AnalysisApi.retrieve_analysis", "description": "Retrieves the trail of analysis for a given project, component, and vulnerability based on their respective UUIDs.", "parameters": {"type": "dict", "required": ["project", "component", "vulnerability"], "properties": {"project": {"type": "string", "description": "The unique identifier (UUID) of the project."}, "component": {"type": "string", "description": "The unique identifier (UUID) of the component within the project."}, "vulnerability": {"type": "string", "description": "The unique identifier (UUID) of the vulnerability associated with the component."}}}}]}
{"id": "live_simple_70-34-0", "question": [[{"role": "user", "content": "Can you retrieve the list of today's alerts for the sensor with serial number Q3CC-CRT3-SZ2G, showing a maximum of 10 alerts per page?"}]], "function": [{"name": "get_sensor_alerts", "description": "Retrieves a paginated list of alerts generated by sensors within a specific time frame, optionally filtered by network IDs, sensor serial number, and trigger metric.", "parameters": {"type": "dict", "required": ["perPage"], "properties": {"perPage": {"type": "integer", "description": "The number of alert entries per page. Must be within the range of 3 to 100."}, "startingAfter": {"type": "string", "description": "A server-generated token representing the start of the page, typically a timestamp or an ID.", "default": null}, "endingBefore": {"type": "string", "description": "A server-generated token representing the end of the page, typically a timestamp or an ID.", "default": null}, "t0": {"type": "string", "description": "The start timestamp for the alert query in the format 'YYYY-MM-DD', up to 365 days from the current date. If not provided, defaults to the current date.", "default": null}, "t1": {"type": "string", "description": "The end timestamp for the alert query in the format 'YYYY-MM-DD', up to 365 days from the start timestamp (t0).", "default": null}, "networkId": {"type": "array", "items": {"type": "string"}, "description": "A list of network IDs to narrow down the alert query.", "default": []}, "timespan": {"type": "integer", "description": "The duration for the alert query in seconds, not to exceed 31,536,000 seconds (365 days).", "default": 86400}, "sensorSerial": {"type": "string", "description": "The specific sensor serial number to filter alerts.", "default": null}, "triggerMetric": {"type": "string", "description": "The metric that triggered the alert.", "enum": ["apparentPower", "co2", "current", "door", "frequency", "humidity", "indoorAirQuality", "noise", "pm25", "powerFactor", "realPower", "temperature", "tvoc", "upstreamPower", "voltage", "water"], "default": null}}}}]}
{"id": "live_simple_71-35-0", "question": [[{"role": "user", "content": "How have millennials views of Apple changed since July 2022?'"}]], "function": [{"name": "extract_parameters_v1", "description": "Extracts parameters for generating a database query to answer a user's question, identifying demographics, target entities, metrics, country, and date range.", "parameters": {"type": "dict", "required": ["targets", "metrics"], "properties": {"demographics": {"type": "array", "items": {"type": "string"}, "description": "Array of strings indicating demographics, geographic, or psychographic characteristics, e.g., 'wealthy', 'women'. If no demographics inferred, return empty array.", "default": []}, "targets": {"type": "array", "items": {"type": "string"}, "description": "Array of target entities and types. Each entry is a string combining 'target' and 'target_type', such as 'brand:Nike' or 'country:USA'."}, "metrics": {"type": "array", "items": {"type": "string"}, "description": "Array of strings for attitudinal metrics about target entities, e.g., 'trust', 'usage frequency'.", "enum": ["favorability", "admired employer", "buzz", "community impact", "purchasing consideration", "trust", "usage frequency", "value", "promoter", "view"]}, "country": {"type": "string", "description": "Country related to the demographics of interest. If unspecified, default is null.", "default": null}, "min_date": {"type": "string", "description": "Start date of the date range in YYYY-MM-DD format. Default is null, representing no specific start date.", "default": null}, "max_date": {"type": "string", "description": "End date of the date range in YYYY-MM-DD format. Default is null, representing no specific end date.", "default": null}, "interval": {"type": "string", "description": "Aggregation interval for data reporting, e.g., 'daily', 'monthly'. If unspecified, default is null.", "enum": ["day", "week", "month", "quarter", "year"], "default": null}}}}]}
{"id": "live_simple_72-36-0", "question": [[{"role": "user", "content": "I have an audio file, and I would like to identify the significant beats. Can you analyze it and filter out the beats with a confidence level below 50%? Also, use a 0.5-second window to ensure the beats are distinct."}]], "function": [{"name": "detect_beats_and_filter", "description": "Analyzes an audio file to detect beats and filters them based on confidence levels and timing. Returns a list of times in seconds, each representing the occurrence of a significant beat within the audio file.", "parameters": {"type": "dict", "required": ["capture_percentage", "confidence_window_size"], "properties": {"capture_percentage": {"type": "integer", "description": "Filters beats by excluding those below a specified confidence percentile. Values range from 0 to 100 (inclusive)."}, "confidence_window_size": {"type": "float", "description": "Selects the highest confidence beat within a given time window in seconds (e.g., 0.5), to ensure distinct beats are chosen in that period."}}}}]}
{"id": "live_simple_73-36-1", "question": [[{"role": "user", "content": "Can you analyze this audio file to determine the timing of significant beats, using a capture percentage of 90 and a confidence window size of 0.25 seconds?"}]], "function": [{"name": "detect_beats_and_filter", "description": "Analyzes an audio file to detect beats and filters them based on confidence levels and timing. Returns a list of times in seconds, each representing the occurrence of a significant beat within the audio file.", "parameters": {"type": "dict", "required": ["capture_percentage", "confidence_window_size"], "properties": {"capture_percentage": {"type": "integer", "description": "Filters beats by excluding those below a specified confidence percentile. Values range from 0 to 100 (inclusive)."}, "confidence_window_size": {"type": "float", "description": "Selects the highest confidence beat within a given time window in seconds (e.g., 0.5), to ensure distinct beats are chosen in that period."}}}}]}
{"id": "live_simple_74-36-2", "question": [[{"role": "user", "content": "I have an audio file that I'd like to analyze. Could you identify significant beats for me if I set the capture percentage to 70 and the confidence window size to 0.8 seconds?"}]], "function": [{"name": "detect_beats_and_filter", "description": "Analyzes an audio file to detect beats and filters them based on confidence levels and timing. Returns a list of times in seconds, each representing the occurrence of a significant beat within the audio file.", "parameters": {"type": "dict", "required": ["capture_percentage", "confidence_window_size"], "properties": {"capture_percentage": {"type": "integer", "description": "Filters beats by excluding those below a specified confidence percentile. Values range from 0 to 100 (inclusive)."}, "confidence_window_size": {"type": "float", "description": "Selects the highest confidence beat within a given time window in seconds (e.g., 0.5), to ensure distinct beats are chosen in that period."}}}}]}
{"id": "live_simple_75-36-3", "question": [[{"role": "user", "content": "Filter the beats to only include those above the 93rd percentile for confidence? Also, make sure to select the highest confidence beat within a 0.5-second window."}]], "function": [{"name": "detect_beats_and_filter", "description": "Analyzes an audio file to detect beats and filters them based on confidence levels and timing. Returns a list of times in seconds, each representing the occurrence of a significant beat within the audio file.", "parameters": {"type": "dict", "required": ["capture_percentage", "confidence_window_size"], "properties": {"capture_percentage": {"type": "integer", "description": "Filters beats by excluding those below a specified confidence percentile. Values range from 0 to 100 (inclusive)."}, "confidence_window_size": {"type": "float", "description": "Selects the highest confidence beat within a given time window in seconds (e.g., 0.5), to ensure distinct beats are chosen in that period."}}}}]}
{"id": "live_simple_76-37-0", "question": [[{"role": "user", "content": "Can you help me convert this sentence from English to French: 'What is your name?'"}]], "function": [{"name": "language_translator.translate", "description": "Translate text from a source language to a target language using an online translation service.", "parameters": {"type": "dict", "required": ["source_language", "target_language", "text"], "properties": {"source_language": {"type": "string", "description": "The language code of the source text, such as 'en' for English or 'hi' for Hindi.", "enum": ["en", "hi", "es", "fr", "de"]}, "target_language": {"type": "string", "description": "The language code for the desired target translation, such as 'hi' for Hindi or 'id' for Indonesian.", "enum": ["hi", "id", "es", "fr", "de"]}, "text": {"type": "string", "description": "The text content to be translated."}}}}]}
{"id": "live_simple_77-38-0", "question": [[{"role": "user", "content": "Could you tell me the current weather conditions, including the temperature, wind, and precipitation, for London in the UK?"}]], "function": [{"name": "weather.get", "description": "Get the current weather conditions, including temperature, wind, and precipitation, for a specified city and country.", "parameters": {"type": "dict", "required": ["city", "country"], "properties": {"city": {"type": "string", "description": "The name of the city for which the weather is requested, e.g., 'Los Angeles'."}, "country": {"type": "string", "description": "The country where the city is located, e.g., 'US' for the United States. Use ISO 3166-1 alpha-2 country codes."}, "units": {"type": "string", "description": "The units for temperature measurement.", "enum": ["metric", "imperial"], "default": "metric"}, "include_forecast": {"type": "boolean", "description": "Whether to include a 5-day weather forecast in the response.", "default": false}}}}]}
{"id": "live_simple_78-39-0", "question": [[{"role": "system", "content": "You are an AI programming assistant, utilizing the Gorilla LLM model, developed by Gorilla LLM, and you only answer questions related to computer science. For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer. If your response contains function you have to put <<function>> infront of it.\n### Instruction: <<function>>[[{\"name\": \"send_email\", \"api_name\": \"send_email\", \"description\": \"template to have an email sent.\", \"parameters\": [{\"name\": \"to_address\", \"description\": \"To address for email\"}, {\"name\": \"subject\", \"description\": \"the subject of the email\"}]}]]\n<<question>>Send Andy from Gorilla an email and ask him for the sales forecast spreadsheet.\n### Response: "}, {"role": "user", "content": "Could you draft an email to <NAME_EMAIL> with the subject 'Sales Forecast Request' and include a message \"where is the latest sales forecast spreadsheet?\""}]], "function": [{"name": "send_email", "description": "Send an email to the specified recipient with a given subject and optional message body.", "parameters": {"type": "dict", "required": ["to_address", "subject"], "properties": {"to_address": {"type": "string", "description": "The email address of the recipient. Format should be a valid email address, such as '<EMAIL>'."}, "subject": {"type": "string", "description": "The subject line of the email."}, "body": {"type": "string", "description": "The main content of the email. Plain text or HTML content is expected.", "default": ""}, "cc_address": {"type": "string", "description": "The email address to be included in the CC field. Format should be a valid email address, such as '<EMAIL>'.", "default": ""}, "bcc_address": {"type": "string", "description": "The email address to be included in the BCC field. Format should be a valid email address, such as '<EMAIL>'.", "default": ""}, "attachments": {"type": "array", "items": {"type": "string"}, "description": "A list of file paths or URLs for files to be attached to the email.", "default": []}}}}]}
{"id": "live_simple_79-40-0", "question": [[{"role": "user", "content": "You are an AI programming assistant, utilizing the Gorilla LLM model, developed by Gorilla LLM, and you only answer questions related to computer science. For politically sensitive questions, security and privacy issues, and other non-computer science questions, you will refuse to answer. <<question>>Find hotels in San Diego\n"}]], "function": [{"name": "search_hotels", "description": "Retrieves a list of hotels based on the specified location.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location of the hotels to search for, in the format of 'City, State (abbr)' or 'City, Country', such as 'Seattle, WA' or 'Paris, France'."}}}}]}
{"id": "live_simple_80-41-0", "question": [[{"role": "user", "content": "I need to add a news item about the latest advancements in AI. The title should be 'Breakthrough in Artificial Intelligence', and the content must cover the recent breakthroughs in machine learning algorithms. Can you set the meta title as 'AI Breakthrough Latest Developments in Machine Learning' and the meta description to 'An overview of the recent significant advancements in artificial intelligence and machine learning technology'? Also, the URL should be 'ai-breakthrough-latest-developments'."}]], "function": [{"name": "sitefinity_create_contentitem", "description": "Create a new content item in Sitefinity CMS with specified metadata for SEO optimization and a unique URL name. Ensures that content is visible and well-represented in search engine results.", "parameters": {"type": "dict", "required": ["Title", "Content", "MetaTitle", "MetaDescription", "UrlName"], "properties": {"Title": {"type": "string", "description": "The title of the content item. It is displayed prominently on the page and used as the clickable link on search engine results pages."}, "Content": {"type": "string", "description": "The body content of the content item. Contains the main text of the content and can include HTML tags."}, "MetaTitle": {"type": "string", "description": "The HTML meta title for SEO. Should be concise and relevant to the content topic. If not provided, the 'Title' will be used."}, "MetaDescription": {"type": "string", "description": "The HTML meta description for SEO. Provides a brief summary of the content and its relevance. If not provided, a summary derived from the 'Content' will be used."}, "UrlName": {"type": "string", "description": "A human-readable and SEO-friendly URL slug for the content item. It is typically constructed using hyphens to separate words and should be unique."}}}}]}
{"id": "live_simple_81-42-0", "question": [[{"role": "user", "content": "I need to add a new article to our Sitefinity CMS. The article is a 'NewsItem' with the title 'Julian is testing12'. Can you set this up for me?"}]], "function": [{"name": "sitefinity_create_contentitem", "description": "Create a new content item in Sitefinity CMS with specified metadata for SEO optimization.", "parameters": {"type": "dict", "required": ["ContentItem", "Title"], "properties": {"ContentItem": {"type": "string", "description": "The content item type the user wants to work with in Sitefinity CMS, such as 'NewsItem', 'BlogPost', 'Event'.", "enum": ["NewsItem", "BlogPost", "Event"]}, "Title": {"type": "string", "description": "The title or name of the content item."}, "Content": {"type": "string", "description": "The detailed content or description of the content item. If not provided, the Title will be used.", "default": null}, "MetaTitle": {"type": "string", "description": "The HTML meta title of the content item for SEO purposes. If not provided, the Title will be used.", "default": null}, "MetaDescription": {"type": "string", "description": "The HTML meta description of the content item for SEO purposes. If not provided, the Title will be used.", "default": null}, "UrlName": {"type": "string", "description": "The URL-friendly name of the content item. If not provided, a sanitized version of the Title will be used.", "default": null}}}}]}
{"id": "live_simple_82-43-0", "question": [[{"role": "user", "content": "I need to add a news article titled 'Julian is testing' to our Sitefinity CMS. Could you help with that?"}]], "function": [{"name": "sitefinity_create_contentitem", "description": "Creates a new content item in Sitefinity CMS with specified metadata for SEO optimization and URL naming.", "parameters": {"type": "dict", "required": ["ContentItem", "Title"], "properties": {"ContentItem": {"type": "string", "description": "The type of content item to create in Sitefinity CMS. Refer to Sitefinity documentation for supported types.", "enum": ["News", "BlogPost", "Event", "Product"]}, "Title": {"type": "string", "description": "The title or name of the content item."}, "Content": {"type": "string", "description": "The detailed content or description of the content item. Defaults to the title if not provided.", "default": null}, "MetaTitle": {"type": "string", "description": "The HTML meta title of the content item for SEO purposes. Defaults to the title if not provided.", "default": null}, "MetaDescription": {"type": "string", "description": "The HTML meta description of the content item for SEO purposes. Defaults to the title if not provided.", "default": null}, "UrlName": {"type": "string", "description": "The URL-friendly name of the content item. This name will be used in the web address of the content item.", "default": null}}}}]}
{"id": "live_simple_83-44-0", "question": [[{"role": "user", "content": "I want to publish a news article titled 'Julian is Testing' with 'a detailed analysis on the impact of sleep patterns on productivity' as content. Could you set up the meta title to be 'Productivity and Sleep Patterns' and the URL to 'julian-testing-productivity'? Let's also add a brief meta description saying 'Exploring how sleep affects work efficiency'."}]], "function": [{"name": "sitefinity_create_contentitem", "description": "Create a new content item in Sitefinity CMS with specified metadata.", "parameters": {"type": "dict", "required": ["ContentItem", "Title", "Content", "MetaTitle", "MetaDescription", "UrlName"], "properties": {"ContentItem": {"type": "string", "description": "The type of content item to create in Sitefinity CMS, such as 'news', 'blog', or 'event'.", "enum": ["news", "blog", "event"]}, "Title": {"type": "string", "description": "The title or name of the content item."}, "Content": {"type": "string", "description": "The full content or body of the content item."}, "MetaTitle": {"type": "string", "description": "The HTML meta title of the content item for SEO purposes. If not provided, it defaults to the title of the content item."}, "MetaDescription": {"type": "string", "description": "The HTML meta description of the content item for SEO purposes. If not provided, it defaults to a summary derived from the content body."}, "UrlName": {"type": "string", "description": "The URL-friendly name of the content item. Should be a URL slug, formatted in lowercase with hyphens, such as 'julian-is-testing'."}}}}]}
{"id": "live_simple_84-45-0", "question": [[{"role": "user", "content": "Can you provide me with the latitude and longitude of Seattle, that city where Mr. Gates works in?"}]], "function": [{"name": "get_coordinates_from_city", "description": "Fetches the latitude and longitude of a specified city using the Maps.co Geocoding API. The function returns a tuple with the latitude and longitude or an error message if the city cannot be found or if there is an issue with the API request.", "parameters": {"type": "dict", "properties": {"city_name": {"type": "string", "description": "The name of the city to retrieve the coordinates for, in plain text, such as 'New York' or 'London'."}}, "required": ["city_name"]}}]}
{"id": "live_simple_85-46-0", "question": [[{"role": "user", "content": "Could you tell me the current temperature in Berkeley using latitude 37.8715 and longitude -122.2730?"}]], "function": [{"name": "open_meteo_api.fetch_weather_data", "description": "Fetches the current temperature from the Open-Meteo API for a specified location using latitude and longitude coordinates.", "parameters": {"type": "dict", "required": ["coordinates"], "properties": {"coordinates": {"type": "array", "items": {"type": "float"}, "description": "An array containing the latitude and longitude of the location for which the weather data is requested. Latitude and longitude should be in decimal degrees."}, "units": {"type": "string", "description": "The unit system in which the temperature will be returned.", "enum": ["Celsius", "Fahrenheit", "Kelvin"], "default": "Celsius"}}}}]}
{"id": "live_simple_86-47-0", "question": [[{"role": "user", "content": "I need the numbers [1, 54, 3, 1, 76, 2343, 21] arranged from the smallest to the largest."}]], "function": [{"name": "sort_array", "description": "Sorts an array of integers in ascending order.", "parameters": {"type": "dict", "required": ["array"], "properties": {"array": {"type": "array", "items": {"type": "integer"}, "description": "The array of integers to be sorted."}, "reverse": {"type": "boolean", "description": "Determines if the array should be sorted in descending order. Defaults to false indicating ascending order.", "default": false}}}}]}
{"id": "live_simple_87-48-0", "question": [[{"role": "user", "content": "Can you tell me the current geographical coordinates of my device in DMS format, including altitude?"}]], "function": [{"name": "get_current_loc", "description": "Retrieves the current geographical location of the device, returning the coordinates as longitude and latitude values.", "parameters": {"type": "dict", "properties": {"coordinate_format": {"type": "string", "description": "The desired format for the returned coordinates, such as 'degrees' or 'DMS' (degrees, minutes, seconds).", "enum": ["degrees", "DMS"], "default": "degrees"}, "include_altitude": {"type": "boolean", "description": "Specifies whether to include altitude information in the location data.", "default": false}, "timeout": {"type": "integer", "description": "The maximum time to wait for a location in seconds.", "default": 30}}, "required": []}}]}
{"id": "live_simple_88-49-0", "question": [[{"role": "user", "content": "I just enjoyed a large cup of chai tea with half and half for my morning snack. Could you record that I drank 16 ounces of chai tea as a snack?"}]], "function": [{"name": "log_food", "description": "Logs a food item with details about the portion size and the meal it is associated with.", "parameters": {"type": "dict", "required": ["food_name", "portion_amount", "meal_name"], "properties": {"food_name": {"type": "string", "description": "The name of the food to log."}, "portion_amount": {"type": "float", "description": "The amount of the food item that was consumed, in specified units."}, "portion_unit": {"type": "string", "description": "The unit of measure for the portion amount. Choose a unit such as 'grams', 'ounces', 'pieces', 'cups', or 'tablespoons'.", "enum": ["grams", "ounces", "pieces", "cups", "tablespoons"], "default": "grams"}, "meal_name": {"type": "string", "description": "The name of the meal with which the food item is associated. Options include 'breakfast', 'lunch', 'dinner', or 'snack'."}}}}]}
{"id": "live_simple_89-50-0", "question": [[{"role": "user", "content": "I'd like to start the process for creating an interior design report. My preferences include a modern style, a warm color scheme, and my budget is mid-range. Could we use data from our internal database and have the report in DOCX format, including visual outputs?"}]], "function": [{"name": "interior_design_analysis.generate_report", "description": "Generates a comprehensive report based on user's interior design preferences and requirements, utilizing historical data and trends to provide style recommendations, material optimization, space utilization analysis, environmental impact assessment, and visual outputs.", "parameters": {"type": "dict", "required": ["user_preferences", "data_source"], "properties": {"user_preferences": {"type": "string", "description": "Serialized JSON string detailing user's design preferences and requirements, including aesthetic and functional needs. Format should follow the structure: '{\"style\": \"modern\", \"color_scheme\": \"warm\", \"budget\": \"mid-range\"}'."}, "data_source": {"type": "string", "description": "A string specifying the source of historical design data and user surveys, such as 'internal_database' or 'survey_api'."}, "output_format": {"type": "string", "description": "Desired format for the output report. Options include 'PDF', 'HTML', 'DOCX'.", "enum": ["PDF", "HTML", "DOCX"], "default": "PDF"}, "include_visuals": {"type": "boolean", "description": "A boolean flag to indicate whether to include visual outputs like mood boards, diagrams, floorplans, and 3D models in the report.", "default": true}, "environmental_impact_focus": {"type": "boolean", "description": "A boolean flag to indicate whether the report should prioritize environmental impact assessments.", "default": false}}}}]}
{"id": "live_simple_90-51-0", "question": [[{"role": "user", "content": "Can you tell me the current temperature in Paris, France?"}]], "function": [{"name": "temperature", "description": "Return the current temperature for a specified location.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which the temperature is requested, in the format of 'City, Country', (e.g., 'Beijing, China')."}, "units": {"type": "string", "description": "The unit of temperature measurement.", "enum": ["Celsius", "Fahrenheit", "Kelvin"], "default": "Celsius"}, "time": {"type": "string", "description": "The desired time for the temperature reading in ISO 8601 format (e.g., '2023-04-12T14:00:00Z'). If not provided, the current temperature is returned.", "default": null}}}}]}
{"id": "live_simple_91-52-0", "question": [[{"role": "user", "content": "Can you tell me the details of the individual who has the identifier number 4?"}]], "function": [{"name": "fetchPerson", "description": "Retrieves the details of a person using their unique identifier.", "parameters": {"type": "dict", "required": ["id"], "properties": {"id": {"type": "integer", "description": "The unique identifier for the person to be fetched."}}}}]}
{"id": "live_simple_92-53-0", "question": [[{"role": "user", "content": "I want to listen to music by K\u00e4\u00e4rij\u00e4, can you start playing it?"}]], "function": [{"name": "play_artist", "description": "Initiates playback of the specified artist's music on the user's default music player.", "parameters": {"type": "dict", "required": ["artist_name"], "properties": {"artist_name": {"type": "string", "description": "The name of the artist whose music is to be played."}}}}]}
{"id": "live_simple_93-54-0", "question": [[{"role": "user", "content": "I'd like to listen to all tracks by K\u00e4\u00e4rij\u00e4."}]], "function": [{"name": "play_artist", "description": "Initiate playback of all tracks associated with the specified artist.", "parameters": {"type": "dict", "required": ["artist_name"], "properties": {"artist_name": {"type": "string", "description": "The name of the artist whose tracks are to be played."}}}}]}
{"id": "live_simple_94-55-0", "question": [[{"role": "user", "content": "Can you play the song 'Cha Cha Cha' by the artist K\u00e4\u00e4rij\u00e4?"}]], "function": [{"name": "play_song", "description": "Plays the specified song by the given artist.", "parameters": {"type": "dict", "required": ["artist_name", "song_name"], "properties": {"artist_name": {"type": "string", "description": "The name of the artist whose song is to be played."}, "song_name": {"type": "string", "description": "The name of the song to be played."}}}}]}
{"id": "live_simple_95-56-0", "question": [[{"role": "user", "content": "I have a list of numerical values: [2.5, 3.6, 4.1, 5.2], and I need to apply normalization to them. Also, I have these categories: ['red', 'blue', 'green'] that I'd like to be one-hot encoded. Could you process this data for me?"}]], "function": [{"name": "process_data", "description": "This function preprocesses the input data by applying normalization and encoding categorical variables. It prepares data for machine learning models, ensuring numerical values are scaled properly and categorical variables are appropriately transformed.", "parameters": {"type": "dict", "required": ["data", "normalize", "categories"], "properties": {"data": {"type": "array", "items": {"type": "float"}, "description": "A list of numerical values representing the data to be processed. Each value should be a floating-point number."}, "normalize": {"type": "boolean", "description": "A flag indicating whether to apply normalization to the numerical data. If set to true, the data will be scaled to a standard range."}, "categories": {"type": "array", "items": {"type": "string"}, "description": "A list of categories for encoding categorical variables. Each category should be a unique string identifier."}, "encoding_type": {"type": "string", "description": "The method used to encode categorical variables. Choose 'label' for label encoding and 'one-hot' for one-hot encoding.", "enum": ["label", "one-hot"], "default": "label"}, "missing_values": {"type": "string", "description": "The strategy for handling missing values in the dataset. Select 'mean' to replace missing values with the mean of the column, 'median' for the median, or 'most_frequent' for the most frequent value.", "enum": ["mean", "median", "most_frequent"], "default": "mean"}}}}]}
{"id": "live_simple_96-57-0", "question": [[{"role": "user", "content": "Could you tell me the current weather conditions in Boston, MA?"}]], "function": [{"name": "get_current_weather", "description": "Retrieve the current weather conditions for a specified location. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)', such as 'San Francisco, CA' or 'New York, NY'."}, "unit": {"type": "string", "description": "The temperature unit for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_97-57-1", "question": [[{"role": "user", "content": "Could you tell me the current weather conditions in El Gastor, Andalusia?"}]], "function": [{"name": "get_current_weather", "description": "Retrieve the current weather conditions for a specified location. If using state, then use short form like CA.", "parameters": {"type": "dict", "required": ["location"], "properties": {"location": {"type": "string", "description": "The location for which to get the weather, in the format of 'City, State (abbr)' or 'City, Location', such as 'San Francisco, CA', or 'New York, NY'."}, "unit": {"type": "string", "description": "The temperature unit for the weather report.", "enum": ["celsius", "fahrenheit"], "default": "fahrenheit"}}}}]}
{"id": "live_simple_98-58-0", "question": [[{"role": "user", "content": "Can you tell me the current weather in Chennai? I believe the latitude is around 13.0827 and the longitude is approximately 80.2707."}]], "function": [{"name": "get_weather_by_coordinates", "description": "Retrieves current weather data for the specified city using its geographical coordinates.", "parameters": {"type": "dict", "required": ["city", "lat", "lon"], "properties": {"city": {"type": "string", "description": "The name of the city, such as 'New York, NY'."}, "lat": {"type": "float", "description": "The latitude of the city in decimal degrees."}, "lon": {"type": "float", "description": "The longitude of the city in decimal degrees."}}}}]}
{"id": "live_simple_99-59-0", "question": [[{"role": "user", "content": "Can you establish a connection to my Bluetooth speaker named 'ue boom'?"}]], "function": [{"name": "connectBluetooth", "description": "Establishes a connection to a Bluetooth device using its name.", "parameters": {"type": "dict", "required": ["device_name"], "properties": {"device_name": {"type": "string", "description": "The name of the Bluetooth device to connect to."}, "timeout": {"type": "integer", "description": "The number of seconds to attempt a connection before timing out.", "default": 30}, "auto_reconnect": {"type": "boolean", "description": "Whether to automatically attempt to reconnect if the connection is lost.", "default": false}}}}]}
{"id": "live_simple_100-59-1", "question": [[{"role": "user", "content": "I want to establish a connection with my Bluetooth speaker called 'JBL Flip 4'."}]], "function": [{"name": "connectBluetooth", "description": "Establishes a connection to a Bluetooth device using its name.", "parameters": {"type": "dict", "required": ["device_name"], "properties": {"device_name": {"type": "string", "description": "The name of the Bluetooth device to connect to."}, "timeout": {"type": "integer", "description": "The number of seconds to attempt a connection before timing out.", "default": 30}, "auto_reconnect": {"type": "boolean", "description": "Whether to automatically attempt to reconnect if the connection is lost.", "default": false}}}}]}
{"id": "live_simple_101-60-0", "question": [[{"role": "user", "content": "I'm trying to connect to a new Wi-Fi network but I forgot the name. Could you ask me for the SSID with a message saying 'Please enter the Wi-Fi network name you wish to connect to:'?"}]], "function": [{"name": "askForSSID", "description": "Prompt the user to enter the SSID when it is not known or provided.", "parameters": {"type": "dict", "required": ["prompt_message"], "properties": {"prompt_message": {"type": "string", "description": "The message displayed to the user when asking for the SSID."}, "default_ssid": {"type": "string", "description": "The default SSID to be used if the user does not enter one.", "default": "default_network"}, "retry_attempts": {"type": "integer", "description": "The number of attempts a user has to enter the SSID before the function fails.", "default": 3}, "hide_input": {"type": "boolean", "description": "Whether the SSID input should be hidden for privacy. Set to true to hide the input.", "default": false}}}}]}
{"id": "live_simple_102-61-0", "question": [[{"role": "user", "content": "I've just made a purchase of $59.99 in San Francisco, California. How much the sales tax will be for this amount?"}]], "function": [{"name": "calculate_tax", "description": "Calculates the applicable sales tax for a given purchase amount and jurisdiction. The function returns the total amount with tax.", "parameters": {"type": "dict", "required": ["purchase_amount", "state"], "properties": {"purchase_amount": {"type": "float", "description": "The total purchase amount in dollars."}, "state": {"type": "string", "description": "The U.S. state abbreviation where the purchase is made, such as 'CA' for California or 'NY' for New York."}, "county": {"type": "string", "description": "The county within the state where the purchase is made. This is optional and used to calculate local tax rates.", "default": null}, "city": {"type": "string", "description": "The city within the county for local tax rates. Optional and only used if the county is provided.", "default": null}, "tax_exempt": {"type": "boolean", "description": "Indicates whether the purchase is tax-exempt.", "default": false}, "discount_rate": {"type": "float", "description": "The discount rate as a percentage if applicable. Optional.", "default": 0.0}, "apply_special_tax": {"type": "boolean", "description": "A flag to apply special tax rules based on item type or promotions. Optional.", "default": false}}}}]}
{"id": "live_simple_103-61-1", "question": [[{"role": "user", "content": "I purchased a new laptop for $999 in California, but I'm not sure how much tax I need to pay. What's the total amount including tax?"}]], "function": [{"name": "calculate_tax", "description": "Calculates the applicable sales tax for a given purchase amount and jurisdiction. The function returns the total amount with tax.", "parameters": {"type": "dict", "required": ["purchase_amount", "state"], "properties": {"purchase_amount": {"type": "float", "description": "The total purchase amount in dollars."}, "state": {"type": "string", "description": "The U.S. state abbreviation where the purchase is made, such as 'CA' for California or 'NY' for New York."}, "county": {"type": "string", "description": "The county within the state where the purchase is made. This is optional and used to calculate local tax rates.", "default": null}, "city": {"type": "string", "description": "The city within the county for local tax rates. Optional and only used if the county is provided.", "default": null}, "tax_exempt": {"type": "boolean", "description": "Indicates whether the purchase is tax-exempt.", "default": false}, "discount_rate": {"type": "float", "description": "The discount rate as a percentage if applicable. Optional.", "default": 0.0}, "apply_special_tax": {"type": "boolean", "description": "A flag to apply special tax rules based on item type or promotions. Optional.", "default": false}}}}]}
{"id": "live_simple_104-61-2", "question": [[{"role": "user", "content": "I made a purchase for $250 in New York. How much the sales tax will be?"}]], "function": [{"name": "calculate_tax", "description": "Calculates the applicable sales tax for a given purchase amount and jurisdiction. The function returns the total amount with tax.", "parameters": {"type": "dict", "required": ["purchase_amount", "state"], "properties": {"purchase_amount": {"type": "float", "description": "The total purchase amount in dollars."}, "state": {"type": "string", "description": "The U.S. state abbreviation where the purchase is made, such as 'CA' for California or 'NY' for New York."}, "county": {"type": "string", "description": "The county within the state where the purchase is made. This is optional and used to calculate local tax rates.", "default": null}, "city": {"type": "string", "description": "The city within the county for local tax rates. Optional and only used if the county is provided.", "default": null}, "tax_exempt": {"type": "boolean", "description": "Indicates whether the purchase is tax-exempt.", "default": false}, "discount_rate": {"type": "float", "description": "The discount rate as a percentage if applicable. Optional.", "default": 0.0}, "apply_special_tax": {"type": "boolean", "description": "A flag to apply special tax rules based on item type or promotions. Optional.", "default": false}}}}]}
{"id": "live_simple_105-62-0", "question": [[{"role": "user", "content": "classify these sentences\\nlink my account\\nconnect my accounts\\nhello"}]], "function": [{"name": "classify", "description": "Classifies input queries into predefined categories based on the content.", "parameters": {"type": "dict", "required": ["clean_hello", "faq_link_accounts_start"], "properties": {"clean_hello": {"type": "array", "items": {"type": "string"}, "description": "An array of strings containing queries that are casual greetings or informal hellos."}, "faq_link_accounts_start": {"type": "array", "items": {"type": "string"}, "description": "An array of strings with queries related to linking accounts within the bank's system."}}}}]}
{"id": "live_simple_106-63-0", "question": [[{"role": "user", "content": "Could you help me classify the following customer queries into the appropriate categories?\n- 'Please provide the routing number for my account.'\n- 'Which account number should I use for setting up eStatements?'\n- 'What rewards are offered for my card?'\n- 'Looking for the closest ATM for cash withdrawal, can you assist me in finding one?'\n- 'What rewards are available on my card?'\n- 'Locate ATM for immediate cash needs'\n- 'I urgently need to find an ATM for cash withdrawal, can you help me locate the nearest one?'\n- 'Activate my new card for loyalty program enrollment'\n- 'I want to cancel my card and ensure the safety of my account.'\n- 'Searching for ATM for money withdrawal'\n- 'Can you walk me through the activation process for my new bank card?'\n- 'What are the benefits of using my rewards points for entertainment?'\n- 'Find ATM for urgent cash needs'\n- 'Need to find ATM for quick cash emergency'\n- 'I'm done here. Goodbye!'\n- 'Please give me the account number for my business checking account.'\n- 'Can I purchase a cashier's check through this chat service?'\n- 'Thanks for your assistance. Goodbye!'\n- 'I need a cashier's check, how can I get one?'\n- 'You're amazing. Goodbye!'"}]], "function": [{"name": "record", "description": "Logs the types of queries users make, classifying them into predefined categories.", "parameters": {"type": "dict", "required": ["acc_routing_start", "activate_card_start", "atm_finder_start", "auto_loan_payment_start", "bank_hours_start", "cancel_card_start", "card_rewards_start", "cashier_check_start", "clean_goodbye_start"], "properties": {"acc_routing_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries requesting the bank routing numbers or account numbers."}, "activate_card_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries related to activating a new bank card."}, "atm_finder_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries asking for locations of ATMs nearby or in a specific area."}, "auto_loan_payment_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries related to making payments on auto or car loans."}, "bank_hours_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries asking for the working hours or locations of bank branches."}, "cancel_card_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries related to cancelling a bank card or asking for guidance on how to cancel a card."}, "card_rewards_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries related to rewards points or benefits associated with a bank card."}, "cashier_check_start": {"type": "array", "items": {"type": "string"}, "description": "List of requests for cashier's checks, drafts, or similar financial instruments from the bank."}, "clean_goodbye_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries saying goodbye or ending the conversation with the chatbot."}}}}]}
{"id": "live_simple_107-64-0", "question": [[{"role": "user", "content": "I have a list of customer queries and need to classify them into categories related to telephone banking services, bank working hours or locations, and descriptions of different types of bank accounts. Here are the queries:\n- 'How do I sign up for tele-banking services through the mobile app?'\n- 'Can I sign up for telephone banking services?'\n- 'Where can I find the application for telephone banking services?'\n- 'I'm interested in opening a long term savings account. What options do you have?'\n- 'Are any banks open in Sri Lanka right now?'\n- 'Do you offer any special accounts for children or minors?'\n- 'Do you offer any special accounts for individuals looking to save for a home purchase or renovation?'"}]], "function": [{"name": "record", "description": "Records the categories to which various banking-related queries are classified.", "parameters": {"type": "dict", "required": ["faq_describe_telephone_banking_start", "bank_hours_start", "faq_describe_accounts_start"], "properties": {"faq_describe_telephone_banking_start": {"type": "array", "items": {"type": "string"}, "description": "An array of strings containing queries about starting or signing up for telephone banking services."}, "bank_hours_start": {"type": "array", "items": {"type": "string"}, "description": "An array of strings containing queries regarding the working hours or locations of bank branches."}, "faq_describe_accounts_start": {"type": "array", "items": {"type": "string"}, "description": "An array of strings containing queries about descriptions of different types of bank accounts."}}}}]}
{"id": "live_simple_108-65-0", "question": [[{"role": "user", "content": "Please classify the following customer service queries:\n- 'Can you show me all outgoing wire transfers?'\n- 'Can you assist me in combining my personal and savings accounts?'\n- 'I need my credit card limit raised.'\n- 'Can you assist me in getting a higher credit card limit?'\n- 'I need to view all transactions labeled as \"checks\" on my account.'\n- 'Please assist me in combining my accounts for a streamlined experience.'\n- 'Can you show me my recent Google Pay transactions?'\n- 'How can I link my auto loan and savings accounts?'\n- 'I would like to see the details of my most recent ATM withdrawal.'\n- 'Can you give me a summary of my recent debit card transactions?'"}]], "function": [{"name": "record", "description": "Records the classification of specified customer service queries.", "parameters": {"type": "dict", "required": ["money_movement_start", "get_transactions_start", "credit_limit_increase_start", "faq_link_accounts_start"], "properties": {"money_movement_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries for transferring funds between accounts."}, "get_transactions_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries for viewing transactions, including deposits, purchases, and transaction details."}, "credit_limit_increase_start": {"type": "array", "items": {"type": "string"}, "description": "List of customer requests to increase credit card limits or complaints about the current limit."}, "faq_link_accounts_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries related to linking accounts within the bank's system."}}}}]}
{"id": "live_simple_109-66-0", "question": [[{"role": "user", "content": "Please classify the following queries:\n[\n  \"Can I use bonuses or commissions as part of my income for a loan application?\",\n  \"Can I schedule automatic withdrawals for different dates?\",\n  \"How much do I owe on my home equity loan?\",\n  \"Will my income be verified during the loan application process?\",\n  \"Is there a minimum income threshold for loan approval?\"\n]"}]], "function": [{"name": "record", "description": "Records all specified queries into their corresponding categories based on intent names provided as keyword arguments.", "parameters": {"type": "dict", "required": [], "properties": {"faq_auto_withdraw_start": {"type": "array", "items": {"type": "string"}, "description": "A list of queries about setting up or understanding automatic withdrawals, including benefits and signup instructions.", "default": []}, "payment_information_start": {"type": "array", "items": {"type": "string"}, "description": "A list of queries regarding balance checks, due dates for credit cards or other financial accounts.", "default": []}, "pma_income_requirements_start": {"type": "array", "items": {"type": "string"}, "description": "A list of queries concerning the income requirements for obtaining mortgages or loans.", "default": []}, "outofscope": {"type": "array", "items": {"type": "string"}, "description": "A list of queries that do not fit into any other specified intent categories.", "default": []}}}}]}
{"id": "live_simple_110-67-0", "question": [[{"role": "user", "content": "I have a customer query asking for 'Need ATM location'. Which category should it be classified under?"}]], "function": [{"name": "record", "description": "Records classifications for a batch of queries based on specified intents. Each parameter represents a different intent and accepts a list of query strings that should be classified under that intent.", "parameters": {"type": "dict", "required": [], "properties": {"acc_routing_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries requesting bank routing numbers or account numbers.", "default": []}, "atm_finder_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries asking for ATM locations or to urgently find an ATM for cash withdrawal.", "default": []}, "faq_link_accounts_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries related to linking accounts within the bank's system.", "default": []}, "get_balance_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries requesting account balances, available funds, or other financial balances.", "default": []}, "get_transactions_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries related to viewing transaction details including deposits and purchases.", "default": []}, "outofscope": {"type": "array", "items": {"type": "string"}, "description": "List of queries that do not classify under any of the other available intents.", "default": []}}}}]}
{"id": "live_simple_111-67-1", "question": [[{"role": "user", "content": "I have a set of customer queries and I need to classify them according to their intent. Here they are:\n- 'What is my balance?'\n- 'Tell me my available balance, please'\n- 'What is my current available balance?'\n- 'Where is the closest ATM to my current location?'\n- 'Find ATM for immediate cash needs'\n- 'Please provide my current account balance'\n- 'Show me my balance information.'\n- 'What is the balance in my account at this moment?'\n- 'How much money is in my account?'\n- 'Tell me my available balance'"}]], "function": [{"name": "record", "description": "Records classifications for a batch of queries based on specified intents. Each parameter represents a different intent and accepts a list of query strings that should be classified under that intent.", "parameters": {"type": "dict", "required": [], "properties": {"acc_routing_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries requesting bank routing numbers or account numbers.", "default": []}, "atm_finder_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries asking for ATM locations or to urgently find an ATM for cash withdrawal.", "default": []}, "faq_link_accounts_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries related to linking accounts within the bank's system.", "default": []}, "get_balance_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries requesting account balances, available funds, or other financial balances.", "default": []}, "get_transactions_start": {"type": "array", "items": {"type": "string"}, "description": "List of queries related to viewing transaction details including deposits and purchases.", "default": []}, "outofscope": {"type": "array", "items": {"type": "string"}, "description": "List of queries that do not classify under any of the other available intents.", "default": []}}}}]}
{"id": "live_simple_112-68-0", "question": [[{"role": "user", "content": "I have a query: 'what is the weather like'. Could you classify it appropriately?"}]], "function": [{"name": "record", "description": "Records classifications for a series of queries based on the intent names provided as keyword arguments. Allows for the bulk recording of query classifications in one function call.", "parameters": {"type": "dict", "required": ["acc_routing_start", "atm_finder_start", "faq_link_accounts_start", "get_balance_start", "get_transactions_start", "outofscope"], "properties": {"acc_routing_start": {"type": "array", "items": {"type": "string"}, "description": "A list of queries requesting the bank routing numbers or account numbers."}, "atm_finder_start": {"type": "array", "items": {"type": "string"}, "description": "A list of queries asking for locations of ATMs, specified in the format of 'City, State (abbr)', such as 'Berkeley, CA' or 'New York, NY'."}, "faq_link_accounts_start": {"type": "array", "items": {"type": "string"}, "description": "A list of queries related to linking accounts within the bank's system."}, "get_balance_start": {"type": "array", "items": {"type": "string"}, "description": "A list of queries asking for account balances, available funds, or other financial balances."}, "get_transactions_start": {"type": "array", "items": {"type": "string"}, "description": "A list of queries related to viewing transactions, deposits, purchases, and transaction details."}, "outofscope": {"type": "array", "items": {"type": "string"}, "description": "A list of queries that do not classify to any other available intents."}}}}]}
{"id": "live_simple_113-69-0", "question": [[{"role": "user", "content": "I'm trying to set up pricing for my SaaS product that offers a one-time form filing service and an annual subscription for unlimited updates and filings. Given that my competitor charges $99 for a one-time filing and $149 for an annual subscription, while another competitor charges $149 and $249 respectively, help me determine a competitive price for a customer located at 34.0522, -118.2437? Let's use a base price of $100 and ensure we don't go below $90."}]], "function": [{"name": "calculate_dynamic_pricing", "description": "Calculates the price for a service based on the geolocation of the customer, ensuring a minimum price threshold is met.", "parameters": {"type": "dict", "required": ["geolocation", "base_price", "minimum_price"], "properties": {"geolocation": {"type": "string", "description": "The geolocation of the customer in the format of 'Latitude, Longitude', such as '36.2522, -105.3437'."}, "base_price": {"type": "float", "description": "The base price for the service before location-based adjustments."}, "minimum_price": {"type": "float", "description": "The minimum price to charge regardless of the customer's location."}, "location_multiplier": {"type": "float", "description": "Multiplier applied to the base price based on customer's geolocation. A higher multiplier indicates a more expensive area.", "default": 1.0}}}}]}
{"id": "live_simple_114-70-0", "question": [[{"role": "user", "content": "I need to update my profile with my new email, <EMAIL>, and my new age, 30. My user ID is 12345."}]], "function": [{"name": "update_user_profile", "description": "Updates the specified user's profile information in the database.", "parameters": {"type": "dict", "required": ["user_id", "profile_data"], "properties": {"user_id": {"type": "integer", "description": "The unique identifier for the user whose profile is being updated."}, "profile_data": {"type": "dict", "description": "A dictionary containing the profile fields that need to be updated.", "properties": {"name": {"type": "string", "description": "The full name of the user."}, "email": {"type": "string", "description": "The user's email address."}, "age": {"type": "integer", "description": "The user's age in years."}, "bio": {"type": "string", "description": "A brief biography of the user.", "default": ""}}}, "notify": {"type": "boolean", "description": "Whether to send a notification to the user about the profile update.", "default": true}}}}]}
{"id": "live_simple_115-71-0", "question": [[{"role": "user", "content": "What is the sum of 133 and 34?"}]], "function": [{"name": "calculate_sum", "description": "Calculates the sum of two numeric values.", "parameters": {"type": "dict", "required": ["number1", "number2"], "properties": {"number1": {"type": "float", "description": "The first number to be added. Can be an integer or a float."}, "number2": {"type": "float", "description": "The second number to be added. Can be an integer or a float."}}}}]}
{"id": "live_simple_116-72-0", "question": [[{"role": "user", "content": "What is the sum of 133 and 34?"}]], "function": [{"name": "sum_numbers", "description": "Calculates the sum of all the numbers provided in the list.", "parameters": {"type": "dict", "required": ["numbers_list"], "properties": {"numbers_list": {"type": "array", "items": {"type": "float"}, "description": "A list of floating-point numbers to be summed."}}}}]}
{"id": "live_simple_117-73-0", "question": [[{"role": "user", "content": "Reverse say hi"}]], "function": [{"name": "reverse_input", "description": "Reverses the given input, returning the opposite or inverted form. For strings, it returns the characters in reverse order. For booleans, it returns the negation. For numbers, it returns the negative value.", "parameters": {"type": "dict", "required": ["input_value"], "properties": {"input_value": {"type": "any", "description": "The value to be reversed. Can be a string, boolean, or number (integer or float)."}}}}]}
{"id": "live_simple_118-74-0", "question": [[{"role": "user", "content": "Could you tell me the current temperature in Hyderabad, India, in Celsius?"}]], "function": [{"name": "get_temperature", "description": "Retrieves the current temperature and returns it in a specified format.", "parameters": {"type": "dict", "required": ["units"], "properties": {"units": {"type": "string", "description": "The unit of measurement for the temperature value.", "enum": ["Celsius", "Fahrenheit", "Kelvin"]}, "location": {"type": "string", "description": "The location for which the temperature is being requested, in the format of 'City, Country' such as 'Paris, France' or 'Tokyo, Japan'.", "default": "Current location"}, "time": {"type": "string", "description": "The time for which the temperature is being requested, in the format of 'YYYY-MM-DD HH:MM:SS'. If null, the current time is used.", "default": null}}}}]}
{"id": "live_simple_119-75-0", "question": [[{"role": "user", "content": "Could you tell me the names of the current prime ministers of Australia, Canada, and India?"}]], "function": [{"name": "GetPrimeMinisters", "description": "Retrieves the current prime ministers' names for a list of specified countries.", "parameters": {"type": "dict", "required": ["countries"], "properties": {"countries": {"type": "array", "items": {"type": "string"}, "description": "An array of country names for which to retrieve the current prime ministers' names. Each country name should be in the format of its common English full name, such as 'Australia', 'New Zealand', and 'India'."}}}}]}
{"id": "live_simple_120-76-0", "question": [[{"role": "user", "content": "Could you tell me what the weather will be like in Paris, France from April 3rd to April 5th 2023. For temperature use Celsius?"}]], "function": [{"name": "weather.forecast", "description": "Provides a weather forecast for a specified location and date range including temperature, precipitation, and wind speed.", "parameters": {"type": "dict", "required": ["location", "start_date", "end_date"], "properties": {"location": {"type": "string", "description": "The location for the forecast, in the format of 'City, State (abbr)' or 'City, Country', such as 'San Francisco, CA' or 'Tokyo, Japan'."}, "start_date": {"type": "string", "description": "The start date of the forecast period in the format of 'YYYY-MM-DD', such as '2023-04-01'."}, "end_date": {"type": "string", "description": "The end date of the forecast period in the format of 'YYYY-MM-DD', such as '2023-04-07'."}, "temperature_unit": {"type": "string", "description": "The unit of measurement for temperature, either Celsius or Fahrenheit.", "enum": ["Celsius", "Fahrenheit"], "default": "Celsius"}, "include_precipitation": {"type": "boolean", "description": "A flag indicating whether to include precipitation information in the forecast.", "default": true}, "include_wind": {"type": "boolean", "description": "A flag indicating whether to include wind speed information in the forecast.", "default": false}}}}]}
{"id": "live_simple_121-77-0", "question": [[{"role": "user", "content": "Can you create a bar chart comparing the salaries of five specific jobs such as Software Engineer, Graphic Designer, Data Analyst, Sales Manager, and Nurse? The salaries are 90000, 50000, 70000, 60000, and 80000 respectively."}]], "function": [{"name": "generate_chart", "description": "Generates a chart based on the provided datasets. Each dataset represents a series of data points to be plotted.", "parameters": {"type": "dict", "required": ["data_labels", "data_values"], "properties": {"data_labels": {"type": "array", "items": {"type": "string"}, "description": "A list of labels for the data points, each label corresponds to a point on the chart. For example, ['January', 'February', 'March']."}, "data_values": {"type": "array", "items": {"type": "float"}, "description": "A list of values for the data points, each value corresponds to a label from the 'data_labels' parameter. Must have the same number of elements as 'data_labels'."}}}}]}
{"id": "live_simple_122-78-0", "question": [[{"role": "user", "content": "I would like to easily be able to extract any informaton from an 'image.png' based on a 'question' using the vision language model vikhyatk/moondream2. The question is \"generate with technically complex attention to detail a description of what you see\"\n"}]], "function": [{"name": "process_data", "description": "Processes an image and a question to provide an answer using a pre-trained question-answering model from the Hugging Face Transformers library.", "parameters": {"type": "dict", "required": ["image_path", "question", "model"], "properties": {"image_path": {"type": "string", "description": "The file path to the image on which the question is based, in the format of 'folder/subfolder/image.png'."}, "question": {"type": "string", "description": "The question to be answered, related to the content of the provided image."}, "model": {"type": "any", "description": "The pre-loaded question-answering model from the Hugging Face Transformers library used to answer the question."}}}}]}
{"id": "live_simple_123-79-0", "question": [[{"role": "user", "content": "Could you analyze the image at http://www.thewowstyle.com/wp-content/uploads/2015/01/Home-Interior-Design-Hd-Wallpaper-Hd-Background-With-Simple-Staircase-And-Plasma-TV-Also-Nice-Laminate-Flooring-Ideas-With-Modern-Furniture-Interior-Minimalist-Design.jpg and provide a detailed description based on the question, 'How does the arrangement of furniture contribute to the minimalist design of the interior?'"}]], "function": [{"name": "pipeline", "description": "Processes an image at the given path and generates a descriptive caption based on a provided complex question template.", "parameters": {"type": "dict", "required": ["image_path", "question"], "properties": {"image_path": {"type": "string", "description": "The file path to the image to be processed. The path should be absolute or relative to the working directory."}, "question": {"type": "string", "description": "A template question that guides the generation of the image description. The question should be detailed and complex."}}}}]}
{"id": "live_simple_124-80-0", "question": [[{"role": "user", "content": "\u8bf7\u5e2e\u6211\u68c0\u7d22\u4e00\u4e0b'\u7ea2\u661f\u79d1\u6280'\u516c\u53f8\u76ee\u524d\u6240\u6709\u672a\u89e3\u51b3\u7684\u5de5\u5355\uff0c\u6211\u60f3\u8981\u67e5\u770b\u6700\u591a10\u4e2a\u3002"}]], "function": [{"name": "get_tickets", "description": "Retrieve a list of tickets for a specified customer based on the company name.", "parameters": {"type": "dict", "required": ["customer"], "properties": {"customer": {"type": "string", "description": "The name of the company for which to retrieve tickets."}, "status": {"type": "string", "description": "The status of the tickets to be retrieved.", "enum": ["open", "closed", "pending", "resolved"], "default": "open"}, "limit": {"type": "integer", "description": "The maximum number of tickets to retrieve.", "default": 50}}}}]}
{"id": "live_simple_125-81-0", "question": [[{"role": "user", "content": "Could you help me combine the names 'John' and 'Doe' into a full name?"}]], "function": [{"name": "concat_strings", "description": "Concatenate a list of strings into a single string.", "parameters": {"type": "dict", "required": ["strings"], "properties": {"strings": {"type": "array", "items": {"type": "string"}, "description": "An array of strings to be concatenated."}, "separator": {"type": "string", "description": "The string to be used as a separator between each string in the array.", "default": ""}}}}]}
{"id": "live_simple_126-82-0", "question": [[{"role": "user", "content": "Can you retrieve the status information for the Ethernet interface on fabric 'Global-Fabric', node 1200, and pod 3?"}]], "function": [{"name": "telemetry.flowrules.interfaceInfo.get", "description": "Retrieves specified telemetry information for a network interface within a given fabric, node, and pod, filtered by the interface and information types.", "parameters": {"type": "dict", "required": ["fabricName", "nodeId", "podId", "interfaceType", "infoType"], "properties": {"fabricName": {"type": "string", "description": "The name of the fabric to limit the nodes pertaining to the given fabric name."}, "nodeId": {"type": "integer", "description": "The identifier of the node to limit the results pertaining to the given node ID."}, "podId": {"type": "integer", "description": "The identifier of the pod to limit the results pertaining to the given pod ID."}, "interfaceType": {"type": "string", "description": "The type of the interface to limit the results pertaining to the given interface type.", "enum": ["svi", "ethernet", "loopback", "port-channel"]}, "infoType": {"type": "string", "description": "The type of information to retrieve about the interface.", "enum": ["interfaces", "status", "statistics", "errors"]}}}}]}
{"id": "live_simple_127-82-1", "question": [[{"role": "user", "content": "Can you retrieve the status information for an ethernet interface in the fabric named 'test-de', on node 5, pod 1?"}]], "function": [{"name": "telemetry.flowrules.interfaceInfo.get", "description": "Retrieves specified telemetry information for a network interface within a given fabric, node, and pod, filtered by the interface and information types.", "parameters": {"type": "dict", "required": ["fabricName", "nodeId", "podId", "interfaceType", "infoType"], "properties": {"fabricName": {"type": "string", "description": "The name of the fabric to limit the nodes pertaining to the given fabric name."}, "nodeId": {"type": "integer", "description": "The identifier of the node to limit the results pertaining to the given node ID."}, "podId": {"type": "integer", "description": "The identifier of the pod to limit the results pertaining to the given pod ID."}, "interfaceType": {"type": "string", "description": "The type of the interface to limit the results pertaining to the given interface type.", "enum": ["svi", "ethernet", "loopback", "port-channel"]}, "infoType": {"type": "string", "description": "The type of information to retrieve about the interface.", "enum": ["interfaces", "status", "statistics", "errors"]}}}}]}
{"id": "live_simple_128-83-0", "question": [[{"role": "user", "content": "How to generate a RESTful API request on Cisco Nexus Dashboard?\nversion:3.1.0\nIP:*************\nAPI name: get nodes list\nParameter_fabricName:PEK-ACI"}]], "function": [{"name": "requests.get", "description": "Sends an HTTP GET request to retrieve a list of nodes associated with a specified fabric in a network.", "parameters": {"type": "dict", "required": ["fabricName"], "properties": {"url": {"type": "string", "description": "The URL to send the GET request to. The URL should include the target IP, API endpoint, and the fabricName query parameter placeholder https://<ip>/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName={fabricName}.", "default": "https://<ip>/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName={fabricName}"}, "fabricName": {"type": "string", "description": "The name of the fabric whose nodes are to be listed. This value is used to replace the {fabricName} placeholder in the URL."}}}}]}
{"id": "live_simple_129-83-1", "question": [[{"role": "user", "content": "get list of nodes for fabric fab-ed and ip is 3.321.3232.2"}]], "function": [{"name": "requests.get", "description": "Sends an HTTP GET request to retrieve a list of nodes associated with a specified fabric in a network.", "parameters": {"type": "dict", "required": ["fabricName"], "properties": {"url": {"type": "string", "description": "The URL to send the GET request to. The URL should include the target IP, API endpoint, and the fabricName query parameter placeholder in https://<ip>/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName={fabricName}.", "default": "https://<ip>/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/nodes?fabricName={fabricName}"}, "fabricName": {"type": "string", "description": "The name of the fabric whose nodes are to be listed. This value is used to replace the {fabricName} placeholder in the URL."}}}}]}
{"id": "live_simple_130-84-0", "question": [[{"role": "user", "content": "Could you retrieve the Ethernet interface statistics from the node with ID 12 in pod 10 of the Network1 fabric, using the IP *************?"}]], "function": [{"name": "requests.get", "description": "Send a GET request to retrieve specified information for an interface from a network telemetry API.", "parameters": {"type": "dict", "required": ["url", "params"], "properties": {"url": {"type": "string", "description": "The URL endpoint to send the GET request to. This should include the base path without query parameters, e.g., 'https://{ip}/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/interfaceInfo'."}, "params": {"type": "dict", "description": "The query parameters for the request.", "properties": {"fabricName": {"type": "string", "description": "The name of the fabric to limit nodes pertaining to."}, "nodeId": {"type": "integer", "description": "The node identifier to limit results pertaining to."}, "podId": {"type": "integer", "description": "The pod identifier to limit results pertaining to."}, "interfaceType": {"type": "string", "description": "The type of the interface to limit results pertaining to.", "enum": ["gigabitethernet", "fastethernet", "ethernet", "serial"]}, "infoType": {"type": "string", "description": "The type of information requested for the interface.", "enum": ["statistics", "status", "config"]}}}}}}]}
{"id": "live_simple_131-84-1", "question": [[{"role": "user", "content": "How to generate a RESTful API request on Cisco Nexus Dashboard?\nVersion: 6.3.1\nIP: *************\nAPI name: get information of an interface\nParameter_fabricName: Network1\nParameter_nodeId: 12\nParameter_podId: 10\nParameter_interfaceType: Ethernet\nParameter_infoType: Speed"}]], "function": [{"name": "requests.get", "description": "Send a GET request to retrieve specified information for an interface from a network telemetry API.", "parameters": {"type": "dict", "required": ["url", "params"], "properties": {"url": {"type": "string", "description": "The URL endpoint to send the GET request to. This should include the base path without query parameters, e.g., 'https://{ip}/sedgeapi/v1/cisco-nir/api/api/telemetry/flowrules/interfaceInfo'."}, "params": {"type": "dict", "description": "The query parameters for the request.", "properties": {"fabricName": {"type": "string", "description": "The name of the fabric to limit nodes pertaining to."}, "nodeId": {"type": "integer", "description": "The node identifier to limit results pertaining to."}, "podId": {"type": "integer", "description": "The pod identifier to limit results pertaining to."}, "interfaceType": {"type": "string", "description": "The type of the interface to limit results pertaining to.", "enum": ["gigabitethernet", "fastethernet", "ethernet", "serial"]}, "infoType": {"type": "string", "description": "The type of information requested for the interface.", "enum": ["statistics", "status", "config", "Speed"]}}}}}}]}
{"id": "live_simple_132-85-0", "question": [[{"role": "user", "content": "information on the device connector versions from https://example.com/device-connector-versions.json?"}]], "function": [{"name": "requests.get", "description": "Sends an HTTP GET request to retrieve Device Connector Versions information from a specified URL.", "parameters": {"type": "dict", "required": ["url"], "properties": {"url": {"type": "string", "description": "The URL to which the GET request is sent. The URL points to a JSON file containing Device Connector Versions information."}, "params": {"type": "dict", "description": "Optional query parameters to include in the GET request as key-value pairs.", "properties": {}, "default": {}}}}}]}
{"id": "live_simple_133-86-0", "question": [[{"role": "user", "content": "Could you retrieve the recommendation details for the advisory with the identifier 'dac' from the following URL: 'https://api.example.com/recommendations'?"}]], "function": [{"name": "requests.get", "description": "Sends a GET request to a specified URL to retrieve recommendation details for an advisory.", "parameters": {"type": "dict", "required": ["url", "params"], "properties": {"url": {"type": "string", "description": "The URL endpoint to send the GET request to. This should include the path to the recommendations resource and be in the format 'https://{hostname}/path'."}, "params": {"type": "dict", "properties": {"advisoryId": {"type": "string", "description": "Unique identifier for the advisory for which recommendations are being requested."}, "ip": {"type": "string", "description": "The IP address or hostname of the API server. This will be used to construct the URL if it is not provided explicitly in the 'url' parameter."}}, "description": "Query parameters to be sent with the GET request. Include 'advisoryId' for the specific advisory's recommendations and 'ip' for the API server."}}}}]}
{"id": "live_simple_134-87-0", "question": [[{"role": "user", "content": "Could you retrieve the Insights Groups Information for the nodes from the following URL: 'https://api.insights.com/groups'? I'd like to see a maximum of 20 results, sorted in descending order."}]], "function": [{"name": "requests.get", "description": "Sends a GET request to the specified URL to retrieve the Insights Groups Information.", "parameters": {"type": "dict", "required": ["url"], "properties": {"url": {"type": "string", "description": "The URL endpoint to send the GET request to, including the protocol (e.g., 'https')."}, "params": {"type": "dict", "description": "A dictionary of URL parameters to append to the URL. Each key-value pair represents a single URL parameter.", "default": {}, "properties": {"query": {"type": "string", "description": "The search query parameter to filter the results."}, "limit": {"type": "integer", "description": "The maximum number of results to return.", "default": 10}, "sort": {"type": "string", "description": "The sorting order of the results.", "enum": ["asc", "desc"], "default": "asc"}}}}}}]}
{"id": "live_simple_135-88-0", "question": [[{"role": "user", "content": "retrieve data from 'https://api.example.com/v1/data' with a limit of 50 items?"}]], "function": [{"name": "requests.get", "description": "Sends a GET request to the specified URL and retrieves data from the endpoint.", "parameters": {"type": "dict", "required": ["url"], "properties": {"url": {"type": "string", "description": "The URL of the endpoint to which the GET request will be sent, such as 'https://api.example.com/v1/data'."}, "params": {"type": "dict", "description": "A dictionary of URL parameters to append to the URL. Each key-value pair represents a single URL parameter.", "default": {}, "properties": {"query": {"type": "string", "description": "The query parameter to be appended to the URL, representing a search or filter term."}, "limit": {"type": "integer", "description": "Limits the number of items to be retrieved. Defaults to 100 items if not specified.", "default": 100}}}}}}]}
{"id": "live_simple_136-89-0", "question": [[{"role": "user", "content": "I need to retrieve the topology information of the SalesApp under the AcmeCorp account. Could you send a GET request to the server at 'https://***********/api/v1/applications/topologies' using the filter 'accountName:AcmeCorp AND applicationName:SalesApp'?"}]], "function": [{"name": "requests.get", "description": "Sends a GET request to the specified URL to retrieve the topology information of an application.", "parameters": {"type": "dict", "required": ["url", "params"], "properties": {"url": {"type": "string", "description": "The URL endpoint from which to retrieve the application topology. The URL must be a valid HTTP or HTTPS URL, formatted as 'https://{ip}/api/v1/applications/topologies' where {ip} is the IP address of the server."}, "params": {"type": "dict", "properties": {"filter": {"type": "string", "description": "A filter in Lucene format, specifying the accountName, controllerName, applicationName. in the format of accountName:DefaultCorp AND applicationName:DefaultApp"}}, "description": "Query parameters to include in the GET request. The 'filter' field specifies search criteria and is optional with a default value if not provided."}}}}]}
{"id": "live_simple_137-90-0", "question": [[{"role": "user", "content": "Reschedule event 'Alice-One-one-One' to November 1, 2023 at 10pm CEST"}]], "function": [{"name": "reschedule", "description": "Moves an event to a new specified time, adjusting for time zones.", "parameters": {"type": "dict", "required": ["identifier", "dateOrTime", "timezone"], "properties": {"identifier": {"type": "string", "description": "The unique identifier for the event to be rescheduled."}, "dateOrTime": {"type": "string", "description": "The new date and time for the event, in ISO-8601 format (e.g., 'YYYY-MM-DDTHH:MM:SS'). Does not include a timezone offset."}, "timezone": {"type": "string", "description": "The Olson timezone identifier representing the timezone for the new event time, such as 'Asia/Tokyo'.", "enum": ["Asia/Tokyo", "America/New_York", "Europe/London", "UTC"]}}}}]}
{"id": "live_simple_138-91-0", "question": [[{"role": "user", "content": "Reschedule event 'Bob-123' to November 1, 2023 at 6pm CEST"}]], "function": [{"name": "reschedule", "description": "Moves an event to a new specified time, adjusting for the provided timezone.", "parameters": {"type": "dict", "required": ["identifier", "dateOrTime"], "properties": {"identifier": {"type": "string", "description": "A unique identifier for the event to be rescheduled."}, "dateOrTime": {"type": "string", "description": "The new date and time to which the event should be moved, in ISO-8601 format (e.g., 'YYYY-MM-DDTHH:MM:SSZ'). Timezone is not included and should be specified separately."}, "timezone": {"type": "string", "description": "The Olson timezone identifier for the new event time, such as 'Asia/Tokyo'.", "default": "UTC", "enum": ["UTC", "Asia/Tokyo", "America/New_York", "Europe/London"]}}}}]}
{"id": "live_simple_139-92-0", "question": [[{"role": "user", "content": "Could you retrieve a summary of anomalies for the fabric named 'network222' using the URL formatted as 'https://{ip}/sedgeapi/v1/cisco-nir/api/api/v1/anomalies/summary', where {ip} is the IP address '***********'?"}]], "function": [{"name": "requests.get", "description": "Sends a GET request to a specified URL to retrieve a summary of anomalies, based on the provided insights group or fabric.", "parameters": {"type": "dict", "required": ["url"], "properties": {"url": {"type": "string", "description": "The URL to which the GET request is sent. It should follow the format 'https://{ip}/sedgeapi/v1/cisco-nir/api/api/v1/anomalies/summary', where {ip} is the actual IP address."}, "params": {"type": "dict", "description": "Optional parameters for the GET request as key-value pairs. This includes the insights group and fabric names.", "properties": {"insightsGroup": {"type": "string", "description": "The name of the insights group for which the anomalies summary is requested. For example, 'network_performance'.", "default": "defaultInsightsGroup"}, "fabric": {"type": "string", "description": "The name of the fabric for which the anomalies summary is requested. For example, 'fabric1'.", "default": "defaultFabric"}}, "default": {}}}}}]}
{"id": "live_simple_140-93-0", "question": [[{"role": "user", "content": "help me send a nice message to my husband, Jeff? My name is Emily."}]], "function": [{"name": "greet_partner", "description": "Generate a greeting message for the partner of the user.", "parameters": {"type": "dict", "properties": {"user_name": {"type": "string", "description": "The name of the user sending the greeting."}, "partner_name": {"type": "string", "description": "The name of the user's partner to whom the greeting is addressed."}, "relationship": {"type": "string", "description": "The relationship of the partner to the user.", "enum": ["wife", "husband", "partner"], "default": "partner"}}, "required": ["user_name", "partner_name"]}}]}
{"id": "live_simple_141-94-0", "question": [[{"role": "user", "content": "check whether the docker is installed using docker --version"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a specified command in the Windows operating system using the os.system() function.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The command line instruction to be passed to os.system() for execution, formatted as a Windows command prompt statement."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_142-94-1", "question": [[{"role": "user", "content": "list file in desktop using dir Desktop"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a specified command in the Windows operating system using the os.system() function.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The command line instruction to be passed to os.system() for execution, formatted as a Windows command prompt statement."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_143-95-0", "question": [[{"role": "user", "content": "is docker running, please use docker ps to find out"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_144-95-1", "question": [[{"role": "user", "content": "close firefox using taskkill command"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_145-95-2", "question": [[{"role": "user", "content": "LIST C DRIIVE"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_146-95-3", "question": [[{"role": "user", "content": "open huggingface using the start command and through the link: https://huggingface.co"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_147-95-4", "question": [[{"role": "user", "content": "remove the timer.exe that i have been set earlier using the taskkill command"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_148-95-5", "question": [[{"role": "user", "content": "what is the file in c."}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_149-95-6", "question": [[{"role": "user", "content": "say hi using the echo command"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_150-95-7", "question": [[{"role": "user", "content": "shutdown the pc, using the exact command shutdown /s /t 0"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_151-95-8", "question": [[{"role": "user", "content": "disconnect wifi, using the command netsh wlan disconnect"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_152-95-9", "question": [[{"role": "user", "content": "list file in d drive and make file called testing.txt. This can be achieved through dir D:\\\\ && echo testing.txt."}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_153-95-10", "question": [[{"role": "user", "content": "remove shortcut of putty from desktop, using the instruction del \"%userprofile%\\Desktop\\putty.lnk\""}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_154-95-11", "question": [[{"role": "user", "content": "open youtube and play rickroll using the command start https://www.youtube.com/watch?v=dQw4w9WgXcQ"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_155-95-12", "question": [[{"role": "user", "content": "what is the date, using the date or echo command"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_156-95-13", "question": [[{"role": "user", "content": "u have the full control of command prompt if the task need to execute and coommand just do it list c drive"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_157-95-14", "question": [[{"role": "user", "content": "what is the time in pc, using the command echo %time%"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_158-95-15", "question": [[{"role": "user", "content": "kill vs code using taskkill for code.exe"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_159-95-16", "question": [[{"role": "user", "content": "set a timer for 10 seconds using the timeout command. You should use timeout 10."}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_160-95-17", "question": [[{"role": "user", "content": "open camera, using the start microsoft.windows.camera: command"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a system-level command using os.system() on Windows operating systems. It can execute single or multiple commands chained with '&&'.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The system command to be executed. To execute multiple commands, separate them with '&&'. For example, 'dir && echo done'."}, "unit": {"type": "string", "description": "The unit of measurement for the command execution time, such as 'seconds' or 'milliseconds'", "enum": ["seconds", "milliseconds"], "default": "N/A"}}}}]}
{"id": "live_simple_161-96-0", "question": [[{"role": "user", "content": "{\n    \"response\": \" The image contains four logos of superheroes from DC Comics and Marvel Comics. They are Superman, Iron Man, Hulk, and Batman. The logos are all in different colors and shapes. Superman's logo is a red and yellow \\\"S\\\" on a blue background. Iron Man's logo is a red and yellow circle with a black face mask. Hulk's logo is a green fist with a black outline. Batman's logo is a black bat on a yellow background.\\n\\nHere is a list of objects that could contain copyrighted material:\\n\\n* Blue square\\n* Red square\\n* Green square\\n* Yellow square\\n* Superman logo\\n* Iron Man logo\\n* Hulk logo\\n* Batman logo\\n* Fist\\n* Bat\\n* Face mask\\n* Outline\"\n}\n"}]], "function": [{"name": "get_items", "description": "Retrieve a list of items that are analyzed for copyright detection purposes.", "parameters": {"type": "dict", "required": ["items"], "properties": {"items": {"type": "array", "items": {"type": "string"}, "description": "A list of item descriptors to be analyzed, such as 'yellow logo', 'signature tune', 'unique font'."}}}}]}
{"id": "live_simple_162-96-1", "question": [[{"role": "user", "content": "{\n    \"response\": \" A 4-image grid of various comic book superheroes. Her is a list of objects that contain copyright material\\n\\nRed and blue circle logo.\\nRed and yellow circle logo.\\nGreen circle logo.\\nBlack circle logo.\"\n}\n"}]], "function": [{"name": "get_items", "description": "Retrieve a list of items that are analyzed for copyright detection purposes.", "parameters": {"type": "dict", "required": ["items"], "properties": {"items": {"type": "array", "items": {"type": "string"}, "description": "A list of item descriptors to be analyzed, such as 'yellow logo', 'signature tune', 'unique font'."}}}}]}
{"id": "live_simple_163-96-2", "question": [[{"role": "user", "content": "The image is a poster for The Lego Movie. It features the Lego logo, which is a red and yellow rectangle with the word \"LEGO\" written in white. The poster also features several Lego characters, including Emmet, Wyldstyle, Batman, and Gandalf. Other objects that could contain copyrighted material include the movie's title, the release date, and the names of the actors who voiced the characters.\n\nred logo\nyellow rectangle\nwhite text\nLego characters\nmovie title\nrelease date\nactor names"}]], "function": [{"name": "get_items", "description": "Retrieve a list of items that are analyzed for copyright detection purposes.", "parameters": {"type": "dict", "required": ["items"], "properties": {"items": {"type": "array", "items": {"type": "string"}, "description": "A list of item descriptors to be analyzed, such as 'yellow logo', 'signature tune', 'unique font'."}}}}]}
{"id": "live_simple_164-97-0", "question": [[{"role": "user", "content": "The image contains the Coca-Cola logo, which is a registered trademark of The Coca-Cola Company. The Coca-Cola Company is a multinational beverage corporation headquartered in Atlanta, Georgia, United States. The company is the world's largest beverage company, refreshing consumers with more than 500 sparkling and still brands. Led by Coca-Cola, one of the world's most valuable brands, our company's portfolio features 20 billion-dollar brands, 19 of which are available in more than 200 countries. These brands include Diet Coke, Fanta, Sprite, Coca-Cola Zero, vitaminwater, Powerade, Minute Maid, Simply, Dasani, FUZE TEA and Del Valle.\\n\\nI am 100% confident that the image contains copyrighted content."}]], "function": [{"name": "get_copyright_info", "description": "Retrieves copyright information for a given content, including the holder's name and a confidence score representing the accuracy of the information.", "parameters": {"type": "dict", "required": ["copyright_content", "copyright_holder", "confidence_score"], "properties": {"copyright_content": {"type": "string", "description": "The specific content that is claimed to be copyrighted."}, "copyright_holder": {"type": "string", "description": "The name of the individual or organization that holds the copyright."}, "confidence_score": {"type": "float", "description": "A decimal value representing the confidence score as a percentage, ranging from 0.0 to 100.0."}}}}]}
{"id": "live_simple_165-98-0", "question": [[{"role": "system", "content": "You are a top-tier algorithm for extracting information from text. Only extract information that is relevant to the provided text. If no information is relevant, use the schema and output an empty list where appropriate.\n\nUse information about the person from the given user input."}, {"role": "user", "content": "Could you help me extract the names and ages from this text? Here it is: ```\n\u6211\u53eb\u674e\u96f7\uff0c\u4eca\u5e7418\uff0c\u6211\u59d0\u59d0\u6bd4\u6211\u5927\u4e09\u5c81\uff0c\u53eb\u674e\u4e3d\n```"}]], "function": [{"name": "extractor.extract_information", "description": "Extracts structured information from a dataset matching the specified schema, focusing on the 'name' and 'age' of individuals.", "parameters": {"type": "dict", "required": ["data"], "properties": {"data": {"type": "array", "items": {"type": "dict"}, "description": "A list of dictionaries, each representing an individual's record with required 'name' and 'age' fields."}, "schema": {"type": "string", "description": "The schema identifier that specifies the format and fields of the information to be extracted. Examples might include 'personal_info' or 'employment_record'.", "enum": ["personal_info", "employment_record", "educational_background"], "default": "personal_info"}}}}]}
{"id": "live_simple_166-99-0", "question": [[{"role": "user", "content": "I need to know the current time on my machine, using the command echo %time%"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a given command using the os.system() function specifically for Windows operating systems. For multiple commands, separate them with '&&'. For complex tasks, create and run a .bat file.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The Windows command line instruction(s) to be executed. Use '&&' between commands for multiple instructions. For complex sequences, encapsulate the logic within a .bat file and provide the execution command here."}}}}]}
{"id": "live_simple_167-99-1", "question": [[{"role": "user", "content": "is vs code running, using the command tasklist /FI \"IMAGENAME eq Code.exe\""}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a given command using the os.system() function specifically for Windows operating systems. For multiple commands, separate them with '&&'. For complex tasks, create and run a .bat file.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The Windows command line instruction(s) to be executed. Use '&&' between commands for multiple instructions. For complex sequences, encapsulate the logic within a .bat file and provide the execution command here."}}}}]}
{"id": "live_simple_168-99-2", "question": [[{"role": "user", "content": "start Docker on window using docker start."}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a given command using the os.system() function specifically for Windows operating systems. For multiple commands, separate them with '&&'. For complex tasks, create and run a .bat file.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The Windows command line instruction(s) to be executed. Use '&&' between commands for multiple instructions. For complex sequences, encapsulate the logic within a .bat file and provide the execution command here."}}}}]}
{"id": "live_simple_169-99-3", "question": [[{"role": "user", "content": "Run the Python script located at d:/playground/pc_contoller/test.py using the Python interpreter in the virtual environment at d:/playground/pc_contoller/env/Scripts/python.exe?"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a given command using the os.system() function specifically for Windows operating systems. For multiple commands, separate them with '&&'. For complex tasks, create and run a .bat file.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The Windows command line instruction(s) to be executed. Use '&&' between commands for multiple instructions. For complex sequences, encapsulate the logic within a .bat file and provide the execution command here."}}}}]}
{"id": "live_simple_170-99-4", "question": [[{"role": "user", "content": "list c drive"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a given command using the os.system() function specifically for Windows operating systems. For multiple commands, separate them with '&&'. For complex tasks, create and run a .bat file.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The Windows command line instruction(s) to be executed. Use '&&' between commands for multiple instructions. For complex sequences, encapsulate the logic within a .bat file and provide the execution command here."}}}}]}
{"id": "live_simple_171-99-5", "question": [[{"role": "user", "content": "show me today's date using the echo %date% command"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a given command using the os.system() function specifically for Windows operating systems. For multiple commands, separate them with '&&'. For complex tasks, create and run a .bat file.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The Windows command line instruction(s) to be executed. Use '&&' between commands for multiple instructions. For complex sequences, encapsulate the logic within a .bat file and provide the execution command here."}}}}]}
{"id": "live_simple_172-99-6", "question": [[{"role": "user", "content": "check weather docker is running using docker ps"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a given command using the os.system() function specifically for Windows operating systems. For multiple commands, separate them with '&&'. For complex tasks, create and run a .bat file.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The Windows command line instruction(s) to be executed. Use '&&' between commands for multiple instructions. For complex sequences, encapsulate the logic within a .bat file and provide the execution command here."}}}}]}
{"id": "live_simple_173-99-7", "question": [[{"role": "user", "content": "open calculator app using the command called start calc"}]], "function": [{"name": "cmd_controller.execute", "description": "Executes a given command using the os.system() function specifically for Windows operating systems. For multiple commands, separate them with '&&'. For complex tasks, create and run a .bat file.", "parameters": {"type": "dict", "required": ["command"], "properties": {"command": {"type": "string", "description": "The Windows command line instruction(s) to be executed. Use '&&' between commands for multiple instructions. For complex sequences, encapsulate the logic within a .bat file and provide the execution command here."}}}}]}
{"id": "live_simple_174-100-0", "question": [[{"role": "user", "content": "\u0e09\u0e31\u0e19\u0e15\u0e49\u0e2d\u0e07\u0e01\u0e32\u0e23\u0e23\u0e2b\u0e31\u0e2a\u0e1a\u0e23\u0e34\u0e01\u0e32\u0e23\u0e2a\u0e33\u0e2b\u0e23\u0e31\u0e1a\u0e1a\u0e23\u0e34\u0e01\u0e32\u0e23\u0e23\u0e35\u0e14\u0e1c\u0e49\u0e32, \u0e04\u0e38\u0e13\u0e0a\u0e48\u0e27\u0e22\u0e2b\u0e32\u0e21\u0e32\u0e43\u0e2b\u0e49\u0e09\u0e31\u0e19\u0e44\u0e14\u0e49\u0e44\u0e2b\u0e21?"}]], "function": [{"name": "get_service_id", "description": "Retrieves the unique identifier of a specific service provided by the housekeeping staff. This ID is used to reference services such as cleaning, ironing, massage, or big cleaning tasks.", "parameters": {"type": "dict", "required": ["service_id"], "properties": {"service_id": {"type": "integer", "description": "The unique identifier for a service. For example, 1 represents cleaning, 2 represents ironing, 7 represents massage, and 13 represents big cleaning.", "enum": [1, 2, 7, 13]}, "unit": {"type": "integer", "description": "The number of service units requested. This is usually the quantity of services needed, such as the number of rooms to clean or the number of clothing items to iron.", "default": 1}}}}]}
{"id": "live_simple_175-101-0", "question": [[{"role": "user", "content": "\u0e0a\u0e48\u0e27\u0e22\u0e2b\u0e32\u0e04\u0e38\u0e13\u0e41\u0e21\u0e48\u0e1a\u0e49\u0e32\u0e19\u0e17\u0e35\u0e48\u0e43\u0e2b\u0e49\u0e1a\u0e23\u0e34\u0e01\u0e32\u0e23\u0e23\u0e35\u0e14\u0e1c\u0e49\u0e32."}]], "function": [{"name": "get_service_id", "description": "Retrieve the unique identifier for a specific service provided. This identifier is used to reference available housekeeping services such as cleaning or ironing.", "parameters": {"type": "dict", "properties": {"service_id": {"type": "integer", "description": "The unique ID of the service. For example, '1' for cleaning, '2' for ironing.", "enum": [1, 2]}, "unit": {"type": "string", "description": "The unit of measurement used for the service count, such as 'session' or 'hour'.", "enum": ["session", "hour"], "default": "session"}}, "required": ["service_id"]}}]}
{"id": "live_simple_176-102-0", "question": [[{"role": "user", "content": "\u0e0a\u0e48\u0e27\u0e22\u0e2b\u0e32\u0e04\u0e38\u0e13\u0e41\u0e21\u0e48\u0e1a\u0e49\u0e32\u0e19\u0e17\u0e35\u0e48\u0e43\u0e2b\u0e49\u0e1a\u0e23\u0e34\u0e01\u0e32\u0e23\u0e23\u0e35\u0e14\u0e1c\u0e49\u0e32"}]], "function": [{"name": "get_service_id", "description": "Retrieve the unique identifier for a specific service based on the service type.", "parameters": {"type": "dict", "required": ["service_id"], "properties": {"service_id": {"type": "integer", "description": "The unique identifier of the service. For example, '1' represents cleaning services, and '2' represents ironing services.", "enum": [1, 2]}, "unit": {"type": "integer", "description": "The quantity of service units requested.", "default": 1}}}}]}
{"id": "live_simple_177-103-0", "question": [[{"role": "user", "content": "Help me find service provider who provide cleaning service."}]], "function": [{"name": "get_service_id", "description": "Retrieve the unique identifier for a specific service type, such as cleaning or ironing services.", "parameters": {"type": "dict", "required": ["service_id"], "properties": {"service_id": {"type": "integer", "description": "The unique identifier of the service. For example, '1' represents cleaning service, and '2' represents ironing service.", "enum": [1, 2]}, "unit": {"type": "integer", "description": "The unit of measurement for the quantity of service, expressed as the number of service sessions or instances.", "default": 1}}}}]}
{"id": "live_simple_178-103-1", "question": [[{"role": "user", "content": "Help find a housekeeper who provides ironing services."}]], "function": [{"name": "get_service_id", "description": "Retrieve the unique identifier for a specific service type, such as cleaning or ironing services.", "parameters": {"type": "dict", "required": ["service_id"], "properties": {"service_id": {"type": "integer", "description": "The unique identifier of the service. For example, '1' represents cleaning service, and '2' represents ironing service.", "enum": [1, 2]}, "unit": {"type": "integer", "description": "The unit of measurement for the quantity of service, expressed as the number of service sessions or instances.", "default": 1}}}}]}
{"id": "live_simple_179-104-0", "question": [[{"role": "user", "content": "get unique identifier for an ironing service within Chiang Mai?"}]], "function": [{"name": "get_service_id", "description": "Retrieve the unique identifier for a specific service within a given province.", "parameters": {"type": "dict", "required": ["service_id", "province_id"], "properties": {"service_id": {"type": "integer", "description": "The unique identifier of the service. For example, '1' for cleaning service, '2' for ironing service, and '3' for extensive cleaning service.", "enum": [1, 2, 3]}, "province_id": {"type": "integer", "description": "The unique identifier of the province where the service is located. For example, '1' for Bangkok, '2' for Chiang Mai, and '3' for Chonburi.", "enum": [1, 2, 3]}}}}]}
{"id": "live_simple_180-105-0", "question": [[{"role": "user", "content": "Help find a housekeeper who provides ironing services in Bangkok, Chatuchak District, with a review score of 4 stars or more"}]], "function": [{"name": "get_service_id", "description": "Retrieve the unique identifier for a specific service based on the service type and location.", "parameters": {"type": "dict", "required": ["service_id"], "properties": {"service_id": {"type": "integer", "description": "The ID corresponding to a service type. For example, '1' represents a cleaning service, '2' represents an ironing service, and '3' represents a comprehensive cleaning service."}, "province_id": {"type": "integer", "description": "The ID representing a specific province. For example, '1' corresponds to Bangkok, '2' to Chiang Mai, and '3' to Chonburi.", "default": null}, "rating": {"type": "float", "description": "The average rating of the service provider's review score. Ratings are represented as floating-point numbers to accommodate fractional scores.", "default": null}}}}]}
{"id": "live_simple_181-106-0", "question": [[{"role": "user", "content": "Help find a housekeeper who provides ironing services in Bangkok, Chatuchak District, with a review score of 4.5 stars or more."}]], "function": [{"name": "get_service_id", "description": "Retrieve the unique identifier for a specific service within a given province based on the service type and location.", "parameters": {"type": "dict", "required": ["service_id"], "properties": {"service_id": {"type": "integer", "description": "The unique identifier of the service. For example, '1' represents a cleaning service, '2' for an ironing service, and '3' for a comprehensive cleaning service."}, "province_id": {"type": "integer", "description": "The unique identifier of the province. For instance, '1' for Bangkok, '2' for Chiang Mai, and '3' for Chonburi.", "default": 1}, "district_name": {"type": "string", "description": "The name of the district where the service is located, such as 'Watthana' or 'Thon Buri'.", "default": "Not Specified"}, "rating": {"type": "float", "description": "The average rating of the service provider's review score, from 0.0 to 5.0, where '4.5' represents a very good rating.", "default": 0.0}}}}]}
{"id": "live_simple_182-107-0", "question": [[{"role": "user", "content": "Help find a housekeeper who provides ironing services in Bangkok, Khlong Sam Wa District, Sai Kong Subdistrict, with a review score of 4.5 stars or more."}]], "function": [{"name": "get_service_providers", "description": "Retrieve a list of service providers based on the given service type and location criteria.", "parameters": {"type": "dict", "required": ["service_id"], "properties": {"service_id": {"type": "integer", "description": "Unique identifier for the service type. For example, 1 represents cleaning service, 2 for ironing service, and 3 for a comprehensive cleaning service."}, "province_id": {"type": "integer", "description": "Unique identifier for the province. For example, 1 corresponds to Bangkok, 2 to Chiang Mai, and 3 to Chonburi.", "default": null}, "district_name": {"type": "string", "description": "The name of the district where the service is required, such as 'Downtown'.", "default": null}, "sub_district_name": {"type": "string", "description": "The name of the sub-district within the district, like 'Old Town'.", "default": null}, "rating": {"type": "float", "description": "The minimum average rating to filter service providers, on a scale from 1.0 to 5.0 with decimals representing fractional ratings.", "default": null}}}}]}
{"id": "live_simple_183-108-0", "question": [[{"role": "system", "content": "Please act like the current date is 2024/02/21"}, {"role": "user", "content": "find profressional cleaning in Bangkok with rating 2.0 or higher"}]], "function": [{"name": "getDataForProfessional", "description": "Retrieve a collection of professional workers that match the specified criteria, such as average rating, province, and services offered.", "parameters": {"type": "dict", "required": ["province_id"], "properties": {"avg_rating": {"type": "float", "description": "The average rating of the service provider's review score, ranging from 1.0 (very bad) to 5.0 (excellent) in increments of 0.5.", "default": 3.0}, "province_id": {"type": "integer", "description": "The unique identifier for a province. For example, 1 represents Bangkok, 2 for Chiang Mai, 3 for Chonburi."}, "districts_name": {"type": "string", "description": "The name of the district in the format of 'District, Province'. If not specified, the search will include all districts within the province.", "default": "All districts"}, "service_id": {"type": "integer", "description": "The unique identifier for the type of service being offered. For example, 1 for cleaning service, 2 for ironing service, 3 for deep cleaning service.", "default": null}}}}]}
{"id": "live_simple_184-109-0", "question": [[{"role": "system", "content": "Please act like the current date is 2024/02/21"}, {"role": "user", "content": "find good reputation profressional cleaning in Bangkok with higher than 4 rating"}]], "function": [{"name": "getDataForProfessional", "description": "Retrieves a collection of professional workers that match the specified service, province, and rating criteria.", "parameters": {"type": "dict", "required": ["service_id", "province_id"], "properties": {"service_id": {"type": "integer", "description": "Unique identifier for the service. For example, 1 for cleaning service, 2 for ironing service, 3 for big cleaning service."}, "province_id": {"type": "integer", "description": "Unique identifier for the province. For example, 1 for Bangkok, 2 for Chiang Mai, 3 for Chonburi."}, "district_name": {"type": "string", "description": "The name of the district where the service is provided.", "default": null}, "rating": {"type": "float", "description": "The minimum average rating of the service provider's review score, represented as a float. A null value represents no specific rating filter.", "default": null}}}}]}
{"id": "live_simple_185-110-0", "question": [[{"role": "user", "content": "Help find a housekeeper who provides ironing services in Chonburi Province. with a review score of 4.5 stars or more, available tomorrow, today is 20230929"}]], "function": [{"name": "get_service_providers", "description": "Retrieve a list of service providers based on service type, location, and other criteria.", "parameters": {"type": "dict", "required": ["service_id"], "properties": {"service_id": {"type": "integer", "description": "Unique identifier of the service. For example: 1 for cleaning service, 2 for ironing service, 3 for comprehensive cleaning service."}, "province_id": {"type": "integer", "description": "Unique identifier of the province. If not provided, service providers from all provinces will be considered. For example, 1 for Bangkok, 2 for Chiang Mai, 3 for Chonburi.", "default": 0}, "district_name": {"type": "string", "description": "Name of the district where the service is required. Leave as null to include all districts.", "default": null}, "sub_district_name": {"type": "string", "description": "Name of the sub-district where the service is required. Leave as null to include all sub-districts.", "default": null}, "rating": {"type": "float", "description": "The minimum average rating of the service provider's review score, ranging from 1.0 to 5.0. If not provided, no minimum rating filter will be applied.", "default": 1.0}, "start_available_date": {"type": "string", "description": "The earliest available date for service provision in the format of 'YYYY-MM-DD', such as '2023-04-15'. If not provided, today's date will be considered as default.", "default": null}}}}]}
{"id": "live_simple_186-111-0", "question": [[{"role": "user", "content": "Help find a housekeeper who provides ironing services in Chonburi Province. with a review score of 4.5 stars or more, available on 12/03/2024 16:00 - 18:00."}]], "function": [{"name": "get_service_providers", "description": "Retrieves a list of service providers based on the specified criteria including service type, location, and availability.", "parameters": {"type": "dict", "required": ["service_id"], "properties": {"service_id": {"type": "integer", "description": "The unique identifier of the service. For example, 1 represents cleaning service, 2 represents ironing service, 3 represents big cleaning service."}, "province_id": {"type": "integer", "description": "The unique identifier of the province. For example, 1 for Bangkok, 2 for Chiang Mai, 3 for Chonburi.", "default": null}, "district_name": {"type": "string", "description": "The name of the district. Example: 'Watthana', 'Thon Buri'.", "default": null}, "sub_district_name": {"type": "string", "description": "The name of the sub-district. Example: 'Phra Khanong', 'Khlong Toei'.", "default": null}, "rating": {"type": "float", "description": "The minimum average rating for filtering service providers. Ratings are on a scale from 1.0 to 5.0.", "default": null}, "start_available_date": {"type": "string", "description": "The start of the availability period for service providers in the format of 'YYYY-MM-DD HH:mm:ss'. Default represents the current date and time.", "default": "null (represents the current date and time)"}, "end_available_date": {"type": "string", "description": "The end of the availability period for service providers in the format of 'YYYY-MM-DD HH:mm:ss'. Default represents no specific end date.", "default": "null (represents no specific end date)"}}}}]}
{"id": "live_simple_187-112-0", "question": [[{"role": "user", "content": "Help find a housekeeper who provides ironing services in Chonburi Province. with a review score of 4.5 stars or more, available on 12/03/2024 16:00 - 18:00 and has never had a history of being late to work"}]], "function": [{"name": "get_service_providers", "description": "Retrieve a list of service providers based on the specified criteria such as service type, location, and availability.", "parameters": {"type": "dict", "required": ["service_id"], "properties": {"service_id": {"type": "integer", "description": "Unique identifier for the service, e.g., 1 for cleaning, 2 for ironing, 3 for extensive cleaning."}, "province_id": {"type": "integer", "description": "Unique identifier for the province. Optional parameter, if not specified, all provinces will be considered. For example, 1 for Bangkok, 2 for Chiang Mai, 3 for Chonburi.", "default": null}, "district_name": {"type": "string", "description": "The name of the district where the service is needed. Optional parameter, defaults to any district if not specified.", "default": "Any"}, "sub_district_name": {"type": "string", "description": "The name of the sub-district where the service is needed. Optional parameter, defaults to any sub-district if not specified.", "default": "Any"}, "rating": {"type": "float", "description": "The minimum average rating required for the service provider, from 0.0 to 5.0, where 5.0 is the best rating.", "default": 0.0}, "start_available_date": {"type": "string", "description": "The start date from which the service provider is available, in the format of 'YYYY-MM-DD'. Optional parameter, defaults to the current date if not specified.", "default": null}, "end_available_date": {"type": "string", "description": "The end date until which the service provider is available, in the format of 'YYYY-MM-DD'. Optional parameter, defaults to no specific end date if not specified.", "default": null}, "has_late_check_in": {"type": "boolean", "description": "Indicates whether the service provider has a record of late check-in. True for providers with a record, and false for those without.", "default": false}}}}]}
{"id": "live_simple_188-113-0", "question": [[{"role": "system", "content": "Please act like the current date is 2024/02/21"}, {"role": "user", "content": "Help find a housewife who provides ironing services in Chiang Mai, Sankampang District, Ton Pao Subdistrict, with a review of 4.5 stars"}]], "function": [{"name": "getDataForProfessional", "description": "Retrieve a list of professional workers who meet specified criteria, such as service type, location, availability, and rating.", "parameters": {"type": "dict", "required": ["service_id", "province_id"], "properties": {"service_id": {"type": "integer", "description": "Unique identifier for the type of service. For example, 1 represents cleaning service, 2 represents ironing service, 3 represents big cleaning service.", "enum": [1, 2, 3]}, "province_id": {"type": "integer", "description": "Unique identifier for the province. For example, 1 for Bangkok, 2 for Chiang Mai, 3 for Chonburi.", "enum": [1, 2, 3]}, "district_name": {"type": "string", "description": "The name of the district where the service is required.", "default": null}, "sub_district_name": {"type": "string", "description": "The name of the sub-district where the service is required.", "default": null}, "start_available_date": {"type": "string", "description": "The start date from which the service provider is available, in the format 'YYYY-MM-DD HH:mm:ss'.", "default": null}, "end_available_date": {"type": "string", "description": "The end date until which the service provider is available, in the format 'YYYY-MM-DD HH:mm:ss'.", "default": null}, "has_late_check_in": {"type": "boolean", "description": "Indicator of whether the service provider has a record of late check-in.", "default": false}, "rating": {"type": "float", "description": "The average rating of the service provider's review score. Ratings are expected to be between 0.0 and 5.0.", "default": null}}}}]}
{"id": "live_simple_189-114-0", "question": [[{"role": "system", "content": "You are a top-tier algorithm for extracting information from text. Only extract information that is relevant to the provided text. If no information is relevant, use the schema and output an empty list where appropriate."}, {"role": "user", "content": "I need to extract information from the following text: ```\\n\\nMy name is Chester. i am 42 years old. My friend Jane is a year older than me.\\n\\n```\\n"}]], "function": [{"name": "extractor.extract_information", "description": "Extract information from the provided data array matching a predefined schema, which includes age, name, and optional nickname of a person.", "parameters": {"type": "dict", "required": ["data"], "properties": {"data": {"type": "array", "description": "An array of dictionaries, each representing an individual's information.", "items": {"type": "dict", "properties": {"age": {"type": "integer", "description": "The age of the person in years. Must be a positive integer."}, "name": {"type": "string", "description": "The full name of the person."}, "nick_name": {"type": "string", "description": "Alias or nickname of the person, if available.", "default": null}}}}}}}]}
{"id": "live_simple_190-115-0", "question": [[{"role": "user", "content": "what is the specs ACHD of type MPN. I want to see images."}]], "function": [{"name": "raptor.mpn.specs", "description": "Retrieve specifications for a given Manufacturer Part Number (MPN), Item Number, Stock Keeping Unit (SKU), or Part Number.", "parameters": {"type": "dict", "required": ["identifier"], "properties": {"identifier": {"type": "string", "description": "The unique identifier, which can be an MPN, Item Number, SKU, or Part Number, for searching the corresponding specs."}, "search_type": {"type": "string", "description": "The type of the provided identifier.", "enum": ["MPN", "ItemNo", "SKU", "PartNumber"], "default": "MPN"}, "include_images": {"type": "boolean", "description": "Specify whether to include images in the search results.", "default": false}}}}]}
{"id": "live_simple_191-115-1", "question": [[{"role": "user", "content": "retrieve just the specifications for part with the MPN identifier 3pak7?"}]], "function": [{"name": "raptor.mpn.specs", "description": "Retrieve specifications for a given Manufacturer Part Number (MPN), Item Number, Stock Keeping Unit (SKU), or Part Number.", "parameters": {"type": "dict", "required": ["identifier"], "properties": {"identifier": {"type": "string", "description": "The unique identifier, which can be an MPN, Item Number, SKU, or Part Number, for searching the corresponding specs."}, "search_type": {"type": "string", "description": "The type of the provided identifier.", "enum": ["MPN", "ItemNo", "SKU", "PartNumber"], "default": "MPN"}, "include_images": {"type": "boolean", "description": "Specify whether to include images in the search results.", "default": false}}}}]}
{"id": "live_simple_192-116-0", "question": [[{"role": "user", "content": "Can you tell me the forecast for Pacifica on April 11th, 2023?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_193-116-1", "question": [[{"role": "user", "content": "Can you tell me the weather forecast for New York on March 8th, 2023?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_194-116-2", "question": [[{"role": "user", "content": "I'm visiting Martinez soon and would like to check the weather there for the date of April 25th 2023, please."}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_195-116-3", "question": [[{"role": "user", "content": "What's the weather forecast for Palo Alto for today April 25th 2023?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_196-116-4", "question": [[{"role": "user", "content": "Could you provide the forecast for Alameda on the upcoming Wednesday, today is Monday April 25th 2023?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_197-116-5", "question": [[{"role": "user", "content": "Could you provide me with the weather forecast for Stinson Beach on April 5th, 2023?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_198-116-6", "question": [[{"role": "user", "content": "Could you tell me what the weather will be like in Healdsburg on the 2nd of march 2023?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_199-116-7", "question": [[{"role": "user", "content": "Could you tell me the weather forecast for Marshall, MN on March 5th 2023?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_200-116-8", "question": [[{"role": "user", "content": "I want to check the weather condition in Fremont on March 1st 2023. "}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_201-116-9", "question": [[{"role": "user", "content": "I'm planning a picnic on the 4th march 2023 and would like to know the forecast for Campbell on that day. Could you provide that information?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_202-116-10", "question": [[{"role": "user", "content": "Can you provide me with the weather forecast for Foster City for April 25th 2023?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_203-116-11", "question": [[{"role": "user", "content": "Hi, could you help me check the weather in Washington, DC for March 1st, 2023 please?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_204-116-12", "question": [[{"role": "user", "content": "Could you tell me what the weather will be like in Rutherford, NJ, on April 22nd 2023?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_205-116-13", "question": [[{"role": "user", "content": "What's the weather forecast for Berkeley on the upcoming Saturday? Today is Tuesday April 25th 2023"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_206-116-14", "question": [[{"role": "user", "content": "Hi, could you tell me the forecast for London, England on the 5th of march 2023?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_207-116-15", "question": [[{"role": "user", "content": "Can you tell me what the weather will be like in Sacramento on April 22nd 2023?"}]], "function": [{"name": "Weather_1_GetWeather", "description": "Retrieves the weather forecast for a specified city on a particular date.", "parameters": {"type": "dict", "required": ["city"], "properties": {"city": {"type": "string", "description": "The name of the city for which weather information is requested."}, "date": {"type": "string", "description": "The date for which weather information is requested, in the format of 'YYYY-MM-DD'. If not provided, the current date is used.", "default": "2019-03-01"}}}}]}
{"id": "live_simple_208-117-0", "question": [[{"role": "user", "content": "I want to find a good film to watch with Duane Whitaker and I want something directed by Quentin Tarantino."}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_209-117-1", "question": [[{"role": "user", "content": "I wish to see the movie which is directed by David Leitch and acted by Lori Pelenise Tuisano."}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_210-117-2", "question": [[{"role": "user", "content": "My friend and I want to go see a movie but we can't find one we like. We really want to see a Drama."}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_211-117-3", "question": [[{"role": "user", "content": "I would like to choose a movie to watch. Can you help me find a movie in the Comic genre with James Corden in it?"}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user or genre is not specified by user", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres.", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_212-117-4", "question": [[{"role": "user", "content": "I'm in the mood for a good laugh. Find a comedy movie directed by Edgar Wright?"}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_213-117-5", "question": [[{"role": "user", "content": "Find me a list of Offbeat genre movies directed by Tim Burton?"}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_214-117-6", "question": [[{"role": "user", "content": "It's suppose to be raining all day so I'm cooped up in the house and bored. Can you help find me a movie to watch, preferably one that is classified as a Comic and directed by Nitesh Tiwari?"}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_215-117-7", "question": [[{"role": "user", "content": "I'm looking for a movie to watch tonight, preferably something in the Fantasy genre. Any suggestions?"}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_216-117-8", "question": [[{"role": "user", "content": "Find me action movies that have David Leitch as the director and include Alex King in the cast?"}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_217-117-9", "question": [[{"role": "user", "content": "Let me enjoy movie directed by Nitesh Tiwari, is there any one?"}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_218-117-10", "question": [[{"role": "user", "content": "I'm at home alone and it is awfully quiet, so it's the perfect time to sit back and watch a movie. Can you find me a movie with Michaela Watkins starring in it and preferably directed by Paul Downs Colaizzo?"}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_219-117-11", "question": [[{"role": "user", "content": "I want to watch a McKinley Belcher III movie. I want something directed by Simon Curtis. It should be a drama."}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_220-117-12", "question": [[{"role": "user", "content": "Can you find me a thriller movie directed by Alexander Mackendrick?"}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_221-117-13", "question": [[{"role": "user", "content": "Can you find me a nice movie directed by James Gray to watch?"}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres. Use 'dontcare' if the genre is not a specific search criterion or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_222-117-14", "question": [[{"role": "user", "content": "I like to watch a movie. will you find me a movie which has sterling K. Brown as a actor and it should be directed by Thurop Van Orman"}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_223-117-15", "question": [[{"role": "user", "content": "I would like to find a movie to watch in the Imaginative fiction genre, as I have started to watch a lot of fantasy-type movies lately, you know!!!!!!"}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_224-117-16", "question": [[{"role": "user", "content": "Could you suggest some Offbeat genre movies directed by Tim Burton with Johnny Depp in the cast?"}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_225-117-17", "question": [[{"role": "user", "content": "I have some leisure time so that i could watch a movie. Will you find me a movie directed by Kirill Mikhanovsky and find something like Comedy-drama genre?"}]], "function": [{"name": "Movies_3_FindMovies", "description": "Retrieves a list of movies based on the director, genre, and cast specified by the user.", "parameters": {"type": "dict", "required": [], "properties": {"directed_by": {"type": "string", "description": "Director of the movie. Use 'dontcare' if the director is not a specific search criterion.", "default": "dontcare"}, "genre": {"type": "string", "description": "Genre of the movie. Use 'dontcare' to include all genres or genre is not specified by user", "enum": ["Offbeat", "Fantasy", "World", "Mystery", "Thriller", "Comedy", "Comedy-drama", "Horror", "Animation", "Sci-fi", "Cult", "Drama", "Anime", "Family", "Action", "dontcare"], "default": "dontcare"}, "cast": {"type": "string", "description": "Names of leading actors or actresses in the movie. Use 'dontcare' if the cast is not a specific search criterion.", "default": "dontcare"}}}}]}
{"id": "live_simple_226-118-0", "question": [[{"role": "user", "content": "Convert the phrase 'I am a pretty girl' into spoken audio in Spanish with a female voice?"}]], "function": [{"name": "text_to_speech.convert", "description": "Converts input text into spoken audio, providing the resulting audio in a specified format.", "parameters": {"type": "dict", "required": ["text"], "properties": {"text": {"type": "string", "description": "The text to be converted to speech."}, "language": {"type": "string", "description": "The language of the input text, use default if nothing specified", "enum": ["en-US", "es-ES", "de-DE", "fr-FR", "it-IT"], "default": "en-US"}, "gender": {"type": "string", "description": "The gender of the voice used for the text-to-speech conversion, use default if nothing specified", "enum": ["male", "female"], "default": "female"}, "format": {"type": "string", "description": "The audio format of the resulting spoken text, use default if nothing specified", "enum": ["mp3", "wav", "ogg"], "default": "mp3"}, "speed": {"type": "float", "description": "The speed of the speech, represented as a float where 1.0 is the normal speed.", "default": 1.0}}}}]}
{"id": "live_simple_227-118-1", "question": [[{"role": "user", "content": "Listen to the phrase '\u6211\u7231\u5b66\u4e60' in a male voice and in Chinese, and could I get the audio in WAV format, please?"}]], "function": [{"name": "text_to_speech.convert", "description": "Converts input text into spoken audio, providing the resulting audio in a specified format.", "parameters": {"type": "dict", "required": ["text"], "properties": {"text": {"type": "string", "description": "The text to be converted to speech."}, "language": {"type": "string", "description": "The language to convert the input text to, use default if nothing specified", "enum": ["en-US", "es-ES", "de-DE", "fr-FR", "it-IT", "zh-CN"], "default": "en-US"}, "gender": {"type": "string", "description": "The gender of the voice used for the text-to-speech conversion, use default if nothing specified", "enum": ["male", "female"], "default": "female"}, "format": {"type": "string", "description": "The audio format of the resulting spoken text, use default if nothing specified", "enum": ["mp3", "wav", "ogg"], "default": "mp3"}, "speed": {"type": "float", "description": "The speed of the speech, represented as a float where 1.0 is the normal speed.", "default": 1.0}}}}]}
{"id": "live_simple_228-119-0", "question": [[{"role": "user", "content": "Convert it to audio format: I am a pretty girl"}]], "function": [{"name": "text_to_speech.convert", "description": "Converts a given text string into spoken words in an audio format.", "parameters": {"type": "dict", "required": ["text"], "properties": {"text": {"type": "string", "description": "The text content to be converted into speech."}, "language": {"type": "string", "description": "The language in which the text is written, use default if nothing specified", "enum": ["en-US", "es-ES", "de-DE", "fr-FR", "it-IT"], "default": "en-US"}, "gender": {"type": "string", "description": "The gender of the voice used for the text-to-speech conversion, use default if nothing specified", "enum": ["male", "female"], "default": "female"}, "speed": {"type": "float", "description": "The speed at which the speech is narrated, ranging from 0.5 (slow) to 2.0 (fast), use default if nothing specified", "default": 1.0}}}}]}
{"id": "live_simple_229-120-0", "question": [[{"role": "user", "content": "Retrieve the list of Business Unit products that are associated with me?"}]], "function": [{"name": "requests.get", "description": "Retrieve a list of Business Unit (BU) products from the specified API endpoint.", "parameters": {"type": "dict", "required": ["anchor"], "properties": {"anchor": {"type": "string", "description": "Specifies the subset of BU products to retrieve, either all available products or only those associated with the user.", "enum": ["all", "user"]}}}}]}
{"id": "live_simple_230-121-0", "question": [[{"role": "user", "content": "Play high quality Roar by Katy Perry. The song_id is wjeiruhuq_roar."}]], "function": [{"name": "play_song", "description": "Initiates playback of a specified song through the Spotify API.", "parameters": {"type": "dict", "required": ["song_id"], "properties": {"song_id": {"type": "string", "description": "The unique identifier for the song to be played."}, "artist": {"type": "string", "description": "The name of the artist performing the song.", "default": null}, "quality": {"type": "string", "description": "The streaming quality preference.", "enum": ["low", "medium", "high"], "default": "medium"}, "device_id": {"type": "string", "description": "The identifier for the device on which the song will be played. If not specified, the default playback device is used.", "default": null}}}}]}
{"id": "live_simple_231-122-0", "question": [[{"role": "user", "content": "Can you move my next gym session, which is event number 456123, to next Thursday at 4:30 PM? Today is monday 2022-10-20"}]], "function": [{"name": "reschedule_event", "description": "Reschedule an event to a new date or time, specified in ISO-8601 format.", "parameters": {"type": "dict", "required": ["event_identifier", "new_datetime"], "properties": {"event_identifier": {"type": "string", "description": "The unique identifier of the event to be rescheduled."}, "new_datetime": {"type": "string", "description": "The new date and time for the event in ISO-8601 format, such as '2023-04-15T13:45:00Z'."}}}}]}
{"id": "live_simple_232-122-1", "question": [[{"role": "user", "content": "I need to change the date for Oscar's medicine appointment to this coming Friday. The event identifier is 'med123'. What's the best way to move it to the 15th of April 2021 at 1:45 pm, UTC?"}]], "function": [{"name": "reschedule_event", "description": "Reschedule an event to a new date or time, specified in ISO-8601 format.", "parameters": {"type": "dict", "required": ["event_identifier", "new_datetime"], "properties": {"event_identifier": {"type": "string", "description": "The unique identifier of the event to be rescheduled."}, "new_datetime": {"type": "string", "description": "The new date and time for the event in ISO-8601 format, such as '2023-04-15T13:45:00Z'."}}}}]}
{"id": "live_simple_233-123-0", "question": [[{"role": "user", "content": "Can you book a flight for me departing from Paris, France, on the 12th of March 2023 at 3 in the afternoon?"}]], "function": [{"name": "book_flight", "description": "Books a flight based on the provided departure location and time. Optionally, a return time can be specified for round-trip bookings.", "parameters": {"type": "dict", "required": ["departure_location", "departure_time"], "properties": {"departure_location": {"type": "string", "description": "The departure location in the format of 'City, State (abbr)' or 'City, Country', such as 'Los Angeles, CA', 'Paris, France', or 'New York, NY'."}, "departure_time": {"type": "string", "description": "The departure time in the format of 'dd/mm/yyyy HH:MM', representing the local time of the departure location."}, "return_time": {"type": "string", "description": "The return time in the format of 'dd/mm/yyyy HH:MM', representing the local time of the departure location. If not specified, a one-way ticket is booked.", "default": null}}}}]}
{"id": "live_simple_234-123-1", "question": [[{"role": "user", "content": "Can you arrange a flight for me from New York, NY to Paris with a departure on the 20th of June 2022 at 5 in the afternoon?"}]], "function": [{"name": "book_flight", "description": "Books a flight based on the provided departure location and time. Optionally, a return time can be specified for round-trip bookings.", "parameters": {"type": "dict", "required": ["departure_location", "departure_time"], "properties": {"departure_location": {"type": "string", "description": "The departure location in the format of 'City (abbr), State', such as 'Los Angeles, CA' or 'New York, NY'."}, "departure_time": {"type": "string", "description": "The departure time in the format of 'dd/mm/yyyy HH:MM', representing the local time of the departure location."}, "return_time": {"type": "string", "description": "The return time in the format of 'dd/mm/yyyy HH:MM', representing the local time of the departure location. If not specified, a one-way ticket is booked.", "default": null}}}}]}
{"id": "live_simple_235-124-0", "question": [[{"role": "user", "content": "Play the song 'Friends' by Marshmello"}]], "function": [{"name": "play_spotify_song", "description": "This function searches for a song on Spotify using a provided query and plays the selected track", "parameters": {"type": "dict", "required": ["query"], "properties": {"query": {"type": "string", "description": "The search query for the song, including track and artist information, in the format 'track:SongName artist:ArtistName', such as 'track:Doxy artist:Miles Davis'."}, "shuffle": {"type": "boolean", "description": "Indicates whether to play the songs in shuffle mode, use default if not specified", "default": false}, "volume": {"type": "integer", "description": "The volume level to set when playing the song, expressed as a percentage from 0 to 100m, use default if not specified", "default": 50}}}}]}
{"id": "live_simple_236-124-1", "question": [[{"role": "user", "content": "I'd like to listen to the song 'Dil Nu' by Maninder Buttar on Spotify."}]], "function": [{"name": "play_spotify_song", "description": "This function searches for a song on Spotify using a provided query and plays the selected track.", "parameters": {"type": "dict", "required": ["query"], "properties": {"query": {"type": "string", "description": "The search query for the song, including track and artist information, in the format 'track:SongName artist:ArtistName', such as 'track:Doxy artist:Miles Davis'."}, "shuffle": {"type": "boolean", "description": "Indicates whether to play the songs in shuffle mode.", "default": false}, "volume": {"type": "integer", "description": "The volume level to set when playing the song, expressed as a percentage from 0 to 100, use default if not specified", "default": 50}}}}]}
{"id": "live_simple_237-125-0", "question": [[{"role": "user", "content": "play wrecking ball by Miley Cyrus"}]], "function": [{"name": "play_spotify_song", "description": "This function searches for a song on Spotify using a specified query and plays it.", "parameters": {"type": "dict", "required": ["query"], "properties": {"query": {"type": "string", "description": "The search query for the song. It should be formatted as 'track:Song Name artist:Artist Name' for specific searches, or just 'Song Name' if the artist is unknown."}, "device_id": {"type": "string", "description": "The unique identifier for the device on which the song should be played.", "default": "null"}, "play": {"type": "boolean", "description": "A flag that indicates whether to start playing the song immediately after it is found. Defaults to true.", "default": true}}}}]}
{"id": "live_simple_238-125-1", "question": [[{"role": "user", "content": "I want to listen to 'Dil Nu' by Maninder Buttar. Can you find it on Spotify."}]], "function": [{"name": "play_spotify_song", "description": "This function searches for a song on Spotify using a specified query and plays it.", "parameters": {"type": "dict", "required": ["query"], "properties": {"query": {"type": "string", "description": "The search query for the song. It should be formatted as 'track:Song Name artist:Artist Name' for specific searches, or just 'Song Name' if the artist is unknown."}, "device_id": {"type": "string", "description": "The unique identifier for the device on which the song should be played.", "default": "null"}, "play": {"type": "boolean", "description": "A flag that indicates whether to start playing the song immediately after it is found. Defaults to true.", "default": true}}}}]}
{"id": "live_simple_239-125-2", "question": [[{"role": "user", "content": "Can you search for the song 'Shape of You'?"}]], "function": [{"name": "play_spotify_song", "description": "This function searches for a song on Spotify using a specified query and plays it.", "parameters": {"type": "dict", "required": ["query"], "properties": {"query": {"type": "string", "description": "The search query for the song. It should be formatted as 'track:Song Name artist:Artist Name' for specific searches, or just 'Song Name' if the artist is unknown."}, "device_id": {"type": "string", "description": "The unique identifier for the device on which the song should be played.", "default": "null"}, "play": {"type": "boolean", "description": "A flag that indicates whether to start playing the song immediately after it is found. Defaults to true.", "default": true}}}}]}
{"id": "live_simple_240-125-3", "question": [[{"role": "user", "content": "Can you search for 'Baby Shark'?"}]], "function": [{"name": "play_spotify_song", "description": "This function searches for a song on Spotify using a specified query and plays it.", "parameters": {"type": "dict", "required": ["query"], "properties": {"query": {"type": "string", "description": "The search query for the song. It should be formatted as 'track:Song Name artist:Artist Name' for specific searches, or just 'Song Name' if the artist is unknown."}, "device_id": {"type": "string", "description": "The unique identifier for the device on which the song should be played.", "default": "null"}, "play": {"type": "boolean", "description": "A flag that indicates whether to start playing the song immediately after it is found. Defaults to true.", "default": true}}}}]}
{"id": "live_simple_241-125-4", "question": [[{"role": "user", "content": "play Johnny Johnny Yes papa"}]], "function": [{"name": "play_spotify_song", "description": "This function searches for a song on Spotify using a specified query and plays it.", "parameters": {"type": "dict", "required": ["query"], "properties": {"query": {"type": "string", "description": "The search query for the song. It should be formatted as 'track:Song Name artist:Artist Name' for specific searches, or just 'Song Name' if the artist is unknown."}, "device_id": {"type": "string", "description": "The unique identifier for the device on which the song should be played.", "default": "null"}, "play": {"type": "boolean", "description": "A flag that indicates whether to start playing the song immediately after it is found. Defaults to true.", "default": true}}}}]}
{"id": "live_simple_242-126-0", "question": [[{"role": "user", "content": "Make the volume 20"}]], "function": [{"name": "set_volume", "description": "Set the global volume for all audio playback. The volume level can be specified as an integer percentage between 0 (mute) and 100 (maximum volume).", "parameters": {"type": "dict", "required": ["volume"], "properties": {"volume": {"type": "integer", "description": "The volume level as a percentage from 0 (silent) to 100 (full volume)."}}}}]}
{"id": "live_simple_243-126-1", "question": [[{"role": "user", "content": "Can you lower the audio playback to 30% volume?"}]], "function": [{"name": "set_volume", "description": "Set the global volume for all audio playback. The volume level can be specified as an integer percentage between 0 (mute) and 100 (maximum volume).", "parameters": {"type": "dict", "required": ["volume"], "properties": {"volume": {"type": "integer", "description": "The volume level as a percentage from 0 (silent) to 100 (full volume)."}}}}]}
{"id": "live_simple_244-126-2", "question": [[{"role": "user", "content": "Set the volume to 70"}]], "function": [{"name": "set_volume", "description": "Set the global volume for all audio playback. The volume level can be specified as an integer percentage between 0 (mute) and 100 (maximum volume).", "parameters": {"type": "dict", "required": ["volume"], "properties": {"volume": {"type": "integer", "description": "The volume level as a percentage from 0 (silent) to 100 (full volume)."}}}}]}
{"id": "live_simple_245-127-0", "question": [[{"role": "user", "content": "I need to get a flight from JFK on the 16th of March 2024. Can you find me something that leaves around 14:00?"}]], "function": [{"name": "book_flight", "description": "Books a flight based on the provided departure location, date, and time.", "parameters": {"type": "dict", "required": ["departure_location", "departure_date", "departure_time"], "properties": {"departure_location": {"type": "string", "description": "The city or airport code from which the flight will depart, such as 'JFK' for John F. Kennedy International Airport."}, "departure_date": {"type": "string", "description": "The departure date for the flight in the format of 'dd/mm/yyyy', such as '15/03/2024'. The date must be after 14/03/2024."}, "departure_time": {"type": "string", "description": "The departure time for the flight in 24-hour format, such as '18:45'."}}}}]}
{"id": "live_simple_246-128-0", "question": [[{"role": "user", "content": "Can you arrange a flight for me departing from New York on the 15th of March, 2024?"}]], "function": [{"name": "book_flight", "description": "Books a flight based on the provided departure location and departure time.", "parameters": {"type": "dict", "required": ["departure_location", "departure_time"], "properties": {"departure_location": {"type": "string", "description": "The city or airport from where the flight will depart. Expected in the format of 'City, State' or 'Airport Code', such as 'New York, NY' or 'LAX'."}, "departure_time": {"type": "string", "description": "The departure time of the flight, specified in the format of 'dd/mm/yyyy'. Must be a date after 14/03/2024."}}}}]}
{"id": "live_simple_247-129-0", "question": [[{"role": "user", "content": "Give me the application version."}]], "function": [{"name": "version_api.VersionApi.get_version", "description": "Retrieves the current version information of the application, including its name and version number.", "parameters": {"type": "dict", "required": [], "properties": {}}}]}
{"id": "live_simple_248-130-0", "question": [[{"role": "user", "content": "What are the active root projects of the TEAM with UUID '12'?"}]], "function": [{"name": "acl_api.AclApi.retrieve_projects", "description": "Retrieve the list of projects assigned to a specified team, with options to exclude inactive or child projects.", "parameters": {"type": "dict", "required": ["uuid"], "properties": {"uuid": {"type": "string", "description": "The UUID of the team for which to retrieve the project mappings."}, "excludeInactive": {"type": "boolean", "description": "If set to true, inactive projects will be excluded from the results.", "default": false}, "onlyRoot": {"type": "boolean", "description": "If set to true, only root projects will be returned, excluding any child projects.", "default": false}}}}]}
{"id": "live_simple_249-131-0", "question": [[{"role": "user", "content": "Hey bunny, How are you doing"}]], "function": [{"name": "chat_with_friend", "description": "Initiates a chat session with an AI-powered virtual bunny, where the user can send messages and receive responses.", "parameters": {"type": "dict", "required": ["user_message"], "properties": {"user_message": {"type": "string", "description": "The user's message to the bunny. The message should be a text string representing the user's query or statement for interaction."}}}}]}
{"id": "live_simple_250-132-0", "question": [[{"role": "user", "content": "Want to have an order for me from McDonald's on Uber Eats? I'd like two Big Macs and one McFlurry."}]], "function": [{"name": "uber.eat.order", "description": "Place an order for food on Uber Eats, specifying the restaurant, items, and their respective quantities.", "parameters": {"type": "dict", "required": ["restaurants", "items", "quantities"], "properties": {"restaurants": {"type": "string", "description": "The name of the restaurant from which to order food."}, "items": {"type": "array", "items": {"type": "string"}, "description": "A list of food item names selected from the restaurant's menu."}, "quantities": {"type": "array", "items": {"type": "integer"}, "description": "An array of integers representing the quantity for each corresponding item in the 'items' list. Must have the same number of elements as 'items'."}}}}]}
{"id": "live_simple_251-133-0", "question": [[{"role": "user", "content": "Hi, I am Karan. Could you tell me the current status of my Indigo flight 6E123? My ticket number is IND4567."}]], "function": [{"name": "flight.status.check", "description": "Checks the current status of a flight using the flight's unique identifier and the passenger's name and ticket number.", "parameters": {"type": "dict", "required": ["flight_id", "passenger_name", "ticket_number"], "properties": {"flight_id": {"type": "string", "description": "The unique identifier of the flight, typically in the format 'XX123' where 'XX' represents the airline code followed by the flight number."}, "passenger_name": {"type": "string", "description": "Full name or first name of the passenger as it appears on the ticket."}, "ticket_number": {"type": "string", "description": "Unique ticket number provided to the passenger upon booking, usually consisting of a series of numbers and/or letters."}}}}]}
{"id": "live_simple_252-134-0", "question": [[{"role": "user", "content": "Hi, I am Karan. Can you tell me the current status of my Indigo flight? My ticket number is IND4567."}]], "function": [{"name": "flight.status.check", "description": "Checks the current status of a flight using the airline, passenger's name, and ticket number.", "parameters": {"type": "dict", "required": ["airlines", "person_name", "ticket_number"], "properties": {"airlines": {"type": "string", "description": "The name of the airline for which to check the flight status, such as 'Delta' or 'United'."}, "person_name": {"type": "string", "description": "The full name or first name of the passenger as it appears on the ticket, in the format 'First Last'. If only one name mentioned assume last name is not specified and instead use LNU"}, "ticket_number": {"type": "string", "description": "The alphanumeric ticket number associated with the passenger's flight booking."}}}}]}
{"id": "live_simple_253-135-0", "question": [[{"role": "user", "content": "I want to order five burgers and six chicken wings from McDonald's."}]], "function": [{"name": "uber.eat.order", "description": "Place an order for food from selected restaurants with specified items and their quantities.", "parameters": {"type": "dict", "required": ["restaurants", "items", "quantities"], "properties": {"restaurants": {"type": "string", "description": "The name of the restaurant from which the user wants to order food. Use the exact name as registered on Uber Eats."}, "items": {"type": "array", "items": {"type": "string"}, "description": "A list of item names that the user wants to order from the restaurant."}, "quantities": {"type": "array", "items": {"type": "integer"}, "description": "A list of integers, each representing the quantity of the corresponding item in the order. The quantities should align with the items list by index."}}}}]}
{"id": "live_simple_254-136-0", "question": [[{"role": "user", "content": "need all pending mandates of Parath with ID U123?"}]], "function": [{"name": "user.mandates", "description": "Fetches a list of mandates for a user given the user's ID and the status of the mandates. The user ID is a required parameter, while the status is optional and can be used to filter the mandates by their current status.", "parameters": {"type": "dict", "required": ["user_id"], "properties": {"user_id": {"type": "string", "description": "The unique identifier of the user for whom the mandates need to be fetched."}, "status": {"type": "string", "description": "The current status of the mandates to fetch. If not specified, mandates with all statuses are fetched.", "enum": ["active", "pending", "inactive"], "default": "active"}}}}]}
{"id": "live_simple_255-136-1", "question": [[{"role": "user", "content": "Get the list of active mandates for the user with ID U123456?"}]], "function": [{"name": "user.mandates", "description": "Fetches a list of mandates for a user given the user's ID and the status of the mandates. The user ID is a required parameter, while the status is optional and can be used to filter the mandates by their current status.", "parameters": {"type": "dict", "required": ["user_id"], "properties": {"user_id": {"type": "string", "description": "The unique identifier of the user for whom the mandates need to be fetched."}, "status": {"type": "string", "description": "The current status of the mandates to fetch. If not specified, mandates with all statuses are fetched.", "enum": ["active", "pending", "inactive"], "default": "active"}}}}]}
{"id": "live_simple_256-137-0", "question": [[{"role": "system", "content": "call HANDOVER function to transfer the request if user asks a question."}, {"role": "user", "content": "I have had submit a compaint and I like to check its status"}]], "function": [{"name": "answer_question", "description": "This function transfers the chat interaction to a human agent when the automated system encounters a question that it cannot answer.", "parameters": {"type": "dict", "required": ["statement"], "properties": {"statement": {"type": "string", "description": "The question posed by the user that needs to be transferred to a human agent."}, "urgency": {"type": "string", "description": "The level of urgency for the question to be answered.", "enum": ["low", "medium", "high"], "default": "medium"}, "language": {"type": "string", "description": "The language in which the question is asked, using ISO 639-1 codes (e.g., 'en' for English, 'es' for Spanish)", "default": "en"}}}}]}
{"id": "live_simple_257-137-1", "question": [[{"role": "system", "content": "call HANDOVER function to transfer the request if user asks a question."}, {"role": "user", "content": "Can you tell me what is the minimum package arrival time? "}]], "function": [{"name": "answer_question", "description": "This function transfers the chat interaction to a human agent when the automated system encounters a question that it cannot answer.", "parameters": {"type": "dict", "required": ["statement"], "properties": {"statement": {"type": "string", "description": "The question posed by the user that needs to be transferred to a human agent."}, "urgency": {"type": "string", "description": "The level of urgency for the question to be answered.", "enum": ["low", "medium", "high"], "default": "medium"}, "language": {"type": "string", "description": "The language in which the question is asked, using ISO 639-1 codes (e.g., 'en' for English, 'es' for Spanish).", "default": "en"}}}}]}