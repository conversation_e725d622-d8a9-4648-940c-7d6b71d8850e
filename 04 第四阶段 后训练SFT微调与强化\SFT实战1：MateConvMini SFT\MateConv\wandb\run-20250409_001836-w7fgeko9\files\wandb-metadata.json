{"os": "Linux-5.15.0-124-generic-x86_64-with-glibc2.35", "python": "CPython 3.10.16", "startedAt": "2025-04-08T16:18:36.060966Z", "args": ["--local_rank=0", "--out_dir", "out", "--epochs", "5", "--use_wandb", "--wandb_project", "MateConv-SFT_final"], "program": "/root/autodl-tmp/MateConv/full_sft.py", "codePath": "full_sft.py", "email": "<EMAIL>", "root": "/root/autodl-tmp/MateConv", "host": "autodl-container-c35b448a49-61520c5e", "executable": "/root/miniconda3/envs/MateConv/bin/python", "codePathLocal": "full_sft.py", "cpu_count": 64, "cpu_count_logical": 128, "gpu": "NVIDIA vGPU-32GB", "gpu_count": 8, "disk": {"/": {"total": "32212254720", "used": "25901277184"}}, "memory": {"total": "1081804357632"}, "cpu": {"count": 64, "countLogical": 128}, "gpu_nvidia": [{"name": "NVIDIA vGPU-32GB", "memoryTotal": "34351349760", "cudaCores": 10240, "architecture": "Ada"}, {"name": "NVIDIA vGPU-32GB", "memoryTotal": "34351349760", "cudaCores": 10240, "architecture": "Ada"}, {"name": "NVIDIA vGPU-32GB", "memoryTotal": "34351349760", "cudaCores": 10240, "architecture": "Ada"}, {"name": "NVIDIA vGPU-32GB", "memoryTotal": "34351349760", "cudaCores": 10240, "architecture": "Ada"}, {"name": "NVIDIA vGPU-32GB", "memoryTotal": "34351349760", "cudaCores": 10240, "architecture": "Ada"}, {"name": "NVIDIA vGPU-32GB", "memoryTotal": "34351349760", "cudaCores": 10240, "architecture": "Ada"}, {"name": "NVIDIA vGPU-32GB", "memoryTotal": "34351349760", "cudaCores": 10240, "architecture": "Ada"}, {"name": "NVIDIA vGPU-32GB", "memoryTotal": "34351349760", "cudaCores": 10240, "architecture": "Ada"}], "cudaVersion": "12.6"}