name: Update API Zoo Data

on:
  push:
    branches:
      - main
    paths:
      - 'data/apizoo/**'

jobs:
  send-updates:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v2
        with:
          fetch-depth: 2
      
      - name: Install Dependencies
        run: sudo apt-get install -y jq curl
      
      - name: Send Updated API Data to Server
        run: |
          REPO_URL="https://github.com/ShishirPatil/gorilla/blob/main/"
          FILES=$(git diff --name-only ${{ github.sha }} ${{ github.event.before }} | grep 'data/apizoo/')
          ERROR_FLAG=0
          ISSUE_BODY=""
          for FILE in $FILES; do
            DATA=$(jq -c . < "$FILE")
            FILE_URL="${REPO_URL}${FILE}"
            JSON_BODY=$(jq --arg file_url "$FILE_URL" 'if type == "array" then map(. + {"file_url": $file_url}) else . + {"file_url": $file_url} end' <<< "$DATA")
            TMP_FILE=$(mktemp)
            echo "$JSON_BODY" > "$TMP_FILE"
            RESPONSE=$(curl -s -w "\n%{http_code}" -X POST -H "Content-Type: application/json" -H "Authorization: Bearer ${{ secrets.APIZOO_UPDATE_TOKEN }}" --data-binary "@$TMP_FILE" https://apizooindex.gorilla-llm.com/api/update)
            BODY=$(echo "$RESPONSE" | head -n -1)
            HTTP_STATUS=$(echo "$RESPONSE" | tail -n1)
            if [ "$HTTP_STATUS" -ne 200 ]; then
              ERROR_FLAG=1
              ISSUE_BODY+="**Error adding API documentation in $FILE to the API Zoo Index**\n\n- HTTP Status: $HTTP_STATUS\n- Response:\n\`\`\`\n"
              while read -r line; do
                ISSUE_BODY+="$line\n"
              done <<< "$BODY"
              ISSUE_BODY+="\`\`\`\n\n"
            fi
            rm "$TMP_FILE"
          done
          if [ "$ERROR_FLAG" -eq 1 ]; then
            echo "ISSUE_BODY<<EOF" >> $GITHUB_ENV
            echo "$ISSUE_BODY" >> $GITHUB_ENV
            echo "EOF" >> $GITHUB_ENV
            echo "ERRORS_DETECTED=true" >> $GITHUB_ENV
          fi
      
      - name: Prepare Issue Content If Errors Were Detected
        if: env.ERRORS_DETECTED == 'true'
        run: |
          echo -e "name: API Zoo Index\nabout: Update Error\n\n**Describe the issue**\nError(s) occurred while adding new API documentation to the API Zoo Index.\n\n$ISSUE_BODY" > issue_content.md
          echo "::set-output name=issue_file::issue_content.md"

      - name: Create GitHub Issue on Error
        if: env.ERRORS_DETECTED == 'true'
        uses: peter-evans/create-issue-from-file@v5
        with:
          title: "[API Zoo] Error Updating API Zoo Index"
          content-filepath: ./issue_content.md
          labels: apizoo-index
          token: ${{ secrets.GITHUB_TOKEN }}
