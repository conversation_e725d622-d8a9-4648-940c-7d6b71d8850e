[{"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.detectLanguage", "api_call": "service.projects().detectLanguage(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. Project or location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id}/locations/{location-id}` or `projects/{project-number-or-id}`. For global calls, use `projects/{project-number-or-id}/locations/global` or `projects/{project-number-or-id}`. Only models within the same region (has same location-id) can be used. Otherwise an INVALID_ARGUMENT (400) error is returned."}, "functionality": "Detects the language of text within a request.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.getSupportedLanguages", "api_call": "service.projects().getSupportedLanguages(parent: str).execute()", "api_version": "v3", "api_arguments": {"displayLanguageCode": "string: Optional. The language to use to return localized, human readable names of supported languages. If missing, then display names are not returned in a response.", "model": "string: Optional. Get supported languages of this model. The format depends on model type: - AutoML Translation models: `projects/{project-number-or-id}/locations/{location-id}/models/{model-id}` - General (built-in) models: `projects/{project-number-or-id}/locations/{location-id}/models/general/nmt`, Returns languages supported by the specified model. If missing, we get supported languages of Google general NMT model.", "parent": "[REQUIRED] string: Required. Project or location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id}` or `projects/{project-number-or-id}/locations/{location-id}`. For global calls, use `projects/{project-number-or-id}/locations/global` or `projects/{project-number-or-id}`. Non-global location is required for AutoML models. Only models within the same region (have same location-id) can be used, otherwise an INVALID_ARGUMENT (400) error is returned."}, "functionality": "Returns a list of supported languages for translation.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.romanizeText", "api_call": "service.projects().romanizeText(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. Project or location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id}/locations/{location-id}` or `projects/{project-number-or-id}`. For global calls, use `projects/{project-number-or-id}/locations/global` or `projects/{project-number-or-id}`."}, "functionality": "Romanize input text written in non-Latin scripts to Latin text.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.translateText", "api_call": "service.projects().translateText(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. Project or location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id}` or `projects/{project-number-or-id}/locations/{location-id}`. For global calls, use `projects/{project-number-or-id}/locations/global` or `projects/{project-number-or-id}`. Non-global location is required for requests using AutoML models or custom glossaries. Models and glossaries must be within the same region (have same location-id), otherwise an INVALID_ARGUMENT (400) error is returned."}, "functionality": "Translates input text and returns translated text.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.adaptiveMtTranslate", "api_call": "service.projects().locations().adaptiveMtTranslate(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. Location to make a regional call. Format: `projects/{project-number-or-id}/locations/{location-id}`."}, "functionality": "Translate text using Adaptive MT.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.batchTranslateDocument", "api_call": "service.projects().locations().batchTranslateDocument(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. Location to make a regional call. Format: `projects/{project-number-or-id}/locations/{location-id}`. The `global` location is not supported for batch translation. Only AutoML Translation models or glossaries within the same region (have the same location-id) can be used, otherwise an INVALID_ARGUMENT (400) error is returned."}, "functionality": "Translates a large volume of document in asynchronous batch mode. This function provides real-time output as the inputs are being processed. If caller cancels a request, the partial results (for an input file, it's all or nothing) may still be available on the specified output location. This call returns immediately and you can use google.longrunning.Operation.name to poll the status of the call.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.batchTranslateText", "api_call": "service.projects().locations().batchTranslateText(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. Location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id}/locations/{location-id}`. The `global` location is not supported for batch translation. Only AutoML Translation models or glossaries within the same region (have the same location-id) can be used, otherwise an INVALID_ARGUMENT (400) error is returned."}, "functionality": "Translates a large volume of text in asynchronous batch mode. This function provides real-time output as the inputs are being processed. If caller cancels a request, the partial results (for an input file, it's all or nothing) may still be available on the specified output location. This call returns immediately and you can use google.longrunning.Operation.name to poll the status of the call.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.detectLanguage", "api_call": "service.projects().locations().detectLanguage(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. Project or location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id}/locations/{location-id}` or `projects/{project-number-or-id}`. For global calls, use `projects/{project-number-or-id}/locations/global` or `projects/{project-number-or-id}`. Only models within the same region (has same location-id) can be used. Otherwise an INVALID_ARGUMENT (400) error is returned."}, "functionality": "Detects the language of text within a request.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.get", "api_call": "service.projects().locations().get(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Resource name for the location."}, "functionality": "Gets information about a location.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.getSupportedLanguages", "api_call": "service.projects().locations().getSupportedLanguages(parent: str).execute()", "api_version": "v3", "api_arguments": {"displayLanguageCode": "string: Optional. The language to use to return localized, human readable names of supported languages. If missing, then display names are not returned in a response.", "model": "string: Optional. Get supported languages of this model. The format depends on model type: - AutoML Translation models: `projects/{project-number-or-id}/locations/{location-id}/models/{model-id}` - General (built-in) models: `projects/{project-number-or-id}/locations/{location-id}/models/general/nmt`, Returns languages supported by the specified model. If missing, we get supported languages of Google general NMT model.", "parent": "[REQUIRED] string: Required. Project or location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id}` or `projects/{project-number-or-id}/locations/{location-id}`. For global calls, use `projects/{project-number-or-id}/locations/global` or `projects/{project-number-or-id}`. Non-global location is required for AutoML models. Only models within the same region (have same location-id) can be used, otherwise an INVALID_ARGUMENT (400) error is returned."}, "functionality": "Returns a list of supported languages for translation.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.list", "api_call": "service.projects().locations().list(name: str).execute()", "api_version": "v3", "api_arguments": {"filter": "string: A filter to narrow down results to a preferred subset. The filtering language accepts strings like `\"displayName=tokyo\"`, and is documented in more detail in [AIP-160](https://google.aip.dev/160).", "name": "[REQUIRED] string: The resource that owns the locations collection, if applicable.", "pageSize": "integer: The maximum number of results to return. If not set, the service selects a default.", "pageToken": "string: A page token received from the `next_page_token` field in the response. Send that page token to receive the subsequent page."}, "functionality": "Lists information about the supported locations for this service.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.romanizeText", "api_call": "service.projects().locations().romanizeText(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. Project or location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id}/locations/{location-id}` or `projects/{project-number-or-id}`. For global calls, use `projects/{project-number-or-id}/locations/global` or `projects/{project-number-or-id}`."}, "functionality": "Romanize input text written in non-Latin scripts to Latin text.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.translateDocument", "api_call": "service.projects().locations().translateDocument(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. Location to make a regional call. Format: `projects/{project-number-or-id}/locations/{location-id}`. For global calls, use `projects/{project-number-or-id}/locations/global` or `projects/{project-number-or-id}`. Non-global location is required for requests using AutoML models or custom glossaries. Models and glossaries must be within the same region (have the same location-id), otherwise an INVALID_ARGUMENT (400) error is returned."}, "functionality": "Translates documents in synchronous mode.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.translateText", "api_call": "service.projects().locations().translateText(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. Project or location to make a call. Must refer to a caller's project. Format: `projects/{project-number-or-id}` or `projects/{project-number-or-id}/locations/{location-id}`. For global calls, use `projects/{project-number-or-id}/locations/global` or `projects/{project-number-or-id}`. Non-global location is required for requests using AutoML models or custom glossaries. Models and glossaries must be within the same region (have same location-id), otherwise an INVALID_ARGUMENT (400) error is returned."}, "functionality": "Translates input text and returns translated text.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.adaptiveMtDatasets.create", "api_call": "service.projects().locations().adaptiveMtDatasets().create(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. Name of the parent project. In form of `projects/{project-number-or-id}/locations/{location-id}`"}, "functionality": "Creates an Adaptive MT dataset.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.adaptiveMtDatasets.delete", "api_call": "service.projects().locations().adaptiveMtDatasets().delete(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Required. Name of the dataset. In the form of `projects/{project-number-or-id}/locations/{location-id}/adaptiveMtDatasets/{adaptive-mt-dataset-id}`"}, "functionality": "Deletes an Adaptive MT dataset, including all its entries and associated metadata.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.adaptiveMtDatasets.get", "api_call": "service.projects().locations().adaptiveMtDatasets().get(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Required. Name of the dataset. In the form of `projects/{project-number-or-id}/locations/{location-id}/adaptiveMtDatasets/{adaptive-mt-dataset-id}`"}, "functionality": "Gets the Adaptive MT dataset.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.adaptiveMtDatasets.importAdaptiveMtFile", "api_call": "service.projects().locations().adaptiveMtDatasets().importAdaptiveMtFile(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. The resource name of the file, in form of `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatasets/{dataset}`"}, "functionality": "Imports an AdaptiveMtFile and adds all of its sentences into the AdaptiveMtDataset.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.adaptiveMtDatasets.list", "api_call": "service.projects().locations().adaptiveMtDatasets().list(parent: str).execute()", "api_version": "v3", "api_arguments": {"filter": "string: Optional. An expression for filtering the results of the request. Filter is not supported yet.", "pageSize": "integer: Optional. Requested page size. The server may return fewer results than requested. If unspecified, the server picks an appropriate default.", "pageToken": "string: Optional. A token identifying a page of results the server should return. Typically, this is the value of ListAdaptiveMtDatasetsResponse.next_page_token returned from the previous call to `ListAdaptiveMtDatasets` method. The first page is returned if `page_token`is empty or missing.", "parent": "[REQUIRED] string: Required. The resource name of the project from which to list the Adaptive MT datasets. `projects/{project-number-or-id}/locations/{location-id}`"}, "functionality": "Lists all Adaptive MT datasets for which the caller has read permission.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.adaptiveMtDatasets.adaptiveMtFiles.delete", "api_call": "service.projects().locations().adaptiveMtDatasets().adaptiveMtFiles().delete(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Required. The resource name of the file to delete, in form of `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}`"}, "functionality": "Deletes an AdaptiveMtFile along with its sentences.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.adaptiveMtDatasets.adaptiveMtFiles.get", "api_call": "service.projects().locations().adaptiveMtDatasets().adaptiveMtFiles().get(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Required. The resource name of the file, in form of `projects/{project-number-or-id}/locations/{location_id}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}`"}, "functionality": "Gets and AdaptiveMtFile", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.adaptiveMtDatasets.adaptiveMtFiles.list", "api_call": "service.projects().locations().adaptiveMtDatasets().adaptiveMtFiles().list(parent: str).execute()", "api_version": "v3", "api_arguments": {"pageSize": "integer: Optional. ", "pageToken": "string: Optional. A token identifying a page of results the server should return. Typically, this is the value of ListAdaptiveMtFilesResponse.next_page_token returned from the previous call to `ListAdaptiveMtFiles` method. The first page is returned if `page_token`is empty or missing.", "parent": "[REQUIRED] string: Required. The resource name of the project from which to list the Adaptive MT files. `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`"}, "functionality": "Lists all AdaptiveMtFiles associated to an AdaptiveMtDataset.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.adaptiveMtDatasets.adaptiveMtFiles.adaptiveMtSentences.list", "api_call": "service.projects().locations().adaptiveMtDatasets().adaptiveMtFiles().adaptiveMtSentences().list(parent: str).execute()", "api_version": "v3", "api_arguments": {"pageSize": "integer", "pageToken": "string: A token identifying a page of results the server should return. Typically, this is the value of ListAdaptiveMtSentencesRequest.next_page_token returned from the previous call to `ListTranslationMemories` method. The first page is returned if `page_token` is empty or missing.", "parent": "[REQUIRED] string: Required. The resource name of the project from which to list the Adaptive MT files. The following format lists all sentences under a file. `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}` The following format lists all sentences within a dataset. `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`"}, "functionality": "Lists all AdaptiveMtSentences under a given file/dataset.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.adaptiveMtDatasets.adaptiveMtSentences.list", "api_call": "service.projects().locations().adaptiveMtDatasets().adaptiveMtSentences().list(parent: str).execute()", "api_version": "v3", "api_arguments": {"pageSize": "integer", "pageToken": "string: A token identifying a page of results the server should return. Typically, this is the value of ListAdaptiveMtSentencesRequest.next_page_token returned from the previous call to `ListTranslationMemories` method. The first page is returned if `page_token` is empty or missing.", "parent": "[REQUIRED] string: Required. The resource name of the project from which to list the Adaptive MT files. The following format lists all sentences under a file. `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}/adaptiveMtFiles/{file}` The following format lists all sentences within a dataset. `projects/{project}/locations/{location}/adaptiveMtDatasets/{dataset}`"}, "functionality": "Lists all AdaptiveMtSentences under a given file/dataset.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.datasets.create", "api_call": "service.projects().locations().datasets().create(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. The project name."}, "functionality": "Creates a Dataset.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.datasets.delete", "api_call": "service.projects().locations().datasets().delete(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Required. The name of the dataset to delete."}, "functionality": "Deletes a dataset and all of its contents.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.datasets.exportData", "api_call": "service.projects().locations().datasets().exportData(dataset: str).execute()", "api_version": "v3", "api_arguments": {"dataset": "[REQUIRED] string: Required. Name of the dataset. In form of `projects/{project-number-or-id}/locations/{location-id}/datasets/{dataset-id}`"}, "functionality": "Exports dataset's data to the provided output location.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.datasets.get", "api_call": "service.projects().locations().datasets().get(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Required. The resource name of the dataset to retrieve."}, "functionality": "Gets a Dataset.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.datasets.importData", "api_call": "service.projects().locations().datasets().importData(dataset: str).execute()", "api_version": "v3", "api_arguments": {"dataset": "[REQUIRED] string: Required. Name of the dataset. In form of `projects/{project-number-or-id}/locations/{location-id}/datasets/{dataset-id}`"}, "functionality": "Import sentence pairs into translation Dataset.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.datasets.list", "api_call": "service.projects().locations().datasets().list(parent: str).execute()", "api_version": "v3", "api_arguments": {"pageSize": "integer: Optional. Requested page size. The server can return fewer results than requested.", "pageToken": "string: Optional. A token identifying a page of results for the server to return. Typically obtained from next_page_token field in the response of a ListDatasets call.", "parent": "[REQUIRED] string: Required. Name of the parent project. In form of `projects/{project-number-or-id}/locations/{location-id}`"}, "functionality": "Lists datasets.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.datasets.examples.list", "api_call": "service.projects().locations().datasets().examples().list(parent: str).execute()", "api_version": "v3", "api_arguments": {"filter": "string: Optional. An expression for filtering the examples that will be returned. Example filter: * `usage=TRAIN`", "pageSize": "integer: Optional. Requested page size. The server can return fewer results than requested.", "pageToken": "string: Optional. A token identifying a page of results for the server to return. Typically obtained from next_page_token field in the response of a ListExamples call.", "parent": "[REQUIRED] string: Required. Name of the parent dataset. In form of `projects/{project-number-or-id}/locations/{location-id}/datasets/{dataset-id}`"}, "functionality": "Lists sentence pairs in the dataset.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.glossaries.create", "api_call": "service.projects().locations().glossaries().create(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. The project name."}, "functionality": "Creates a glossary and returns the long-running operation. Returns NOT_FOUND, if the project doesn't exist.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.glossaries.delete", "api_call": "service.projects().locations().glossaries().delete(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Required. The name of the glossary to delete."}, "functionality": "Deletes a glossary, or cancels glossary construction if the glossary isn't created yet. Returns NOT_FOUND, if the glossary doesn't exist.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.glossaries.get", "api_call": "service.projects().locations().glossaries().get(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Required. The name of the glossary to retrieve."}, "functionality": "Gets a glossary. Returns NOT_FOUND, if the glossary doesn't exist.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.glossaries.list", "api_call": "service.projects().locations().glossaries().list(parent: str).execute()", "api_version": "v3", "api_arguments": {"filter": "string: Optional. Filter specifying constraints of a list operation. Specify the constraint by the format of \"key=value\", where key must be \"src\" or \"tgt\", and the value must be a valid language code. For multiple restrictions, concatenate them by \"AND\" (uppercase only), such as: \"src=en-US AND tgt=zh-CN\". Notice that the exact match is used here, which means using 'en-US' and 'en' can lead to different results, which depends on the language code you used when you create the glossary. For the unidirectional glossaries, the \"src\" and \"tgt\" add restrictions on the source and target language code separately. For the equivalent term set glossaries, the \"src\" and/or \"tgt\" add restrictions on the term set. For example: \"src=en-US AND tgt=zh-CN\" will only pick the unidirectional glossaries which exactly match the source language code as \"en-US\" and the target language code \"zh-CN\", but all equivalent term set glossaries which contain \"en-US\" and \"zh-CN\" in their language set will be picked. If missing, no filtering is performed.", "pageSize": "integer: Optional. Requested page size. The server may return fewer glossaries than requested. If unspecified, the server picks an appropriate default.", "pageToken": "string: Optional. A token identifying a page of results the server should return. Typically, this is the value of [ListGlossariesResponse.next_page_token] returned from the previous call to `ListGlossaries` method. The first page is returned if `page_token`is empty or missing.", "parent": "[REQUIRED] string: Required. The name of the project from which to list all of the glossaries."}, "functionality": "Lists glossaries in a project. Returns NOT_FOUND, if the project doesn't exist.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.glossaries.patch", "api_call": "service.projects().locations().glossaries().patch(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Required. The resource name of the glossary. Glossary names have the form `projects/{project-number-or-id}/locations/{location-id}/glossaries/{glossary-id}`.", "updateMask": "string: The list of fields to be updated. Currently only `display_name` and 'input_config'"}, "functionality": "Updates a glossary. A LRO is used since the update can be async if the glossary's entry file is updated.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.glossaries.glossaryEntries.create", "api_call": "service.projects().locations().glossaries().glossaryEntries().create(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. The resource name of the glossary to create the entry under."}, "functionality": "Creates a glossary entry.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.glossaries.glossaryEntries.delete", "api_call": "service.projects().locations().glossaries().glossaryEntries().delete(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Required. The resource name of the glossary entry to delete"}, "functionality": "Deletes a single entry from the glossary", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.glossaries.glossaryEntries.get", "api_call": "service.projects().locations().glossaries().glossaryEntries().get(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Required. The resource name of the glossary entry to get"}, "functionality": "Gets a single glossary entry by the given id.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.glossaries.glossaryEntries.list", "api_call": "service.projects().locations().glossaries().glossaryEntries().list(parent: str).execute()", "api_version": "v3", "api_arguments": {"pageSize": "integer: Optional. Requested page size. The server may return fewer glossary entries than requested. If unspecified, the server picks an appropriate default.", "pageToken": "string: Optional. A token identifying a page of results the server should return. Typically, this is the value of [ListGlossaryEntriesResponse.next_page_token] returned from the previous call. The first page is returned if `page_token`is empty or missing.", "parent": "[REQUIRED] string: Required. The parent glossary resource name for listing the glossary's entries."}, "functionality": "List the entries for the glossary.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.glossaries.glossaryEntries.patch", "api_call": "service.projects().locations().glossaries().glossaryEntries().patch(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Required. The resource name of the entry. Format: \"projects/*/locations/*/glossaries/*/glossaryEntries/*\""}, "functionality": "Updates a glossary entry.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.models.create", "api_call": "service.projects().locations().models().create(parent: str).execute()", "api_version": "v3", "api_arguments": {"parent": "[REQUIRED] string: Required. The project name, in form of `projects/{project}/locations/{location}`"}, "functionality": "Creates a Model.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.models.delete", "api_call": "service.projects().locations().models().delete(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Required. The name of the model to delete."}, "functionality": "Deletes a model.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.models.get", "api_call": "service.projects().locations().models().get(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: Required. The resource name of the model to retrieve."}, "functionality": "Gets a model.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.models.list", "api_call": "service.projects().locations().models().list(parent: str).execute()", "api_version": "v3", "api_arguments": {"filter": "string: Optional. An expression for filtering the models that will be returned. Supported filter: `dataset_id=${dataset_id}`", "pageSize": "integer: Optional. Requested page size. The server can return fewer results than requested.", "pageToken": "string: Optional. A token identifying a page of results for the server to return. Typically obtained from next_page_token field in the response of a ListModels call.", "parent": "[REQUIRED] string: Required. Name of the parent project. In form of `projects/{project-number-or-id}/locations/{location-id}`"}, "functionality": "Lists models.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.operations.cancel", "api_call": "service.projects().locations().operations().cancel(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: The name of the operation resource to be cancelled."}, "functionality": "Starts asynchronous cancellation on a long-running operation. The server makes a best effort to cancel the operation, but success is not guaranteed. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Clients can use Operations.GetOperation or other methods to check whether the cancellation succeeded or whether the operation completed despite cancellation. On successful cancellation, the operation is not deleted; instead, it becomes an operation with an Operation.error value with a google.rpc.Status.code of 1, corresponding to `Code.CANCELLED`.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.operations.delete", "api_call": "service.projects().locations().operations().delete(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: The name of the operation resource to be deleted."}, "functionality": "Deletes a long-running operation. This method indicates that the client is no longer interested in the operation result. It does not cancel the operation. If the server doesn't support this method, it returns `google.rpc.Code.UNIMPLEMENTED`.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.operations.get", "api_call": "service.projects().locations().operations().get(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: The name of the operation resource."}, "functionality": "Gets the latest state of a long-running operation. Clients can use this method to poll the operation result at intervals as recommended by the API service.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.operations.list", "api_call": "service.projects().locations().operations().list(name: str).execute()", "api_version": "v3", "api_arguments": {"filter": "string: The standard list filter.", "name": "[REQUIRED] string: The name of the operation's parent resource.", "pageSize": "integer: The standard list page size.", "pageToken": "string: The standard list page token."}, "functionality": "Lists operations that match the specified filter in the request. If the server doesn't support this method, it returns `UNIMPLEMENTED`.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}, {"user_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "api_name": "Google Translate API - projects.locations.operations.wait", "api_call": "service.projects().locations().operations().wait(name: str).execute()", "api_version": "v3", "api_arguments": {"name": "[REQUIRED] string: The name of the operation resource to wait on."}, "functionality": "Waits until the specified long-running operation is done or reaches at most a specified timeout, returning the latest state. If the operation is already done, the latest state is immediately returned. If the timeout specified is greater than the default HTTP/RPC timeout, the HTTP/RPC timeout is used. If the server does not support this method, it returns `google.rpc.Code.UNIMPLEMENTED`. Note that this method is on a best-effort basis. It may return the latest state before the specified timeout (including immediately), meaning even an immediate response is no guarantee that the operation is done.", "env_requirements": ["google-auth", "google-auth-o<PERSON>hlib", "google-auth-httplib2", "google-api-python-client"], "metadata": {"documentation_link": "https://cloud.google.com/translate/docs/reference/rest"}}]