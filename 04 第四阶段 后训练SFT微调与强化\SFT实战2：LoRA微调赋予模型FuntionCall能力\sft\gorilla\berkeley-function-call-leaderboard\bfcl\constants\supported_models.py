# -----------------------------------------------------------------------------
# Supported Model Index  •  Convenience helper
#
# The canonical model-config mapping lives in `model_config.py` and is ~2000
# lines long. Navigating that file just to see whether a model key exists was
# getting painful, so this lightweight companion keeps **only** the keys in a
# flat list so you can:
#
#   •  skim the supported models at a glance;
#   •  hit ⌘/Ctrl-F and jump straight to the one you need;
#   •  import the list in quick scripts/tests without hauling in the whole
#      config (e.g. `if model_name in SUPPORTED_MODELS:`).
# -----------------------------------------------------------------------------

SUPPORTED_MODELS = [
    "gorilla-openfunctions-v2",
    "DeepSeek-R1",
    "DeepSeek-V3-FC",
    "gpt-4.5-preview-2025-02-27",
    "gpt-4.5-preview-2025-02-27-FC",
    "gpt-4.1-2025-04-14-FC",
    "gpt-4.1-2025-04-14",
    "gpt-4.1-mini-2025-04-14-FC",
    "gpt-4.1-mini-2025-04-14",
    "gpt-4.1-nano-2025-04-14-FC",
    "gpt-4.1-nano-2025-04-14",
    "o1-2024-12-17-FC",
    "o1-2024-12-17",
    "o3-mini-2025-01-31-FC",
    "o3-mini-2025-01-31",
    "gpt-4o-2024-11-20",
    "gpt-4o-2024-11-20-FC",
    "gpt-4o-mini-2024-07-18",
    "gpt-4o-mini-2024-07-18-FC",
    "claude-3-opus-20240229",
    "claude-3-opus-20240229-FC",
    "claude-3-7-sonnet-20250219",
    "claude-3-7-sonnet-20250219-FC",
    "claude-3-5-sonnet-20241022",
    "claude-3-5-sonnet-20241022-FC",
    "claude-3-5-haiku-20241022",
    "claude-3-5-haiku-20241022-FC",
    "nova-pro-v1.0",
    "nova-lite-v1.0",
    "nova-micro-v1.0",
    "open-mistral-nemo-2407",
    "open-mistral-nemo-2407-FC",
    "mistral-large-2411",
    "mistral-large-2411-FC",
    "mistral-small-2503",
    "mistral-small-2503-FC",
    "firefunction-v2-FC",
    "Nexusflow-Raven-v2",
    "gemini-2.0-flash-lite-001-FC",
    "gemini-2.0-flash-lite-001",
    "gemini-2.0-flash-001-FC",
    "gemini-2.0-flash-001",
    "gemini-2.5-pro-exp-03-25-FC",
    "gemini-2.5-pro-exp-03-25",
    "gemini-2.0-flash-thinking-exp-01-21",
    "meetkai/functionary-small-v3.1-FC",
    "meetkai/functionary-medium-v3.1-FC",
    "databricks-dbrx-instruct",
    "command-r-plus-FC",
    "command-r7b-12-2024-FC",
    "command-a-03-2025-FC",
    "snowflake/arctic",
    "nvidia/nemotron-4-340b-instruct",
    "BitAgent/GoGoAgent",
    "palmyra-x-004",
    "grok-3-beta-FC",
    "grok-3-beta",
    "grok-3-mini-beta-FC",
    "grok-3-mini-beta",
    "xiaoming-14B",
    "deepseek-ai/DeepSeek-R1",
    "google/gemma-3-1b-it",
    "google/gemma-3-4b-it",
    "google/gemma-3-12b-it",
    "google/gemma-3-27b-it",
    "google/gemma-2-2b-it",
    "meta-llama/Llama-3.1-8B-Instruct-FC",
    "meta-llama/Llama-3.1-8B-Instruct",
    "meta-llama/Llama-3.1-70B-Instruct-FC",
    "meta-llama/Llama-3.1-70B-Instruct",
    "meta-llama/Llama-3.2-1B-Instruct-FC",
    "meta-llama/Llama-3.2-3B-Instruct-FC",
    "meta-llama/Llama-3.3-70B-Instruct-FC",
    "meta-llama/Llama-4-Scout-17B-16E-Instruct-FC",
    "meta-llama/Llama-4-Maverick-17B-128E-Instruct-FP8-FC",
    "Salesforce/Llama-xLAM-2-70b-fc-r",
    "Salesforce/Llama-xLAM-2-8b-fc-r",
    "Salesforce/xLAM-2-32b-fc-r",
    "Salesforce/xLAM-2-3b-fc-r",
    "Salesforce/xLAM-2-1b-fc-r",
    "mistralai/Ministral-8B-Instruct-2410",
    "microsoft/phi-4",
    "microsoft/Phi-4-mini-instruct",
    "microsoft/Phi-4-mini-instruct-FC",
    "ibm-granite/granite-20b-functioncalling",
    "MadeAgents/Hammer2.1-7b",
    "MadeAgents/Hammer2.1-3b",
    "MadeAgents/Hammer2.1-1.5b",
    "MadeAgents/Hammer2.1-0.5b",
    "THUDM/glm-4-9b-chat",
    "Qwen/Qwen2.5-0.5B-Instruct-FC",
    "Qwen/Qwen2.5-0.5B-Instruct",
    "Qwen/Qwen2.5-1.5B-Instruct-FC",
    "Qwen/Qwen2.5-1.5B-Instruct",
    "Qwen/Qwen2.5-3B-Instruct-FC",
    "Qwen/Qwen2.5-3B-Instruct",
    "Qwen/Qwen2.5-7B-Instruct-FC",
    "Qwen/Qwen2.5-7B-Instruct",
    "Qwen/Qwen2.5-14B-Instruct-FC",
    "Qwen/Qwen2.5-14B-Instruct",
    "Qwen/Qwen2.5-32B-Instruct-FC",
    "Qwen/Qwen2.5-32B-Instruct",
    "Qwen/Qwen2.5-72B-Instruct-FC",
    "Qwen/Qwen2.5-72B-Instruct",
    "Team-ACE/ToolACE-2-8B",
    "openbmb/MiniCPM3-4B",
    "openbmb/MiniCPM3-4B-FC",
    "watt-ai/watt-tool-8B",
    "watt-ai/watt-tool-70B",
    "ZJared/Haha-7B",
    "speakleash/Bielik-11B-v2.3-Instruct",
    "NovaSky-AI/Sky-T1-32B-Preview",
    "Qwen/QwQ-32B-Preview",
    "tiiuae/Falcon3-10B-Instruct-FC",
    "tiiuae/Falcon3-7B-Instruct-FC",
    "tiiuae/Falcon3-3B-Instruct-FC",
    "tiiuae/Falcon3-1B-Instruct-FC",
    "uiuc-convai/CoALM-8B",
    "uiuc-convai/CoALM-70B",
    "uiuc-convai/CoALM-405B",
    "BitAgent/BitAgent-8B",
    "ThinkAgents/ThinkAgent-1B",
    "meta-llama/llama-4-maverick-17b-128e-instruct-fp8-novita",
    "meta-llama/llama-4-maverick-17b-128e-instruct-fp8-FC-novita",
    "meta-llama/llama-4-scout-17b-16e-instruct-novita",
    "meta-llama/llama-4-scout-17b-16e-instruct-FC-novita",
    "qwen/qwq-32b-FC-novita",
    "qwen/qwq-32b-novita",
    "qwen/qwen3-4B",
]
