{"cells": [{"cell_type": "markdown", "id": "751962fd-529e-4d29-a4ea-899fe48a59b0", "metadata": {}, "source": ["# MateConv mini inference"]}, {"cell_type": "code", "execution_count": 1, "id": "208b734c-cee3-4fe5-8343-ff675595db1a", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/root/miniconda3/envs/MateConv/lib/python3.10/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["import itertools\n", "import re\n", "import json\n", "import jsonlines\n", "import psutil\n", "import ujson\n", "import numpy as np\n", "import pandas as pd\n", "from transformers import AutoTokenizer\n", "from datasets import load_dataset\n", "import os\n", "from tqdm import tqdm\n", "import torch\n", "from model.model import Transformer  # 确保路径正确\n", "from model.LMConfig import LMConfig   # 导入 LMConfig"]}, {"cell_type": "code", "execution_count": 2, "id": "1a19b89f-b998-4eee-a316-16e891cc94ba", "metadata": {}, "outputs": [], "source": ["# 定义BOS和EOS标记\n", "bos_token = \"<s>\"\n", "eos_token = \"</s>\""]}, {"cell_type": "code", "execution_count": 3, "id": "73a08ecd-14f6-4905-851d-b8794d4b6d36", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["加载的tokenizer词表大小: 6400\n"]}], "source": ["# 加载训练好的分词器路径\n", "tokenizer = AutoTokenizer.from_pretrained('/root/autodl-tmp/MateConv/model/mateconv_tokenizer', use_fast=False)\n", "print(f'加载的tokenizer词表大小: {len(tokenizer)}')"]}, {"cell_type": "code", "execution_count": 4, "id": "73e123fa-65ed-446d-b851-ad7a3e068b29", "metadata": {}, "outputs": [], "source": ["# 创建配置对象\n", "lm_config = LMConfig()"]}, {"cell_type": "code", "execution_count": 5, "id": "d0240abb-5a5b-4333-939f-b932503e152e", "metadata": {}, "outputs": [], "source": ["# 初始化 Transformer 模型\n", "model = Transformer(lm_config)"]}, {"cell_type": "code", "execution_count": 6, "id": "7a744ab6-f2e6-4c82-b8d9-e6d4260dee5b", "metadata": {}, "outputs": [], "source": ["device = torch.device(\"cuda\" if torch.cuda.is_available() else \"cpu\")"]}, {"cell_type": "code", "execution_count": 7, "id": "da4d3851-b1c5-4d89-a0a5-0b2371a9a7a6", "metadata": {}, "outputs": [{"data": {"text/plain": ["device(type='cuda')"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["device"]}, {"cell_type": "code", "execution_count": 8, "id": "641a636e-4c9c-43db-b08e-1418d1c49d93", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Transformer(\n", "  (tok_embeddings): Embedding(6400, 512)\n", "  (dropout): Dropout(p=0.0, inplace=False)\n", "  (layers): ModuleList(\n", "    (0-7): 8 x TransformerBlock(\n", "      (attention): Attention(\n", "        (wq): Linear(in_features=512, out_features=512, bias=False)\n", "        (wk): Linear(in_features=512, out_features=256, bias=False)\n", "        (wv): Linear(in_features=512, out_features=256, bias=False)\n", "        (wo): Linear(in_features=512, out_features=512, bias=False)\n", "        (attn_dropout): Dropout(p=0.0, inplace=False)\n", "        (resid_dropout): Dropout(p=0.0, inplace=False)\n", "      )\n", "      (attention_norm): RMSNorm()\n", "      (ffn_norm): RMSNorm()\n", "      (feed_forward): FeedForward(\n", "        (w1): Linear(in_features=512, out_features=1408, bias=False)\n", "        (w2): Linear(in_features=1408, out_features=512, bias=False)\n", "        (w3): Linear(in_features=512, out_features=1408, bias=False)\n", "        (dropout): Dropout(p=0.0, inplace=False)\n", "      )\n", "    )\n", "  )\n", "  (norm): RMSNorm()\n", "  (output): Linear(in_features=512, out_features=6400, bias=False)\n", ")\n"]}], "source": ["model.to(device)\n", "\n", "# 检查模型结构和参数\n", "print(model)"]}, {"cell_type": "code", "execution_count": 9, "id": "7acb473f-0c3f-4434-82e6-46223a9470d9", "metadata": {}, "outputs": [{"data": {"text/plain": ["Transformer(\n", "  (tok_embeddings): Embedding(6400, 512)\n", "  (dropout): Dropout(p=0.0, inplace=False)\n", "  (layers): ModuleList(\n", "    (0-7): 8 x TransformerBlock(\n", "      (attention): Attention(\n", "        (wq): Linear(in_features=512, out_features=512, bias=False)\n", "        (wk): Linear(in_features=512, out_features=256, bias=False)\n", "        (wv): Linear(in_features=512, out_features=256, bias=False)\n", "        (wo): Linear(in_features=512, out_features=512, bias=False)\n", "        (attn_dropout): Dropout(p=0.0, inplace=False)\n", "        (resid_dropout): Dropout(p=0.0, inplace=False)\n", "      )\n", "      (attention_norm): RMSNorm()\n", "      (ffn_norm): RMSNorm()\n", "      (feed_forward): FeedForward(\n", "        (w1): Linear(in_features=512, out_features=1408, bias=False)\n", "        (w2): Linear(in_features=1408, out_features=512, bias=False)\n", "        (w3): Linear(in_features=512, out_features=1408, bias=False)\n", "        (dropout): Dropout(p=0.0, inplace=False)\n", "      )\n", "    )\n", "  )\n", "  (norm): RMSNorm()\n", "  (output): Linear(in_features=512, out_features=6400, bias=False)\n", ")"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# 加载模型权重【这里要修改为你的模型地址】\n", "model.load_state_dict(torch.load('out/pretrain_512.pth', map_location=device))\n", "model.eval()  # 切换到评估模式"]}, {"cell_type": "code", "execution_count": 43, "id": "fcba18c6-7e26-4789-8b52-9bc7dc7b7964", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["基于人类数据的计算方法。\n", "在本书中，作者通过对比分析了大脑和大脑之间的关系、思维方式以及行为模式等问题，并结合大量的数据资料进行了深入剖析，为读者提供了一个很好的参考工具。\n"]}], "source": ["# 准备输入文本\n", "input_text = \"决策树是机器学习中的一种算法，决策树是\"\n", "input_ids = tokenizer.encode(input_text, return_tensors='pt').to(device)\n", "\n", "# 生成文本\n", "max_new_tokens = 500  # 生成 100 个 token\n", "eos_token_id = tokenizer.eos_token_id  # 终止 token\n", "\n", "# 在model.py中我们定义了generate方法、专用于生成\n", "# 调用 model.generate() 进行生成\n", "output_ids = next(model.generate(\n", "    idx=input_ids,\n", "    eos=eos_token_id,\n", "    max_new_tokens=max_new_tokens,\n", "    temperature=0.2,  # 控制创造性\n", "    top_k=10,  # 限制 top-k 采样\n", "    rp=1.2,  # 避免重复\n", "    stream=False  # 关闭流式返回\n", "))\n", "\n", "# 解码生成的 token\n", "generated_text = tokenizer.decode(output_ids[0], skip_special_tokens=True)\n", "\n", "# 打印最终生成的文本\n", "print(generated_text)"]}, {"cell_type": "code", "execution_count": 45, "id": "56de782d-2a17-4700-918b-b22d5e96ccc6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["在一起。\n", "我爱你，也许你会觉得有点不公平了，但我会努力奋斗一辈子！我爱你，也许只是一段美好的时光而已。”他如此的坚定信念：“对不起，我的爱，是我的力量。希望能帮助你的人生，让生命更加美丽！”\n", "“我们是最好的男人！”，他说到最后。“如果有一天你不会再见过我，我一定会珍惜这份温暖和关怀。\n"]}], "source": ["# 准备输入文本\n", "input_text = \"我与你\"\n", "input_ids = tokenizer.encode(input_text, return_tensors='pt').to(device)\n", "\n", "# 生成文本\n", "max_new_tokens = 500  # 生成 100 个 token\n", "eos_token_id = tokenizer.eos_token_id  # 终止 token\n", "\n", "# 在model.py中我们定义了generate方法、专用于生成\n", "# 调用 model.generate() 进行生成\n", "output_ids = next(model.generate(\n", "    idx=input_ids,\n", "    eos=eos_token_id,\n", "    max_new_tokens=max_new_tokens,\n", "    temperature=1,  # 控制创造性\n", "    top_k=10,  # 限制 top-k 采样\n", "    rp=1.2,  # 避免重复\n", "    stream=False  # 关闭流式返回\n", "))\n", "\n", "# 解码生成的 token\n", "generated_text = tokenizer.decode(output_ids[0], skip_special_tokens=True)\n", "\n", "# 打印最终生成的文本\n", "print(generated_text)"]}, {"cell_type": "code", "execution_count": null, "id": "1febf6f8-6f5a-483d-91d7-58ac81714180", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.16"}}, "nbformat": 4, "nbformat_minor": 5}