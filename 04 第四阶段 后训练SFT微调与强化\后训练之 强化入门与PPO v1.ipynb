import random

def run_episode():
    s = 0                      # 起点
    total_reward = 0
    while True:
        # 建立一个“代理”来选择相应的动作
        # 这个“代理”决定了接下来会执行什么动作
        # “代理”可以是一段代码、一个算法、或者是一个程序
        # 例如：可以是神经网络，也可以就是一个单纯的random.choice
        # 现在的策略是，左右各有50%概率
        a = random.choice(['L', 'R']) 
        # s：用格子编号来代表状态
        s = max(0, min(4, s + (1 if a == 'R' else -1)))
        # r：如果在4号格子就给1分、如果在其他格子就不给分
        r = 1 if s == 4 else 0
        print(f"state={s}, action={a}, reward={r}")
        total_reward += r
        if r == 1:
            break
    return total_reward

run_episode()

# 定义方程组求解 V(s)，假设策略是随机策略（左右走各 0.5 概率），gamma=0.9

import numpy as np

# 状态数
n_states = 5
gamma = 0.9

# 建立线性方程组 A * V = b
A = np.zeros((n_states, n_states))
b = np.zeros(n_states)

# 状态 0 到 3 的方程（V[i] = 0.5*V[i-1] + 0.5*V[i+1]）
for s in range(n_states):
    if s == 0:
        A[s, s] = 1
        A[s, s] -= 0.5 * gamma  # self
        A[s, s+1] -= 0.5 * gamma
    elif s == n_states - 1:
        A[s, s] = 1
        b[s] = 1  # 终点奖励为 1
    else:
        A[s, s] = 1
        A[s, s-1] -= 0.5 * gamma
        A[s, s+1] -= 0.5 * gamma

# 求解 V
V = np.linalg.solve(A, b)

import pandas as pd

df = pd.DataFrame({
    'State (s)': list(range(n_states)),
    'V(s)': V.round(4)
})

df

