{"cells": [{"cell_type": "markdown", "id": "a48c5c25-96e6-4bb9-b216-a9fdc638e035", "metadata": {}, "source": ["- 环境配置"]}, {"cell_type": "markdown", "id": "0f81dca0-987f-4379-a3fb-9b752148454a", "metadata": {}, "source": ["```shell\n", "\n", "#建立线上虚拟环境，命名为MateConv\n", "conda create --name MateConv python=3.10\n", "conda init\n", "source ~/.bashrc\n", "conda activate MateConv\n", "\n", "#创建<PERSON><PERSON><PERSON>\n", "conda install jupyterlab\n", "conda install ipykernel\n", "python -m ipykernel install --user --name MateConv --display-name \"Python (MateConv)\"\n", "\n", "#创建项目主目录\n", "cd ~/autodl-tmp\n", "mk<PERSON>\n", "\n", "#打开jupyter\n", "cd ~/autodl-tmp/MateConv\n", "jupyter lab --allow-root\n", "\n", "#根据requirements.txt配置环境\n", "cd ~/autodl-tmp/MateConv\n", "pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple\n", "```"]}, {"cell_type": "markdown", "id": "d64c2a82-9213-4f7e-ad93-457386285ddc", "metadata": {}, "source": ["- 数据集"]}, {"cell_type": "markdown", "id": "96a11855-033c-4ea1-9d99-bed5938dab06", "metadata": {}, "source": ["匠数科技大模型sft数据集官方地址：https://www.modelscope.cn/datasets/deepctrl/deepctrl-sft-data"]}, {"cell_type": "markdown", "id": "fc0142f2-a1cc-452e-af97-481dca289373", "metadata": {}, "source": ["- 指令微调数据集获取与数据集清洗"]}, {"cell_type": "markdown", "id": "98da9866-b505-4c8d-aa60-a2dea6fce738", "metadata": {}, "source": ["```shell\n", "conda activate MateConv\n", "pip install modelscope\n", "\n", "cd /root/autodl-tmp/MateConv\n", "\n", "sudo apt update\n", "sudo apt install git-lfs -y\n", "\n", "#设置modelscope环境为阿里云，加速下载\n", "export MODELSCOPE_ENVIRONMENT=cloud\n", "\n", "git lfs install\n", "# 大约需要5s时间进行拉取\n", "git clone https://www.modelscope.cn/datasets/deepctrl/deepctrl-sft-data.git\n", "\n", "# 拉取完所有git文件后，执行下面的代码，正式开始拉取数据\n", "# 速度大约在8MB/s下，拉取32G文件、需拉取1小时左右\n", "# 其中18G为中文文件，12G为英文文件\n", "cd deepctrl-sft-data\n", "git lfs install\n", "git lfs pull\n", "\n", "# 大部分modelscope的数据是通过下面CLI的方式下载\n", "# 下载流程都不太稳定，依据网速可能需要3-4小时时间\n", "# 可以直接从百度网盘中下载后上传，在autodl限速下大概传1.5小时时间\n", "# modelscope download --dataset deepctrl/deepctrl-sft-data --local_dir ./data\n", "\n", "# 拉取完毕后、执行清洗代码\n", "cd /root/autodl-tmp/MateConv\n", "python data_process.py\n", "\n", "```"]}, {"cell_type": "markdown", "id": "4974d20c-45e3-4946-9583-04bd0f941cc4", "metadata": {}, "source": ["- 开始训练"]}, {"cell_type": "markdown", "id": "cddeb098-f936-4091-8e86-096a07b67e25", "metadata": {}, "source": ["```shell\n", "pip install wandb\n", "\n", "deepspeed --master_port 29500 --num_gpus=8 full_sft.py --out_dir out --epochs 5 --use_wandb --wandb_project \"MateConv-SFT\"\n", "```"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.7"}}, "nbformat": 4, "nbformat_minor": 5}